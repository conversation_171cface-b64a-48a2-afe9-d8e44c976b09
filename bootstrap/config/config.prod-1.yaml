# Production endpoints configuration
region: us-east-1
auth:
  client_id: ""
  admin_secret_arn: "arn:aws:secretsmanager:us-east-2:459006232362:secret:admin_user_creds-Xdp0vT"
services:
  orgs:
    host: orgs.api.tcu.gethero.com
    port: 443
  assets:
    host: workflow.api.tcu.gethero.com
    port: 443
  situations:
    host: workflow.api.tcu.gethero.com
    port: 443
  communications:
    host: communications.api.tcu.gethero.com
    port: 443
  sensors:
    host: sensors.api.tcu.gethero.com
    port: 443
  command:
    host: command.api.tcu.gethero.com
    port: 443
  permissions:
    host: perms.api.tcu.gethero.com
    port: 443 
  filerepository:
    host: filerepository.api.tcu.gethero.com
    port: 443