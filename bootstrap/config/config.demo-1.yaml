# Demo development endpoints configuration
region: us-west-2
auth:
  client_id: "7t4q2jiimuu10t08cgu2c55sp7"
  admin_secret_arn: "arn:aws:secretsmanager:us-west-2:898896902511:secret:admin_user_creds-5FmVVU"
services:
  orgs:
    host: orgs.api.demo-1.gethero.com
    port: 443
  assets:
    host: workflow.api.demo-1.gethero.com
    port: 443
  situations:
    host: workflow.api.demo-1.gethero.com
    port: 443
  communications:
    host: communications.api.demo-1.gethero.com
    port: 443
  sensors:
    host: sensors.api.demo-1.gethero.com
    port: 443
  command:
    host: command.api.demo-1.gethero.com
    port: 443
  permissions:
    host: perms.api.demo-1.gethero.com
    port: 443 
  filerepository:
    host: filerepository.api.demo-1.gethero.com
    port: 443