name: Deploy

on:
  push:
    branches:
      - main
    paths:
      - 'services/**'
      - 'infra/**'
      - 'lib/**'
      - 'apps/**'
      - '.github/workflows/**'

# Add concurrency control to queue deployments
concurrency:
  group: production-deployment
  cancel-in-progress: false  # Don't cancel running deployments, queue them instead

permissions:
  id-token: write
  contents: write

jobs:
  build-and-push:
    name: Build and Push Docker Images
    runs-on:
      group: large_runners
    # Add timeout to prevent stuck deployments
    timeout-minutes: 60

    steps:
    # Step 0: Notify deployment start
    - name: Notify deployment start
      run: |
        echo "::notice::🚀 Starting deployment for commit ${{ github.sha }}"
        echo "::notice::📋 Deployment will be queued if another deployment is running"
        
    # Step 1: Checkout the code
    - name: Checkout code
      uses: actions/checkout@v3
      with:
        fetch-depth: 0  # Ensures all commits and tags are fetched

    # Step 2: Check for changes in sensors service and its dependencies
    - name: Check for changes in sensors service
      id: check_changes
      run: |
        # Retrieve the second most recent tag matching the pattern 'v*'
        # 1. List all tags matching 'v*'
        # 2. Sort them in version order (-V)
        # 3. Get the last two tags (tail -n 2)
        # 4. Select the second-to-last tag (head -n 1)
        PREV_TAG=$(git tag -l 'v*' | sort -V | tail -n 2 | head -n 1)
        if [ -z "$PREV_TAG" ]; then
          echo "No previous tag found, will build all services"
          echo "sensors_changed=true" >> $GITHUB_OUTPUT
        else
          echo "Comparing against previous deployment tag: $PREV_TAG"
          # Check for changes in sensors service and shared dependencies
          if git diff --quiet $PREV_TAG HEAD -- ./services/sensors/; then
            echo "sensors_changed=false" >> $GITHUB_OUTPUT
          else
            echo "sensors_changed=true" >> $GITHUB_OUTPUT
          fi
        fi

    # Step 3: Set up Docker Buildx
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2

    # Step 4: Configure AWS credentials for shared account
    - name: configure aws credentials for shared account
      uses: aws-actions/configure-aws-credentials@v4
      with:
        role-to-assume: arn:aws:iam::************:role/GitHubActionsDeployRole
        aws-region: us-west-2

    # Step 5: Log in to Amazon ECR
    - name: Log in to Amazon ECR
      uses: aws-actions/amazon-ecr-login@v1

    # Step 6: Build and push Docker images for all services
    - name: Build and push Docker images
      run: |
        if [ "$(echo "${{ steps.check_changes.outputs.sensors_changed }}" | xargs | tr '[:upper:]' '[:lower:]')" = "false" ]; then
          echo "Skipping sensors service build - no changes detected"
          SERVICES_TO_SKIP=sensors ./infra/scripts/build-and-publish-services.sh
        else
          ./infra/scripts/build-and-publish-services.sh
        fi
      env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    # Step 7: Set up Go
    - name: Set up Go
      uses: actions/setup-go@v5
      with:
        go-version: '1.23'

    # Step 8: Set up Node.js
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '22'

    # Step 9: Prepare CDK (build lambda zip files)
    - name: Prepare CDK
      run: ./infra/scripts/prepare-cdk.sh

    # Step 10: Configure AWS credentials for demo account
    - name: configure aws credentials for demo account
      uses: aws-actions/configure-aws-credentials@v4
      with:
        role-to-assume: arn:aws:iam::************:role/GitHubActionsRole
        aws-region: us-west-2

    # Step 11: Deploy Migration Runner Lambda
    - name: CDK Deploy MigrationRunnerStack for Migration Lambda
      run: |
        cd infra
        npm install
        CDK_ENV=demo-1 cdk deploy --require-approval never --exclusively MigrationRunnerStack 

    # Step 12: Invoke Migration Lambda
    - name: Invoke Migration Lambda
      run: |
        echo "::notice::📦 Invoking migration lambda..."
        aws lambda invoke \
          --function-name MigrationRunnerLambda \
          --payload '{}' \
          /tmp/migration-output.json
        cat /tmp/migration-output.json
        if jq -e '.message | test("Migrations ran successfully!")' /tmp/migration-output.json > /dev/null; then
            echo "::notice::Migration lambda finished successfully."
        else
            echo "::error::Migration lambda returned an error."
            jq '.' /tmp/migration-output.json
            exit 1
        fi

    # Step 13: Run CDK Deploy for all main services
    - name: Run CDK Deploy
      run: ./infra/scripts/deploy-services.sh

    # Step 14: Invalidate CloudFront cache
    - name: Invalidate CloudFront cache
      run: |
        aws cloudfront create-invalidation \
          --distribution-id E121HXCBRCHQXV \
          --paths "/*"

    # Step 15: Notify deployment success
    - name: Notify deployment success
      run: |
        echo "::notice::✅ Deployment completed successfully for commit ${{ github.sha }}"