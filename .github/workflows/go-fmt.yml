name: Fmt

on:
  push:
    branches:
      - main
  pull_request:

jobs:
  lint-and-format:
    name: Format and Lint Go Code
    runs-on: ubuntu-latest

    steps:
    # Step 1: Checkout code
    - name: Checkout code
      uses: actions/checkout@v3

    # Step 2: Set up Go
    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: 1.23  # Use the desired Go version

    # Step 3: Install linters and formatters
    - name: Install tools
      run: |
        go install golang.org/x/tools/cmd/goimports@latest

    # Step 4: Run gofmt
    - name: Run gofmt
      run: |
        echo "Running gofmt..."
        unformatted=$(gofmt -l .)
        if [ -n "$unformatted" ]; then
          echo "The following files are not formatted:"
          echo "$unformatted"
          exit 1
        fi

    # Step 5: Run goimports
    - name: Run goimports
      run: |
        echo "Running goimports..."
        cd services
        goimports -l -w .
        git diff --exit-code || (echo "Goimports modified files. Please commit changes." && exit 1)

    # Optional: Fail if git status is dirty
    - name: Ensure no uncommitted changes
      run: |
        if [ -n "$(git status --porcelain)" ]; then
          echo "Uncommitted changes detected after formatting. Please commit changes."
          exit 1
        fi
