-- +goose Up
-- +goose StatementBegin
/*───────────────────────────────────────────────────────────────────────────────
 hero.etl.v1 – relational schema
───────────────────────────────────────────────────────────────────────────────
  ⬢  Multi‑tenant RLS enforced by org_id for organization isolation
  ⬢  ETL job lifecycle: PENDING → EXTRACTING → TRANSFORMING → LOADING → COMPLETED/FAILED/CANCELLED
  ⬢  Supports compliance data standards: NIBRS
  ⬢  Progress tracking with comprehensive retry logic and error handling
  ⬢  Generated content storage with size limits and integrity tracking
  ⬢  Audit trail for job creation, updates, and error events
───────────────────────────────────────────────────────────────────────────────*/

-- Extensions
CREATE EXTENSION IF NOT EXISTS pgcrypto;    -- gen_random_uuid()

/*==============================================================================
  1. Main table: etl_jobs (ETLJob)
==============================================================================*/
CREATE TABLE etl_jobs (
    -- Core identity
    id                      TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    org_id                  INTEGER NOT NULL REFERENCES orgs(id) ON DELETE CASCADE,
    agency_id               TEXT NOT NULL,                     -- Target agency (FBI, STATE_POLICE, etc.)
    output_format           INTEGER NOT NULL,                  -- OutputFormat enum (NIBRS, etc.)
    status                  INTEGER NOT NULL DEFAULT 1,        -- JobStatus enum (PENDING=1)
    
    -- Processing filters
    report_ids              TEXT[],                            -- Specific reports to process
    date_from               TEXT,                              -- ISO8601 date range start
    date_to                 TEXT,                              -- ISO8601 date range end
    case_types              TEXT[],                            -- Filter by case types
    
    -- Progress tracking
    total_reports           INTEGER DEFAULT 0 CHECK (total_reports >= 0),                 -- Total reports to process
    reports_processed       INTEGER DEFAULT 0 CHECK (reports_processed >= 0),             -- Successfully processed
    reports_failed          INTEGER DEFAULT 0 CHECK (reports_failed >= 0),               -- Failed processing
    reports_skipped         INTEGER DEFAULT 0 CHECK (reports_skipped >= 0),              -- Skipped (invalid data)
    
    -- Timestamps
    created_at              TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    started_at              TIMESTAMPTZ,                       -- When job started
    completed_at            TIMESTAMPTZ,                       -- When job completed
    last_updated_at         TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    
    -- Results and content
    error_message           TEXT,                              -- Primary error message
    generated_content       BYTEA,                             -- Generated content
    content_type            TEXT,                              -- MIME type
    content_size_bytes      BIGINT DEFAULT 0 CHECK (content_size_bytes >= 0),            -- Size of generated content
    
    -- Submission details
    agency_submission_id    TEXT,                              -- Agency's tracking ID
    submission_response     TEXT,                              -- Agency's response message
    submitted_at            TIMESTAMPTZ,                       -- When submitted to agency
    
    -- Retry information
    retry_count             INTEGER DEFAULT 0 CHECK (retry_count >= 0),                  -- Number of retries made
    max_retries             INTEGER DEFAULT 3 CHECK (max_retries >= 0),                  -- Max retries allowed
    last_retry_at           TIMESTAMPTZ,                       -- Last retry timestamp
    auto_retry_enabled      BOOLEAN DEFAULT true,              -- Auto retry on failure
    
    -- Metadata
    created_by_asset_id     TEXT,                              -- Who created the job
    preview_only            BOOLEAN DEFAULT false,             -- Generate but don't send
    job_name                TEXT                               -- Human-readable name
);

COMMENT ON TABLE etl_jobs IS 'ETL job tracking for compliance data standards (NIBRS, etc.)';
COMMENT ON COLUMN etl_jobs.status IS 'JobStatus enum: 0=UNSPECIFIED, 1=PENDING, 2=EXTRACTING, 3=TRANSFORMING, 4=LOADING, 5=COMPLETED, 6=FAILED, 7=CANCELLED';
COMMENT ON COLUMN etl_jobs.output_format IS 'OutputFormat enum: 0=UNSPECIFIED, 1=NIBRS_XML';
COMMENT ON COLUMN etl_jobs.agency_id IS 'Target agency/recipient (e.g., "FBI", "STATE_POLICE", "UNIVERSITY_POLICE")';
COMMENT ON COLUMN etl_jobs.generated_content IS 'Generated content for inspection and submission';
COMMENT ON COLUMN etl_jobs.content_size_bytes IS 'Size of generated content in bytes';

/*==============================================================================
  2. Error tracking table: etl_job_errors (JobError)
==============================================================================*/
CREATE TABLE etl_job_errors (
    id                      TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    job_id                  TEXT NOT NULL REFERENCES etl_jobs(id) ON DELETE CASCADE,
    org_id                  INTEGER NOT NULL REFERENCES orgs(id) ON DELETE CASCADE,
    error_code              TEXT NOT NULL,                     -- Error code (INVALID_DATA, etc.)
    error_message           TEXT NOT NULL,                     -- Human-readable message
    report_id               TEXT,                              -- Specific report that failed
    field_path              TEXT,                              -- Field that caused error
    occurred_at             TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    is_retryable            BOOLEAN DEFAULT false,             -- Whether error is retryable
    retry_attempt           INTEGER DEFAULT 0 CHECK (retry_attempt >= 0)    -- Which retry attempt
);

COMMENT ON TABLE etl_job_errors IS 'Detailed error tracking for ETL job failures with retry context';
COMMENT ON COLUMN etl_job_errors.error_code IS 'Error classification: INVALID_DATA, NETWORK_ERROR, VALIDATION_FAILED, etc.';
COMMENT ON COLUMN etl_job_errors.report_id IS 'Specific report ID that caused the error (optional)';
COMMENT ON COLUMN etl_job_errors.field_path IS 'Specific field path that failed validation (optional)';
COMMENT ON COLUMN etl_job_errors.retry_attempt IS 'Which retry attempt this error occurred on (0 = initial attempt)';

-- Auto-populate org_id from parent job
CREATE OR REPLACE FUNCTION set_etl_error_org_id()
RETURNS trigger LANGUAGE plpgsql AS $$
BEGIN
    NEW.org_id := (SELECT org_id FROM etl_jobs WHERE id = NEW.job_id);
    RETURN NEW;
END;
$$;

CREATE TRIGGER etl_job_errors_set_org_id
    BEFORE INSERT ON etl_job_errors
    FOR EACH ROW EXECUTE FUNCTION set_etl_error_org_id();

-- Row Level Security
ALTER TABLE etl_jobs ENABLE ROW LEVEL SECURITY;

CREATE POLICY etl_jobs_organization_access
ON etl_jobs
USING (
  org_id = ANY(
    string_to_array(
      coalesce(current_setting('app.allowed_org_ids', true),''),
      ','
    )::integer[]
  )
)
WITH CHECK (
  org_id = ANY(
    string_to_array(
      coalesce(current_setting('app.allowed_org_ids', true),''),
      ','
    )::integer[]
  )
);

ALTER TABLE etl_job_errors ENABLE ROW LEVEL SECURITY;

CREATE POLICY etl_job_errors_organization_access
ON etl_job_errors
USING (
  org_id = ANY(
    string_to_array(
      coalesce(current_setting('app.allowed_org_ids', true),''),
      ','
    )::integer[]
  )
)
WITH CHECK (
  org_id = ANY(
    string_to_array(
      coalesce(current_setting('app.allowed_org_ids', true),''),
      ','
    )::integer[]
  )
);

-- Indexes for performance (based on actual service method usage)
CREATE INDEX idx_etl_jobs_org_id ON etl_jobs (org_id);                    -- RLS and basic filtering
CREATE INDEX idx_etl_jobs_status ON etl_jobs (status);                    -- ListJobs status filter
CREATE INDEX idx_etl_jobs_agency_id ON etl_jobs (agency_id);              -- ListJobs, GetJobStats agency filter
CREATE INDEX idx_etl_jobs_output_format ON etl_jobs (output_format);      -- GetJobStats format filter
CREATE INDEX idx_etl_jobs_created_at ON etl_jobs (created_at DESC);       -- ListJobs, GetJobStats date filtering
CREATE INDEX idx_etl_jobs_agency_status ON etl_jobs (agency_id, status);  -- Common ListJobs pattern

-- Error table indexes (minimal for GetJob error retrieval)
CREATE INDEX idx_etl_job_errors_org_id ON etl_job_errors (org_id);        -- RLS
CREATE INDEX idx_etl_job_errors_job_id ON etl_job_errors (job_id);        -- GetJob error lookup

-- Auto-update last_updated_at timestamp
CREATE OR REPLACE FUNCTION update_etl_job_timestamp()
RETURNS trigger LANGUAGE plpgsql AS $$
BEGIN
    NEW.last_updated_at := CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;

CREATE TRIGGER etl_jobs_update_timestamp
    BEFORE UPDATE ON etl_jobs
    FOR EACH ROW EXECUTE FUNCTION update_etl_job_timestamp();

-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
/*───────────────────────────────────────────────────────────────────────────────
  DOWN - Drop in reverse dependency order
───────────────────────────────────────────────────────────────────────────────*/

-- Drop triggers and functions
DROP TRIGGER IF EXISTS etl_job_errors_set_org_id ON etl_job_errors;
DROP FUNCTION IF EXISTS set_etl_error_org_id();
DROP TRIGGER IF EXISTS etl_jobs_update_timestamp ON etl_jobs;
DROP FUNCTION IF EXISTS update_etl_job_timestamp();

-- Drop indexes
DROP INDEX IF EXISTS idx_etl_job_errors_job_id;
DROP INDEX IF EXISTS idx_etl_job_errors_org_id;

DROP INDEX IF EXISTS idx_etl_jobs_agency_status;
DROP INDEX IF EXISTS idx_etl_jobs_created_at;
DROP INDEX IF EXISTS idx_etl_jobs_output_format;
DROP INDEX IF EXISTS idx_etl_jobs_agency_id;
DROP INDEX IF EXISTS idx_etl_jobs_status;
DROP INDEX IF EXISTS idx_etl_jobs_org_id;

-- Drop policies and disable RLS
DROP POLICY IF EXISTS etl_job_errors_organization_access ON etl_job_errors;
ALTER TABLE etl_job_errors DISABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS etl_jobs_organization_access ON etl_jobs;
ALTER TABLE etl_jobs DISABLE ROW LEVEL SECURITY;

-- Drop tables
DROP TABLE IF EXISTS etl_job_errors;
DROP TABLE IF EXISTS etl_jobs;

-- +goose StatementEnd
