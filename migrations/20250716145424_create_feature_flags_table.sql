-- +goose Up
-- +goose StatementBegin

/*===============================================================================
 Feature Flags Table - Simple feature toggle system
===============================================================================

Purpose:
--------
Provides a simple, hierarchical feature flag system where features can be:
1. Enabled/disabled organization-wide (asset_id = NULL)
2. Enabled/disabled for specific assets (overrides org-wide settings)

Key Design Decisions:
--------------------
1. Features are pre-defined enums in protobuf, not dynamically created
2. Asset-specific settings override organization-wide settings
3. Uses partial unique indexes instead of single UNIQUE constraint to handle NULLs
4. ON DELETE CASCADE ensures cleanup when assets are deleted

Hierarchy:
----------
- Check asset-specific setting first
- Fall back to org-wide setting if no asset-specific setting exists
- Default to disabled if no setting exists at all

===============================================================================*/

-- Enable UUID generation
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- Main feature flags table
CREATE TABLE feature_flags (
    -- Primary key
    id                      TEXT PRIMARY KEY DEFAULT gen_random_uuid()::text,
    
    -- Multi-tenant organization reference (required)
    org_id                  INTEGER NOT NULL REFERENCES orgs(id) ON DELETE CASCADE,
    
    -- Feature identifier from protobuf enum
    feature                 INTEGER NOT NULL,
    
    -- Target asset ID (NULL = org-wide setting)
    -- ON DELETE CASCADE ensures feature flags are cleaned up when assets are deleted
    asset_id                TEXT REFERENCES assets(id) ON DELETE CASCADE,
    
    -- Feature state
    enabled                 BOOLEAN NOT NULL DEFAULT false,
    
    -- Audit timestamps
    created_at              TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at              TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- Table documentation
COMMENT ON TABLE feature_flags IS 'Hierarchical feature flag system: asset-specific settings override org-wide settings';
COMMENT ON COLUMN feature_flags.org_id IS 'Organization ID for multi-tenant isolation';
COMMENT ON COLUMN feature_flags.feature IS 'Feature enum from proto (e.g., 1=NEW_DASHBOARD, 2=ADVANCED_REPORTS)';
COMMENT ON COLUMN feature_flags.asset_id IS 'Target asset ID. NULL = org-wide setting, non-NULL = asset-specific override';
COMMENT ON COLUMN feature_flags.enabled IS 'Whether the feature is enabled (true) or disabled (false)';

/*===============================================================================
 Row Level Security (RLS)
===============================================================================*/

-- Enable RLS for multi-tenant isolation
ALTER TABLE feature_flags ENABLE ROW LEVEL SECURITY;

-- Policy: Users can only access feature flags for their allowed organizations
CREATE POLICY feature_flags_organization_access
ON feature_flags
USING (
  org_id = ANY(
    string_to_array(
      coalesce(current_setting('app.allowed_org_ids', true),''),
      ','
    )::integer[]
  )
)
WITH CHECK (
  org_id = ANY(
    string_to_array(
      coalesce(current_setting('app.allowed_org_ids', true),''),
      ','
    )::integer[]
  )
);

/*===============================================================================
 Indexes
===============================================================================*/

-- Performance indexes
CREATE INDEX idx_feature_flags_org_id ON feature_flags (org_id);
CREATE INDEX idx_feature_flags_asset_id ON feature_flags (asset_id) WHERE asset_id IS NOT NULL;
CREATE INDEX idx_feature_flags_org_feature ON feature_flags (org_id, feature);
CREATE INDEX idx_feature_flags_lookup ON feature_flags (org_id, feature, asset_id);

/*===============================================================================
 Unique Constraints - Partial Indexes
===============================================================================
 
IMPORTANT: We use partial unique indexes instead of a single UNIQUE constraint
because PostgreSQL treats NULL values as distinct in UNIQUE constraints.
This would allow multiple rows with (org_id=1, feature=1, asset_id=NULL).

Our solution uses two partial indexes:
1. For org-wide settings (where asset_id IS NULL)
2. For asset-specific settings (where asset_id IS NOT NULL)

This ensures proper uniqueness while handling NULL values correctly.

NOTE: These partial indexes cannot be used with ON CONFLICT directly.
The repository code uses UPDATE-then-INSERT pattern instead of UPSERT.
===============================================================================*/

-- Ensure only one org-wide setting per feature per organization
CREATE UNIQUE INDEX unique_org_wide_feature 
ON feature_flags (org_id, feature) 
WHERE asset_id IS NULL;

-- Ensure only one asset-specific setting per feature per organization per asset
CREATE UNIQUE INDEX unique_asset_feature 
ON feature_flags (org_id, feature, asset_id) 
WHERE asset_id IS NOT NULL;

/*===============================================================================
 Triggers
===============================================================================*/

-- Function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_feature_flags_timestamp()
RETURNS trigger LANGUAGE plpgsql AS $$
BEGIN
    NEW.updated_at := CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;

-- Trigger to call the function on every UPDATE
CREATE TRIGGER feature_flags_update_timestamp
    BEFORE UPDATE ON feature_flags
    FOR EACH ROW EXECUTE FUNCTION update_feature_flags_timestamp();

-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin

/*===============================================================================
 Rollback Migration - Drop everything in reverse order
===============================================================================*/

-- Drop triggers first
DROP TRIGGER IF EXISTS feature_flags_update_timestamp ON feature_flags;
DROP FUNCTION IF EXISTS update_feature_flags_timestamp();

-- Drop indexes
DROP INDEX IF EXISTS unique_asset_feature;
DROP INDEX IF EXISTS unique_org_wide_feature;
DROP INDEX IF EXISTS idx_feature_flags_lookup;
DROP INDEX IF EXISTS idx_feature_flags_org_feature;
DROP INDEX IF EXISTS idx_feature_flags_asset_id;
DROP INDEX IF EXISTS idx_feature_flags_org_id;

-- Drop RLS policies
DROP POLICY IF EXISTS feature_flags_organization_access ON feature_flags;
ALTER TABLE feature_flags DISABLE ROW LEVEL SECURITY;

-- Finally drop the table
DROP TABLE IF EXISTS feature_flags;

-- +goose StatementEnd