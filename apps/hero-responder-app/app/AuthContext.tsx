import * as Sentry from '@sentry/react-native';
import {
  AuthRequestConfig,
  AuthSessionResult,
  DiscoveryDocument,
  exchangeCodeAsync,
  makeRedirectUri,
  refreshAsync,
  ResponseType,
  revokeAsync,
  TokenResponse,
  useAuthRequest,
} from "expo-auth-session";
import * as SecureStore from "expo-secure-store";
import * as WebBrowser from "expo-web-browser";
import { jwtDecode } from "jwt-decode";
import React, {
  createContext,
  PropsWithChildren,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { Alert } from "react-native";
import { EnvironmentConfig } from "./services/discoveryService";
import { setAccessGetter as setWorkflowAccessGetter } from "./apis/services/workflow/axiosInstance";
import { setAccessGetter as setCommunicationsAccessGetter } from "./apis/services/communications/axiosInstance";

WebBrowser.maybeCompleteAuthSession();

// No default config - only use discovered environment

const redirectUri = makeRedirectUri({
  native: "myapp://callback/",
  scheme: "myapp",
  path: "callback/",
});
console.log("redirectUri:", redirectUri);

/**
 * Create discovery document from environment configuration
 */
const createDiscoveryDocument = (config: EnvironmentConfig): DiscoveryDocument => ({
  authorizationEndpoint: `${config.userPoolUrl}/oauth2/authorize`,
  tokenEndpoint: `${config.userPoolUrl}/oauth2/token`,
  revocationEndpoint: `${config.userPoolUrl}/oauth2/revoke`,
});

interface AuthContextValue {
  authTokens: TokenResponse | null;
  promptAsync: () => Promise<AuthSessionResult>;
  logout: () => Promise<void>;
  isLoading: boolean;
  isAuthReady: boolean;
}

interface DecodedToken {
  sub: string;
  email?: string;
  username?: string;
}

const AuthContext = createContext<AuthContextValue>({} as AuthContextValue);

// Helper function to create a safe key from userPoolUrl
const createSafeKey = (userPoolUrl: string): string => {
  // Extract domain from URL and make it SecureStore-safe
  // https://auth.demo-1.gethero.com -> auth.demo-1.gethero.com
  const domain = userPoolUrl.replace(/^https?:\/\//, '').replace(/[^a-zA-Z0-9.-]/g, '_');
  return `authTokens_${domain}`;
};

// Helper functions to manage tokens in SecureStore
const saveTokens = async (tokens: TokenResponse | null, userPoolUrl?: string) => {
  const key = userPoolUrl ? createSafeKey(userPoolUrl) : "authTokens";
  if (tokens) {
    await SecureStore.setItemAsync(key, JSON.stringify(tokens));
  } else {
    await SecureStore.deleteItemAsync(key);
  }
};

const getTokens = async (userPoolUrl?: string): Promise<TokenResponse | null> => {
  const key = userPoolUrl ? createSafeKey(userPoolUrl) : "authTokens";
  const tokensString = await SecureStore.getItemAsync(key);
  return tokensString ? JSON.parse(tokensString) : null;
};

// Helper function to set Sentry user context
const setSentryUserContext = (tokens: TokenResponse | null) => {
  if (tokens?.idToken) {
    try {
      const decoded = jwtDecode<DecodedToken>(tokens.idToken);
      Sentry.setUser({
        id: decoded.sub,
        email: decoded.email,
        username: decoded.username,
      });
    } catch (error) {
      console.error("Error decoding token for Sentry context:", error);
      Sentry.setUser(null);
    }
  } else {
    Sentry.setUser(null);
  }
};

interface AuthProviderProps extends PropsWithChildren {
  environmentConfig?: EnvironmentConfig | null;
  onClearEnvironmentConfig?: () => void;
}

/**
 * Provider that wraps your app with the AuthContext.
 * It handles the expo-auth-session for Cognito and manages token persistence.
 * 
 * @param environmentConfig - Dynamic environment configuration from discovery service
 */
export function AuthProvider({ children, environmentConfig = null, onClearEnvironmentConfig }: AuthProviderProps) {
  const [authTokens, setAuthTokens] = useState<TokenResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Return early if no config - this shouldn't happen with key prop but safety first
  if (!environmentConfig) {
    return <AuthContext.Provider value={{
      authTokens: null,
      promptAsync: () => Promise.reject(new Error("Environment not configured")),
      logout: async () => {},
      isLoading: false,
      isAuthReady: false,
    }}>{children}</AuthContext.Provider>;
  }

  const config = environmentConfig;
  
  console.log("AuthProvider environmentConfig:", config.userPoolUrl);
  
  // Create discovery document and request config once per mount
  const discovery = createDiscoveryDocument(config);
  const requestConfig: AuthRequestConfig = {
    clientId: config.clientId,
    responseType: ResponseType.Code,
    redirectUri,
    usePKCE: true,
  };

  const [request, response, promptAsync] = useAuthRequest(
    requestConfig,
    discovery
  );

  // Track if auth request is ready (request is loaded with URL)
  const isAuthReady = !!request?.url;

  console.log("useAuthRequest created, request ID:", request?.url?.slice(-10), "isAuthReady:", isAuthReady);

  // Load tokens from SecureStore on initial mount
  useEffect(() => {
    const loadTokens = async () => {
      try {
        const tokens = await getTokens(config.userPoolUrl);
        if (tokens) {
          console.log("Loading existing tokens for config:", config.userPoolUrl);
          const expirationTime = tokens.issuedAt + (tokens.expiresIn || 0);
          const currentTime = Math.floor(Date.now() / 1000);

          if (currentTime > expirationTime) {
            // Attempt refresh if expired
            try {
              const newTokens = await refreshAsync(
                { clientId: config.clientId, refreshToken: tokens.refreshToken },
                discovery
              );
              if (!newTokens.refreshToken) {
                newTokens.refreshToken = tokens.refreshToken;
              }
              setAuthTokens(newTokens);
              setSentryUserContext(newTokens);
            } catch (error) {
              console.error("Token refresh failed:", error);
              await saveTokens(null, config.userPoolUrl);
              setAuthTokens(null);
              setSentryUserContext(null);
            }
          } else {
            setAuthTokens(tokens);
            setSentryUserContext(tokens);
          }
        }
      } catch (error) {
        console.error("Error loading tokens:", error);
        setAuthTokens(null);
        setSentryUserContext(null);
      } finally {
        setIsLoading(false);
      }
    };
    loadTokens();
  }, []);

  // Schedule background refresh
  useEffect(() => {
    if (!authTokens || !authTokens.expiresIn) return;
  
    const expirationTime = authTokens.issuedAt + authTokens.expiresIn;
    const currentTime = Math.floor(Date.now() / 1000);
    const timeUntilExpiry = expirationTime - currentTime;
  
    const refreshBuffer = 300;
    const refreshDelay = Math.max(0, (timeUntilExpiry - refreshBuffer) * 1000);
  
    const timeoutId = setTimeout(async () => {
      try {
        const newTokens = await refreshAsync(
          { clientId: config.clientId, refreshToken: authTokens.refreshToken },
          discovery
        );
        if (!newTokens.refreshToken) {
          newTokens.refreshToken = authTokens.refreshToken;
        }
        setAuthTokens(newTokens);
        setSentryUserContext(newTokens);
      } catch (error) {
        console.error("Background refresh failed:", error);
        setAuthTokens(null);
        setSentryUserContext(null);
      }
    }, refreshDelay);
  
    return () => clearTimeout(timeoutId);
  }, [authTokens]);  

  // Save tokens to SecureStore whenever they change
  useEffect(() => {
    if (!isLoading) {
      saveTokens(authTokens, config.userPoolUrl);
    }
  }, [authTokens, isLoading]);

  // Exchange the authorization code for tokens when response is "success"
  useEffect(() => {
    async function exchangeToken() {
      if (!request || !response) return;

      if (response.type === "success") {
        try {
          console.log("Token exchange using redirectUri:", redirectUri);
          console.log("Token exchange using clientId:", config.clientId);
          console.log("Token exchange using userPoolUrl:", config.userPoolUrl);
          
          const exchangeResponse = await exchangeCodeAsync(
            {
              code: response.params.code,
              clientId: config.clientId,
              redirectUri,
              extraParams: {
                code_verifier: request.codeVerifier || "",
              },
            },
            discovery
          );

          setAuthTokens(exchangeResponse);
          setSentryUserContext(exchangeResponse);
        } catch (error) {
          console.error("Error exchanging code:", error);
          Alert.alert(
            "Error",
            "Failed to exchange authorization code for tokens"
          );
        }
      } else if (response.type === "error") {
        Alert.alert(
          "Authentication error",
          response.params.error_description || "Something went wrong"
        );
      }
    }

    exchangeToken();
  }, [request, response]);

  // Helper function to clear all auth-related cache
  const clearAllAuthCache = async () => {
    try {
      // Clear Expo auth session PKCE cache
      await SecureStore.deleteItemAsync("expo-auth-session");
      
      // Clear organization-specific tokens
      await saveTokens(null, config.userPoolUrl);
      
      // Try to clear other possible Expo auth keys
      const possibleKeys = [
        "expo-auth-session", 
        "expo-auth-session-state",
        "authTokens" // Legacy key
      ];
      
      for (const key of possibleKeys) {
        try {
          await SecureStore.deleteItemAsync(key);
        } catch {
          // Ignore errors for non-existent keys
        }
      }
      
      // Clear WebView cookies and complete any pending auth sessions
      WebBrowser.maybeCompleteAuthSession({ skipRedirectCheck: true });
      
    } catch (error) {
      console.error("Error clearing auth cache:", error);
    }
  };

  // Logout -> proper hosted UI logout + comprehensive cache clearing
  const logout = async (): Promise<void> => {
    try {
      // Step 1: Open Cognito logout URL to clear hosted UI session
      const logoutUrl = `${config.userPoolUrl}/logout?client_id=${config.clientId}&logout_uri=${encodeURIComponent(redirectUri)}`;
      console.log("Opening hosted UI logout:", logoutUrl);
      
      const logoutResult = await WebBrowser.openAuthSessionAsync(logoutUrl, redirectUri);
      console.log("Hosted UI logout result:", logoutResult.type);
      
      // Step 2: Revoke refresh token (if logout was successful or cancelled)
      if (authTokens?.refreshToken && (logoutResult.type === "success" || logoutResult.type === "cancel")) {
        try {
          await revokeAsync(
            {
              clientId: config.clientId,
              token: authTokens.refreshToken,
            },
            discovery
          );
          console.log("Refresh token revoked successfully");
        } catch (revokeError) {
          console.error("Token revocation failed:", revokeError);
          // Continue with logout even if revocation fails
        }
      }
      
      // Step 3: Clear all local auth cache
      await clearAllAuthCache();
      
      // Step 4: Clear local state
      setAuthTokens(null);
      setSentryUserContext(null);
      
      // Step 5: Clear environment config to trigger provider unmount
      onClearEnvironmentConfig?.();
      
      console.log("Logout completed successfully");
      
    } catch (error) {
      console.error("Logout error:", error);
      
      // Always clear local state even if logout fails
      await clearAllAuthCache();
      setAuthTokens(null);
      setSentryUserContext(null);
      onClearEnvironmentConfig?.();
    }
  };

  // Set access getter functions for axios instances whenever authTokens changes
  useEffect(() => {
    setWorkflowAccessGetter(() => authTokens?.accessToken);
    setCommunicationsAccessGetter(() => authTokens?.accessToken);
  }, [authTokens]);

  // Memoize the context value for performance
  const value = useMemo<AuthContextValue>(
    () => ({
      authTokens,
      promptAsync,
      logout,
      isLoading,
      isAuthReady,
    }),
    [authTokens, promptAsync, logout, isLoading, isAuthReady]
  );

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

/**
 * Custom hook to consume the AuthContext
 */
export function useAuth(): AuthContextValue {
  return useContext(AuthContext);
}
