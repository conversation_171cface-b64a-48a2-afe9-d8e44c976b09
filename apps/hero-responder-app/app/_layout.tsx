import { ActionSheetProvider } from "@expo/react-native-action-sheet";
import { BottomSheetModalProvider } from "@gorhom/bottom-sheet";
import * as Sentry from "@sentry/react-native";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { Stack } from "expo-router";
import React, { useEffect, useState } from "react";
import { LogBox } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { NavigationProvider } from "./(app)/V2/contexts/NavigationContext";
import { AuthProvider } from "./AuthContext";
import ErrorBoundary from "./ErrorBoundary";
import { ReportsProvider } from "./ReportContext";
import useLocationStore from "./store/useLocationStore";
import { DarkModeProvider } from "./DarkModeContext";
import { EnvironmentConfig } from "./services/discoveryService";
import { EnvironmentConfigProvider } from "./contexts/EnvironmentConfigContext";

Sentry.init({
  dsn: "https://<EMAIL>/4509527654793216",

  // Adds more context data to events (IP address, cookies, user, etc.)
  // For more information, visit: https://docs.sentry.io/platforms/react-native/data-management/data-collected/
  sendDefaultPii: true,

  // Configure Session Replay
  replaysSessionSampleRate: 0.1,
  replaysOnErrorSampleRate: 1,
  integrations: [
    Sentry.mobileReplayIntegration({
      maskAllText: false,
      maskAllImages: false,
      maskAllVectors: false,
    }),
  ],

  // uncomment the line below to enable Spotlight (https://spotlightjs.com)
  // spotlight: __DEV__,
});

const queryClient = new QueryClient();

export default Sentry.wrap(function RootLayout() {
  LogBox.ignoreAllLogs();

  // Environment configuration state - lifted above AuthProvider
  const [environmentConfig, setEnvironmentConfig] = useState<EnvironmentConfig | null>(null);
  

  const startLocationUpdates = useLocationStore(
    (state) => state.startLocationUpdates
  );
  const stopLocationUpdates = useLocationStore(
    (state) => state.stopLocationUpdates
  );

  // Manage location services based on authentication state
  useEffect(() => {
    if (environmentConfig) {
      // User has discovered their organization and is in auth flow
      console.log("Starting location services for authenticated session");
      startLocationUpdates();
    } else {
      // User is logged out or not yet discovered
      console.log("Stopping location services - no active session");
      stopLocationUpdates();
    }
    
    return () => {
      stopLocationUpdates();
    };
  }, [environmentConfig, startLocationUpdates, stopLocationUpdates]);

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      {/* Wrap the entire app in the ErrorBoundary */}
      <ErrorBoundary>
        <DarkModeProvider>
          <QueryClientProvider client={queryClient}>
            <BottomSheetModalProvider>
              <ActionSheetProvider>
                <EnvironmentConfigProvider 
                  environmentConfig={environmentConfig}
                  setEnvironmentConfig={setEnvironmentConfig}
                >
                  <AuthProvider 
                    key={environmentConfig?.userPoolUrl ?? "no-env"}
                    environmentConfig={environmentConfig}
                    onClearEnvironmentConfig={() => setEnvironmentConfig(null)}
                  >
                    <ReportsProvider>
                      <NavigationProvider>
                        <Stack>
                          <Stack.Screen
                            name="(app)"
                            options={{ headerShown: false }}
                          />
                          <Stack.Screen
                            name="sign-in"
                            options={{ headerShown: false }}
                          />
                        </Stack>
                      </NavigationProvider>
                    </ReportsProvider>
                  </AuthProvider>
                </EnvironmentConfigProvider>
              </ActionSheetProvider>
            </BottomSheetModalProvider>
          </QueryClientProvider>
        </DarkModeProvider>
      </ErrorBoundary>
    </GestureHandlerRootView>
  );
});
