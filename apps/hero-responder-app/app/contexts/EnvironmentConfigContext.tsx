import React, { createContext, useContext, ReactNode } from 'react';
import { EnvironmentConfig } from '../services/discoveryService';

interface EnvironmentConfigContextValue {
  environmentConfig: EnvironmentConfig | null;
  setEnvironmentConfig: (config: EnvironmentConfig | null) => void;
}

const EnvironmentConfigContext = createContext<EnvironmentConfigContextValue | null>(null);

interface EnvironmentConfigProviderProps {
  children: ReactNode;
  environmentConfig: EnvironmentConfig | null;
  setEnvironmentConfig: (config: EnvironmentConfig | null) => void;
}

export function EnvironmentConfigProvider({ 
  children, 
  environmentConfig,
  setEnvironmentConfig 
}: EnvironmentConfigProviderProps) {
  return (
    <EnvironmentConfigContext.Provider value={{ environmentConfig, setEnvironmentConfig }}>
      {children}
    </EnvironmentConfigContext.Provider>
  );
}

export function useEnvironmentConfig() {
  const context = useContext(EnvironmentConfigContext);
  if (!context) {
    throw new Error('useEnvironmentConfig must be used within EnvironmentConfigProvider');
  }
  return context;
}