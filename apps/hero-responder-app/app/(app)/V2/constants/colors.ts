export const Colors = {
  // Base colors that are the same in both modes
  brandBlue: "#0060FF",
  white: "#FFFFFF",

  // Color palette
  gray: {
    50: { light: "#F9FAFB", dark: "#11151D" },
    100: { light: "#F3F4F6", dark: "#161A23" },
    200: { light: "#E5E7EB", dark: "#1C212B" },
    300: { light: "#D1D5DC", dark: "#252A37" },
    400: { light: "#98A1AE", dark: "#465167" },
    500: { light: "#697282", dark: "#717F96" },
    600: { light: "#4A5565", dark: "#A0AEC0" },
    700: { light: "#364153", dark: "#CBD5E0" },
    800: { light: "#1E2938", dark: "#E2E8F0" },
    900: { light: "#101828", dark: "#F1F5F9" },
    950: { light: "#030712", dark: "#F8FAFC" },
  },

  blue: {
    50: { light: "#F5F9FF", dark: "#11182D" },
    100: { light: "#E5EFFF", dark: "#192448" },
    200: { light: "#CCDFFF", dark: "#0C2C61" },
    300: { light: "#9EC3FF", dark: "#182444" },
    600: { light: "#0060FF", dark: "#2979FF" }, // Brand Blue
    700: { light: "#1447E6", dark: "#6BB6FF" },
    800: { light: "#193CB8", dark: "#85C5FF" },
  },

  purple: {
    100: { light: "#EDE9FE", dark: "#EDE9FE" },
    600: { light: "#8200DB", dark: "#8200DB" },
  },

  rose: {
    50: { light: "#FFF1F2", dark: "#1A0A0B" },
    100: { light: "#FFE4E6", dark: "#321B20" },
    200: { light: "#FFCCD3", dark: "#322328" },
    300: { light: "#FFA1AD", dark: "#413035" },
    600: { light: "#EC003F", dark: "#FF5C85" },
    700: { light: "#C70036", dark: "#FF7BA3" },
    800: { light: "#A50036", dark: "#FFA8C5" },
  },

  pink: {
    100: { light: "#FCE7F3", dark: "#FCE7F3" },
    600: { light: "#E60076", dark: "#E60076" },
  },

  vine: {
    100: { light: "#CEF1EC", dark: "#102732" },
    600: { light: "#009F89", dark: "#32C8AA" },
    tint: { light: "#32C8AA", dark: "#0E3F47" }, // 20% for light, 30% for dark
  },

  amber: {
    100: { light: "#FFEDD4", dark: "#2E3542" },
    600: { light: "#E17100", dark: "#FF8000" },
    tint: { light: "#FFA733", dark: "#FFA733" }, // 20% for light, 12% for dark
  },

  yellow: {
    100: { light: "#FEF9C2", dark: "#FEF9C2" },
    600: { light: "#FDC700", dark: "#FDC700" },
  },

  lime: {
    100: { light: "#DCFCE7", dark: "#DCFCE7" },
    600: { light: "#00A63E", dark: "#00A63E" },
  },

  // Map-specific colors
  map: {
    unavailableUnit: { light: "#AD0363", dark: "#F10473" },
    availableUnit: { light: "#009F89", dark: "#32C8AA" }, // Using vine.600
    incident: { light: "#E17100", dark: "#FF8000" }, // Using amber.600
  },

  light: {
    // Background colors
    background: "#FFFFFF",
    surface: "#FFFFFF",

    // Text colors
    text: "#111111",
    textSecondary: "#6B7280",

    // Button colors
    buttonBackground: "#FFFFFF",
    buttonIcon: "#07090A",

    // Incident header
    incidentBackground: "#162456",
    incidentText: "#FFFFFF",

    // Direction Card (mobile)
    directionCard: "#162456",

    // Dividers and borders
    divider: "#E5E7EB",

    // Switch colors
    switchTrackFalse: "#E5E7EB",
    switchTrackTrue: "#0060FF",
    switchThumb: "#f4f3f4",
  },

  dark: {
    // Background colors
    background: "#0B0F17",
    surface: "#0B0F17",

    // Text colors
    text: "#F1F5F9",
    textSecondary: "#9CA3AF",

    // Button colors
    buttonBackground: "#0B0F17",
    buttonIcon: "#F1F5F9",

    // Incident header
    incidentBackground: "#004DCC",
    incidentText: "#FFFFFF",

    // Direction Card (mobile)
    directionCard: "#004DCC",

    // Dividers and borders
    divider: "#374151",

    // Switch colors
    switchTrackFalse: "#E5E7EB",
    switchTrackTrue: "#0060FF",
    switchThumb: "#ffffff",
  },
};

// Helper function to get color value based on theme
export const getColor = (colorPath: string, isDark?: boolean): string => {
  // Handle special case for "background"
  if (colorPath === "background") {
    return isDark ? Colors.dark.background : Colors.light.background;
  }

  const pathParts = colorPath.split(".");
  let color: any = Colors;

  for (const part of pathParts) {
    color = color[part];
  }

  if (typeof color === "object" && color.light && color.dark) {
    return isDark ? color.dark : color.light;
  }

  return color;
};

export const getColors = (isDark: boolean) =>
  isDark ? Colors.dark : Colors.light;
