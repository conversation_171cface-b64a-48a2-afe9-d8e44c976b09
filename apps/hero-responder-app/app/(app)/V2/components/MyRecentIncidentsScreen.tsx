import React, { useMemo, useEffect, useRef } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  SafeAreaView,
  Animated,
} from "react-native";
import { useRouter } from "expo-router";
import Svg, { Path } from "react-native-svg";
import { useListSituations } from "../../../apis/services/workflow/situations/hooks";
import { IncidentCard } from "./BottomSheetComponents/IncidentCard";
import { useDarkMode } from "../../../DarkModeContext";
import { getColor } from "../constants/colors";
import { useNavigation } from "../contexts/NavigationContext";



function CloseIcon({ isDark }: { isDark?: boolean }) {
  return (
    <Svg width={24} height={24} viewBox="0 0 24 24" fill="none">
      <Path
        d="M18 6L6 18M6 6L18 18"
        stroke={getColor('gray.900', isDark)}
        strokeWidth={2}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </Svg>
  );
}

// Skeleton loading component that matches IncidentCard layout
function SkeletonIncidentCard({ isDarkMode }: { isDarkMode?: boolean }) {
  const shimmerValue = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    const createShimmerAnimation = () => {
      return Animated.sequence([
        Animated.timing(shimmerValue, {
          toValue: 1,
          duration: 1000,
          useNativeDriver: true,
        }),
        Animated.timing(shimmerValue, {
          toValue: 0,
          duration: 1000,
          useNativeDriver: true,
        }),
      ]);
    };

    const animation = Animated.loop(createShimmerAnimation());
    animation.start();

    return () => animation.stop();
  }, [shimmerValue]);

  const shimmerOpacity = shimmerValue.interpolate({
    inputRange: [0, 1],
    outputRange: [0.3, 0.7],
  });

  return (
    <View style={[styles.skeletonCard, { backgroundColor: getColor('gray.100', isDarkMode), borderColor: getColor('gray.200', isDarkMode) }]}>
      {/* Title row skeleton */}
      <View style={styles.skeletonTitleRow}>
        <Animated.View 
          style={[
            styles.skeletonTitle, 
            { opacity: shimmerOpacity, backgroundColor: getColor('gray.200', isDarkMode) }
          ]} 
        />
        <Animated.View 
          style={[
            styles.skeletonCaret, 
            { opacity: shimmerOpacity, backgroundColor: getColor('gray.200', isDarkMode) }
          ]} 
        />
      </View>
      
      {/* Address skeleton */}
      <Animated.View 
        style={[
          styles.skeletonAddress, 
          { opacity: shimmerOpacity, backgroundColor: getColor('gray.200', isDarkMode) }
        ]} 
      />
      
      {/* Bottom row skeleton */}
      <View style={styles.skeletonBottomRow}>
        <View style={styles.skeletonLeftInfo}>
          <Animated.View 
            style={[
              styles.skeletonPriority, 
              { opacity: shimmerOpacity, backgroundColor: getColor('gray.200', isDarkMode) }
            ]} 
          />
          <Animated.View 
            style={[
              styles.skeletonStatus, 
              { opacity: shimmerOpacity, backgroundColor: getColor('gray.200', isDarkMode) }
            ]} 
          />
          <Animated.View 
            style={[
              styles.skeletonTime, 
              { opacity: shimmerOpacity, backgroundColor: getColor('gray.200', isDarkMode) }
            ]} 
          />
        </View>
        
        <View style={styles.skeletonRightInfo}>
          <Animated.View 
            style={[
              styles.skeletonResponder, 
              { opacity: shimmerOpacity, backgroundColor: getColor('gray.200', isDarkMode) }
            ]} 
          />
        </View>
      </View>
    </View>
  );
}

// Loading skeleton with multiple cards
function LoadingSkeleton({ isDarkMode }: { isDarkMode?: boolean }) {
  return (
    <View style={styles.incidentsList}>
      {[1, 2, 3, 4, 5].map((index) => (
        <SkeletonIncidentCard key={index} isDarkMode={isDarkMode} />
      ))}
    </View>
  );
}

export default function MyRecentIncidentsScreen() {
  const router = useRouter();
  const { isDarkMode } = useDarkMode();
  const { navigateToIncident, closeDrawer } = useNavigation();



  // Calculate 24 hours ago
  const now = new Date();
  const twentyFourHoursAgo = new Date(now.getTime() - 24 * 60 * 60 * 1000);

  // Search parameters for all incidents
  const searchParams = useMemo(() => {
    return {
      page_size: 100,
    } as any;
  }, []);

  const { data: allSituationsData, isLoading, error } = useListSituations(searchParams);

  // Filter situations to only show those created in the last 24 hours
  const searchResults = useMemo(() => {
    if (!allSituationsData?.situations) {
      return allSituationsData;
    }

    const filteredSituations = allSituationsData.situations.filter((situation) => {
      if (!situation.createTime) return false;
      
      const situationDate = new Date(situation.createTime);
      return situationDate >= twentyFourHoursAgo;
    });

    return {
      ...allSituationsData,
      situations: filteredSituations
    };
  }, [allSituationsData, twentyFourHoursAgo]);

  const handleIncidentPress = (situation: any) => {
    console.log("Incident pressed:", situation.id);
    // Navigate to incident details, close the modal, and close the drawer
    navigateToIncident(situation);
    closeDrawer();
    router.back();
  };

  const handleClose = () => {
    router.back();
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: getColor('background', isDarkMode) }]}>
      <View style={[styles.header, { backgroundColor: getColor('background', isDarkMode) }]}>
        <View style={styles.headerContent}>
          <View style={styles.titleContainer}>
            <Text style={[styles.title, { color: getColor('gray.900', isDarkMode) }]}>Recent Incidents</Text>
            <Text style={[styles.subtitle, { color: getColor('gray.500', isDarkMode) }]}>Last 24 Hours</Text>
          </View>
          <TouchableOpacity
            style={[styles.closeButton, { backgroundColor: getColor('gray.100', isDarkMode) }]}
            onPress={handleClose}
            activeOpacity={0.7}
          >
            <CloseIcon isDark={isDarkMode} />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView 
        style={styles.scrollContainer}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {isLoading ? (
          <LoadingSkeleton isDarkMode={isDarkMode} />
        ) : error ? (
          <View style={styles.errorContainer}>
            <Text style={[styles.errorText, { color: getColor('rose.600', isDarkMode) }]}>Failed to load incidents</Text>
            <Text style={[styles.errorDetails, { color: getColor('gray.500', isDarkMode) }]}>{error?.message}</Text>
          </View>
        ) : !searchResults?.situations?.length ? (
          <View style={styles.emptyContainer}>
            <Text style={[styles.emptyTitle, { color: getColor('gray.900', isDarkMode) }]}>No Incidents</Text>
            <Text style={[styles.emptyText, { color: getColor('gray.500', isDarkMode) }]}>
              No incidents in the last 24 hours.
            </Text>
          </View>
        ) : (
          <View style={styles.incidentsList}>
            {searchResults.situations.map((situation) => (
              <IncidentCard
                key={situation.id}
                situation={situation}
                assets={[]}
                onPress={() => handleIncidentPress(situation)}
                isDarkMode={isDarkMode}
              />
            ))}
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 24,
    paddingTop: 36,
    paddingBottom: 24,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "flex-start",
    justifyContent: "space-between",
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontSize: 28,
    fontWeight: "700",
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    fontWeight: "400",
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginTop: -8,
    marginRight: -8,
  },
  scrollContainer: {
    flex: 1,
    paddingBottom: 80,
  },
  scrollContent: {
    paddingHorizontal: 24,
    paddingBottom: 40,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 60,
  },
  loadingText: {
    fontSize: 16,
    // Color will be applied inline using getColor
  },
  errorContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 60,
  },
  errorText: {
    fontSize: 16,
    marginBottom: 8,
  },
  errorDetails: {
    fontSize: 14,
    textAlign: "center",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 60,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: "600",
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 16,
    textAlign: "center",
    lineHeight: 24,
  },
  incidentsList: {
    gap: 0,
  },
  // Skeleton styles
  skeletonCard: {
    borderRadius: 12,
    padding: 16,
    marginVertical: 6,
    borderWidth: 1,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  skeletonTitleRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  skeletonTitle: {
    height: 16,
    borderRadius: 4,
    flex: 1,
    marginRight: 12,
  },
  skeletonCaret: {
    width: 16,
    height: 16,
    borderRadius: 4,
  },
  skeletonAddress: {
    height: 14,
    borderRadius: 4,
    width: "80%",
    marginBottom: 12,
  },
  skeletonBottomRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  skeletonLeftInfo: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  skeletonPriority: {
    width: 28,
    height: 20,
    borderRadius: 4,
  },
  skeletonStatus: {
    width: 60,
    height: 12,
    borderRadius: 4,
  },
  skeletonTime: {
    width: 40,
    height: 12,
    borderRadius: 4,
  },
  skeletonRightInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  skeletonResponder: {
    width: 50,
    height: 12,
    borderRadius: 4,
  },
}); 