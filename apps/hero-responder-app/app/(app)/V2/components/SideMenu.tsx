import { useRouter } from "expo-router";
import React from "react";
import {
  Dimensions,
  Linking,
  StyleSheet,
  Switch,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import Svg, { Defs, LinearGradient, Path, Stop } from "react-native-svg";
import { useAuth } from "../../../AuthContext";
import { useDarkMode } from "../../../DarkModeContext";

const { width } = Dimensions.get("window");

function HeroLogo() {
  return (
    <Svg width={27} height={33} viewBox="0 0 27 33" fill="none">
      <Defs>
        <LinearGradient
          id="paint0_linear_3216_34853"
          x1="13.1675"
          y1="32.0378"
          x2="13.1675"
          y2="0"
          gradientUnits="userSpaceOnUse"
        >
          <Stop offset="0" stopColor="#0148BE" />
          <Stop offset="0.434672" stopColor="#005FDD" />
          <Stop offset="1" stopColor="#01B9FF" />
        </LinearGradient>
      </Defs>
      <Path
        d="M11.707 0.279863C12.6445 -0.0932459 13.6915 -0.0933297 14.6289 0.279863L24.4941 4.2076C25.6063 4.65055 26.3349 5.71799 26.335 6.90486V13.3238C26.3349 19.9367 22.93 26.0935 17.3027 29.6558L13.8535 31.8394C13.4356 32.104 12.9004 32.1039 12.4824 31.8394L9.03223 29.6558C3.40493 26.0935 8.29607e-05 19.9367 0 13.3238V6.90486C7.00484e-05 5.71803 0.728737 4.65058 1.84082 4.2076L11.707 0.279863ZM22.8135 8.29451C22.8133 7.78109 22.2898 7.43343 21.8164 7.6324L15.2285 10.4019C13.9111 10.9557 12.4258 10.9557 11.1084 10.4019L4.52051 7.6324C4.04715 7.43343 3.52361 7.78109 3.52344 8.29451V13.3267C3.52358 18.8277 6.31642 23.9493 10.9316 26.9127L13.1689 28.3492L15.4053 26.9127C20.0205 23.9493 22.8133 18.8277 22.8135 13.3267V8.29451ZM13.0693 2.62068C11.6611 2.62069 10.5195 3.76227 10.5195 5.17049C10.5196 6.57867 11.6611 7.72028 13.0693 7.72029C14.4775 7.72029 15.6191 6.57867 15.6191 5.17049C15.6191 3.76226 14.4776 2.62068 13.0693 2.62068Z"
        fill="url(#paint0_linear_3216_34853)"
      />
    </Svg>
  );
}

function IncidentsIcon({ isDark }: { isDark?: boolean }) {
  return (
    <Svg width={24} height={25} viewBox="0 0 24 25" fill="none">
      <Path
        d="M12.7041 9.24219H2.7041C2.1541 9.24219 1.7041 9.69219 1.7041 10.2422C1.7041 10.7922 2.1541 11.2422 2.7041 11.2422H12.7041C13.2541 11.2422 13.7041 10.7922 13.7041 10.2422C13.7041 9.69219 13.2541 9.24219 12.7041 9.24219ZM12.7041 5.24219H2.7041C2.1541 5.24219 1.7041 5.69219 1.7041 6.24219C1.7041 6.79219 2.1541 7.24219 2.7041 7.24219H12.7041C13.2541 7.24219 13.7041 6.79219 13.7041 6.24219C13.7041 5.69219 13.2541 5.24219 12.7041 5.24219ZM2.7041 15.2422H8.7041C9.2541 15.2422 9.7041 14.7922 9.7041 14.2422C9.7041 13.6922 9.2541 13.2422 8.7041 13.2422H2.7041C2.1541 13.2422 1.7041 13.6922 1.7041 14.2422C1.7041 14.7922 2.1541 15.2422 2.7041 15.2422ZM21.9141 11.4522L22.0041 11.5422C22.3941 11.9322 22.3941 12.5622 22.0041 12.9522L16.4241 18.5422C16.0341 18.9322 15.4041 18.9322 15.0141 18.5422L11.9241 15.4522C11.5341 15.0622 11.5341 14.4322 11.9241 14.0422L12.0141 13.9522C12.4041 13.5622 13.0341 13.5622 13.4241 13.9522L15.7241 16.2522L20.5041 11.4622C20.8841 11.0622 21.5241 11.0622 21.9141 11.4522Z"
        fill={isDark ? "#F1F5F9" : "black"}
      />
    </Svg>
  );
}

function ProfileIcon({ isDark }: { isDark?: boolean }) {
  return (
    <Svg width={24} height={25} viewBox="0 0 24 25" fill="none">
      <Path
        d="M10.085 11.5391C12.2941 11.5391 14.085 9.7482 14.085 7.53906C14.085 5.32992 12.2941 3.53906 10.085 3.53906C7.87582 3.53906 6.08496 5.32992 6.08496 7.53906C6.08496 9.7482 7.87582 11.5391 10.085 11.5391Z"
        fill={isDark ? "#F1F5F9" : "black"}
      />
      <Path
        d="M10.755 12.5591C10.535 12.5491 10.315 12.5391 10.085 12.5391C7.66496 12.5391 5.40496 13.2091 3.47496 14.3591C2.59496 14.8791 2.08496 15.8591 2.08496 16.8891V19.5391H11.345C10.555 18.4091 10.085 17.0291 10.085 15.5391C10.085 14.4691 10.335 13.4691 10.755 12.5591Z"
        fill={isDark ? "#F1F5F9" : "black"}
      />
      <Path
        d="M20.835 15.5391C20.835 15.3191 20.805 15.1191 20.775 14.9091L21.915 13.8991L20.915 12.1691L19.465 12.6591C19.145 12.3891 18.785 12.1791 18.385 12.0291L18.085 10.5391H16.085L15.785 12.0291C15.385 12.1791 15.025 12.3891 14.705 12.6591L13.255 12.1691L12.255 13.8991L13.395 14.9091C13.365 15.1191 13.335 15.3191 13.335 15.5391C13.335 15.7591 13.365 15.9591 13.395 16.1691L12.255 17.1791L13.255 18.9091L14.705 18.4191C15.025 18.6891 15.385 18.8991 15.785 19.0491L16.085 20.5391H18.085L18.385 19.0491C18.785 18.8991 19.145 18.6891 19.465 18.4191L20.915 18.9091L21.915 17.1791L20.775 16.1691C20.805 15.9591 20.835 15.7591 20.835 15.5391ZM17.085 17.5391C15.985 17.5391 15.085 16.6391 15.085 15.5391C15.085 14.4391 15.985 13.5391 17.085 13.5391C18.185 13.5391 19.085 14.4391 19.085 15.5391C19.085 16.6391 18.185 17.5391 17.085 17.5391Z"
        fill={isDark ? "#F1F5F9" : "black"}
      />
    </Svg>
  );
}

function LogoutIcon({ isDark }: { isDark?: boolean }) {
  return (
    <Svg width={24} height={25} viewBox="0 0 24 25" fill="none">
      <Path
        d="M17 8.03906L15.59 9.44906L17.17 11.0391H9V13.0391H17.17L15.59 14.6191L17 16.0391L21 12.0391L17 8.03906ZM5 5.03906H12V3.03906H5C3.9 3.03906 3 3.93906 3 5.03906V19.0391C3 20.1391 3.9 21.0391 5 21.0391H12V19.0391H5V5.03906Z"
        fill={isDark ? "#F1F5F9" : "black"}
      />
    </Svg>
  );
}

function PrivacyIcon({ isDark }: { isDark?: boolean }) {
  return (
    <Svg width={24} height={25} viewBox="0 0 24 25" fill="none">
      <Path
        d="M12 1.5391L3 5.5391V11.5391C3 16.8891 6.84 21.8691 12 22.5391C17.16 21.8691 21 16.8891 21 11.5391V5.5391L12 1.5391ZM12 7.5391C13.1 7.5391 14 8.4391 14 9.5391C14 10.6391 13.1 11.5391 12 11.5391C10.9 11.5391 10 10.6391 10 9.5391C10 8.4391 10.9 7.5391 12 7.5391ZM12 17.5391C10.33 17.5391 8.86 16.6291 8 15.2391C8.04 13.8191 10.67 13.0391 12 13.0391C13.33 13.0391 15.96 13.8191 16 15.2391C15.14 16.6291 13.67 17.5391 12 17.5391Z"
        fill={isDark ? "#F1F5F9" : "black"}
      />
    </Svg>
  );
}

function TermsIcon({ isDark }: { isDark?: boolean }) {
  return (
    <Svg width={24} height={25} viewBox="0 0 24 25" fill="none">
      <Path
        d="M14 17.5391H7V15.5391H14V17.5391ZM17 13.5391H7V11.5391H17V13.5391ZM17 9.5391H7V7.5391H17V9.5391ZM19 3.5391H5C3.9 3.5391 3 4.4391 3 5.5391V19.5391C3 20.6391 3.9 21.5391 5 21.5391H19C20.1 21.5391 21 20.6391 21 19.5391V5.5391C21 4.4391 20.1 3.5391 19 3.5391ZM19 19.5391H5V5.5391H19V19.5391Z"
        fill={isDark ? "#F1F5F9" : "black"}
      />
    </Svg>
  );
}

function DarkModeIcon({ isDark }: { isDark?: boolean }) {
  return (
    <Svg width={24} height={24} viewBox="0 0 24 24" fill="none">
      <Path
        d="M12,22 C17.5228475,22 22,17.5228475 22,12 C22,6.4771525 17.5228475,2 12,2 C6.4771525,2 2,6.4771525 2,12 C2,17.5228475 6.4771525,22 12,22 Z M12,20 L12,4 C16.418278,4 20,7.581722 20,12 C20,16.418278 16.418278,20 12,20 Z"
        fill={isDark ? "#F1F5F9" : "#212121"}
      />
    </Svg>
  );
}

export default function SideMenu() {
  const { logout } = useAuth();
  const router = useRouter();
  const { isDarkMode, toggleDarkMode, isLoading } = useDarkMode();

  const handleProfilePress = () => {
    router.push("/(app)/(overlay)/ProfileScreen");
  };

  const handleRecentIncidentsPress = () => {
    router.push("/(app)/(overlay)/MyRecentIncidentsScreen");
  };

  const handlePrivacyPress = async () => {
    const url = "https://www.gethero.com/privacy";
    const supported = await Linking.canOpenURL(url);
    if (supported) {
      await Linking.openURL(url);
    }
  };

  const handleTermsPress = async () => {
    const url = "https://www.gethero.com/tos";
    const supported = await Linking.canOpenURL(url);
    if (supported) {
      await Linking.openURL(url);
    }
  };

  return (
    <View style={[styles.container, isDarkMode && styles.darkContainer]}>
      <View style={styles.header}>
        <HeroLogo />
        <Text style={[styles.title, isDarkMode && styles.darkText]}>
          Hero Safety
        </Text>
      </View>
      <View style={[styles.divider, isDarkMode && styles.darkDivider]} />
      <TouchableOpacity
        style={styles.menuItem}
        activeOpacity={0.7}
        onPress={handleRecentIncidentsPress}
      >
        <IncidentsIcon isDark={isDarkMode} />
        <Text style={[styles.menuText, isDarkMode && styles.darkText]}>
          Recent Incidents
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={styles.menuItem}
        activeOpacity={0.7}
        onPress={handleProfilePress}
      >
        <ProfileIcon isDark={isDarkMode} />
        <Text style={[styles.menuText, isDarkMode && styles.darkText]}>
          Profile
        </Text>
      </TouchableOpacity>

      {/* Dark Mode Toggle */}
      <View style={styles.menuItem}>
        <DarkModeIcon isDark={isDarkMode} />
        <Text style={[styles.menuText, isDarkMode && styles.darkText]}>
          Dark Mode
        </Text>
        <Switch
          value={isDarkMode}
          onValueChange={toggleDarkMode}
          disabled={isLoading}
          trackColor={{ false: "#E5E7EB", true: "#005FDD" }}
          thumbColor={isDarkMode ? "#ffffff" : "#f4f3f4"}
          ios_backgroundColor="#E5E7EB"
          style={styles.switch}
        />
      </View>

      <View style={[styles.divider, isDarkMode && styles.darkDivider]} />
      <Text
        style={[styles.sectionTitle, isDarkMode && styles.darkSectionTitle]}
      >
        About
      </Text>
      <TouchableOpacity
        style={styles.menuItem}
        activeOpacity={0.7}
        onPress={handlePrivacyPress}
      >
        <PrivacyIcon isDark={isDarkMode} />
        <Text style={[styles.menuText, isDarkMode && styles.darkText]}>
          Privacy Policy
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        style={styles.menuItem}
        activeOpacity={0.7}
        onPress={handleTermsPress}
      >
        <TermsIcon isDark={isDarkMode} />
        <Text style={[styles.menuText, isDarkMode && styles.darkText]}>
          Terms of Service
        </Text>
      </TouchableOpacity>

      <View style={[styles.divider, isDarkMode && styles.darkDivider]} />
      <TouchableOpacity
        style={styles.menuItem}
        activeOpacity={0.7}
        onPress={logout}
      >
        <LogoutIcon isDark={isDarkMode} />
        <Text style={[styles.menuText, isDarkMode && styles.darkText]}>
          Log Out
        </Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: width * 0.7,
    backgroundColor: "#fff",
    paddingTop: 80,
    paddingHorizontal: 24,
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: "700",
    marginLeft: 12,
    color: "#111",
  },
  divider: {
    height: 1,
    backgroundColor: "#E5E7EB",
    marginVertical: 18,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#6B7280",
    marginBottom: 8,
    marginTop: 4,
  },
  menuItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 16,
    gap: 12,
  },
  menuIcon: {
    marginRight: 18,
  },
  menuText: {
    fontSize: 18,
    color: "#111",
    fontWeight: "500",
  },
  switch: {
    marginLeft: "auto",
  },
  darkContainer: {
    backgroundColor: "#0B0F17",
    color: "#F1F5F9",
  },
  darkText: {
    color: "#F1F5F9",
  },
  darkDivider: {
    backgroundColor: "#374151",
  },
  darkSectionTitle: {
    color: "#9CA3AF",
  },
});
