import React from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Animated,
  ActivityIndicator,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { getColor } from "../constants/colors";

interface UpdateBannerProps {
  isVisible: boolean;
  isUpdating: boolean;
  onUpdatePress: () => void;
  onDismiss: () => void;
  slideAnim: Animated.Value;
  isDarkMode?: boolean;
}

export const UpdateBanner: React.FC<UpdateBannerProps> = ({
  isVisible,
  isUpdating,
  onUpdatePress,
  onDismiss,
  slideAnim,
  isDarkMode = false,
}) => {
  if (!isVisible && !isUpdating) return null;

  return (
    <Animated.View
      style={[
        styles.container,
        {
          backgroundColor: isDarkMode ? getColor('blue.100', isDarkMode) : '#F0F8FF',
          borderBottomColor: getColor('gray.200', isDarkMode),
          transform: [
            {
              translateY: slideAnim.interpolate({
                inputRange: [0, 1],
                outputRange: [-100, 0],
              }),
            },
          ],
        },
      ]}
    >
      <View style={styles.content}>
        <View style={styles.leftContent}>
          <Ionicons name="download-outline" size={20} color={getColor('blue.600', isDarkMode)} />
          <Text style={[styles.title, { color: getColor('gray.900', isDarkMode) }]}>Update Available</Text>
        </View>
        
        <View style={styles.rightContent}>
          {isUpdating ? (
            <View style={styles.updatingContainer}>
              <ActivityIndicator size="small" color={getColor('blue.600', isDarkMode)} />
              <Text style={[styles.updatingText, { color: getColor('blue.600', isDarkMode) }]}>Updating...</Text>
            </View>
          ) : (
            <>
              <TouchableOpacity
                style={styles.dismissButton}
                onPress={onDismiss}
                activeOpacity={0.7}
              >
                <Ionicons name="close" size={18} color={getColor('gray.600', isDarkMode)} />
              </TouchableOpacity>
              
              <TouchableOpacity
                style={[styles.updateButton, { backgroundColor: getColor('blue.600', isDarkMode) }]}
                onPress={onUpdatePress}
                activeOpacity={0.8}
              >
                <Text style={styles.updateButtonText}>Update</Text>
              </TouchableOpacity>
            </>
          )}
        </View>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    // backgroundColor and borderBottomColor are now dynamic
    borderBottomWidth: 1,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    zIndex: 1000,
  },
  content: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 50, // Account for status bar
  },
  leftContent: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: "600",
    // color is now dynamic
    marginLeft: 8,
  },
  rightContent: {
    flexDirection: "row",
    alignItems: "center",
  },
  dismissButton: {
    padding: 8,
    marginRight: 8,
  },
  updateButton: {
    // backgroundColor is now dynamic
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  updateButtonText: {
    color: "white",
    fontSize: 14,
    fontWeight: "600",
  },
  updatingContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  updatingText: {
    // color is now dynamic
    fontSize: 14,
    fontWeight: "500",
    marginLeft: 8,
  },
}); 