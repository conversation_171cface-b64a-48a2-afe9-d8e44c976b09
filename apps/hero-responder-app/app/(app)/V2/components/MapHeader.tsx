import { MaterialCommunityIcons } from "@expo/vector-icons";
import React, { useEffect, useRef, useState } from "react";
import {
  Animated,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useDarkMode } from "../../../DarkModeContext";
import { getColors } from "../constants/colors";
import { getSituationType } from "../utils";

interface MapHeaderProps {
  onMenuPress: () => void;
  onCenterLocationPress: (bottomSheetIndex: number) => void;
  bottomSheetIndex: number;
  assignedIncident?: any;
  userLocation?: { latitude: number; longitude: number } | null;
  selectedIncidentId?: string | null;
  onArrivedPress?: () => void;
  firstAssistMemberOrder?: any;
  currentBottomSheetViewType?: string;
}

// Calculate distance between two coordinates in miles
const calculateDistance = (
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number => {
  const R = 3959; // Earth's radius in miles
  const dLat = (lat2 - lat1) * (Math.PI / 180);
  const dLon = (lon2 - lon1) * (Math.PI / 180);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * (Math.PI / 180)) *
      Math.cos(lat2 * (Math.PI / 180)) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
};

export default function MapHeader({
  onMenuPress,
  onCenterLocationPress,
  bottomSheetIndex,
  assignedIncident,
  userLocation,
  selectedIncidentId,
  onArrivedPress,
  firstAssistMemberOrder,
  currentBottomSheetViewType,
}: MapHeaderProps) {
  const insets = useSafeAreaInsets();
  const { isDarkMode } = useDarkMode();
  const colors = getColors(isDarkMode);
  const buttonsTranslateY = useRef(new Animated.Value(0)).current;
  const headerTranslateY = useRef(new Animated.Value(-200)).current;
  const headerOpacity = useRef(new Animated.Value(0)).current;
  const expandAnimation = useRef(new Animated.Value(0)).current;
  const caretRotation = useRef(new Animated.Value(0)).current;

  // Local state for optimistic UI updates and expansion
  const [hasArrivedBeenPressed, setHasArrivedBeenPressed] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);

  // Check if user is already assisting based on order status
  const isAlreadyAssisting =
    firstAssistMemberOrder?.typeSpecificStatus === "ASSISTING";

  // Check if user is currently viewing the incident detail (not incoming incident)
  const isViewingIncidentDetail =
    currentBottomSheetViewType === "incident-detail";

  // Only show incident header if user is assigned AND currently viewing that incident detail
  // AND hasn't pressed arrived AND is not already assisting
  const shouldShowIncidentHeader = Boolean(
    assignedIncident &&
      selectedIncidentId === assignedIncident.id &&
      isViewingIncidentDetail &&
      !hasArrivedBeenPressed &&
      !isAlreadyAssisting
  );

  // Reset the arrived state when incident changes or is cleared
  useEffect(() => {
    if (!assignedIncident || selectedIncidentId !== assignedIncident.id) {
      setHasArrivedBeenPressed(false);
      setIsExpanded(false);
    }
  }, [assignedIncident?.id, selectedIncidentId]);

  useEffect(() => {
    if (shouldShowIncidentHeader) {
      // Animate buttons up and out
      Animated.timing(buttonsTranslateY, {
        toValue: -200,
        duration: 300,
        useNativeDriver: true,
      }).start(() => {
        // After buttons are gone, animate header down
        Animated.parallel([
          Animated.timing(headerTranslateY, {
            toValue: 0,
            duration: 400,
            useNativeDriver: true,
          }),
          Animated.timing(headerOpacity, {
            toValue: 1,
            duration: 400,
            useNativeDriver: true,
          }),
        ]).start();
      });
    } else {
      // Reset expansion state when hiding header
      setIsExpanded(false);
      expandAnimation.setValue(0);
      caretRotation.setValue(0);

      // Animate header up and out
      Animated.parallel([
        Animated.timing(headerTranslateY, {
          toValue: -200,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(headerOpacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start(() => {
        // After header is gone, animate buttons down
        Animated.timing(buttonsTranslateY, {
          toValue: 0,
          duration: 400,
          useNativeDriver: true,
        }).start();
      });
    }
  }, [shouldShowIncidentHeader]);

  // Handle expansion animation
  useEffect(() => {
    Animated.parallel([
      Animated.timing(expandAnimation, {
        toValue: isExpanded ? 1 : 0,
        duration: 300,
        useNativeDriver: false,
      }),
      Animated.timing(caretRotation, {
        toValue: isExpanded ? 1 : 0,
        duration: 300,
        useNativeDriver: true,
      }),
    ]).start();
  }, [isExpanded]);

  // Calculate distance if we have both locations
  const distance = React.useMemo(() => {
    if (
      assignedIncident?.latitude &&
      assignedIncident?.longitude &&
      userLocation?.latitude &&
      userLocation?.longitude
    ) {
      const dist = calculateDistance(
        userLocation.latitude,
        userLocation.longitude,
        assignedIncident.latitude,
        assignedIncident.longitude
      );
      return dist;
    }
    return null;
  }, [assignedIncident, userLocation]);

  // Handle arrived button press with optimistic update
  const handleArrivedPress = () => {
    setHasArrivedBeenPressed(true);
    onArrivedPress?.();
  };

  // Handle header press to toggle expansion
  const handleHeaderPress = () => {
    setIsExpanded(!isExpanded);
  };

  // Calculate caret rotation
  const caretRotationStyle = caretRotation.interpolate({
    inputRange: [0, 1],
    outputRange: ["0deg", "180deg"],
  });

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      {/* Normal Header Buttons */}
      <Animated.View
        style={[
          styles.buttonsContainer,
          {
            top: insets.top,
            transform: [{ translateY: buttonsTranslateY }],
          },
        ]}
      >
        <TouchableOpacity
          style={[styles.button, { backgroundColor: colors.buttonBackground }]}
          onPress={onMenuPress}
          activeOpacity={0.7}
        >
          <MaterialCommunityIcons
            name="menu"
            size={24}
            color={colors.buttonIcon}
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, { backgroundColor: colors.buttonBackground }]}
          onPress={() => onCenterLocationPress(bottomSheetIndex)}
          activeOpacity={0.7}
        >
          <MaterialCommunityIcons
            name="crosshairs-gps"
            size={24}
            color={colors.buttonIcon}
          />
        </TouchableOpacity>
      </Animated.View>

      {/* Incident Header */}
      <Animated.View
        style={[
          styles.incidentHeader,
          {
            top: insets.top,
            transform: [{ translateY: headerTranslateY }],
            opacity: headerOpacity,
            backgroundColor: colors.incidentBackground,
          },
        ]}
      >
        <TouchableOpacity
          style={styles.incidentContent}
          onPress={handleHeaderPress}
          activeOpacity={0.8}
        >
          <View style={styles.incidentInfo}>
            <Text
              style={[styles.incidentLocation, { color: colors.incidentText }]}
              numberOfLines={1}
            >
              {assignedIncident?.address || "Location not available"}
            </Text>
            <Text
              style={[styles.incidentType, { color: colors.incidentText }]}
              numberOfLines={1}
            >
              {getSituationType(assignedIncident?.type) || "Unknown"}
            </Text>
          </View>
          <View style={styles.rightSection}>
            {distance !== null && (
              <Text
                style={[styles.distanceText, { color: colors.incidentText }]}
              >
                {distance.toFixed(1)} mi
              </Text>
            )}
            <Animated.View
              style={{ transform: [{ rotate: caretRotationStyle }] }}
            >
              <MaterialCommunityIcons
                name="chevron-down"
                size={24}
                color={colors.incidentText}
              />
            </Animated.View>
          </View>
        </TouchableOpacity>

        {/* Arrived Button - Only show when expanded */}
        <Animated.View
          style={[
            styles.arrivedButtonContainer,
            {
              maxHeight: expandAnimation.interpolate({
                inputRange: [0, 1],
                outputRange: [0, 100],
              }),
              opacity: expandAnimation,
            },
          ]}
        >
          <TouchableOpacity
            style={styles.arrivedButton}
            onPress={handleArrivedPress}
            activeOpacity={0.8}
          >
            <Text style={styles.arrivedButtonText}>Arrived</Text>
          </TouchableOpacity>
        </Animated.View>
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    zIndex: 100,
    pointerEvents: "box-none",
  },
  buttonsContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    paddingHorizontal: 24,
    paddingVertical: 12,
    pointerEvents: "box-none",
  },
  button: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  incidentHeader: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    borderRadius: 16,
    marginHorizontal: 16,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    overflow: "hidden",
  },
  incidentContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  incidentInfo: {
    flex: 1,
    marginRight: 16,
  },
  incidentLocation: {
    fontSize: 20,
    fontWeight: "700",
    marginBottom: 4,
  },
  incidentType: {
    fontSize: 14,
    opacity: 0.8,
  },
  rightSection: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  distanceText: {
    fontSize: 16,
    fontWeight: "600",
  },
  arrivedButtonContainer: {
    overflow: "hidden",
  },
  arrivedButton: {
    backgroundColor: "#FFFFFF",
    marginHorizontal: 20,
    marginBottom: 16,
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: "center",
  },
  arrivedButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#162456",
  },
});
