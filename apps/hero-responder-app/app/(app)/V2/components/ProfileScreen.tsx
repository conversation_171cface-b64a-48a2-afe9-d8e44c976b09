import { useRouter } from "expo-router";
import { jwtDecode } from "jwt-decode";
import React from "react";
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { useGetAssetByCognitoSub } from "../../../apis/services/workflow/assets/hooks";
import { useAuth } from "../../../AuthContext";
import { useDarkMode } from "../../../DarkModeContext";
import { getColor } from "../constants/colors";
import { DecodedToken } from "../types";

export default function ProfileScreen() {
  const router = useRouter();
  const { authTokens } = useAuth();
  const { isDarkMode } = useDarkMode();

  // Get current user's cognitoJwtSub from auth token
  const currentUserCognitoSub = authTokens?.idToken
    ? (() => {
        try {
          return jwtDecode<DecodedToken>(authTokens.idToken).sub;
        } catch (error) {
          console.error("Error decoding idToken:", error);
          return "";
        }
      })()
    : "";

  // Fetch user asset data
  const { data: assetData } = useGetAssetByCognitoSub(currentUserCognitoSub);
  const userAsset = assetData?.asset;

  const handleClose = () => {
    router.back();
  };

  // Extract unit (first 3 digits of ID)
  const getUnit = (id: string | undefined) => {
    if (!id) return "";
    return id.replace(/[^0-9]/g, "").slice(0, 3);
  };

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: getColor('background', isDarkMode) }]}
    >
      <StatusBar
        barStyle={isDarkMode ? "light-content" : "dark-content"}
        backgroundColor={getColor('background', isDarkMode)}
      />
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.title, { color: getColor('gray.900', isDarkMode) }]}>Profile</Text>
          <TouchableOpacity
            onPress={handleClose}
            style={[
              styles.closeButton,
              { backgroundColor: getColor('gray.100', isDarkMode) },
            ]}
          >
            <Text style={[styles.closeButtonText, { color: getColor('gray.900', isDarkMode) }]}>
              ✕
            </Text>
          </TouchableOpacity>
        </View>

        {/* Form Fields */}
        <View style={styles.form}>
          <View style={styles.fieldContainer}>
            <Text style={[styles.label, { color: getColor('gray.500', isDarkMode) }]}>
              Name
            </Text>
            <View
              style={[
                styles.inputContainer,
                {
                  backgroundColor: getColor('gray.100', isDarkMode),
                  borderColor: getColor('gray.200', isDarkMode),
                },
              ]}
            >
              <Text style={[styles.inputText, { color: getColor('gray.900', isDarkMode) }]}>
                {userAsset?.name || ""}
              </Text>
            </View>
          </View>

          <View style={styles.fieldContainer}>
            <Text style={[styles.label, { color: getColor('gray.500', isDarkMode) }]}>
              Unit
            </Text>
            <View
              style={[
                styles.inputContainer,
                {
                  backgroundColor: getColor('gray.100', isDarkMode),
                  borderColor: getColor('gray.200', isDarkMode),
                },
              ]}
            >
              <Text style={[styles.inputText, { color: getColor('gray.900', isDarkMode) }]}>
                {getUnit(userAsset?.id)}
              </Text>
            </View>
          </View>

          <View style={styles.fieldContainer}>
            <Text style={[styles.label, { color: getColor('gray.500', isDarkMode) }]}>
              Phone Number
            </Text>
            <View
              style={[
                styles.inputContainer,
                {
                  backgroundColor: getColor('gray.100', isDarkMode),
                  borderColor: getColor('gray.200', isDarkMode),
                },
              ]}
            >
              <Text style={[styles.inputText, { color: getColor('gray.900', isDarkMode) }]}>
                {userAsset?.contactNo || ""}
              </Text>
            </View>
          </View>

          <View style={styles.fieldContainer}>
            <Text style={[styles.label, { color: getColor('gray.500', isDarkMode) }]}>
              Email
            </Text>
            <View
              style={[
                styles.inputContainer,
                {
                  backgroundColor: getColor('gray.100', isDarkMode),
                  borderColor: getColor('gray.200', isDarkMode),
                },
              ]}
            >
              <Text style={[styles.inputText, { color: getColor('gray.900', isDarkMode) }]}>
                {userAsset?.contactEmail || ""}
              </Text>
            </View>
          </View>
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 30,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: "600",
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginTop: -8,
    marginRight: -8,
  },
  closeButtonText: {
    fontSize: 24,
    fontWeight: "300",
  },
  form: {
    flex: 1,
  },
  fieldContainer: {
    marginBottom: 24,
  },
  label: {
    fontSize: 14,
    marginBottom: 8,
    marginLeft: 10,
    fontWeight: "400",
  },
  inputContainer: {
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderWidth: 1,
  },
  inputText: {
    fontSize: 16,
    fontWeight: "500",
  },
});
