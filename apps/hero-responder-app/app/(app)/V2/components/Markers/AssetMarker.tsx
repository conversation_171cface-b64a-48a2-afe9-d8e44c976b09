import { Asset } from "proto/hero/assets/v2/assets_pb";
import React from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import { getColor } from "../../constants/colors";

interface AssetMarkerProps {
  asset: Asset;
  onPress: () => void;
  isSelected?: boolean;
  isDarkMode?: boolean;
}

export const AssetMarker: React.FC<AssetMarkerProps> = ({
  asset,
  onPress,
  isSelected = false,
  isDarkMode = false,
}) => {
  // Get initials from asset name
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((word) => word[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  // Determine marker color based on status using theme-aware colors
  const getMarkerColor = () => {
    //@ts-ignore
    if (asset.status === "ASSET_STATUS_AVAILABLE") {
      return getColor("map.availableUnit", isDarkMode);
    }
    //@ts-ignore
    if (asset.status === "ASSET_STATUS_BUSY") {
      return getColor("map.unavailableUnit", isDarkMode);
    }
    return getColor("map.availableUnit", isDarkMode);
  };

  const markerColor = getMarkerColor();
  const backgroundColor = getColor("background", isDarkMode);
  const textColor = getColor("gray.900", isDarkMode);

  return (
    <TouchableOpacity onPress={onPress} style={styles.container}>
      <View
        style={[
          styles.marker,
          {
            borderColor: markerColor,
            backgroundColor: isSelected ? markerColor : backgroundColor,
          },
        ]}
      >
        <Text
          style={[
            styles.initials,
            {
              color: isSelected ? backgroundColor : markerColor,
            },
          ]}
        >
          {getInitials(asset.name)}
        </Text>
      </View>
      <Text
        style={[
          styles.assetId,
          {
            color: isSelected ? backgroundColor : markerColor,
            backgroundColor: isSelected ? markerColor : backgroundColor + "E6", // Adding 90% opacity in hex
          },
        ]}
      >
        {asset.id?.replace(/[^0-9]/g, "").slice(0, 3)}
      </Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    justifyContent: "center",
  },
  marker: {
    width: 36,
    height: 36,
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 2,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  initials: {
    fontSize: 14,
    fontWeight: "bold",
  },
  assetId: {
    marginTop: 5,
    fontSize: 10,
    fontWeight: "bold",
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    textAlign: "center",
    minWidth: 30,
  },
});
