import { Ionicons } from "@expo/vector-icons";
import { BottomSheetScrollView } from "@gorhom/bottom-sheet";
import { jwtDecode } from "jwt-decode";
import {
  CompleteOrderRequest,
  ListOrdersForSituationRequest,
  Order,
  UpdateOrderRequest,
} from "proto/hero/orders/v2/orders_pb";
import { AddSituationUpdateRequest } from "proto/hero/situations/v2/situations_pb";
import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from "react";
import {
  Alert,
  Dimensions,
  Keyboard,
  KeyboardAvoidingView,
  Linking,
  Modal,
  Platform,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import Svg, { Defs, G, Mask, Path, Rect } from "react-native-svg";
import { TabBar, TabView } from "react-native-tab-view";
import { hookOrderStatusToString } from "../../../../apis/services/workflow/orders/enumConverters";
import {
  useCompleteOrder,
  useListOrdersForSituation,
  useUpdateOrder,
} from "../../../../apis/services/workflow/orders/hooks";
import {
  useAddSituationUpdate,
  useListSituations,
} from "../../../../apis/services/workflow/situations/hooks";
import { useAuth } from "../../../../AuthContext";
import { createUpdateEntry } from "../../../utils";
import { getColor } from "../../constants/colors";
import { getSituationType } from "../../utils";
import { NotesTab } from "./NotesTab";

// Helper function to safely parse JSON
const safeJsonParse = (jsonString: string, fallback: any = {}) => {
  try {
    return JSON.parse(jsonString);
  } catch (error) {
    console.warn("Failed to parse JSON:", error, "String:", jsonString);
    return fallback;
  }
};

const screenWidth = Dimensions.get("window").width;

interface IncidentDetailViewProps {
  incident: any;
  allAssets: any[];
  onBack: () => void;
  firstAssistMemberOrder?: any;
  userInfoFromServer?: any;
  mapBottomSheetRef?: any;
  optimisticAssistingStatus?: string | null;
  bottomSheetIndex?: number;
  onAddUpdate?: (message: string) => Promise<void>;
  shouldShowAddUpdateButton?: boolean;
  onTabChange?: (tabIndex: number) => void;
  onIncidentCleared?: () => void;
  isDarkMode?: boolean;
}

interface DecodedToken {
  email: string;
  sub: string;
}

// Custom Shield Person Icon Component
const ShieldPersonIcon: React.FC<{ size?: number; color?: string }> = ({
  size = 16,
  color = "#697282",
}) => (
  <Svg width={size} height={size} viewBox="0 0 12 15" fill="none">
    <Path
      d="M6 8.25C6.725 8.25 7.34375 7.99375 7.85625 7.48125C8.36875 6.96875 8.625 6.35 8.625 5.625C8.625 4.9 8.36875 4.28125 7.85625 3.76875C7.34375 3.25625 6.725 3 6 3C5.275 3 4.65625 3.25625 4.14375 3.76875C3.63125 4.28125 3.375 4.9 3.375 5.625C3.375 6.35 3.63125 6.96875 4.14375 7.48125C4.65625 7.99375 5.275 8.25 6 8.25ZM6 15C4.175 14.5375 2.71875 13.5375 1.63125 12C0.54375 10.4625 0 8.7375 0 6.825V2.25L6 0L12 2.25V6.825C12 8.7375 11.4563 10.4625 10.3687 12C9.28125 13.5375 7.825 14.5375 6 15ZM6 13.425C6.7375 13.1875 7.39062 12.8156 7.95938 12.3094C8.52812 11.8031 9.025 11.2312 9.45 10.5938C8.9125 10.3188 8.35313 10.1094 7.77188 9.96563C7.19063 9.82188 6.6 9.75 6 9.75C5.4 9.75 4.80938 9.82188 4.22813 9.96563C3.64688 10.1094 3.0875 10.3188 2.55 10.5938C2.975 11.2312 3.47188 11.8031 4.04063 12.3094C4.60938 12.8156 5.2625 13.1875 6 13.425Z"
      fill={color}
    />
  </Svg>
);

// System Update Icon Component
const SystemUpdateIcon: React.FC<{ size?: number; color?: string }> = ({
  size = 16,
  color = "#697282",
}) => (
  <Svg width={size} height={size} viewBox="0 0 16 16" fill="none">
    <Defs>
      <Mask
        id="mask0_3121_42805"
        maskUnits="userSpaceOnUse"
        x="0"
        y="0"
        width="16"
        height="16"
      >
        <Rect width="16" height="16" fill="#D9D9D9" />
      </Mask>
    </Defs>
    <G mask="url(#mask0_3121_42805)">
      <Path
        d="M2 13.3346V12.0013H3.83333L3.56667 11.768C2.98889 11.2569 2.58333 10.6735 2.35 10.018C2.11667 9.36241 2 8.7013 2 8.03464C2 6.8013 2.36944 5.70408 3.10833 4.74297C3.84722 3.78186 4.81111 3.14575 6 2.83464V4.23464C5.2 4.52352 4.55556 5.01519 4.06667 5.70964C3.57778 6.40408 3.33333 7.17908 3.33333 8.03464C3.33333 8.53464 3.42778 9.02075 3.61667 9.49297C3.80556 9.96519 4.1 10.4013 4.5 10.8013L4.66667 10.968V9.33463H6V13.3346H2ZM13.95 7.33464H12.6C12.5444 6.94575 12.425 6.57352 12.2417 6.21797C12.0583 5.86241 11.8111 5.52352 11.5 5.2013L11.3333 5.03464V6.66797H10V2.66797H14V4.0013H12.1667L12.4333 4.23464C12.8889 4.7013 13.2389 5.19575 13.4833 5.71797C13.7278 6.24019 13.8833 6.77908 13.95 7.33464ZM11.3333 15.3346L11.1333 14.3346C11 14.2791 10.875 14.2207 10.7583 14.1596C10.6417 14.0985 10.5222 14.0235 10.4 13.9346L9.43333 14.2346L8.76667 13.1013L9.53333 12.4346C9.51111 12.2791 9.5 12.1346 9.5 12.0013C9.5 11.868 9.51111 11.7235 9.53333 11.568L8.76667 10.9013L9.43333 9.76797L10.4 10.068C10.5222 9.97908 10.6417 9.90408 10.7583 9.84297C10.875 9.78186 11 9.72352 11.1333 9.66797L11.3333 8.66797H12.6667L12.8667 9.66797C13 9.72352 13.125 9.78464 13.2417 9.8513C13.3583 9.91797 13.4778 10.0013 13.6 10.1013L14.5667 9.76797L15.2333 10.9346L14.4667 11.6013C14.4889 11.7346 14.5 11.8735 14.5 12.018C14.5 12.1624 14.4889 12.3013 14.4667 12.4346L15.2333 13.1013L14.5667 14.2346L13.6 13.9346C13.4778 14.0235 13.3583 14.0985 13.2417 14.1596C13.125 14.2207 13 14.2791 12.8667 14.3346L12.6667 15.3346H11.3333ZM12 13.3346C12.3667 13.3346 12.6806 13.2041 12.9417 12.943C13.2028 12.6819 13.3333 12.368 13.3333 12.0013C13.3333 11.6346 13.2028 11.3207 12.9417 11.0596C12.6806 10.7985 12.3667 10.668 12 10.668C11.6333 10.668 11.3194 10.7985 11.0583 11.0596C10.7972 11.3207 10.6667 11.6346 10.6667 12.0013C10.6667 12.368 10.7972 12.6819 11.0583 12.943C11.3194 13.2041 11.6333 13.3346 12 13.3346Z"
        fill={color}
      />
    </G>
  </Svg>
);

// Custom Call Icon Component
const CallIcon: React.FC<{ size?: number; color?: string }> = ({
  size = 24,
  color = "#005FDD",
}) => (
  <Svg width={size} height={size} viewBox="0 0 24 25" fill="none">
    <Path
      d="M19.2222 15.7695L16.6822 15.4795C16.0722 15.4095 15.4722 15.6195 15.0422 16.0495L13.2022 17.8895C10.3722 16.4495 8.05223 14.1395 6.61223 11.2995L8.46223 9.44953C8.89223 9.01953 9.10223 8.41953 9.03223 7.80953L8.74223 5.28953C8.62223 4.27953 7.77223 3.51953 6.75223 3.51953H5.02223C3.89223 3.51953 2.95223 4.45953 3.02223 5.58953C3.55223 14.1295 10.3822 20.9495 18.9122 21.4795C20.0422 21.5495 20.9822 20.6095 20.9822 19.4795V17.7495C20.9922 16.7395 20.2322 15.8895 19.2222 15.7695Z"
      fill={color}
    />
  </Svg>
);

export interface IncidentDetailViewRef {
  openAddUpdateModal: () => void;
}

export const IncidentDetailView = forwardRef<
  IncidentDetailViewRef,
  IncidentDetailViewProps
>(
  (
    {
      incident,
      allAssets,
      onBack,
      firstAssistMemberOrder,
      userInfoFromServer,
      mapBottomSheetRef,
      optimisticAssistingStatus,
      bottomSheetIndex = 0,
      onAddUpdate,
      shouldShowAddUpdateButton = false,
      onTabChange,
      onIncidentCleared,
      isDarkMode = false,
    },
    ref
  ) => {
    const { authTokens } = useAuth();
    const currentUserCognitoSub = authTokens?.idToken
      ? (() => {
          try {
            return jwtDecode<DecodedToken>(authTokens.idToken).sub;
          } catch (error) {
            console.error("Error decoding idToken:", error);
            return "";
          }
        })()
      : "";

    const [tabIndex, setTabIndex] = useState(0);
    const [hasSceneBeenCleared, setHasSceneBeenCleared] = useState(false);
    const [hasAutoExpanded, setHasAutoExpanded] = useState(false);
    const [showUpdateModal, setShowUpdateModal] = useState(false);
    const [updateMessage, setUpdateMessage] = useState("");
    const [optimisticUpdates, setOptimisticUpdates] = useState<any[]>([]);
    const [incidentNotes, setIncidentNotes] = useState("");
    const [optimisticClearedTime, setOptimisticClearedTime] = useState<
      string | null
    >(null);
    const truncatedId =
      incident.id?.replace(/[^0-9]/g, "").slice(0, 3) || "000";

    // Mutations for Clear Scene functionality
    const completeOrder = useCompleteOrder();
    const addSituationUpdate = useAddSituationUpdate();
    const updateOrder = useUpdateOrder();

    // Check if user is already assisting based on order status (including optimistic status)
    const isAlreadyAssisting =
      firstAssistMemberOrder?.typeSpecificStatus === "ASSISTING" ||
      optimisticAssistingStatus === "ASSISTING";

    // Check if current user has an active order for THIS SPECIFIC incident (same logic as IncidentCard)
    const hasCurrentUserOrderForThisIncident = useMemo(() => {
      if (!firstAssistMemberOrder || !userInfoFromServer) return false;

      // Check if the current assist member order is for this specific incident
      const isForThisIncident =
        firstAssistMemberOrder.situationId === incident.id;

      // Check if the order is still active (not completed, cancelled, or rejected)
      const isActiveOrder =
        hookOrderStatusToString(firstAssistMemberOrder.status) !==
          "ORDER_STATUS_COMPLETED" &&
        hookOrderStatusToString(firstAssistMemberOrder.status) !==
          "ORDER_STATUS_CANCELLED" &&
        hookOrderStatusToString(firstAssistMemberOrder.status) !==
          "ORDER_STATUS_REJECTED";

      return isForThisIncident && isActiveOrder;
    }, [firstAssistMemberOrder, userInfoFromServer, incident.id]);

    // Fetch situations data with polling to get updated incident information
    const { data: situationsData, refetch: refetchSituations } =
      useListSituations({} as any);

    // Get the current incident from the situations data (this will have the latest updates)
    const currentIncident = useMemo(() => {
      if (!situationsData?.situations) return incident;

      const updatedIncident = situationsData.situations.find(
        (s) => s.id === incident.id
      );
      return updatedIncident || incident;
    }, [situationsData, incident]);

    // Fetch orders for this incident with polling
    const { data: ordersData, refetch: refetchOrders } =
      useListOrdersForSituation({
        situationId: incident.id,
        pageSize: 50,
        pageToken: "",
      } as ListOrdersForSituationRequest);

    // Set up polling for both situations and orders every 10 seconds
    useEffect(() => {
      const interval = setInterval(() => {
        console.log(
          `[Incident Detail] Refetching data for incident ${incident.id} at`,
          new Date().toISOString()
        );
        refetchSituations();
        refetchOrders();
      }, 10000);

      return () => clearInterval(interval);
    }, [refetchSituations, refetchOrders, incident.id]);

    // Check if the incident has been cleared by looking at all orders for this incident
    const isIncidentCleared = useMemo(() => {
      if (!ordersData?.orders || !userInfoFromServer) return false;

      // Look through ALL orders for this incident to find if current user has a completed one
      const userCompletedOrder = ordersData.orders.find(
        (order) =>
          order.assetId === userInfoFromServer.id &&
          // @ts-ignore
          order.type === "ORDER_TYPE_ASSIST_MEMBER" &&
          hookOrderStatusToString(order.status) === "ORDER_STATUS_COMPLETED"
      );

      return !!userCompletedOrder;
    }, [ordersData, userInfoFromServer]);

    // Get the cleared time from the user's completed order
    const clearedTime = useMemo(() => {
      if (!ordersData?.orders || !userInfoFromServer) return null;

      const userCompletedOrder = ordersData.orders.find(
        (order) =>
          order.assetId === userInfoFromServer.id &&
          // @ts-ignore
          order.type === "ORDER_TYPE_ASSIST_MEMBER" &&
          hookOrderStatusToString(order.status) === "ORDER_STATUS_COMPLETED"
      );

      if (userCompletedOrder?.additionalInfoJson) {
        try {
          const additionalInfo = JSON.parse(
            userCompletedOrder.additionalInfoJson
          );
          return additionalInfo.clearedTime || null;
        } catch (error) {
          console.error("Error parsing additionalInfoJson:", error);
          return null;
        }
      }

      return null;
    }, [ordersData, userInfoFromServer]);

    // Auto-expand to full height when cleared incident is detected (only once)
    useEffect(() => {
      if (isIncidentCleared && mapBottomSheetRef?.current && !hasAutoExpanded) {
        // Small delay to ensure the component has rendered
        setTimeout(() => {
          mapBottomSheetRef.current.forceExpandToFull();
          setHasAutoExpanded(true);
        }, 100);
      }
    }, [isIncidentCleared, mapBottomSheetRef, hasAutoExpanded]);

    // Clear optimistic cleared state when real cleared status is confirmed
    useEffect(() => {
      if (hasSceneBeenCleared && isIncidentCleared) {
        setHasSceneBeenCleared(false);
      }
    }, [hasSceneBeenCleared, isIncidentCleared]);

    // Clear optimistic cleared time when real cleared time becomes available
    useEffect(() => {
      if (optimisticClearedTime && clearedTime) {
        setOptimisticClearedTime(null);
      }
    }, [optimisticClearedTime, clearedTime]);

    const shouldShowClearScene =
      (isAlreadyAssisting && hasCurrentUserOrderForThisIncident) ||
      isIncidentCleared ||
      hasSceneBeenCleared ||
      (optimisticAssistingStatus === "ASSISTING" &&
        firstAssistMemberOrder?.situationId === incident.id);

    // Handle Clear Scene button press
    const handleClearScene = async () => {
      if (!firstAssistMemberOrder || !userInfoFromServer) {
        return;
      }

      try {
        // Set optimistic state immediately
        setHasSceneBeenCleared(true);

        const orderId = firstAssistMemberOrder.id;
        const clearedTimestamp = new Date().toISOString();

        // Set optimistic cleared time immediately
        setOptimisticClearedTime(clearedTimestamp);

        // Notify parent that this incident was cleared
        onIncidentCleared?.();

        if (orderId) {
          console.log("[Clear Scene] Completing order:", orderId);

          // First, update the order with the cleared time in additionalInfoJson
          const existingAdditionalInfo =
            firstAssistMemberOrder.additionalInfoJson
              ? safeJsonParse(firstAssistMemberOrder.additionalInfoJson)
              : {};

          const updatedAdditionalInfo = {
            ...existingAdditionalInfo,
            clearedTime: clearedTimestamp,
          };

          await updateOrder.mutateAsync({
            order: {
              id: orderId,
              additionalInfoJson: JSON.stringify(updatedAdditionalInfo),
            } as Order,
          } as UpdateOrderRequest);

          // Then complete the order
          await completeOrder.mutateAsync({
            id: orderId,
          } as CompleteOrderRequest);

          await addSituationUpdate.mutateAsync({
            id: currentIncident.id || "",
            update: createUpdateEntry(
              "COMPLETED",
              "status change",
              userInfoFromServer.id || "",
              userInfoFromServer.name || ""
            ),
          } as AddSituationUpdateRequest);

          console.log(
            "[Clear Scene] Successfully completed order and added situation update"
          );
        }
      } catch (error) {
        console.error("Error clearing scene:", error);
        // Revert optimistic updates on error
        setHasSceneBeenCleared(false);
        setOptimisticClearedTime(null);
        Alert.alert("Error", "Failed to clear scene. Please try again.");
      }
    };

    // Calculate assigned responders with current user first
    const assignedResponders = useMemo(() => {
      if (!ordersData?.orders) return [];

      const responderAssets = ordersData.orders

        .filter(
          (order) =>
            // @ts-ignore
            order.type === "ORDER_TYPE_ASSIST_MEMBER" &&
            // @ts-ignore
            order.status !== "ORDER_STATUS_CANCELLED" &&
            // @ts-ignore
            order.status !== "ORDER_STATUS_REJECTED"
        )
        .map((order) => allAssets.find((asset) => asset.id === order.assetId))
        .filter(Boolean);

      // Find current user and other responders
      const currentUserAsset = responderAssets.find(
        (asset) => asset!.cognitoJwtSub === currentUserCognitoSub
      );
      const otherResponders = responderAssets.filter(
        (asset) => asset!.cognitoJwtSub !== currentUserCognitoSub
      );

      // Create sorted list with current user first
      const sortedResponders = [];
      if (currentUserAsset) {
        sortedResponders.push({
          id: currentUserAsset.id.replace(/[^0-9]/g, "").slice(0, 3),
          isCurrentUser: true,
        });
      }

      otherResponders.forEach((asset) => {
        sortedResponders.push({
          id: asset!.id.replace(/[^0-9]/g, "").slice(0, 3),
          isCurrentUser: false,
        });
      });

      return sortedResponders;
    }, [ordersData, allAssets, currentUserCognitoSub]);

    // Format responder display text with current user in blue
    const getResponderDisplayText = ():
      | { text: string; isCurrentUser: boolean }[]
      | string => {
      if (assignedResponders.length === 0) return "No responders assigned";

      if (assignedResponders.length === 1) {
        return [
          {
            text: assignedResponders[0].id,
            isCurrentUser: assignedResponders[0].isCurrentUser,
          },
        ];
      }

      if (assignedResponders.length === 2) {
        return [
          {
            text: assignedResponders[0].id + ", ",
            isCurrentUser: assignedResponders[0].isCurrentUser,
          },
          {
            text: assignedResponders[1].id,
            isCurrentUser: assignedResponders[1].isCurrentUser,
          },
        ];
      }

      // More than 2: show first 2 + count
      const additionalCount = assignedResponders.length - 2;
      return [
        {
          text: assignedResponders[0].id + ", ",
          isCurrentUser: assignedResponders[0].isCurrentUser,
        },
        {
          text: assignedResponders[1].id + " ",
          isCurrentUser: assignedResponders[1].isCurrentUser,
        },
        {
          text: `+${additionalCount}`,
          isCurrentUser: false,
        },
      ];
    };

    const responderDisplayElements = getResponderDisplayText();

    // Get status display text (same logic as IncidentCard)
    const getStatusText = () => {
      // Use the current incident status and format it properly
      const status =
        currentIncident.status
          ?.replace("SITUATION_STATUS_", "")
          .toLowerCase() || "unknown";

      // Capitalize first letter
      return status.charAt(0).toUpperCase() + status.slice(1);
    };

    // Get priority color
    const getPriorityColor = (priority: number) => {
      switch (priority) {
        case 1:
          return {
            backgroundColor: getColor("rose.100", isDarkMode),
            color: getColor("rose.600", isDarkMode),
          };
        case 2:
          return {
            backgroundColor: getColor("amber.100", isDarkMode),
            color: getColor("amber.600", isDarkMode),
          };
        case 3:
          return {
            backgroundColor: getColor("blue.100", isDarkMode),
            color: getColor("blue.600", isDarkMode),
          };
        case 4:
          return {
            backgroundColor: getColor("blue.100", isDarkMode),
            color: getColor("blue.600", isDarkMode),
          };
        default:
          return {
            backgroundColor: getColor("gray.100", isDarkMode),
            color: getColor("gray.600", isDarkMode),
          };
      }
    };

    // Check if current user is assigned to this incident (appears in responders list)
    const isUserAssignedToIncident = useMemo(() => {
      if (!ordersData?.orders || !userInfoFromServer) return false;

      // Check if user has any order (past or present) for this incident
      const userOrder = ordersData.orders.find(
        (order) =>
          order.assetId === userInfoFromServer.id &&
          // @ts-ignore
          order.type === "ORDER_TYPE_ASSIST_MEMBER"
      );

      return !!userOrder;
    }, [ordersData, userInfoFromServer]);

    // Find the user's order for this incident to save/load notes
    const userOrderForIncident = useMemo(() => {
      if (!ordersData?.orders || !userInfoFromServer) return null;

      return (
        ordersData.orders.find(
          (order) =>
            order.assetId === userInfoFromServer.id &&
            // @ts-ignore
            order.type === "ORDER_TYPE_ASSIST_MEMBER"
        ) || null
      );
    }, [ordersData, userInfoFromServer]);

    // Load notes from the user's order when it's available
    useEffect(() => {
      if (userOrderForIncident?.notes) {
        setIncidentNotes(userOrderForIncident.notes);
      }
    }, [userOrderForIncident?.notes]);

    // Handle notes change - save to user's order
    const handleNotesChange = useCallback(
      async (notes: string) => {
        setIncidentNotes(notes);

        if (!userOrderForIncident) {
          console.warn(
            "No user order found for this incident, cannot save notes"
          );
          return;
        }

        try {
          updateOrder.mutate(
            {
              order: {
                id: userOrderForIncident.id,
                notes: notes,
              } as Order,
            } as UpdateOrderRequest,
            {
              onSuccess: () => {
                console.log(
                  "Notes saved successfully to order:",
                  userOrderForIncident.id
                );
              },
              onError: (error) => {
                console.error("Error saving notes to order:", error);
                // Could show a toast or alert here if needed
              },
            }
          );
        } catch (error) {
          console.error("Error saving notes to order:", error);
        }
      },
      [userOrderForIncident, updateOrder]
    );

    // Tab routes - dynamically include Notes tab if user is assigned
    const routes = useMemo(() => {
      const baseRoutes = [
        { key: "details", title: "Details" },
        { key: "updates", title: "Log" },
      ];

      if (isUserAssignedToIncident) {
        baseRoutes.push({ key: "notes", title: "Notes" });
      }

      return baseRoutes;
    }, [isUserAssignedToIncident]);

    // Handle tab change and notify parent
    const handleTabChange = useCallback(
      (newTabIndex: number) => {
        setTabIndex(newTabIndex);
        onTabChange?.(newTabIndex);
      },
      [onTabChange]
    );

    // Render Details tab content
    const renderDetailsTab = useCallback(
      () => (
        <BottomSheetScrollView
          style={styles.tabContent}
          contentContainerStyle={styles.scrollContent}
          nestedScrollEnabled={true}
          showsVerticalScrollIndicator={false}
        >
          {/* Incident Description */}
          <Text
            style={[
              styles.sectionTitle,
              { color: getColor("gray.600", isDarkMode) },
            ]}
          >
            Incident Description
          </Text>
          <View style={styles.section}>
            <View
              style={[
                styles.descriptionCard,
                {
                  backgroundColor: isDarkMode
                    ? getColor("gray.100", isDarkMode)
                    : "#F8F9FA",
                  borderColor: getColor("gray.200", isDarkMode),
                },
              ]}
            >
              <Text
                style={[
                  styles.descriptionText,
                  { color: getColor("gray.900", isDarkMode) },
                ]}
              >
                {currentIncident.description || "No description available"}
              </Text>
            </View>
          </View>

          {/* Caller Information */}
          <Text
            style={[
              styles.sectionTitle,
              { color: getColor("gray.600", isDarkMode) },
            ]}
          >
            Caller Information
          </Text>
          <View style={styles.section}>
            {currentIncident.callerPhone ||
            currentIncident.contactNo ||
            (currentIncident.additionalInfoJson &&
              safeJsonParse(currentIncident.additionalInfoJson).callerNo) ? (
              <TouchableOpacity
                style={[
                  styles.callerCard,
                  {
                    backgroundColor: isDarkMode
                      ? getColor("gray.100", isDarkMode)
                      : "#F8F9FA",
                    borderColor: getColor("gray.200", isDarkMode),
                  },
                ]}
                onPress={() =>
                  handleCallPress(
                    currentIncident.callerPhone ||
                      currentIncident.contactNo ||
                      safeJsonParse(currentIncident.additionalInfoJson).callerNo
                  )
                }
              >
                <View style={styles.callerInfo}>
                  <Text
                    style={[
                      styles.callerName,
                      { color: getColor("gray.900", isDarkMode) },
                    ]}
                  >
                    {currentIncident.callerName ||
                      currentIncident.reporterName ||
                      "Unknown Caller"}
                  </Text>
                  <Text
                    style={[
                      styles.callerPhone,
                      { color: getColor("gray.600", isDarkMode) },
                    ]}
                  >
                    {currentIncident.callerPhone ||
                      currentIncident.contactNo ||
                      safeJsonParse(currentIncident.additionalInfoJson)
                        .callerNo}
                  </Text>
                </View>
                <CallIcon size={20} color="#005FDD" />
              </TouchableOpacity>
            ) : (
              <View
                style={[
                  styles.callerCard,
                  {
                    backgroundColor: isDarkMode
                      ? getColor("gray.100", isDarkMode)
                      : "#F8F9FA",
                    borderColor: getColor("gray.200", isDarkMode),
                  },
                ]}
              >
                <Text
                  style={[
                    styles.callerName,
                    { color: getColor("gray.900", isDarkMode) },
                  ]}
                >
                  {currentIncident.callerName ||
                    currentIncident.reporterName ||
                    "Unknown Caller"}
                </Text>
              </View>
            )}
          </View>

          {/* Address */}
          <Text
            style={[
              styles.sectionTitle,
              { color: getColor("gray.600", isDarkMode) },
            ]}
          >
            Location
          </Text>
          <View style={styles.section}>
            <View
              style={[
                styles.addressCard,
                {
                  backgroundColor: isDarkMode
                    ? getColor("gray.100", isDarkMode)
                    : "#F8F9FA",
                  borderColor: getColor("gray.200", isDarkMode),
                },
              ]}
            >
              <Text
                style={[
                  styles.addressText,
                  { color: getColor("gray.900", isDarkMode) },
                ]}
              >
                {currentIncident.address || "Address not available"}
              </Text>
            </View>
          </View>

          {/* Incident Details Group */}
          <Text
            style={[
              styles.sectionTitle,
              { color: getColor("gray.600", isDarkMode) },
            ]}
          >
            Incident Details
          </Text>
          <View style={styles.section}>
            <View
              style={[
                styles.detailsGroupCard,
                {
                  backgroundColor: isDarkMode
                    ? getColor("gray.100", isDarkMode)
                    : "#F8F9FA",
                  borderColor: getColor("gray.200", isDarkMode),
                },
              ]}
            >
              <View style={styles.detailRow}>
                <Text
                  style={[
                    styles.detailLabel,
                    { color: getColor("gray.600", isDarkMode) },
                  ]}
                >
                  Incident Type
                </Text>
                <Text
                  style={[
                    styles.detailValue,
                    { color: getColor("gray.900", isDarkMode) },
                  ]}
                >
                  {getSituationType(currentIncident.type) || "Unknown"}
                </Text>
              </View>
              <View
                style={[
                  styles.detailSeparator,
                  { backgroundColor: getColor("gray.200", isDarkMode) },
                ]}
              />
              <View style={styles.detailRow}>
                <Text
                  style={[
                    styles.detailLabel,
                    { color: getColor("gray.600", isDarkMode) },
                  ]}
                >
                  Incident #
                </Text>
                <Text
                  style={[
                    styles.detailValue,
                    { color: getColor("gray.900", isDarkMode) },
                  ]}
                >
                  {truncatedId}
                </Text>
              </View>
            </View>
          </View>
        </BottomSheetScrollView>
      ),
      [currentIncident, truncatedId]
    );

    // Handle call press with confirmation
    const handleCallPress = (phoneNumber: string) => {
      Linking.openURL(`tel:${phoneNumber}`).catch((err) => {
        console.error("Failed to make call:", err);
        Alert.alert("Error", "Unable to make phone call");
      });
    };

    // Render Updates tab content with optimistic updates
    const renderUpdatesTab = useCallback(() => {
      // Combine real updates with optimistic updates, but avoid duplicates
      const realUpdates = currentIncident.updates || [];

      // Filter out optimistic updates that might be duplicated by server data
      const filteredOptimisticUpdates = optimisticUpdates.filter(
        (optimisticUpdate) => {
          // Look for a real update with similar timestamp (within 10 seconds) and same message
          const hasSimilarRealUpdate = realUpdates.some((realUpdate: any) => {
            const timeDiff = Math.abs(
              new Date(realUpdate.timestamp).getTime() -
                new Date(optimisticUpdate.timestamp).getTime()
            );
            return (
              timeDiff < 10000 && // Within 10 seconds
              realUpdate.message === optimisticUpdate.message &&
              realUpdate.eventType === optimisticUpdate.eventType
            );
          });
          return !hasSimilarRealUpdate;
        }
      );

      const allUpdates = [...filteredOptimisticUpdates, ...realUpdates];

      return (
        <BottomSheetScrollView
          style={styles.tabContent}
          contentContainerStyle={styles.scrollContent}
          nestedScrollEnabled={true}
          showsVerticalScrollIndicator={false}
        >
          {allUpdates.length > 0 ? (
            allUpdates
              .slice()
              .filter(
                (update: any) =>
                  update.eventType !== "status change" &&
                  update.eventType !== "order created"
              )
              .sort((a: any, b: any) => {
                const aTime = a.timestamp ? new Date(a.timestamp).getTime() : 0;
                const bTime = b.timestamp ? new Date(b.timestamp).getTime() : 0;
                return bTime - aTime;
              })
              .map((update: any, index: number) => {
                const isSystemUpdate = update.eventType === "info change";

                return (
                  <View
                    key={update.optimisticId || `${update.timestamp}-${index}`}
                    style={[
                      styles.updateCard,
                      {
                        backgroundColor: getColor("gray.100", isDarkMode),
                        borderColor: getColor("gray.200", isDarkMode),
                      },
                    ]}
                  >
                    <View style={styles.updateHeader}>
                      <Text
                        style={[
                          styles.updateTime,
                          { color: getColor("gray.600", isDarkMode) },
                        ]}
                      >
                        {new Date(update.timestamp).toLocaleTimeString(
                          "en-US",
                          {
                            hour12: false,
                            hour: "2-digit",
                            minute: "2-digit",
                          }
                        )}
                      </Text>
                    </View>
                    <View style={styles.updateContent}>
                      {isSystemUpdate ? (
                        <View style={styles.systemUpdateRow}>
                          <SystemUpdateIcon
                            size={16}
                            color={getColor("gray.600", isDarkMode)}
                          />
                          <Text
                            style={[
                              styles.systemUpdateText,
                              { color: getColor("gray.600", isDarkMode) },
                            ]}
                          >
                            {update.message}
                          </Text>
                        </View>
                      ) : (
                        <Text
                          style={[
                            styles.regularUpdateText,
                            { color: getColor("gray.900", isDarkMode) },
                          ]}
                        >
                          {update.message}
                        </Text>
                      )}
                    </View>
                  </View>
                );
              })
          ) : (
            <Text
              style={[
                styles.emptyText,
                { color: getColor("gray.600", isDarkMode) },
              ]}
            >
              No updates available
            </Text>
          )}
        </BottomSheetScrollView>
      );
    }, [currentIncident.updates, optimisticUpdates]);

    // Render Notes tab content
    const renderNotesTab = useCallback(
      () => (
        <NotesTab
          incidentId={currentIncident.id || ""}
          onNotesChange={handleNotesChange}
          initialNotes={incidentNotes}
          isDarkMode={isDarkMode}
        />
      ),
      [currentIncident.id, handleNotesChange, incidentNotes]
    );

    // Render scene for TabView
    const renderScene = useCallback(
      ({ route }: { route: { key: string } }) => {
        switch (route.key) {
          case "details":
            return renderDetailsTab();
          case "updates":
            return renderUpdatesTab();
          case "notes":
            return renderNotesTab();
          default:
            return null;
        }
      },
      [renderDetailsTab, renderUpdatesTab, renderNotesTab]
    );

    // Handle add update with optimistic update
    const handleAddUpdate = async () => {
      if (!updateMessage.trim() || !userInfoFromServer) {
        return;
      }

      // Create optimistic update with unique ID
      const optimisticUpdate = {
        message: updateMessage.trim(),
        eventType: "quick note",
        updaterId: userInfoFromServer.id || "",
        timestamp: new Date().toISOString(),
        optimisticId: `optimistic-${Date.now()}`, // Unique ID for tracking
      };

      try {
        // Add optimistic update immediately
        setOptimisticUpdates((prev) => [optimisticUpdate, ...prev]);

        // Clear input and close modal
        const messageToSend = updateMessage.trim();
        setUpdateMessage("");
        setShowUpdateModal(false);

        // Call the parent handler
        if (onAddUpdate) {
          await onAddUpdate(messageToSend);
        } else {
          // Fallback to direct API call if no handler provided
          await addSituationUpdate.mutateAsync({
            id: currentIncident.id || "",
            update: createUpdateEntry(
              messageToSend,
              "quick note",
              userInfoFromServer.id || "",
              userInfoFromServer.name || ""
            ),
          } as AddSituationUpdateRequest);
        }

        // Remove the optimistic update after server data arrives
        setTimeout(() => {
          setOptimisticUpdates((prev) =>
            prev.filter(
              (update) => update.optimisticId !== optimisticUpdate.optimisticId
            )
          );
        }, 5000); // Give server time to send real data
      } catch (error) {
        console.error("Error adding update:", error);
        // Remove optimistic update on error
        setOptimisticUpdates((prev) =>
          prev.filter(
            (update) => update.optimisticId !== optimisticUpdate.optimisticId
          )
        );
        Alert.alert("Error", "Failed to add update. Please try again.");
      }
    };

    // Expose modal trigger function via ref
    useImperativeHandle(ref, () => ({
      openAddUpdateModal: () => setShowUpdateModal(true),
    }));

    return (
      <>
        <View
          style={[
            styles.container,
            {
              backgroundColor: getColor("background", isDarkMode),
            },
          ]}
        >
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.headerLeft}>
              <Text
                style={[
                  styles.incidentTitle,
                  { color: getColor("gray.900", isDarkMode) },
                ]}
              >
                <Text style={styles.incidentId}>{truncatedId}</Text>
                <Text
                  style={[
                    styles.incidentType,
                    { color: getColor("gray.900", isDarkMode) },
                  ]}
                >
                  {" "}
                  {getSituationType(currentIncident.type) || "Unknown"}
                </Text>
              </Text>
              <View style={styles.statusRow}>
                <View
                  style={[
                    styles.priorityBadge,
                    getPriorityColor(currentIncident.priority),
                  ]}
                >
                  <Text
                    style={[
                      styles.priorityText,
                      getPriorityColor(currentIncident.priority),
                    ]}
                  >
                    P{currentIncident.priority || "-"}
                  </Text>
                </View>
                <View
                  style={[
                    styles.statusBadge,
                    {
                      backgroundColor: isDarkMode
                        ? getColor("gray.200", isDarkMode)
                        : getColor("gray.100", isDarkMode),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.statusText,
                      { color: getColor("gray.600", isDarkMode) },
                    ]}
                  >
                    {getStatusText()}
                  </Text>
                </View>
                <View style={styles.responderBadge}>
                  <ShieldPersonIcon
                    size={12}
                    color={
                      Array.isArray(responderDisplayElements) &&
                      responderDisplayElements.length > 0 &&
                      responderDisplayElements[0].isCurrentUser
                        ? "#0060FF"
                        : "#4B5563"
                    }
                  />
                  <View style={styles.responderTextContainer}>
                    {Array.isArray(responderDisplayElements) ? (
                      responderDisplayElements.map(
                        (
                          element: { text: string; isCurrentUser: boolean },
                          index: number
                        ) => (
                          <Text
                            key={index}
                            style={[
                              styles.responderText,
                              element.isCurrentUser && styles.currentUserText,
                            ]}
                          >
                            {element.text}
                          </Text>
                        )
                      )
                    ) : (
                      <Text style={styles.responderText}>
                        {responderDisplayElements}
                      </Text>
                    )}
                  </View>
                </View>
              </View>
            </View>

            <TouchableOpacity
              onPress={onBack}
              style={[
                styles.closeButton,
                { backgroundColor: getColor("gray.100", isDarkMode) },
              ]}
            >
              <Ionicons
                name="close"
                size={24}
                color={getColor("gray.900", isDarkMode)}
              />
            </TouchableOpacity>
          </View>

          {/* Clear Scene Card - Show when user is assisting */}
          {shouldShowClearScene && (
            <View style={styles.clearSceneContainer}>
              {hasSceneBeenCleared || isIncidentCleared ? (
                <View
                  style={[
                    styles.sceneClearedCard,
                    {
                      backgroundColor: isDarkMode
                        ? getColor("vine.tint", isDarkMode)
                        : "#E8F5E8",
                      borderColor: isDarkMode
                        ? getColor("vine.600", isDarkMode)
                        : "#10B981",
                    },
                  ]}
                >
                  <View style={styles.sceneClearedContent}>
                    <Ionicons
                      name="checkmark-circle"
                      size={20}
                      color={
                        isDarkMode
                          ? getColor("vine.600", isDarkMode)
                          : "#10B981"
                      }
                    />
                    <Text
                      style={[
                        styles.sceneClearedText,
                        {
                          color: isDarkMode
                            ? getColor("vine.600", isDarkMode)
                            : "#059669",
                        },
                      ]}
                    >
                      Scene Cleared
                    </Text>
                  </View>
                  <Text
                    style={[
                      styles.sceneClearedTime,
                      {
                        color: isDarkMode
                          ? getColor("vine.600", isDarkMode)
                          : "#059669",
                      },
                    ]}
                  >
                    {clearedTime || optimisticClearedTime ? (
                      <>
                        {new Date(
                          clearedTime || optimisticClearedTime!
                        ).toLocaleDateString("en-US", {
                          month: "2-digit",
                          day: "2-digit",
                          year: "2-digit",
                        })}{" "}
                        {new Date(
                          clearedTime || optimisticClearedTime!
                        ).toLocaleTimeString("en-US", {
                          hour12: false,
                          hour: "2-digit",
                          minute: "2-digit",
                        })}
                      </>
                    ) : (
                      <>--/--/-- --:--</>
                    )}
                  </Text>
                </View>
              ) : (
                <View style={styles.incidentCard}>
                  <View style={styles.incidentCardContent}>
                    <View style={styles.incidentCardInfo}>
                      <Text
                        style={styles.incidentCardLocation}
                        numberOfLines={1}
                      >
                        {currentIncident?.address || "Location not available"}
                      </Text>
                      <Text style={styles.incidentCardType} numberOfLines={1}>
                        {getSituationType(currentIncident?.type) || "Unknown"}
                      </Text>
                    </View>
                    <TouchableOpacity
                      style={styles.clearSceneButton}
                      onPress={handleClearScene}
                      activeOpacity={0.8}
                    >
                      <Text style={styles.clearSceneButtonText}>
                        Clear Scene
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              )}
            </View>
          )}

          {/* Tab View */}
          <TabView
            navigationState={{ index: tabIndex, routes }}
            renderScene={renderScene}
            onIndexChange={handleTabChange}
            initialLayout={{ width: screenWidth }}
            renderTabBar={(props) => (
              <TabBar
                {...props}
                indicatorStyle={[
                  styles.tabIndicator,
                  { backgroundColor: getColor("blue.600", isDarkMode) },
                ]}
                style={[
                  styles.tabBarStyle,
                  {
                    backgroundColor: getColor("background", isDarkMode),
                    borderBottomColor: getColor("gray.200", isDarkMode),
                  },
                ]}
                activeColor={getColor("blue.600", isDarkMode)}
                inactiveColor={getColor("gray.500", isDarkMode)}
                tabStyle={styles.tabStyle}
              />
            )}
          />
        </View>

        {/* Add Update Modal */}
        <Modal
          visible={showUpdateModal}
          transparent={true}
          animationType="slide"
          onRequestClose={() => setShowUpdateModal(false)}
        >
          <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
            <KeyboardAvoidingView
              style={styles.modalOverlay}
              behavior={Platform.OS === "ios" ? "padding" : "height"}
            >
              <TouchableWithoutFeedback onPress={() => Keyboard.dismiss()}>
                <View
                  style={[
                    styles.modalContainer,
                    {
                      backgroundColor: getColor("background", isDarkMode),
                    },
                  ]}
                >
                  <View style={styles.modalHeader}>
                    <Text
                      style={[
                        styles.modalTitle,
                        { color: getColor("gray.900", isDarkMode) },
                      ]}
                    >
                      Add Update
                    </Text>
                    <TouchableOpacity
                      onPress={() => setShowUpdateModal(false)}
                      style={styles.modalCloseButton}
                    >
                      <Ionicons
                        name="close"
                        size={24}
                        color={getColor("gray.600", isDarkMode)}
                      />
                    </TouchableOpacity>
                  </View>
                  <TextInput
                    style={[
                      styles.modalTextInput,
                      {
                        backgroundColor: isDarkMode
                          ? getColor("gray.100", isDarkMode)
                          : "white",
                        borderColor: getColor("gray.200", isDarkMode),
                        color: getColor("gray.900", isDarkMode),
                      },
                    ]}
                    multiline={true}
                    numberOfLines={4}
                    placeholder="Type your update here..."
                    placeholderTextColor={getColor("gray.500", isDarkMode)}
                    value={updateMessage}
                    onChangeText={setUpdateMessage}
                    autoFocus={true}
                    blurOnSubmit={false}
                  />
                  <View style={styles.modalActions}>
                    <TouchableOpacity
                      style={[
                        styles.modalCancelButton,
                        { borderColor: getColor("gray.200", isDarkMode) },
                      ]}
                      onPress={() => setShowUpdateModal(false)}
                    >
                      <Text
                        style={[
                          styles.modalCancelText,
                          { color: getColor("gray.600", isDarkMode) },
                        ]}
                      >
                        Cancel
                      </Text>
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={[
                        styles.modalSubmitButton,
                        { backgroundColor: getColor("blue.600", isDarkMode) },
                        !updateMessage.trim() &&
                          styles.modalSubmitButtonDisabled,
                      ]}
                      onPress={handleAddUpdate}
                      disabled={!updateMessage.trim()}
                    >
                      <Text style={styles.modalSubmitText}>Submit</Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </TouchableWithoutFeedback>
            </KeyboardAvoidingView>
          </TouchableWithoutFeedback>
        </Modal>
      </>
    );
  }
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    padding: 20,
    paddingBottom: 16,
    paddingTop: 16,
  },
  headerLeft: {
    flex: 1,
  },
  incidentTitle: {
    fontSize: 22,
    fontWeight: "400",
    marginBottom: 12,
  },
  incidentId: {
    fontWeight: "700",
  },
  incidentType: {
    fontWeight: "400",
  },
  statusRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    minWidth: 28,
    alignItems: "center",
  },
  priorityText: {
    fontSize: 12,
    fontWeight: "600",
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    minWidth: 28,
    alignItems: "center",
  },
  statusText: {
    fontSize: 14,
    fontWeight: "500",
  },
  responderBadge: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  responderTextContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  responderText: {
    fontSize: 12,
    fontWeight: "600",
    color: "#4B5563",
  },
  currentUserText: {
    color: "#0060FF",
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginTop: -8,
  },
  tabBarStyle: {
    elevation: 0,
    shadowOpacity: 0,
    borderBottomWidth: 1,
  },
  tabStyle: {
    paddingVertical: 4,
  },
  tabIndicator: {
    height: 2,
  },
  tabContent: {
    flex: 1,
    paddingTop: 20,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    marginLeft: 36,
    fontSize: 14,
    fontWeight: "500",
    marginBottom: 8,
  },
  descriptionCard: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
  },
  descriptionText: {
    fontSize: 16,
    lineHeight: 24,
  },
  callerCard: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  callerInfo: {
    flex: 1,
  },
  callerName: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 4,
  },
  callerPhone: {
    fontSize: 14,
  },
  addressCard: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
  },
  addressText: {
    fontSize: 16,
    lineHeight: 24,
  },
  detailsGroupCard: {
    borderRadius: 12,
    borderWidth: 1,
    overflow: "hidden",
  },
  detailRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
  },
  detailSeparator: {
    height: 1,
    marginHorizontal: 16,
  },
  detailLabel: {
    fontSize: 14,
    fontWeight: "400",
  },
  detailValue: {
    fontSize: 14,
    fontWeight: "500",
    textAlign: "right",
    flex: 1,
    marginLeft: 16,
  },
  locationRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    backgroundColor: "#F8F9FA",
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: "#E5E5E5",
    marginBottom: 12,
  },
  locationLabel: {
    fontSize: 14,
    fontWeight: "400",
    color: "#4A5565",
  },
  locationValue: {
    fontSize: 14,
    fontWeight: "500",
    color: "#000",
    textAlign: "right",
    flex: 1,
    marginLeft: 16,
  },
  updateCard: {
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 20,
    marginBottom: 12,
    borderWidth: 1,
  },
  updateHeader: {
    marginBottom: 8,
  },
  updateTime: {
    fontSize: 12,
    fontWeight: "600",
  },
  updateContent: {
    flex: 1,
  },
  systemUpdateRow: {
    flexDirection: "row",
    alignItems: "flex-start",
    gap: 8,
  },
  systemUpdateText: {
    fontSize: 14,
    fontStyle: "italic",
    flex: 1,
    lineHeight: 20,
  },
  regularUpdateText: {
    fontSize: 14,
    fontWeight: "500",
    lineHeight: 20,
  },
  emptyText: {
    textAlign: "center",
    fontSize: 16,
    marginTop: 40,
    paddingHorizontal: 20,
  },
  clearSceneContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  sceneClearedCard: {
    backgroundColor: "#E8F5E8",
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: "#10B981",
  },
  sceneClearedContent: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
    marginBottom: 8,
  },
  sceneClearedText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#059669",
  },
  sceneClearedTime: {
    fontSize: 14,
    color: "#059669",
    opacity: 0.8,
  },
  incidentCard: {
    backgroundColor: "#162456",
    borderRadius: 16,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  incidentCardContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  incidentCardInfo: {
    flex: 1,
    marginRight: 16,
  },
  incidentCardLocation: {
    fontSize: 20,
    fontWeight: "700",
    color: "#FFFFFF",
    marginBottom: 4,
  },
  incidentCardType: {
    fontSize: 14,
    color: "#FFFFFF",
    opacity: 0.8,
  },
  clearSceneButton: {
    backgroundColor: "#FFFFFF",
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: "center",
    minWidth: 120,
  },
  clearSceneButtonText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#162456",
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContainer: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingTop: 20,
    paddingHorizontal: 20,
    paddingBottom: 50,
    maxHeight: "50%",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  modalCloseButton: {
    padding: 4,
  },
  modalTextInput: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 16,
    fontSize: 16,
    minHeight: 120,
    textAlignVertical: "top",
    marginBottom: 20,
  },
  modalActions: {
    flexDirection: "row",
    justifyContent: "space-between",
    gap: 12,
    paddingBottom: 10,
  },
  modalCancelButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: "center",
  },
  modalCancelText: {
    fontSize: 16,
    fontWeight: "500",
  },
  modalSubmitButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: "center",
  },
  modalSubmitButtonDisabled: {
    backgroundColor: "#C0C0C0",
  },
  modalSubmitText: {
    fontSize: 16,
    color: "white",
    fontWeight: "600",
  },
});
