import { BottomSheetScrollView } from "@gorhom/bottom-sheet";
import React, { useCallback, useMemo, useState } from "react";
import { Dimensions, StyleSheet, Text, View } from "react-native";
import { TabBar, TabView } from "react-native-tab-view";
import { getColor } from "../../constants/colors";
import { IncidentCard } from "./IncidentCard";
import { UnitCard } from "./UnitCard";

const screenWidth = Dimensions.get("window").width;

interface BottomSheetListViewProps {
  activeSituations: any[];
  activeAssets: any[];
  allAssets: any[];
  currentUserCognitoSub?: string;
  activeOrders?: any[];
  onSituationPress?: (situation: any) => void;
  onAssetPress?: (asset: any) => void;
  isDarkMode?: boolean;
}

export const BottomSheetListView: React.FC<BottomSheetListViewProps> = ({
  activeSituations,
  activeAssets,
  allAssets,
  currentUserCognitoSub,
  activeOrders = [],
  onSituationPress,
  onAssetPress,
  isDarkMode = false,
}) => {
  const [tabIndex, setTabIndex] = useState(0);

  // Tab routes
  const routes = useMemo(
    () => [
      { key: "incidents", title: "Active Incidents" },
      { key: "units", title: "Active Units" },
    ],
    []
  );

  // Render incidents tab content
  const renderIncidentsTab = useCallback(
    () => (
      <BottomSheetScrollView
        style={styles.listContainer}
        contentContainerStyle={styles.scrollContent}
        nestedScrollEnabled={true}
        showsVerticalScrollIndicator={false}
      >
        {activeSituations.length > 0 ? (
          activeSituations.map((situation) => (
            <IncidentCard
              key={situation.id}
              situation={situation}
              assets={allAssets}
              currentUserCognitoSub={currentUserCognitoSub}
              activeOrders={activeOrders}
              onPress={() => onSituationPress?.(situation)}
              isDarkMode={isDarkMode}
            />
          ))
        ) : (
          <Text
            style={[
              styles.emptyText,
              { color: getColor("gray.600", isDarkMode) },
            ]}
          >
            No active incidents
          </Text>
        )}
      </BottomSheetScrollView>
    ),
    [
      activeSituations,
      allAssets,
      currentUserCognitoSub,
      activeOrders,
      onSituationPress,
    ]
  );

  // Render units tab content
  const renderUnitsTab = useCallback(
    () => (
      <BottomSheetScrollView
        style={styles.listContainer}
        contentContainerStyle={styles.scrollContent}
        nestedScrollEnabled={true}
        showsVerticalScrollIndicator={false}
      >
        {activeAssets.length > 0 ? (
          activeAssets.map((asset) => (
            <UnitCard
              key={asset.id}
              asset={asset}
              activeSituations={activeSituations}
              onPress={() => onAssetPress?.(asset)}
              isDarkMode={isDarkMode}
            />
          ))
        ) : (
          <Text
            style={[
              styles.emptyText,
              { color: getColor("gray.600", isDarkMode) },
            ]}
          >
            No active units
          </Text>
        )}
      </BottomSheetScrollView>
    ),
    [activeAssets, activeSituations, onAssetPress]
  );

  // Render scene for TabView
  const renderScene = useCallback(
    ({ route }: { route: { key: string } }) => {
      switch (route.key) {
        case "incidents":
          return renderIncidentsTab();
        case "units":
          return renderUnitsTab();
        default:
          return null;
      }
    },
    [renderIncidentsTab, renderUnitsTab]
  );

  return (
    <View style={styles.container}>
      <TabView
        navigationState={{ index: tabIndex, routes }}
        renderScene={renderScene}
        onIndexChange={setTabIndex}
        initialLayout={{ width: screenWidth }}
        renderTabBar={(props) => (
          <TabBar
            {...props}
            indicatorStyle={[
              styles.tabIndicator,
              { backgroundColor: getColor("blue.600", isDarkMode) },
            ]}
            style={[
              styles.tabBarStyle,
              {
                backgroundColor: getColor("background", isDarkMode),
                borderBottomColor: getColor("gray.200", isDarkMode),
              },
            ]}
            activeColor={getColor("gray.700", isDarkMode)}
            inactiveColor={getColor("gray.400", isDarkMode)}
            tabStyle={styles.tabStyle}
          />
        )}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  tabBarStyle: {
    elevation: 0,
    shadowOpacity: 0,
    borderBottomWidth: 1,
  },
  tabStyle: {
    paddingVertical: 4,
  },
  tabIndicator: {
    height: 2,
  },
  listContainer: {
    flex: 1,
  },
  scrollContent: {
    paddingHorizontal: 16,
    paddingTop: 16,
    paddingBottom: 80,
  },
  emptyText: {
    textAlign: "center",
    fontSize: 16,
    marginTop: 40,
  },
});
