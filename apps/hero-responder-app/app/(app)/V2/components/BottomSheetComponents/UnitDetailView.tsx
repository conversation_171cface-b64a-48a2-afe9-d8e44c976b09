import { Ionicons } from "@expo/vector-icons";
import { BottomSheetScrollView } from "@gorhom/bottom-sheet";
import {
  ListOrdersForAssetRequest,
  ListOrdersForSituationRequest,
} from "proto/hero/orders/v2/orders_pb";
import React, { useEffect, useMemo, useState } from "react";
import { StyleSheet, Text, TouchableOpacity, View } from "react-native";
import Svg, { Path } from "react-native-svg";
import {
  useListOrdersForAsset,
  useListOrdersForSituation,
} from "../../../../apis/services/workflow/orders/hooks";
import { reverseGeocode } from "../../../apis";
import { getColor } from "../../constants/colors";
import { useNavigation } from "../../contexts/NavigationContext";
import { getSituationType } from "../../utils";

interface UnitDetailViewProps {
  unit: any;
  activeSituations: any[];
  onBack: () => void;
  isDarkMode?: boolean;
}

// Custom Shield Person Icon Component
const ShieldPersonIcon: React.FC<{ size?: number; color?: string }> = ({
  size = 16,
  color = "#697282",
}) => (
  <Svg width={size} height={size} viewBox="0 0 12 15" fill="none">
    <Path
      d="M6 8.25C6.725 8.25 7.34375 7.99375 7.85625 7.48125C8.36875 6.96875 8.625 6.35 8.625 5.625C8.625 4.9 8.36875 4.28125 7.85625 3.76875C7.34375 3.25625 6.725 3 6 3C5.275 3 4.65625 3.25625 4.14375 3.76875C3.63125 4.28125 3.375 4.9 3.375 5.625C3.375 6.35 3.63125 6.96875 4.14375 7.48125C4.65625 7.99375 5.275 8.25 6 8.25ZM6 15C4.175 14.5375 2.71875 13.5375 1.63125 12C0.54375 10.4625 0 8.7375 0 6.825V2.25L6 0L12 2.25V6.825C12 8.7375 11.4563 10.4625 10.3687 12C9.28125 13.5375 7.825 14.5375 6 15ZM6 13.425C6.7375 13.1875 7.39062 12.8156 7.95938 12.3094C8.52812 11.8031 9.025 11.2312 9.45 10.5938C8.9125 10.3188 8.35313 10.1094 7.77188 9.96563C7.19063 9.82188 6.6 9.75 6 9.75C5.4 9.75 4.80938 9.82188 4.22813 9.96563C3.64688 10.1094 3.0875 10.3188 2.55 10.5938C2.975 11.2312 3.47188 11.8031 4.04063 12.3094C4.60938 12.8156 5.2625 13.1875 6 13.425Z"
      fill={color}
    />
  </Svg>
);

export const UnitDetailView: React.FC<UnitDetailViewProps> = ({
  unit,
  activeSituations,
  onBack,
  isDarkMode = false,
}) => {
  // Safety check for unit object
  if (!unit || !unit.id) {
    console.error("Unit object is invalid:", unit);
    return (
      <BottomSheetScrollView
        style={[
          { flex: 1, backgroundColor: getColor("background", isDarkMode) },
        ]}
      >
        <View style={{ padding: 20 }}>
          <Text style={{ color: getColor("gray.900", isDarkMode) }}>
            Error: Unit data not available
          </Text>
        </View>
      </BottomSheetScrollView>
    );
  }

  const truncatedId = unit.id.replace(/[^0-9]/g, "").slice(0, 3);
  const { navigateToIncident } = useNavigation();
  const [currentAddress, setCurrentAddress] =
    useState<string>("Loading address...");
  const [isLoadingAddress, setIsLoadingAddress] = useState(false);

  // Get unit name with proper formatting
  const unitName = unit.name
    ? unit.name.includes(" ")
      ? unit.name.split(" ")[0].trim()
      : unit.name.includes("+")
      ? unit.name.split("+")[0].trim()
      : unit.name
    : "";

  // Fetch active orders for this unit
  const { data: ordersData, refetch: refetchOrders } = useListOrdersForAsset({
    assetId: unit.id,
    pageSize: 50,
    pageToken: "",
  } as ListOrdersForAssetRequest);

  // Fetch address using reverse geocoding
  useEffect(() => {
    const fetchAddress = async () => {
      if (unit.latitude && unit.longitude) {
        setIsLoadingAddress(true);
        try {
          const address = await reverseGeocode(unit.latitude, unit.longitude);
          setCurrentAddress(address);
        } catch (error) {
          console.error("Failed to fetch address for unit:", unit.id, error);
          setCurrentAddress("Address not available");
        } finally {
          setIsLoadingAddress(false);
        }
      } else {
        setCurrentAddress("Location not available");
      }
    };

    fetchAddress();
  }, [unit.latitude, unit.longitude, unit.id]);

  // Set up polling for orders
  useEffect(() => {
    const interval = setInterval(() => {
      console.log(
        `[Unit Detail] Refetching orders for unit ${unit.id} at`,
        new Date().toISOString()
      );
      refetchOrders();
    }, 10000); // Poll every 10 seconds

    return () => clearInterval(interval);
  }, [refetchOrders, unit.id]);

  // Find active ASSIST_MEMBER order
  const activeOrder = useMemo(() => {
    if (!ordersData?.orders) return null;

    return ordersData.orders.find(
      (order) =>
        // @ts-ignore
        order.type === "ORDER_TYPE_ASSIST_MEMBER" &&
        // @ts-ignore
        order.status !== "ORDER_STATUS_COMPLETED" &&
        // @ts-ignore
        order.status !== "ORDER_STATUS_CANCELLED"
    );
  }, [ordersData]);

  // Find the assigned incident if there's an active order
  const assignedIncident = useMemo(() => {
    if (!activeOrder?.situationId) return null;

    return activeSituations.find(
      (situation) => situation.id === activeOrder.situationId
    );
  }, [activeOrder, activeSituations]);

  // Fetch all orders for the assigned incident to get all responders
  const { data: incidentOrdersData } = useListOrdersForSituation({
    situationId: assignedIncident?.id || "",
  } as ListOrdersForSituationRequest);

  // Calculate all assigned responders for the incident
  const assignedResponders = useMemo(() => {
    if (!incidentOrdersData?.orders || !assignedIncident) {
      console.log("[Unit Detail] No incident orders data or assigned incident");
      return [];
    }

    console.log(
      "[Unit Detail] Processing incident orders:",
      incidentOrdersData.orders.length
    );

    const responderIds = Array.from(
      new Set(
        incidentOrdersData.orders
          .filter((order) => {
            // @ts-ignore
            const isAssistOrder = order.type === "ORDER_TYPE_ASSIST_MEMBER";            
            const isActiveOrder =
              // @ts-ignore
              order.status !== "ORDER_STATUS_COMPLETED" &&
              // @ts-ignore
              order.status !== "ORDER_STATUS_CANCELLED";
            console.log(
              `[Unit Detail] Order ${order.id}: type=${order.type}, status=${order.status}, isAssist=${isAssistOrder}, isActive=${isActiveOrder}`
            );
            return isAssistOrder && isActiveOrder;
          })
          .map((order) => order.assetId)
          .filter(Boolean)
      )
    );

    console.log("[Unit Detail] Found responder IDs:", responderIds);

    // Convert asset IDs to short display format
    const shortIds = responderIds.map((id) =>
      id.replace(/[^0-9]/g, "").slice(0, 3)
    );
    console.log("[Unit Detail] Short responder IDs:", shortIds);

    return shortIds;
  }, [incidentOrdersData, assignedIncident]);

  // Format responder display text (same logic as IncidentCard)
  const getResponderDisplayText = () => {
    if (assignedResponders.length === 0) {
      return truncatedId; // Fallback to current unit
    }
    if (assignedResponders.length === 1) return assignedResponders[0];
    if (assignedResponders.length === 2)
      return `${assignedResponders[0]}, ${assignedResponders[1]}`;

    // More than 2: show first 2 + count
    const additionalCount = assignedResponders.length - 2;
    return `${assignedResponders[0]}, ${assignedResponders[1]} +${additionalCount}`;
  };

  // Get status display text and color
  const getStatusInfo = () => {
    if (activeOrder) {
      // @ts-ignore
      switch (activeOrder.status) {
        // @ts-ignore
        case "ORDER_STATUS_CREATED":
          return {
            text: "Dispatched",
            color: getColor("amber.600", isDarkMode),
            backgroundColor: getColor("amber.100", isDarkMode),
          };
        // @ts-ignore
        case "ORDER_STATUS_ACKNOWLEDGED":
          return {
            text: "En Route",
            color: getColor("rose.600", isDarkMode),
            backgroundColor: getColor("rose.100", isDarkMode),
          };
        // @ts-ignore
        case "ORDER_STATUS_IN_PROGRESS":
          return {
            text: "On Scene",
            color: getColor("rose.600", isDarkMode),
            backgroundColor: getColor("rose.100", isDarkMode),
          };
        default:
          // @ts-ignore
          return {
            text:
              (activeOrder.status as unknown as string)
                ?.replace("ORDER_STATUS_", "")
                .toLowerCase() || "Unknown",
            color: getColor("gray.600", isDarkMode),
            backgroundColor: getColor("gray.100", isDarkMode),
          };
      }
    }

    // Available/On Patrol status
    if (unit.status === "ASSET_STATUS_AVAILABLE") {
      return {
        text: "On Patrol",
        color: getColor("vine.600", isDarkMode),
        backgroundColor: getColor("vine.100", isDarkMode),
      };
    }

    return {
      text: "Unknown",
      color: getColor("gray.600", isDarkMode),
      backgroundColor: getColor("gray.100", isDarkMode),
    };
  };

  const statusInfo = getStatusInfo();

  // Calculate time since last update
  const getTimeSince = () => {
    const updateTime = activeOrder?.updateTime || unit.updatedAt;
    if (!updateTime) return "";

    const now = Date.now();
    const updated = new Date(updateTime).getTime();
    const minutesAgo = Math.max(0, Math.floor((now - updated) / 60000));
    const hoursAgo = Math.floor(minutesAgo / 60);
    const remainingMinutes = minutesAgo % 60;

    if (hoursAgo > 0) {
      return `Since ${hoursAgo}:${remainingMinutes
        .toString()
        .padStart(2, "0")}`;
    }
    return `Since ${remainingMinutes}m ago`;
  };

  // Handle assignment card press
  const handleAssignmentPress = () => {
    if (assignedIncident) {
      navigateToIncident(assignedIncident);
    }
  };

  return (
    <BottomSheetScrollView
      style={[
        styles.container,
        { backgroundColor: getColor("background", isDarkMode) },
      ]}
      contentContainerStyle={styles.scrollContent}
      nestedScrollEnabled={true}
      showsVerticalScrollIndicator={false}
    >
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text
            style={[
              styles.unitTitle,
              { color: getColor("gray.900", isDarkMode) },
            ]}
          >
            <Text style={styles.unitId}>{truncatedId}</Text>
            {unitName && (
              <Text
                style={[
                  styles.unitName,
                  { color: getColor("gray.600", isDarkMode) },
                ]}
              >
                {" "}
                {unitName}
              </Text>
            )}
          </Text>
          <View style={styles.statusRow}>
            <View
              style={[
                styles.statusBadge,
                {
                  backgroundColor:
                    statusInfo?.backgroundColor ||
                    getColor("gray.100", isDarkMode),
                },
              ]}
            >
              <Text
                style={[
                  styles.statusText,
                  {
                    color:
                      statusInfo?.color || getColor("gray.600", isDarkMode),
                  },
                ]}
              >
                {statusInfo?.text || "Unknown"}
              </Text>
            </View>
            <Text
              style={[
                styles.timeSince,
                { color: getColor("gray.600", isDarkMode) },
              ]}
            >
              {getTimeSince()}
            </Text>
          </View>
        </View>

        <TouchableOpacity
          onPress={onBack}
          style={[
            styles.closeButton,
            { backgroundColor: getColor("gray.100", isDarkMode) },
          ]}
        >
          <Ionicons
            name="close"
            size={24}
            color={getColor("gray.900", isDarkMode)}
          />
        </TouchableOpacity>
      </View>

      {/* Assignment Section - Only show if assigned */}
      {assignedIncident && (
        <View style={styles.section}>
          <Text
            style={[
              styles.sectionTitle,
              { color: getColor("gray.600", isDarkMode) },
            ]}
          >
            Current Assignment:
          </Text>
          <TouchableOpacity
            style={[
              styles.assignmentCard,
              {
                backgroundColor: isDarkMode
                  ? getColor("gray.100", isDarkMode)
                  : "#F8F9FA",
                borderColor: getColor("gray.200", isDarkMode),
              },
            ]}
            onPress={handleAssignmentPress}
            activeOpacity={0.7}
          >
            <View style={styles.assignmentHeader}>
              <View style={styles.assignmentTitleRow}>
                <Text
                  style={[
                    styles.incidentTitle,
                    { color: getColor("gray.900", isDarkMode) },
                  ]}
                >
                  <Text style={styles.incidentId}>
                    {assignedIncident.id?.replace(/[^0-9]/g, "").slice(0, 3) ||
                      "000"}
                  </Text>{" "}
                  {getSituationType(assignedIncident.type) || "Unknown"}
                </Text>
                <Ionicons
                  name="chevron-forward"
                  size={20}
                  color={getColor("gray.500", isDarkMode)}
                />
              </View>
              <Text
                style={[
                  styles.incidentAddress,
                  { color: getColor("gray.600", isDarkMode) },
                ]}
              >
                {assignedIncident.address || "Location not available"}
              </Text>
            </View>

            <View style={styles.assignmentFooter}>
              <View style={styles.assignmentDetails}>
                <View
                  style={[
                    styles.priorityBadge,
                    { backgroundColor: getColor("amber.100", isDarkMode) },
                  ]}
                >
                  <Text
                    style={[
                      styles.priorityText,
                      { color: getColor("amber.600", isDarkMode) },
                    ]}
                  >
                    P{assignedIncident.priority || "-"}
                  </Text>
                </View>
                <Text
                  style={[
                    styles.dispatchStatus,
                    { color: getColor("gray.600", isDarkMode) },
                  ]}
                >
                  Dispatched
                </Text>
                <Text
                  style={[
                    styles.dispatchTime,
                    { color: getColor("gray.600", isDarkMode) },
                  ]}
                >
                  1m ago
                </Text>
              </View>

              <View style={styles.responderBadge}>
                <ShieldPersonIcon
                  size={12}
                  color={getColor("gray.600", isDarkMode)}
                />
                <Text
                  style={[
                    styles.responderText,
                    { color: getColor("gray.600", isDarkMode) },
                  ]}
                >
                  {getResponderDisplayText()}
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        </View>
      )}

      {/* Current Location Section */}
      <View style={styles.section}>
        <View
          style={[
            styles.locationHeader,
            {
              backgroundColor: isDarkMode
                ? getColor("gray.100", isDarkMode)
                : "#F8F9FA",
              borderColor: getColor("gray.200", isDarkMode),
            },
          ]}
        >
          <Text
            style={[
              styles.locationLabel,
              { color: getColor("gray.600", isDarkMode) },
            ]}
          >
            Current Location
          </Text>
          <Text
            style={[
              styles.locationAddress,
              { color: getColor("gray.900", isDarkMode) },
            ]}
          >
            {isLoadingAddress ? "Loading address..." : currentAddress}
          </Text>
        </View>
      </View>
    </BottomSheetScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    padding: 20,
    paddingBottom: 16,
    paddingTop: 16,
  },
  headerLeft: {
    flex: 1,
  },
  unitTitle: {
    fontSize: 22,
    fontWeight: "400",
    marginBottom: 12,
  },
  unitId: {
    fontWeight: "700",
  },
  unitName: {
    fontWeight: "400",
  },
  statusRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
  statusText: {
    fontSize: 14,
    fontWeight: "500",
  },
  timeSince: {
    fontSize: 14,
  },
  closeButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginTop: -8,
  },
  section: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: "400",
    marginBottom: 12,
    marginTop: 12,
  },
  assignmentCard: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
  },
  assignmentHeader: {
    marginBottom: 16,
  },
  assignmentTitleRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  incidentTitle: {
    fontSize: 18,
    fontWeight: "400",
    flex: 1,
  },
  incidentId: {
    fontWeight: "700",
  },
  incidentAddress: {
    fontSize: 16,
  },
  assignmentFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  assignmentDetails: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  priorityBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 4,
    minWidth: 28,
    alignItems: "center",
  },
  priorityText: {
    fontSize: 12,
    fontWeight: "600",
  },
  dispatchStatus: {
    fontSize: 12,
    fontWeight: "600",
  },
  dispatchTime: {
    fontSize: 12,
  },
  responderBadge: {
    flexDirection: "row",
    alignItems: "center",
    gap: 4,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  responderText: {
    fontSize: 12,
    fontWeight: "600",
  },
  locationHeader: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    // Content on same line but with a gap
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  locationLabel: {
    fontSize: 14,
    lineHeight: 16,
    fontWeight: "400",
  },
  locationAddress: {
    fontSize: 14,
    fontWeight: "500",
  },
});
