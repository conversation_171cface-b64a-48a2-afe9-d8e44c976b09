import { Ionicons } from "@expo/vector-icons";
import {
  BottomSheetScrollView,
  BottomSheetTextInput,
} from "@gorhom/bottom-sheet";
import {
  ExpoSpeechRecognitionModule,
  useSpeechRecognitionEvent,
} from "expo-speech-recognition";
import React, { useEffect, useRef, useState } from "react";
import {
  Animated,
  Dimensions,
  Platform,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { getColor } from "../../constants/colors";

interface Bar {
  value: number;
  anim: Animated.Value;
}

interface NotesTabProps {
  incidentId: string;
  onNotesChange?: (notes: string) => void;
  initialNotes?: string;
  isDarkMode?: boolean;
}

const { width: screenWidth } = Dimensions.get("window");
const NORMAL_BUTTON_WIDTH = 50;
const EXPANDED_BUTTON_WIDTH = screenWidth - 70;
const MAX_WAVE_HEIGHT = 40;
const BAR_WIDTH = 3;
const BAR_MARGIN = 2;
const BAR_EFFECTIVE_WIDTH = BAR_WIDTH + BAR_MARGIN * 2;

const AnimatedWaveformBar = ({ bar, isDarkMode = false }: { bar: Bar, isDarkMode?: boolean }) => {
  const animatedScale = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(animatedScale, {
      toValue: bar.value,
      duration: 300,
      useNativeDriver: true,
    }).start();
  }, [bar.value]);

  return (
    <Animated.View
      style={[
        styles.waveformBar,
        {
          position: "absolute",
          right: 0,
          backgroundColor: getColor('blue.600', isDarkMode),
          transform: [{ translateX: bar.anim }, { scaleY: animatedScale }],
        },
      ]}
    />
  );
};

export const NotesTab: React.FC<NotesTabProps> = ({
  incidentId,
  onNotesChange,
  initialNotes = "",
  isDarkMode = false,
}) => {
  const [notes, setNotes] = useState<string>(initialNotes);
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [bars, setBars] = useState<Bar[]>([]);
  const [currentVolume, setCurrentVolume] = useState<number>(0);

  // Animated values for recording UI
  const animatedWidth = useRef(new Animated.Value(NORMAL_BUTTON_WIDTH)).current;
  const micOpacity = useRef(new Animated.Value(1)).current;
  const recordingOpacity = useRef(new Animated.Value(0)).current;

  const currentVolumeRef = useRef(currentVolume);
  const lastSavedNotesRef = useRef(initialNotes);

  // Add refs to track speech recognition state
  const originalTextRef = useRef<string>("");
  const currentTranscriptRef = useRef<string>("");

  useEffect(() => {
    currentVolumeRef.current = currentVolume;
  }, [currentVolume]);

  // Update lastSavedNotesRef when initialNotes changes (when notes are loaded from server)
  useEffect(() => {
    lastSavedNotesRef.current = initialNotes;
    setNotes(initialNotes);
  }, [initialNotes]);

  // Debounced notes update - only save if notes have actually changed
  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      if (notes !== lastSavedNotesRef.current) {
        lastSavedNotesRef.current = notes;
        onNotesChange?.(notes);
      }
    }, 500);

    return () => clearTimeout(debounceTimer);
  }, [notes, onNotesChange]);

  // Speech recognition event listeners
  useSpeechRecognitionEvent("start", () => {
    setIsRecording(true);
    // Store the current text when recording starts
    originalTextRef.current = notes;
    currentTranscriptRef.current = "";
  });

  useSpeechRecognitionEvent("result", (ev) => {
    if (ev.results && ev.results.length > 0) {
      const transcript = ev.results[ev.results.length - 1].transcript || "";
      const isFinal = ev.isFinal || false;

      if (isFinal) {
        // For final results, append to original text permanently
        const newText =
          originalTextRef.current +
          (originalTextRef.current ? " " : "") +
          transcript;
        setNotes(newText);
        originalTextRef.current = newText; // Update original text for next speech segment
        currentTranscriptRef.current = "";
      } else {
        // For interim results, show original text + current interim result
        currentTranscriptRef.current = transcript;
        const previewText =
          originalTextRef.current +
          (originalTextRef.current ? " " : "") +
          transcript;
        setNotes(previewText);
      }
    }
  });

  useSpeechRecognitionEvent("end", () => {
    setIsRecording(false);
    // If we have any remaining transcript, append it
    if (currentTranscriptRef.current) {
      const finalText =
        originalTextRef.current +
        (originalTextRef.current ? " " : "") +
        currentTranscriptRef.current;
      setNotes(finalText);
    }
    // Reset refs
    originalTextRef.current = "";
    currentTranscriptRef.current = "";
  });

  useSpeechRecognitionEvent("error", (event) => {
    console.log("Speech recognition error:", event.error, event.message);
    setIsRecording(false);
    // Reset to original text on error
    setNotes(originalTextRef.current);
    originalTextRef.current = "";
    currentTranscriptRef.current = "";
  });

  useSpeechRecognitionEvent("volumechange", (ev: any) => {
    const normalized = Math.max(0, Math.min(1, (ev.value + 2) / 12));
    setCurrentVolume(normalized);
  });

  // Waveform animation
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isRecording) {
      interval = setInterval(() => {
        setBars((prevBars) => {
          const newLength = prevBars.length + 1;
          prevBars.forEach((bar, index) => {
            const target = -BAR_EFFECTIVE_WIDTH * (newLength - 1 - index);
            Animated.timing(bar.anim, {
              toValue: target,
              duration: 250,
              useNativeDriver: true,
            }).start();
          });
          const newBar: Bar = {
            value: currentVolumeRef.current,
            anim: new Animated.Value(0),
          };
          return [...prevBars, newBar];
        });
      }, 250);
    } else {
      setBars([]);
    }
    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isRecording]);

  const startRecording = async () => {
    try {
      const micStatus =
        await ExpoSpeechRecognitionModule.requestMicrophonePermissionsAsync();
      if (!micStatus.granted) {
        alert("Microphone permission is required.");
        return;
      }
      if (Platform.OS === "ios") {
        const speechStatus =
          await ExpoSpeechRecognitionModule.requestSpeechRecognizerPermissionsAsync();
        if (!speechStatus.granted) {
          alert("Speech recognizer permission is required.");
          return;
        }
      }
      ExpoSpeechRecognitionModule.start({
        lang: "en-US",
        continuous: true,
        interimResults: true,
        addsPunctuation: true,
        volumeChangeEventOptions: { enabled: true, intervalMillis: 300 },
      });
    } catch (error) {
      console.error("Error starting speech recognition:", error);
    }
  };

  const stopRecording = async () => {
    try {
      ExpoSpeechRecognitionModule.stop();
    } catch (error) {
      console.error("Error stopping speech recognition:", error);
    }
  };

  const toggleRecording = () => {
    if (isRecording) {
      stopRecording();
      setIsRecording(false);
      Animated.parallel([
        Animated.timing(micOpacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(recordingOpacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(animatedWidth, {
          toValue: NORMAL_BUTTON_WIDTH,
          duration: 300,
          useNativeDriver: false,
        }),
      ]).start();
    } else {
      setIsRecording(true);
      Animated.parallel([
        Animated.timing(micOpacity, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(recordingOpacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(animatedWidth, {
          toValue: EXPANDED_BUTTON_WIDTH,
          duration: 300,
          useNativeDriver: false,
        }),
      ]).start();
      startRecording();
    }
  };

  return (
    <BottomSheetScrollView
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      nestedScrollEnabled={true}
      showsVerticalScrollIndicator={false}
    >
      <View style={styles.section}>
        <Text style={[styles.sectionTitle, { color: getColor('gray.600', isDarkMode) }]}>Incident Notes</Text>
        <View style={styles.inputContainer}>
          <BottomSheetTextInput
            style={[styles.textInput, {
              backgroundColor: isDarkMode ? getColor('gray.100', isDarkMode) : '#FFFFFF',
              borderColor: getColor('gray.200', isDarkMode),
              color: getColor('gray.900', isDarkMode)
            }]}
            placeholder="Tap to speak or type..."
            placeholderTextColor={getColor('gray.500', isDarkMode)}
            multiline
            value={notes}
            onChangeText={setNotes}
            textAlignVertical="top"
          />

          {/* Recording UI */}
          <Animated.View
            style={[styles.recordingContainer, { 
              width: animatedWidth,
              backgroundColor: isDarkMode ? getColor('gray.100', isDarkMode) : '#FFF'
            }]}
          >
            {/* Microphone button */}
            <Animated.View
              style={[
                StyleSheet.absoluteFill,
                {
                  flexDirection: "row",
                  justifyContent: "flex-end",
                  alignItems: "center",
                  paddingRight: 11,
                  opacity: micOpacity,
                },
              ]}
            >
              <TouchableOpacity onPress={toggleRecording} activeOpacity={0.8}>
                <Ionicons name="mic" size={28} color={getColor('blue.600', isDarkMode)} />
              </TouchableOpacity>
            </Animated.View>

            {/* Recording waveform */}
            <Animated.View
              style={[
                StyleSheet.absoluteFill,
                {
                  flexDirection: "row",
                  alignItems: "center",
                  justifyContent: "space-between",
                  paddingLeft: 8,
                  paddingRight: 2,
                  opacity: recordingOpacity,
                },
              ]}
            >
              <View style={styles.waveformContainer}>
                {bars.map((bar, index) => (
                  <AnimatedWaveformBar key={index} bar={bar} isDarkMode={isDarkMode} />
                ))}
              </View>
              <TouchableOpacity
                onPress={toggleRecording}
                style={styles.stopButton}
              >
                <Ionicons
                  name="stop-circle-outline"
                  size={30}
                  color={getColor('blue.600', isDarkMode)}
                />
              </TouchableOpacity>
            </Animated.View>
          </Animated.View>
        </View>
      </View>
    </BottomSheetScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    paddingVertical: 20,
  },
  section: {
    paddingHorizontal: 20,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: "500",
    marginBottom: 12,
  },
  inputContainer: {
    position: "relative",
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    minHeight: 300,
    paddingBottom: 80, // Space for recording button
  },
  recordingContainer: {
    position: "absolute",
    bottom: 10,
    right: 10,
    height: NORMAL_BUTTON_WIDTH,
    borderRadius: NORMAL_BUTTON_WIDTH / 2,
    paddingHorizontal: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  waveformContainer: {
    flex: 1,
    height: MAX_WAVE_HEIGHT,
    position: "relative",
    overflow: "hidden",
  },
  waveformBar: {
    width: BAR_WIDTH,
    height: MAX_WAVE_HEIGHT,
    marginHorizontal: BAR_MARGIN,
    borderRadius: 5,
  },
  stopButton: {
    width: NORMAL_BUTTON_WIDTH,
    height: NORMAL_BUTTON_WIDTH,
    alignItems: "center",
    justifyContent: "center",
  },
});
