import axios from 'axios';
import { DISCOVERY_CONFIG } from '../config/discovery';

/**
 * Discovery Service
 * 
 * Handles communication with the discovery lambda to find the correct
 * Cognito environment configuration based on user email domain.
 */

export interface DiscoveryRequest {
  email: string;
}

export interface DiscoveryResponse {
  userPoolUrl: string;
  clientId: string;
}

export interface EnvironmentConfig {
  userPoolUrl: string;
  clientId: string;
}

/**
 * Error thrown when discovery service fails
 */
export class DiscoveryError extends Error {
  constructor(message: string, public statusCode?: number) {
    super(message);
    this.name = 'DiscoveryError';
  }
}


/**
 * Discover environment configuration for a given email address
 * 
 * @param email - User's email address
 * @param abortSignal - Optional abort signal for cancellation
 * @returns Promise resolving to environment configuration
 * @throws DiscoveryError if discovery fails
 */
export async function discoverEnvironment(
  email: string, 
  abortSignal?: AbortSignal
): Promise<EnvironmentConfig> {
  try {
    const response = await axios.post<DiscoveryResponse>(
      DISCOVERY_CONFIG.FUNCTION_URL,
      { email: email.trim() },
      {
        headers: {
          'Content-Type': 'application/json',
          'X-Api-Key': DISCOVERY_CONFIG.API_KEY,
        },
        timeout: 10000, // 10 second timeout
        signal: abortSignal, // Support cancellation
      }
    );

    const { userPoolUrl, clientId } = response.data;
    
    return {
      userPoolUrl,
      clientId,
    };

  } catch (error) {
    if (axios.isAxiosError(error)) {
      const statusCode = error.response?.status;
      const errorMessage = error.response?.data?.error || error.message;
      
      // Handle specific error cases
      if (statusCode === 404) {
        throw new DiscoveryError(
          'No organization found for this email domain. Please check your email address.',
          statusCode
        );
      }
      
      if (statusCode === 403) {
        throw new DiscoveryError(
          'Authentication failed. Please try again.',
          statusCode
        );
      }
      
      if (error.code === 'ECONNABORTED') {
        throw new DiscoveryError('Request timed out. Please check your connection and try again.');
      }
      
      if (error.code === 'ERR_CANCELED') {
        throw new DiscoveryError('Request was cancelled.');
      }
      
      throw new DiscoveryError(
        `Discovery failed: ${errorMessage}`,
        statusCode
      );
    }
    
    // Non-axios errors (network, etc.)
    throw new DiscoveryError(
      'Unable to connect to discovery service. Please check your internet connection and try again.'
    );
  }
}

/**
 * Validate email format (supports subdomain TLDs like .co.uk)
 * @param email - Email address to validate
 * @returns True if email format is valid
 */
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[a-zA-Z]{2,}(\.[a-zA-Z]{2,})?$/;
  return emailRegex.test(email);
}