import { useLocalSearchParams } from "expo-router";
import React from "react";
import { useEnvironmentConfig } from "./contexts/EnvironmentConfigContext";
import SignInScreen from "./screens/SignInScreen";
import { EnvironmentConfig } from "./services/discoveryService";

export default function SignInRoute() {
  const { setEnvironmentConfig } = useEnvironmentConfig();
  const { email } = useLocalSearchParams();
  
  const handleDiscoveryComplete = (config: EnvironmentConfig) => {
    // Update the environment config in the parent layout
    setEnvironmentConfig(config);
    
    // No navigation needed - SignInScreen handles the complete flow
    // including auto-triggering authentication
  };

  return (
    <SignInScreen 
      onDiscoveryComplete={handleDiscoveryComplete}
      initialEmail={typeof email === 'string' ? email : ''}
    />
  );
}