import axios from "axios";
import { COMMUNICATIONS_SERVICE_URL } from '../../config';
import { registerAxiosAuth } from '../../axiosAuth';

const axiosInstance = axios.create({
  baseURL: COMMUNICATIONS_SERVICE_URL,
  timeout: 10000, // Optional timeout
  headers: {
    "Content-Type": "application/json",
  },
});

export const setAccessGetter = registerAxiosAuth(axiosInstance, 'Communications');

axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle errors globally
    return Promise.reject(error);
  }
);

export default axiosInstance;
