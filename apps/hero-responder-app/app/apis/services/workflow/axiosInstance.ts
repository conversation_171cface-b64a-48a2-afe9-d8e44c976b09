import axios from "axios";
import { registerAxiosAuth } from '../../axiosAuth';
import { WORKFLOW_SERVICE_URL } from '../../config';

const axiosInstance = axios.create({
  baseURL: WORKFLOW_SERVICE_URL,
  timeout: 10000, // Optional timeout
  headers: {
    "Content-Type": "application/json",
  },
});

export const setAccessGetter = registerAxiosAuth(axiosInstance, 'Workflow');

axiosInstance.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle errors globally
    const url = error.config?.url ? `${error.config.baseURL || ''}${error.config.url}` : 'Unknown URL';
    console.log(`Error calling ${url}: ${error.message || error}`);
    return Promise.reject(error);
  }
);

export default axiosInstance;
