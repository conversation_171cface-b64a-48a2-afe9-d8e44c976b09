import { AxiosInstance } from 'axios';

/**
 * Registers dynamic authentication for an axios instance.
 * Allows setting a getter function that provides the current access token.
 * 
 * @param axiosInstance - The axios instance to add auth to
 * @param name - Name for debugging (optional)
 * @returns setAccessGetter function to register the token provider
 */
export const registerAxiosAuth = (axiosInstance: AxiosInstance, name?: string) => {
  let accessGetter = (): string | undefined => undefined;
  
  const setAccessGetter = (fn: () => string | undefined) => { 
    accessGetter = fn; 
  };
  
  axiosInstance.interceptors.request.use((config) => {
    const token = accessGetter();
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  }, (error) => Promise.reject(error));
  
  return setAccessGetter;
};