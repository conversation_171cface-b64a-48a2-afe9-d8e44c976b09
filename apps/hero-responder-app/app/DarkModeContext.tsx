import * as SecureStore from "expo-secure-store";
import React, {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useState,
} from "react";

interface DarkModeContextType {
  isDarkMode: boolean;
  toggleDarkMode: () => void;
  isLoading: boolean;
}

const DarkModeContext = createContext<DarkModeContextType | undefined>(
  undefined
);

const DARK_MODE_STORAGE_KEY = "hero_dark_mode";

interface DarkModeProviderProps {
  children: ReactNode;
}

export function DarkModeProvider({ children }: DarkModeProviderProps) {
  const [isDarkMode, setIsDarkMode] = useState(true);
  const [isLoading, setIsLoading] = useState(true);

  // Load dark mode preference from AsyncStorage on app start
  useEffect(() => {
    loadDarkModePreference();
  }, []);

  const loadDarkModePreference = async () => {
    try {
      const storedPreference = await SecureStore.getItemAsync(
        DARK_MODE_STORAGE_KEY
      );
      
      if (storedPreference !== null && storedPreference !== undefined) {
        const parsedValue = JSON.parse(storedPreference);
        setIsDarkMode(parsedValue);
      } else {
        setIsDarkMode(true);
      }
    } catch (error) {
      console.error("[DarkMode] Error loading dark mode preference:", error);
      // Fallback to true on error
      setIsDarkMode(true);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleDarkMode = async () => {
    try {
      const newDarkModeState = !isDarkMode;
      setIsDarkMode(newDarkModeState);
      
      const valueToStore = JSON.stringify(newDarkModeState);
      
      await SecureStore.setItemAsync(DARK_MODE_STORAGE_KEY, valueToStore);
      
      // Verify it was stored correctly
      const verification = await SecureStore.getItemAsync(DARK_MODE_STORAGE_KEY);
      
    } catch (error) {
      console.error("[DarkMode] Error saving dark mode preference:", error);
      // Revert the state if saving failed
      setIsDarkMode(!isDarkMode);
    }
  };

  const value: DarkModeContextType = {
    isDarkMode,
    toggleDarkMode,
    isLoading,
  };

  return (
    <DarkModeContext.Provider value={value}>
      {children}
    </DarkModeContext.Provider>
  );
}

export function useDarkMode(): DarkModeContextType {
  const context = useContext(DarkModeContext);
  if (context === undefined) {
    throw new Error("useDarkMode must be used within a DarkModeProvider");
  }
  return context;
}
