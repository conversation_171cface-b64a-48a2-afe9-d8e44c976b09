import { CSSProperties, forwardRef, HTMLAttributes, ReactNode, useState } from "react";
import { colors, radius, spacing } from "../tokens";
import { ColorToken } from "../tokens/colors";
import { Typography } from "./Typography";

export type LabelSize = "small" | "medium" | "large";

export interface LabelProps
  extends Omit<HTMLAttributes<HTMLDivElement>, "color" | "style"> {
  label: string;
  secondLabel?: string;
  size?: LabelSize;
  color?: ColorToken;
  prominence?: boolean;
  pilled?: boolean;
  twoToned?: boolean;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  className?: string;
}

const labelSizeStyles: Record<
  LabelSize,
  { padding: string; iconSize: string; lineHeight: string; borderRadius: { default: string; pilled: string } }
> = {
  small: {
    padding: "4px 8px",
    iconSize: "17px",
    lineHeight: "17px",
    borderRadius: {
      default: radius.s,
      pilled: "16px",
    },
  },
  medium: {
    padding: "4px 8px",
    iconSize: "17px",
    lineHeight: "17px",
    borderRadius: {
      default: radius.s,
      pilled: "16px",
    },
  },
  large: {
    padding: "4px 12px",
    iconSize: "22px",
    lineHeight: "22px",
    borderRadius: {
      default: radius.m,
      pilled: "24px",
    },
  },
};

// Helper to safely access color values
const getColorValue = (colorToken: ColorToken, shade: 100 | 600): string => {
  const colorObj = colors[colorToken];

  if (shade in colorObj) {
    return colorObj[shade as keyof typeof colorObj];
  }

  if (shade === 100) {
    return colorObj[100];
  } else {
    if ("600" in colorObj) {
      return colorObj[600 as keyof typeof colorObj];
    }
    if ("700" in colorObj) {
      return colorObj[700 as keyof typeof colorObj];
    }
    return colors.grey[800];
  }
};

export const Label = forwardRef<HTMLDivElement, LabelProps>(({
  label,
  secondLabel,
  size = "medium",
  color = "blue",
  prominence = false,
  pilled = false,
  twoToned = false,
  leftIcon,
  rightIcon,
  className = "",
  ...props
}, ref) => {
  const [isHovered, setIsHovered] = useState(false);
  const { padding, iconSize, lineHeight, borderRadius } = labelSizeStyles[size];

  // Get colors based on prominence
  const getStylesForProminence = (isProminent: boolean) => {
    const backgroundColor = isProminent
      ? getColorValue(color, 600)
      : getColorValue(color, 100);
    const textColor = isProminent
      ? colors.grey[50]
      : getColorValue(color, 600);

    return { backgroundColor, textColor };
  };

  const primaryStyles = getStylesForProminence(prominence);
  const secondaryStyles = getStylesForProminence(!prominence);

  // Build label style
  const labelBaseStyles: CSSProperties = {
    display: "flex",
    alignItems: "center",
    justifyContent: twoToned ? "flex-start" : "center",
    gap: twoToned ? 0 : spacing.xs,
    padding: twoToned ? "0" : padding,
    backgroundColor: primaryStyles.backgroundColor,
    color: primaryStyles.textColor,
    borderRadius: pilled ? borderRadius.pilled : borderRadius.default,
    boxSizing: "border-box",
    width: "fit-content",
    overflow: "hidden",
  };

  // If two-toned, we need to create the left and right parts separately
  if (twoToned && secondLabel) {
    return (
      <div
        ref={ref}
        className={className}
        style={labelBaseStyles}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        {...props}
      >
        <div
          style={{
            display: "flex",
            alignItems: "center",
            padding,
            backgroundColor: primaryStyles.backgroundColor,
            color: primaryStyles.textColor,
            gap: spacing.xs,
          }}
        >
          {leftIcon && (
            <span
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                width: iconSize,
                height: iconSize,
              }}
            >
              {leftIcon}
            </span>
          )}
          <Typography
            style={size === "large" ? "body3" : "tag2"}
            color={primaryStyles.textColor}
            lineHeight={lineHeight}
          >
            {label}
          </Typography>
        </div>
        <div
          style={{
            display: "flex",
            alignItems: "center",
            padding,
            backgroundColor: secondaryStyles.backgroundColor,
            color: secondaryStyles.textColor,
            gap: spacing.xs,
          }}
        >
          <Typography
            style={size === "large" ? "body3" : "tag2"}
            color={secondaryStyles.textColor}
            lineHeight={lineHeight}
          >
            {secondLabel}
          </Typography>
          {rightIcon && (
            <span
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                width: iconSize,
                height: iconSize,
              }}
            >
              {rightIcon}
            </span>
          )}
        </div>
      </div>
    );
  }

  // Single-tone label
  return (
    <div
      ref={ref}
      className={className}
      style={labelBaseStyles}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      {...props}
    >
      {leftIcon && (
        <span
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            width: iconSize,
            height: iconSize,
          }}
        >
          {leftIcon}
        </span>
      )}
      <Typography
        style={size === "small" ? "tag2" : "body2"}
        color={primaryStyles.textColor}
        lineHeight={lineHeight}
      >
        {label}
      </Typography>
      {rightIcon && (
        <span
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            width: iconSize,
            height: iconSize,
          }}
        >
          {rightIcon}
        </span>
      )}
    </div>
  );
});

Label.displayName = "Label"; 