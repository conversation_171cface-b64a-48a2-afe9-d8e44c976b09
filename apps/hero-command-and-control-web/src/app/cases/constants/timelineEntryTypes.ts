export const TIMELINE_ENTRY_TYPE_OPTIONS = [
  { label: 'Witness Interview Conducted', value: 'Witness Interview Conducted' },
  { label: 'Victim Interview Conducted', value: 'Victim Interview Conducted' },
  { label: 'Suspect Interview Conducted', value: 'Suspect Interview Conducted' },
  { label: 'Reporting Party Contacted (Phone/In-Person)', value: 'Reporting Party Contacted (Phone/In-Person)' },
  { label: 'Field Canvass Completed', value: 'Field Canvass Completed' },
  { label: 'Evidence Collected in Field', value: 'Evidence Collected in Field' },
  { label: 'Evidence Delivered to Lab', value: 'Evidence Delivered to Lab' },
  { label: 'Lab Results Received', value: 'Lab Results Received' },
  { label: 'Warrant Drafted / Submitted', value: 'Warrant Drafted / Submitted' },
  { label: 'Surveillance Footage Reviewed', value: 'Surveillance Footage Reviewed' },
  { label: 'Meeting with Prosecutor / DA', value: 'Meeting with Prosecutor / DA' },
  { label: 'Victim Services Coordinated', value: 'Victim Services Coordinated' },
  { label: 'Multi-Agency Briefing / Coordination', value: 'Multi-Agency Briefing / Coordination' },
  { label: 'Tip / Intel Received', value: 'Tip / Intel Received' },
  { label: 'Other', value: 'Other' },
];