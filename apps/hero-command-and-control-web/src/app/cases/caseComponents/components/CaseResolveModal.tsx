"use client";
import CloseIcon from "@mui/icons-material/Close";
import {
    <PERSON>,
    <PERSON>ton,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    IconButton,
    Typography
} from "@mui/material";
import { Case, CaseStatus } from "proto/hero/cases/v1/cases_pb";
import React, { useEffect, useState } from "react";
import { Dropdown, DropdownOption } from "../../../../design-system/components/Dropdown";
import { TextInput } from "../../../../design-system/components/TextInput";

type CaseResolveModalProps = {
    open: boolean;
    onClose: () => void;
    onResolve: (status: CaseStatus, explanation: string) => void;
    caseData?: Case;
    isLoading?: boolean;
};

const CaseResolveModal: React.FC<CaseResolveModalProps> = ({
    open,
    onClose,
    onResolve,
    caseData,
    isLoading = false,
}) => {
    const [status, setStatus] = useState<string | null>(null);
    const [explanation, setExplanation] = useState("");

    // Reset state when modal opens/closes
    useEffect(() => {
        if (!open) {
            setStatus(null);
            setExplanation("");
        }
    }, [open]);

    const handleResolve = () => {
        if (status) {
            // Convert string back to CaseStatus enum
            const statusEnum = parseInt(status) as CaseStatus;
            onResolve(statusEnum, explanation);
        }
    };

    const handleClose = () => {
        onClose();
    };

    const statusOptions: DropdownOption[] = [
        { label: "Resolved", value: CaseStatus.RESOLVED.toString() },
        { label: "Closed", value: CaseStatus.CLOSED.toString() },
        { label: "Archived", value: CaseStatus.ARCHIVED.toString() },
        { label: "On Hold", value: CaseStatus.ON_HOLD.toString() },
    ];

    // Get case ID for display
    const caseId = caseData?.id
        ? String(caseData.id).replace(/[^0-9]/g, "").slice(0, 8)
        : "Unknown";

    return (
        <Dialog
            open={open}
            onClose={handleClose}
            fullWidth
            maxWidth="xs"
            PaperProps={{
                style: {
                    backgroundColor: "white",
                    padding: "24px",
                    borderRadius: "12px",
                },
            }}
            BackdropProps={{
                style: {
                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                },
            }}
        >
            <DialogTitle
                sx={{
                    textAlign: "left",
                    position: "relative",
                    padding: "0 0 24px 0",
                    fontSize: "16px",
                    fontWeight: 500,
                }}
            >
                {`Resolve Case`}
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    {`Case ${caseId}`}
                </Typography>
                <IconButton
                    onClick={handleClose}
                    sx={{
                        position: "absolute",
                        right: -12,
                        top: -12,
                        color: "rgba(0, 0, 0, 0.54)",
                    }}
                >
                    <CloseIcon />
                </IconButton>
            </DialogTitle>

            <DialogContent sx={{ p: 0 }}>
                <Box sx={{ mb: 3 }}>
                    <Dropdown
                        title="Resolution Status"
                        placeholder="Select Status"
                        options={statusOptions}
                        value={status}
                        onChange={(val: string | null) => setStatus(val)}
                    />
                </Box>

                <Box sx={{ mb: 3 }}>
                    <TextInput
                        title="Explanation"
                        placeholder="Enter explanation for resolution..."
                        value={explanation}
                        onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => setExplanation(e.target.value)}
                        type="text"
                        numOfLines={3}
                    />
                </Box>
            </DialogContent>

            <DialogActions sx={{ p: 0, mt: 2, gap: 1 }}>
                <Button
                    onClick={handleClose}
                    disabled={isLoading}
                    sx={{
                        color: 'text.secondary',
                        textTransform: 'none',
                    }}
                >
                    Cancel
                </Button>
                <Button
                    variant="contained"
                    onClick={handleResolve}
                    disabled={!status || isLoading}
                    sx={{
                        bgcolor: '#0066FF',
                        textTransform: 'none',
                        '&:hover': {
                            bgcolor: '#0052CC',
                        },
                        '&.Mui-disabled': {
                            bgcolor: 'rgba(0, 102, 255, 0.12)',
                            color: 'rgba(0, 102, 255, 0.38)',
                        },
                    }}
                >
                    {isLoading ? "Resolving..." : "Resolve Case"}
                </Button>
            </DialogActions>
        </Dialog>
    );
};

export default CaseResolveModal;
