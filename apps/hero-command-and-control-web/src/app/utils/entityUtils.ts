import { RecordStatus } from "proto/hero/entity/v1/entity_pb";

export const isArchivedStatus = (status: RecordStatus | string): boolean => {
    return status === RecordStatus.ARCHIVED || status === "RECORD_STATUS_ARCHIVED";
};

export const isActiveStatus = (status: RecordStatus | string): boolean => {
    return status === RecordStatus.ACTIVE || status === "RECORD_STATUS_ACTIVE";
};

export const isDraftStatus = (status: RecordStatus | string): boolean => {
    return status === RecordStatus.DRAFT || status === "RECORD_STATUS_DRAFT";
};

export const getStatusDisplay = (status: RecordStatus | string): string => {
    switch (status) {
        case RecordStatus.ACTIVE:
        case "RECORD_STATUS_ACTIVE":
            return "Active";
        case RecordStatus.DRAFT:
        case "RECORD_STATUS_DRAFT":
            return "Draft";
        case RecordStatus.ARCHIVED:
        case "RECORD_STATUS_ARCHIVED":
            return "Archived";
        case RecordStatus.DEPRECATED:
        case "RECORD_STATUS_DEPRECATED":
            return "Deprecated";
        default:
            return "Unknown";
    }
}; 