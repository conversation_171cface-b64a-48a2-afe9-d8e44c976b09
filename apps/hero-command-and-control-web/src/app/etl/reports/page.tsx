'use client';

import React, { useState, useRef, useEffect, useMemo, useCallback } from 'react';
import { NIBRS_MAPPING_CONFIG, NIBRS_TEMPLATE } from './constants';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Link,
  Typography,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Alert,
  Tabs,
  Tab,
  CircularProgress,
  Checkbox,
  FormControlLabel,
  Container,
  Grid2,
  Paper,
  List,
  ListItem,
  ListItemText,
  Chip,
  IconButton,
  Tooltip,
  Collapse
} from '@mui/material';
import {
  Description as FileJsonIcon,
  Code as FileCodeIcon,
  TextSnippet as FileTextIcon,
  Search as SearchIcon,
  Close as CloseIcon,
  KeyboardArrowUp,
  KeyboardArrowDown,
  ExpandMore,
  ChevronRight,
  ContentCopy
} from '@mui/icons-material';
import { 
  useExtractReportData, 
  useTestReportMapping, 
  useTestReportTransformation
} from '@/app/apis/services/workflow/etl';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = React.memo(function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  if (value !== index) {
    return null;
  }

  return (
    <div
      role="tabpanel"
      id={`etl-tabpanel-${index}`}
      aria-labelledby={`etl-tab-${index}`}
      {...other}
    >
      <Box sx={{ pt: 3 }}>{children}</Box>
    </div>
  );
});

interface SearchableOutputProps {
  content: string;
  label: string;
  rows?: number;
  isJson?: boolean;
}

// Collapsible JSON viewer component
interface JsonNodeProps {
  data: any;
  name: string;
  level?: number;
  isLast?: boolean;
}

const JsonNode = React.memo(function JsonNode({ data, name, level = 0, isLast = true }: JsonNodeProps) {
  const [isExpanded, setIsExpanded] = useState(level < 2); // Auto-expand first 2 levels
  
  const isObject = data !== null && typeof data === 'object' && !Array.isArray(data);
  const isArray = Array.isArray(data);
  const isExpandable = isObject || isArray;
  
  const indent = level * 20;
  
  const getValuePreview = () => {
    if (isArray) return `[${data.length}]`;
    if (isObject) return `{${Object.keys(data).length}}`;
    return '';
  };
  
  const renderValue = () => {
    if (data === null) return <span style={{ color: '#999' }}>null</span>;
    if (typeof data === 'boolean') return <span style={{ color: '#0066cc' }}>{String(data)}</span>;
    if (typeof data === 'number') return <span style={{ color: '#098658' }}>{data}</span>;
    if (typeof data === 'string') return <span style={{ color: '#d14' }}>&quot;{data}&quot;</span>;
    return null;
  };
  
  return (
    <Box>
      <Box 
        sx={{ 
          display: 'flex', 
          alignItems: 'center',
          ml: `${indent}px`,
          py: 0.25,
          '&:hover': { bgcolor: 'rgba(0,0,0,0.02)' }
        }}
      >
        {isExpandable && (
          <IconButton 
            size="small" 
            onClick={() => setIsExpanded(!isExpanded)}
            sx={{ p: 0.25, mr: 0.5 }}
          >
            {isExpanded ? <ExpandMore fontSize="small" /> : <ChevronRight fontSize="small" />}
          </IconButton>
        )}
        {!isExpandable && <Box sx={{ width: 20 }} />}
        
        <Typography 
          component="span" 
          sx={{ 
            fontFamily: 'monospace',
            fontSize: '0.875rem',
            color: '#881391',
            mr: 0.5
          }}
        >
          {name}:
        </Typography>
        
        {!isExpandable && renderValue()}
        {isExpandable && !isExpanded && (
          <Typography 
            component="span" 
            sx={{ 
              fontFamily: 'monospace',
              fontSize: '0.875rem',
              color: '#666',
              fontStyle: 'italic'
            }}
          >
            {getValuePreview()}
          </Typography>
        )}
        {!isLast && !isExpanded && <span style={{ color: '#666' }}>,</span>}
      </Box>
      
      <Collapse in={isExpanded}>
        {isObject && Object.entries(data).map(([key, value], index) => (
          <JsonNode 
            key={key} 
            data={value} 
            name={key} 
            level={level + 1}
            isLast={index === Object.entries(data).length - 1}
          />
        ))}
        {isArray && data.map((item: any, index: number) => (
          <JsonNode 
            key={index} 
            data={item} 
            name={`[${index}]`} 
            level={level + 1}
            isLast={index === data.length - 1}
          />
        ))}
        {isExpandable && (
          <Typography 
            sx={{ 
              ml: `${indent}px`,
              fontFamily: 'monospace',
              fontSize: '0.875rem',
              color: '#666'
            }}
          >
            {isArray ? ']' : '}'}
            {!isLast && ','}
          </Typography>
        )}
      </Collapse>
    </Box>
  );
});

interface CollapsibleJsonProps {
  content: string;
}

const CollapsibleJson = React.memo(function CollapsibleJson({ content }: CollapsibleJsonProps) {
  const [showRaw, setShowRaw] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);
  
  const jsonData = useMemo(() => {
    try {
      return JSON.parse(content);
    } catch {
      return null;
    }
  }, [content]);
  
  const handleCopy = () => {
    navigator.clipboard.writeText(content);
    setCopySuccess(true);
    setTimeout(() => setCopySuccess(false), 2000);
  };
  
  if (!jsonData) {
    return <Typography sx={{ fontFamily: 'monospace', fontSize: '0.875rem', color: '#666' }}>
      Invalid JSON format
    </Typography>;
  }
  
  return (
    <Box sx={{ position: 'relative' }}>
      <Box sx={{ position: 'absolute', right: 8, top: 8, display: 'flex', gap: 1 }}>
        <Button 
          size="small" 
          variant="outlined"
          onClick={() => setShowRaw(!showRaw)}
          sx={{ 
            borderColor: '#e0e0e0',
            color: '#666',
            textTransform: 'none',
            fontSize: '0.75rem'
          }}
        >
          {showRaw ? 'Tree View' : 'Raw JSON'}
        </Button>
        <Tooltip title={copySuccess ? 'Copied!' : 'Copy to clipboard'}>
          <IconButton 
            size="small" 
            onClick={handleCopy}
            sx={{ 
              border: '1px solid #e0e0e0',
              color: copySuccess ? '#4caf50' : '#666'
            }}
          >
            <ContentCopy fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>
      
      {showRaw ? (
        <Box 
          sx={{ 
            fontFamily: 'monospace', 
            fontSize: '0.875rem',
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-word',
            color: '#000',
            pt: 5
          }}
        >
          {content}
        </Box>
      ) : (
        <Box sx={{ pt: 5 }}>
          <JsonNode 
            data={jsonData} 
            name={Array.isArray(jsonData) ? 'root' : 'data'} 
          />
        </Box>
      )}
    </Box>
  );
});

// Create a component for the highlighted content
const HighlightedContent = ({ text, searchTerm, currentMatchIndex }: { text: string; searchTerm: string; currentMatchIndex: number }) => {
  if (!searchTerm) return <>{text}</>;
  
  const parts: React.ReactNode[] = [];
  const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
  let lastIndex = 0;
  let matchIndex = 0;
  let match;
  
  while ((match = regex.exec(text)) !== null) {
    // Add text before match
    if (match.index > lastIndex) {
      parts.push(text.substring(lastIndex, match.index));
    }
    
    // Add highlighted match
    const isCurrentMatch = matchIndex === currentMatchIndex;
    parts.push(
      <span
        key={`match-${matchIndex}`}
        data-match-index={matchIndex}
        style={{
          backgroundColor: isCurrentMatch ? '#ffeb3b' : '#fff9c4',
          color: '#000',
          fontWeight: isCurrentMatch ? 'bold' : 'normal',
          padding: '0 2px',
          borderRadius: '2px',
          display: 'inline-block',
          boxShadow: isCurrentMatch ? '0 0 3px rgba(0,0,0,0.3)' : 'none'
        }}
      >
        {match[1]}
      </span>
    );
    
    matchIndex++;
    lastIndex = match.index + match[0].length;
  }
  
  // Add remaining text
  if (lastIndex < text.length) {
    parts.push(text.substring(lastIndex));
  }
  
  return <>{parts}</>;
};

const SearchableOutput = React.memo(function SearchableOutput({ content, label, rows = 20, isJson = false }: SearchableOutputProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const [currentMatch, setCurrentMatch] = useState(0);
  const [matchPositions, setMatchPositions] = useState<number[]>([]);
  const containerRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');

  // Debounce search term
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  useEffect(() => {
    if (!debouncedSearchTerm || !showSearch) {
      setMatchPositions([]);
      setCurrentMatch(0);
      return;
    }

    // Find all match positions
    const regex = new RegExp(debouncedSearchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
    const positions: number[] = [];
    let match;
    
    while ((match = regex.exec(content)) !== null) {
      positions.push(match.index);
    }
    
    setMatchPositions(positions);
    setCurrentMatch(0); // Reset to first match when search changes
  }, [debouncedSearchTerm, content, showSearch]);

  const scrollToMatch = useCallback(() => {
    if (!containerRef.current) return;
    
    const container = containerRef.current.querySelector('.highlight-container');
    if (!container) return;
    
    // We need to find the actual element to scroll to
    // Wait a bit for the highlight to render
    setTimeout(() => {
      const targetElement = container.querySelector(`span[data-match-index="${currentMatch}"]`) as HTMLElement;
      if (targetElement) {
        // Get the container's current scroll position
        const containerRect = container.getBoundingClientRect();
        const elementRect = targetElement.getBoundingClientRect();
        
        // Calculate the element's position relative to the container's content
        const elementTop = elementRect.top - containerRect.top + container.scrollTop;
        const elementBottom = elementTop + elementRect.height;
        
        // Container viewport boundaries
        const scrollTop = container.scrollTop;
        const scrollBottom = scrollTop + container.clientHeight;
        
        // Add some padding to ensure the highlight is clearly visible
        const padding = 50;
        
        // Check if element is fully visible with padding
        if (elementTop < scrollTop + padding || elementBottom > scrollBottom - padding) {
          // Scroll to center the element in the viewport
          const targetScroll = elementTop - (container.clientHeight / 2) + (elementRect.height / 2);
          container.scrollTo({
            top: Math.max(0, targetScroll),
            behavior: 'smooth'
          });
        }
        
        // Also ensure horizontal visibility if there's horizontal scrolling
        const elementLeft = elementRect.left - containerRect.left + container.scrollLeft;
        const elementRight = elementLeft + elementRect.width;
        const scrollLeft = container.scrollLeft;
        const scrollRight = scrollLeft + container.clientWidth;
        
        if (elementLeft < scrollLeft || elementRight > scrollRight) {
          container.scrollTo({
            left: Math.max(0, elementLeft - 50),
            behavior: 'smooth'
          });
        }
      }
    }, 100);
  }, [currentMatch]);

  // Separate effect for scrolling to current match
  useEffect(() => {
    if (matchPositions.length > 0 && matchPositions[currentMatch] !== undefined && debouncedSearchTerm) {
      scrollToMatch();
    }
  }, [currentMatch, matchPositions, debouncedSearchTerm, scrollToMatch]);

  const handleNextMatch = () => {
    if (matchPositions.length > 0) {
      const next = (currentMatch + 1) % matchPositions.length;
      setCurrentMatch(next);
    }
  };

  const handlePrevMatch = () => {
    if (matchPositions.length > 0) {
      const prev = currentMatch === 0 ? matchPositions.length - 1 : currentMatch - 1;
      setCurrentMatch(prev);
    }
  };

  const handleSearchClose = () => {
    setShowSearch(false);
    setSearchTerm('');
    setDebouncedSearchTerm('');
    setMatchPositions([]);
    setCurrentMatch(0);
  };


  return (
    <Box sx={{ position: 'relative' }}>
      {showSearch && (
        <Paper
          sx={{
            position: 'absolute',
            top: 8,
            right: 8,
            zIndex: 10,
            p: 1,
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            backgroundColor: '#ffffff',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
            border: '1px solid #e0e0e0'
          }}
        >
          <TextField
            inputRef={searchInputRef}
            size="small"
            placeholder="Search..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter' && e.shiftKey) {
                e.preventDefault();
                handlePrevMatch();
              } else if (e.key === 'Enter') {
                e.preventDefault();
                handleNextMatch();
              } else if (e.key === 'Escape') {
                handleSearchClose();
              }
            }}
            autoFocus
            sx={{ width: 200 }}
            slotProps={{
              input: {
                sx: { fontSize: '0.875rem' }
              }
            }}
          />
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
            <Tooltip title="Previous match (Shift+Enter)">
              <IconButton size="small" onClick={handlePrevMatch} disabled={matchPositions.length === 0}>
                <KeyboardArrowUp fontSize="small" />
              </IconButton>
            </Tooltip>
            <Typography variant="caption" sx={{ minWidth: 50, textAlign: 'center', fontWeight: 'bold' }}>
              {matchPositions.length > 0 ? `${currentMatch + 1}/${matchPositions.length}` : '0/0'}
            </Typography>
            <Tooltip title="Next match (Enter)">
              <IconButton size="small" onClick={handleNextMatch} disabled={matchPositions.length === 0}>
                <KeyboardArrowDown fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
          <Tooltip title="Close search (Escape)">
            <IconButton size="small" onClick={handleSearchClose}>
              <CloseIcon fontSize="small" />
            </IconButton>
          </Tooltip>
        </Paper>
      )}
      
      <Box ref={containerRef} sx={{ position: 'relative', width: '100%' }}>
        <Typography variant="body2" sx={{ mb: 1, fontWeight: 500 }}>
          {showSearch && debouncedSearchTerm && matchPositions.length > 0 
            ? `${label} - Match ${currentMatch + 1} of ${matchPositions.length}` 
            : label}
        </Typography>
        <Box
          className="highlight-container"
          sx={{
            border: '1px solid',
            borderColor: 'divider',
            borderRadius: 1,
            backgroundColor: 'background.paper',
            p: 2,
            fontFamily: 'monospace',
            fontSize: '0.875rem',
            whiteSpace: 'pre-wrap',
            wordBreak: 'break-word',
            height: `${rows * 1.5}em`,
            overflowY: 'auto',
            position: 'relative',
            color: '#000',
            '&::-webkit-scrollbar': {
              width: '8px',
            },
            '&::-webkit-scrollbar-track': {
              backgroundColor: '#f1f1f1',
            },
            '&::-webkit-scrollbar-thumb': {
              backgroundColor: '#888',
              borderRadius: '4px',
              '&:hover': {
                backgroundColor: '#555',
              }
            },
          }}
        >
          {isJson && !showSearch ? (
            <CollapsibleJson content={content} />
          ) : showSearch && debouncedSearchTerm ? (
            <HighlightedContent 
              text={content} 
              searchTerm={debouncedSearchTerm} 
              currentMatchIndex={currentMatch}
            />
          ) : (
            content
          )}
        </Box>
        {!showSearch && (
          <IconButton 
            onClick={() => setShowSearch(true)} 
            size="small"
            sx={{ 
              position: 'absolute', 
              right: 8, 
              top: 8,
              bgcolor: '#ffffff',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
              '&:hover': {
                bgcolor: '#f5f5f5'
              }
            }}
          >
            <SearchIcon fontSize="small" />
          </IconButton>
        )}
      </Box>
    </Box>
  );
});

export default function ETLReportsPlayground() {
  // Lazy load tab content to improve performance
  const [hasVisitedTab, setHasVisitedTab] = useState<boolean[]>([true, false, false]);
  
  // Common state
  const [reportId, setReportId] = useState('');
  const [outputFormat, setOutputFormat] = useState('OUTPUT_FORMAT_NIBRS_XML');
  const [tabValue, setTabValue] = useState(0);
  
  // Mapping config state
  const [mappingConfigJson, setMappingConfigJson] = useState('');
  const [useMappingConfig, setUseMappingConfig] = useState(false);
  
  // Template state
  const [templateContent, setTemplateContent] = useState('');
  const [useCustomTemplate, setUseCustomTemplate] = useState(false);
  
  // API hooks
  const extractReportData = useExtractReportData();
  const testReportMapping = useTestReportMapping();
  const testReportTransformation = useTestReportTransformation();

  // Handlers
  const handleExtractReportData = async () => {
    if (!reportId) {
      alert('Please enter a Report ID');
      return;
    }
    
    await extractReportData.mutateAsync({ reportId } as any);
  };

  const handleTestMapping = async () => {
    if (!reportId) {
      alert('Please enter a Report ID');
      return;
    }

    const request = {
      reportId,
      outputFormat: outputFormat as any,
      ...(useMappingConfig && mappingConfigJson ? { mappingConfigJson } : {})
    } as any;
    
    await testReportMapping.mutateAsync(request);
  };

  const handleTestTransformation = async () => {
    if (!reportId) {
      alert('Please enter a Report ID');
      return;
    }

    const request = {
      reportId,
      outputFormat: outputFormat as any,
      validate: true,
      ...(useMappingConfig && mappingConfigJson ? { mappingConfigJson } : {}),
      ...(useCustomTemplate && templateContent ? { templateContent } : {})
    } as any;
    
    await testReportTransformation.mutateAsync(request);
  };

  const formatJson = useMemo(() => (data: any) => {
    try {
      return JSON.stringify(data, null, 2);
    } catch {
      return String(data);
    }
  }, []);

  const decodeBase64 = (base64: string) => {
    try {
      return atob(base64);
    } catch {
      return base64;
    }
  };

  const isJsonContent = (str: string) => {
    try {
      JSON.parse(str);
      return true;
    } catch {
      return false;
    }
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    setHasVisitedTab(prev => {
      const newVisited = [...prev];
      newVisited[newValue] = true;
      return newVisited;
    });
  };

  return (
    <Box sx={{ height: '100vh', overflow: 'auto', bgcolor: '#f5f5f5' }}>
      <Container maxWidth="xl" sx={{ py: 4, pb: 10 }}>
        <Typography variant="h3" gutterBottom sx={{ color: '#212121', fontWeight: 600 }}>
          ETL Reports Playground
        </Typography>
      
      {/* Common Input Section */}
      <Card sx={{ mb: 3, bgcolor: '#ffffff', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ color: '#212121', fontWeight: 600 }}>
            Configuration
          </Typography>
          <Grid2 container spacing={3}>
            <Grid2 size={6}>
              <TextField
                fullWidth
                label="Report ID"
                placeholder="e.g., 2d38c610-16a7-4cb4-b3f5-7015e7311e0e"
                value={reportId}
                onChange={(e) => setReportId(e.target.value)}
              />
            </Grid2>
            <Grid2 size={6}>
              <FormControl fullWidth>
                <InputLabel>Output Format</InputLabel>
                <Select
                  value={outputFormat}
                  label="Output Format"
                  onChange={(e) => setOutputFormat(e.target.value)}
                >
                  <MenuItem value="OUTPUT_FORMAT_NIBRS_XML">NIBRS XML</MenuItem>
                </Select>
              </FormControl>
            </Grid2>
          </Grid2>
        </CardContent>
      </Card>

      <Box sx={{ borderBottom: 1, borderColor: '#e0e0e0', bgcolor: '#ffffff' }}>
        <Tabs value={tabValue} onChange={handleTabChange} 
          sx={{ 
            '& .MuiTab-root': { color: '#757575', fontWeight: 500 },
            '& .Mui-selected': { color: '#1976d2', fontWeight: 600 },
            '& .MuiTabs-indicator': { backgroundColor: '#1976d2' }
          }}>
          <Tab 
            icon={<FileJsonIcon />} 
            iconPosition="start" 
            label="Extract Report Data" 
          />
          <Tab 
            icon={<FileCodeIcon />} 
            iconPosition="start" 
            label="Test Report Mapping" 
          />
          <Tab 
            icon={<FileTextIcon />} 
            iconPosition="start" 
            label="Test Report Transformation" 
          />
        </Tabs>
      </Box>

      {/* Section 1: Extract Report Data */}
      <TabPanel value={tabValue} index={0}>
        {hasVisitedTab[0] ? (
        <Card sx={{ bgcolor: '#ffffff', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ color: '#212121', fontWeight: 600 }}>
              Extract Report Data
            </Typography>
            <Typography variant="body2" sx={{ mb: 2, color: '#666' }}>
              Extract raw report data without any transformation. This shows all the data available for the report.
            </Typography>
            
            <Button 
              variant="contained"
              onClick={handleExtractReportData}
              disabled={extractReportData.isPending}
              startIcon={extractReportData.isPending ? <CircularProgress size={20} /> : null}
            >
              Extract Data
            </Button>

            {extractReportData.error && (
              <Alert severity="error" sx={{ mt: 2, bgcolor: '#ffebee', color: '#c62828', '& .MuiAlert-icon': { color: '#c62828' } }}>
                Error: {extractReportData.error.message}
              </Alert>
            )}

            {extractReportData.data && (
              <Box sx={{ mt: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">Extraction Result</Typography>
                  <Typography variant="body2" sx={{ color: '#666' }}>
                    {extractReportData.data.dataSetsCount} data sets extracted
                  </Typography>
                </Box>
                
                {extractReportData.data.success ? (
                  <Box>
                    <SearchableOutput
                      content={formatJson(extractReportData.data.extractedData)}
                      label="Extracted Data"
                      rows={20}
                      isJson={true}
                    />
                    
                    {extractReportData.data.warnings && extractReportData.data.warnings.length > 0 && (
                      <Alert severity="warning" sx={{ mt: 2, bgcolor: '#fff3e0', color: '#e65100', '& .MuiAlert-icon': { color: '#e65100' } }}>
                        Warnings: {extractReportData.data.warnings.join(', ')}
                      </Alert>
                    )}
                  </Box>
                ) : (
                  <Alert severity="error">
                    Failed: {extractReportData.data.errorMessage}
                  </Alert>
                )}
              </Box>
            )}
          </CardContent>
        </Card>
        ) : (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        )}
      </TabPanel>

      {/* Section 2: Test Report Mapping */}
      <TabPanel value={tabValue} index={1}>
        {hasVisitedTab[1] ? (
        <Card sx={{ bgcolor: '#ffffff', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ color: '#212121', fontWeight: 600 }}>
              Test Report Mapping
            </Typography>
            <Typography variant="body2" sx={{ mb: 2, color: '#666' }}>
              Preview mapping configuration transformation without template formatting. 
              Shows the intermediate data structure after mapping.
            </Typography>

            <Box sx={{ my: 3 }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={useMappingConfig}
                    onChange={(e) => setUseMappingConfig(e.target.checked)}
                  />
                }
                label="Use custom mapping configuration"
              />

              {useMappingConfig && (
                <>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mt: 2, mb: 1 }}>
                    <Typography variant="subtitle2" sx={{ color: '#666', fontWeight: 500 }}>
                      Mapping Configuration (JSON)
                    </Typography>
                    <Button
                      size="small"
                      variant="outlined"
                      onClick={() => {
                        setMappingConfigJson(JSON.stringify(NIBRS_MAPPING_CONFIG, null, 2));
                      }}
                      sx={{ textTransform: 'none' }}
                    >
                      Hydrate with NIBRS Config
                    </Button>
                  </Box>
                  <TextField
                    fullWidth
                    multiline
                    rows={10}
                    placeholder='{"field_mappings": [...], "lookup_tables": {...}}'
                    value={mappingConfigJson}
                    onChange={(e) => setMappingConfigJson(e.target.value)}
                    label=""
                    sx={{ mt: 0 }}
                    slotProps={{
                      input: {
                        sx: { 
                          fontFamily: 'monospace', 
                          fontSize: '0.875rem',
                          backgroundColor: '#ffffff',
                          color: '#000'
                        }
                      }
                    }}
                  />
                </>
              )}
            </Box>

            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button 
                variant="outlined"
                onClick={handleExtractReportData}
                disabled={extractReportData.isPending}
                startIcon={extractReportData.isPending ? <CircularProgress size={20} /> : null}
              >
                Extract Data First
              </Button>
              <Button 
                variant="contained"
                onClick={handleTestMapping}
                disabled={testReportMapping.isPending || !extractReportData.data}
                startIcon={testReportMapping.isPending ? <CircularProgress size={20} /> : null}
              >
                Test Mapping
              </Button>
            </Box>

            {testReportMapping.error && (
              <Alert severity="error" sx={{ mt: 2, bgcolor: '#ffebee', color: '#c62828', '& .MuiAlert-icon': { color: '#c62828' } }}>
                Error: {testReportMapping.error.message}
              </Alert>
            )}

            {testReportMapping.data && (
              <Box sx={{ mt: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">Mapping Result</Typography>
                  <Typography variant="body2" sx={{ color: '#666' }}>
                    Config: {testReportMapping.data.mappingConfigUsed}
                  </Typography>
                </Box>
                
                {testReportMapping.data.success ? (
                  <Box>
                    <Grid2 container spacing={2}>
                      <Grid2 size={6}>
                        <Typography variant="subtitle2" gutterBottom sx={{ color: '#666', fontWeight: 500 }}>
                          Extracted Data (Input)
                        </Typography>
                        <SearchableOutput
                          content={formatJson(extractReportData.data?.extractedData || {})}
                          label="Extracted Data"
                          rows={20}
                          isJson={true}
                        />
                      </Grid2>
                      <Grid2 size={6}>
                        <Typography variant="subtitle2" gutterBottom sx={{ color: '#666', fontWeight: 500 }}>
                          Mapped Data (Output)
                        </Typography>
                        <SearchableOutput
                          content={formatJson(testReportMapping.data.mappedData)}
                          label="Mapped Data"
                          rows={20}
                          isJson={true}
                        />
                      </Grid2>
                    </Grid2>
                    
                    {testReportMapping.data.warnings && testReportMapping.data.warnings.length > 0 && (
                      <Alert severity="warning" sx={{ mt: 2, bgcolor: '#fff3e0', color: '#e65100', '& .MuiAlert-icon': { color: '#e65100' } }}>
                        Warnings: {testReportMapping.data.warnings.join(', ')}
                      </Alert>
                    )}
                  </Box>
                ) : (
                  <Alert severity="error">
                    Failed: {testReportMapping.data.errorMessage}
                  </Alert>
                )}
              </Box>
            )}
          </CardContent>
        </Card>
        ) : (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        )}
      </TabPanel>

      {/* Section 3: Test Report Transformation */}
      <TabPanel value={tabValue} index={2}>
        {hasVisitedTab[2] ? (
        <Card sx={{ bgcolor: '#ffffff', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
          <CardContent>
            <Typography variant="h6" gutterBottom sx={{ color: '#212121', fontWeight: 600 }}>
              Test Report Transformation
            </Typography>
            <Typography variant="body2" sx={{ mb: 2, color: '#666' }}>
              Complete transformation pipeline - extract, map, and format. 
              Shows the final output ready for submission.
            </Typography>

            <Box sx={{ my: 3 }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={useMappingConfig}
                    onChange={(e) => setUseMappingConfig(e.target.checked)}
                  />
                }
                label="Use custom mapping configuration"
              />

              {useMappingConfig && (
                <>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mt: 2, mb: 1 }}>
                    <Typography variant="subtitle2" sx={{ color: '#666', fontWeight: 500 }}>
                      Mapping Configuration (JSON)
                    </Typography>
                    <Button
                      size="small"
                      variant="outlined"
                      onClick={() => {
                        setMappingConfigJson(JSON.stringify(NIBRS_MAPPING_CONFIG, null, 2));
                      }}
                      sx={{ textTransform: 'none' }}
                    >
                      Hydrate with NIBRS Config
                    </Button>
                  </Box>
                  <TextField
                    fullWidth
                    multiline
                    rows={10}
                    placeholder='{"field_mappings": [...], "lookup_tables": {...}}'
                    value={mappingConfigJson}
                    onChange={(e) => setMappingConfigJson(e.target.value)}
                    label=""
                    sx={{ mt: 0 }}
                    slotProps={{
                      input: {
                        sx: { 
                          fontFamily: 'monospace', 
                          fontSize: '0.875rem',
                          backgroundColor: '#ffffff',
                          color: '#000'
                        }
                      }
                    }}
                  />
                </>
              )}
            </Box>

            <Box sx={{ my: 3 }}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={useCustomTemplate}
                    onChange={(e) => setUseCustomTemplate(e.target.checked)}
                  />
                }
                label="Use custom template"
              />

              {useCustomTemplate && (
                <>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mt: 2, mb: 1 }}>
                    <Typography variant="subtitle2" sx={{ color: '#666', fontWeight: 500 }}>
                      Template Content
                    </Typography>
                    <Button
                      size="small"
                      variant="outlined"
                      onClick={() => {
                        setTemplateContent(NIBRS_TEMPLATE);
                      }}
                      sx={{ textTransform: 'none' }}
                    >
                      Hydrate with NIBRS Template
                    </Button>
                  </Box>
                  <TextField
                    fullWidth
                    multiline
                    rows={10}
                    placeholder='<?xml version="1.0" encoding="UTF-8"?>...'
                    value={templateContent}
                    onChange={(e) => setTemplateContent(e.target.value)}
                    label=""
                    sx={{ mt: 0 }}
                    slotProps={{
                      input: {
                        sx: { 
                          fontFamily: 'monospace', 
                          fontSize: '0.875rem',
                          backgroundColor: '#ffffff',
                          color: '#000'
                        }
                      }
                    }}
                  />
                </>
              )}
            </Box>

            <Box sx={{ display: 'flex', gap: 2 }}>
              <Button 
                variant="outlined"
                onClick={async () => {
                  await handleExtractReportData();
                  await handleTestMapping();
                }}
                disabled={extractReportData.isPending || testReportMapping.isPending}
                startIcon={(extractReportData.isPending || testReportMapping.isPending) ? <CircularProgress size={20} /> : null}
              >
                Run Extract & Map First
              </Button>
              <Button 
                variant="contained"
                onClick={handleTestTransformation}
                disabled={testReportTransformation.isPending}
                startIcon={testReportTransformation.isPending ? <CircularProgress size={20} /> : null}
              >
                Test Full Transformation
              </Button>
            </Box>

            {testReportTransformation.error && (
              <Alert severity="error" sx={{ mt: 2, bgcolor: '#ffebee', color: '#c62828', '& .MuiAlert-icon': { color: '#c62828' } }}>
                Error: {testReportTransformation.error.message}
              </Alert>
            )}

            {testReportTransformation.data && (
              <Box sx={{ mt: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">Transformation Pipeline</Typography>
                  <Box>
                    <Chip label={`Type: ${testReportTransformation.data.contentType}`} size="small" sx={{ mr: 1 }} />
                    <Chip label={`Template: ${testReportTransformation.data.templateUsed}`} size="small" />
                  </Box>
                </Box>
                
                {testReportTransformation.data.success ? (
                  <Box>
                    <Grid2 container spacing={2}>
                      <Grid2 size={4}>
                        <Typography variant="subtitle2" gutterBottom sx={{ color: '#666', fontWeight: 500 }}>
                          1. Extracted Data
                        </Typography>
                        <SearchableOutput
                          content={formatJson(extractReportData.data?.extractedData || {})}
                          label="Raw Data"
                          rows={20}
                          isJson={true}
                        />
                      </Grid2>
                      <Grid2 size={4}>
                        <Typography variant="subtitle2" gutterBottom sx={{ color: '#666', fontWeight: 500 }}>
                          2. Mapped Data
                        </Typography>
                        <SearchableOutput
                          content={formatJson(testReportMapping.data?.mappedData || {})}
                          label="After Mapping"
                          rows={20}
                          isJson={true}
                        />
                      </Grid2>
                      <Grid2 size={4}>
                        <Typography variant="subtitle2" gutterBottom sx={{ color: '#666', fontWeight: 500 }}>
                          3. Final Output
                        </Typography>
                        <SearchableOutput
                          content={testReportTransformation.data.readableContent || 
                                decodeBase64(testReportTransformation.data.transformedContent?.toString() || '')}
                          label="Transformed Content"
                          rows={20}
                          isJson={isJsonContent(testReportTransformation.data.readableContent || 
                                decodeBase64(testReportTransformation.data.transformedContent?.toString() || ''))}
                        />
                      </Grid2>
                    </Grid2>
                    
                    {testReportTransformation.data.validationErrors && 
                     testReportTransformation.data.validationErrors.length > 0 && (
                      <Alert severity="error" sx={{ mt: 2, bgcolor: '#ffebee', color: '#c62828', '& .MuiAlert-icon': { color: '#c62828' } }}>
                        <Typography variant="subtitle2" gutterBottom>
                          Validation Errors:
                        </Typography>
                        {testReportTransformation.data.validationErrors.map((error, idx) => (
                          <Typography key={idx} variant="body2">
                            {error.fieldPath}: {error.errorMessage} ({error.errorCode})
                          </Typography>
                        ))}
                      </Alert>
                    )}
                    
                    {testReportTransformation.data.warnings && 
                     testReportTransformation.data.warnings.length > 0 && (
                      <Alert severity="warning" sx={{ mt: 2, bgcolor: '#fff3e0', color: '#e65100', '& .MuiAlert-icon': { color: '#e65100' } }}>
                        Warnings: {testReportTransformation.data.warnings.join(', ')}
                      </Alert>
                    )}
                  </Box>
                ) : (
                  <Alert severity="error">
                    Failed: {testReportTransformation.data.errorMessage}
                  </Alert>
                )}
              </Box>
            )}
          </CardContent>
        </Card>
        ) : (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        )}
      </TabPanel>

      {/* Help Section */}
      <Card sx={{ mt: 4, bgcolor: '#ffffff', boxShadow: '0 2px 4px rgba(0,0,0,0.1)' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ color: '#212121', fontWeight: 600 }}>
            {tabValue === 0 ? 'Extract Report Data - Quick Reference' : 
             tabValue === 1 ? 'Test Report Mapping - Configuration Guide' : 
             'Test Report Transformation - Template Guide'}
          </Typography>
          
          {/* Tab 0: Extract Report Data */}
          {tabValue === 0 && (
            <>
              <Box sx={{ mt: 2 }}>
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
                  Sample Report IDs
                </Typography>
                <Paper sx={{ p: 1, bgcolor: '#f5f5f5', border: '1px solid #e0e0e0' }}>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242' }}>
                    2d38c610-16a7-4cb4-b3f5-7015e7311e0e
                  </Typography>
                </Paper>
              </Box>
              
              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
                  What You&apos;ll Get
                </Typography>
                <List dense>
                  <ListItem>
                    <ListItemText 
                      primary="Raw Data Structure"
                      secondary="Complete extracted data from all services: reports, entities, assets, relationships, situations"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText 
                      primary="Data Set Information"
                      secondary="Count and types of data sets extracted"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText 
                      primary="Warnings & Errors"
                      secondary="Any issues encountered during extraction"
                    />
                  </ListItem>
                </List>
              </Box>
              
              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
                  Use Cases
                </Typography>
                <List dense>
                  <ListItem>
                    <ListItemText 
                      primary="Data Exploration"
                      secondary="Understand the structure and content of your report data"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText 
                      primary="Mapping Preparation"
                      secondary="Identify field paths for creating custom mapping configurations"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemText 
                      primary="Debugging"
                      secondary="Troubleshoot data issues before transformation"
                    />
                  </ListItem>
                </List>
              </Box>
            </>
          )}
          
          {/* Tab 1: Test Report Mapping */}
          {tabValue === 1 && (
            <>
              {/* Table of Contents */}
              <Box sx={{ mt: 2, mb: 4, p: 3, bgcolor: '#f8f9fa', borderRadius: 2, border: '1px solid #e0e0e0' }}>
                <Typography variant="h6" gutterBottom sx={{ color: '#212121', fontWeight: 600, mb: 2 }}>
                  📚 Quick Navigation - Click to Jump to Section
                </Typography>
                <Grid2 container spacing={2}>
                  <Grid2 size={6}>
                    <Typography variant="subtitle2" sx={{ color: '#666', fontWeight: 500, mb: 1 }}>
                      Basic Transformations
                    </Typography>
                    <Box component="ul" sx={{ m: 0, pl: 3, '& li': { fontSize: '0.875rem', mb: 0.5 } }}>
                      <li><Link href="#copy-data" underline="hover" sx={{ cursor: 'pointer' }} onClick={(e) => { e.preventDefault(); document.getElementById('copy-data')?.scrollIntoView({ behavior: 'smooth' }); }}>Copy Data Without Changes</Link></li>
                      <li><Link href="#format-text" underline="hover" sx={{ cursor: 'pointer' }} onClick={(e) => { e.preventDefault(); document.getElementById('format-text')?.scrollIntoView({ behavior: 'smooth' }); }}>Add Prefixes, Suffixes, or Format Numbers</Link></li>
                      <li><Link href="#date-format" underline="hover" sx={{ cursor: 'pointer' }} onClick={(e) => { e.preventDefault(); document.getElementById('date-format')?.scrollIntoView({ behavior: 'smooth' }); }}>Change How Dates Look</Link></li>
                      <li><Link href="#lookup-codes" underline="hover" sx={{ cursor: 'pointer' }} onClick={(e) => { e.preventDefault(); document.getElementById('lookup-codes')?.scrollIntoView({ behavior: 'smooth' }); }}>Convert Codes to Readable Text</Link></li>
                    </Box>
                  </Grid2>
                  <Grid2 size={6}>
                    <Typography variant="subtitle2" sx={{ color: '#666', fontWeight: 500, mb: 1 }}>
                      Advanced Transformations
                    </Typography>
                    <Box component="ul" sx={{ m: 0, pl: 3, '& li': { fontSize: '0.875rem', mb: 0.5 } }}>
                      <li><Link href="#filter-data" underline="hover" sx={{ cursor: 'pointer' }} onClick={(e) => { e.preventDefault(); document.getElementById('filter-data')?.scrollIntoView({ behavior: 'smooth' }); }}>Find Specific Items in Your Data</Link></li>
                      <li><Link href="#add-numbers" underline="hover" sx={{ cursor: 'pointer' }} onClick={(e) => { e.preventDefault(); document.getElementById('add-numbers')?.scrollIntoView({ behavior: 'smooth' }); }}>Number Your Items in Order</Link></li>
                      <li><Link href="#sort-items" underline="hover" sx={{ cursor: 'pointer' }} onClick={(e) => { e.preventDefault(); document.getElementById('sort-items')?.scrollIntoView({ behavior: 'smooth' }); }}>Put Items in Order</Link></li>
                      <li><Link href="#group-items" underline="hover" sx={{ cursor: 'pointer' }} onClick={(e) => { e.preventDefault(); document.getElementById('group-items')?.scrollIntoView({ behavior: 'smooth' }); }}>Organize by Category</Link></li>
                    </Box>
                  </Grid2>
                  <Grid2 size={6}>
                    <Typography variant="subtitle2" sx={{ color: '#666', fontWeight: 500, mb: 1, mt: 2 }}>
                      Path & Data Access
                    </Typography>
                    <Box component="ul" sx={{ m: 0, pl: 3, '& li': { fontSize: '0.875rem', mb: 0.5 } }}>
                      <li><Link href="#path-syntax" underline="hover" sx={{ cursor: 'pointer' }} onClick={(e) => { e.preventDefault(); document.getElementById('path-syntax')?.scrollIntoView({ behavior: 'smooth' }); }}>How to Navigate Your Data</Link></li>
                      <li><Link href="#advanced-features" underline="hover" sx={{ cursor: 'pointer' }} onClick={(e) => { e.preventDefault(); document.getElementById('advanced-features')?.scrollIntoView({ behavior: 'smooth' }); }}>Process Multiple Items at Once</Link></li>
                    </Box>
                  </Grid2>
                  <Grid2 size={6}>
                    <Typography variant="subtitle2" sx={{ color: '#666', fontWeight: 500, mb: 1, mt: 2 }}>
                      Reference
                    </Typography>
                    <Box component="ul" sx={{ m: 0, pl: 3, '& li': { fontSize: '0.875rem', mb: 0.5 } }}>
                      <li><Link href="#filter-options" underline="hover" sx={{ cursor: 'pointer' }} onClick={(e) => { e.preventDefault(); document.getElementById('filter-options')?.scrollIntoView({ behavior: 'smooth' }); }}>Filter Options Guide</Link></li>
                    </Box>
                  </Grid2>
                </Grid2>
              </Box>

              <Box sx={{ mt: 2 }} id="copy-data">
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
                  1. Copy Data Without Changes (Direct)
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                  The simplest way to move data from your source to your output. Perfect when you just need to rename a field or move it to a different location without modifying the content.
                </Typography>
                <Grid2 container spacing={2}>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e8f5e8', border: '1px solid #4caf50', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#2e7d32', fontWeight: 'bold', mb: 1 }}>
                        📥 INPUT DATA
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "reports": [{
    "report": {
      "title": "Incident Report #123",
      "id": "abc-123"
    }
  }]
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#fff3e0', border: '1px solid #ff9800', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#ef6c00', fontWeight: 'bold', mb: 1 }}>
                        ⚙️ MAPPING CONFIG
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "name": "report_title",
  "description": "Copy report title without changes",
  "input_path": "reports[0].report.title",
  "transformation": "direct",
  "output_path": "ReportTitle",
  "config": {}
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e3f2fd', border: '1px solid #2196f3', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#1976d2', fontWeight: 'bold', mb: 1 }}>
                        📤 OUTPUT
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "ReportTitle": "Incident Report #123"
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                </Grid2>
              </Box>
              
              <Box sx={{ mt: 3 }} id="format-text">
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
                  2. Add Prefixes, Suffixes, or Format Numbers (Format)
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                  Add text before or after your data, or control how numbers appear. For example, turn &quot;123&quot; into &quot;INC-123&quot; or convert 5 to &quot;05&quot;. Common uses: adding agency prefixes to case numbers, formatting badge numbers with leading zeros.
                </Typography>
                <Grid2 container spacing={2}>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e8f5e8', border: '1px solid #4caf50', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#2e7d32', fontWeight: 'bold', mb: 1 }}>
                        📥 INPUT DATA
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "reports": [{
    "report": {
      "id": "abc-123",
      "org_id": 12345
    }
  }]
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#fff3e0', border: '1px solid #ff9800', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#ef6c00', fontWeight: 'bold', mb: 1 }}>
                        ⚙️ MAPPING CONFIG
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "name": "incident_number",
  "description": "Add prefix to report ID",
  "input_path": "reports[0].report.id",
  "transformation": "format",
  "output_path": "IncidentNumber",
  "config": {
    "format_pattern": "INC-%s"
  }
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e3f2fd', border: '1px solid #2196f3', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#1976d2', fontWeight: 'bold', mb: 1 }}>
                        📤 OUTPUT
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "IncidentNumber": "INC-abc-123"
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                </Grid2>
              </Box>
              
              <Box sx={{ mt: 3 }} id="date-format">
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
                  3. Change How Dates Look (Date Format)
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                  Convert dates to match your agency&apos;s requirements. Change &quot;2024-07-14T15:30:00Z&quot; to &quot;07/14/2024&quot; or &quot;July 14, 2024&quot;. Essential for reports that need specific date formats for compliance or readability.
                </Typography>
                <Grid2 container spacing={2}>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e8f5e8', border: '1px solid #4caf50', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#2e7d32', fontWeight: 'bold', mb: 1 }}>
                        📥 INPUT DATA
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "reports": [{
    "report": {
      "created_at": "2024-07-14T15:30:00Z"
    }
  }]
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#fff3e0', border: '1px solid #ff9800', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#ef6c00', fontWeight: 'bold', mb: 1 }}>
                        ⚙️ MAPPING CONFIG
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "name": "report_date",
  "description": "Convert to YYYY-MM-DD format",
  "input_path": "reports[0].report.created_at",
  "transformation": "date_format",
  "output_path": "ReportDate",
  "config": {
    "date_format": "2006-01-02"
  }
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e3f2fd', border: '1px solid #2196f3', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#1976d2', fontWeight: 'bold', mb: 1 }}>
                        📤 OUTPUT
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "ReportDate": "2024-07-14"
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                </Grid2>
              </Box>
              
              <Box sx={{ mt: 3 }} id="lookup-codes">
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
                  4. Convert Codes to Readable Text (Lookup)
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                  Turn cryptic codes into meaningful text using a conversion table. For example, convert &quot;M&quot; to &quot;Male&quot;, &quot;F&quot; to &quot;Female&quot;, or department code &quot;1&quot; to &quot;Metropolitan Police Department&quot;. Like having a translation dictionary for your data.
                </Typography>
                <Grid2 container spacing={2}>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e8f5e8', border: '1px solid #4caf50', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#2e7d32', fontWeight: 'bold', mb: 1 }}>
                        📥 INPUT DATA
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "entities": [{
    "entity": {
      "data": {
        "gender": "Male"
      }
    }
  }]
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#fff3e0', border: '1px solid #ff9800', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#ef6c00', fontWeight: 'bold', mb: 1 }}>
                        ⚙️ MAPPING CONFIG + LOOKUP
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "name": "gender_code",
  "description": "Convert gender to single letter code",
  "input_path": "entities[0].entity.data.gender",
  "transformation": "lookup",
  "output_path": "GenderCode",
  "config": {
    "lookup_table": "sex_codes",
    "default_value": "U"
  }
}

LOOKUP TABLE:
{
  "lookup_tables": {
    "sex_codes": {
      "Male": "M",
      "Female": "F",
      "Unknown": "U"
    }
  }
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e3f2fd', border: '1px solid #2196f3', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#1976d2', fontWeight: 'bold', mb: 1 }}>
                        📤 OUTPUT
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "GenderCode": "M"
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                </Grid2>
              </Box>
              
              <Box sx={{ mt: 3 }} id="filter-data">
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
                  5. Find Specific Items in Your Data (Filter)
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                  Extract only the data you need from a larger set. For example, find all people over 18, all vehicles of a certain type, or all incidents from a specific location. Like using a search filter to narrow down results.
                </Typography>
                <Grid2 container spacing={2}>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e8f5e8', border: '1px solid #4caf50', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#2e7d32', fontWeight: 'bold', mb: 1 }}>
                        📥 INPUT DATA
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "entities": [
    {
      "entity": {
        "entity_type": "ENTITY_TYPE_PERSON",
        "data": {"age": 25}
      }
    },
    {
      "entity": {
        "entity_type": "ENTITY_TYPE_VEHICLE",
        "data": {}
      }
    },
    {
      "entity": {
        "entity_type": "ENTITY_TYPE_PERSON",
        "data": {"age": 17}
      }
    }
  ]
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#fff3e0', border: '1px solid #ff9800', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#ef6c00', fontWeight: 'bold', mb: 1 }}>
                        ⚙️ MAPPING CONFIG
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "name": "person_entities",
  "description": "Get only person entities",
  "input_path": "entities",
  "transformation": "filter",
  "output_path": "PersonEntities",
  "config": {
    "field": "entity.entity_type",
    "operator": "equals",
    "value": "ENTITY_TYPE_PERSON"
  }
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e3f2fd', border: '1px solid #2196f3', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#1976d2', fontWeight: 'bold', mb: 1 }}>
                        📤 OUTPUT
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "PersonEntities": [
    {
      "entity": {
        "entity_type": "ENTITY_TYPE_PERSON",
        "data": {"age": 25}
      }
    },
    {
      "entity": {
        "entity_type": "ENTITY_TYPE_PERSON",
        "data": {"age": 17}
      }
    }
  ]
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                </Grid2>
              </Box>
              
              <Box sx={{ mt: 3 }} id="add-numbers">
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
                  6. Number Your Items in Order (Add Sequence)
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                  Automatically add numbers to your list items, like &quot;01&quot;, &quot;02&quot;, &quot;03&quot;. Perfect for creating numbered witness lists, evidence items, or any report that requires sequential numbering for official documentation.
                </Typography>
                <Grid2 container spacing={2}>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e8f5e8', border: '1px solid #4caf50', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#2e7d32', fontWeight: 'bold', mb: 1 }}>
                        📥 INPUT DATA
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "PersonEntities": [
    {
      "entity": {
        "data": {"name": "John Doe"}
      }
    },
    {
      "entity": {
        "data": {"name": "Jane Smith"}
      }
    }
  ]
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#fff3e0', border: '1px solid #ff9800', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#ef6c00', fontWeight: 'bold', mb: 1 }}>
                        ⚙️ MAPPING CONFIG
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "name": "persons_with_numbers",
  "description": "Add sequence numbers starting at 1",
  "input_path": "PersonEntities",
  "transformation": "add_sequence",
  "output_path": "PersonsWithSequence",
  "config": {
    "sequence_field": "SequenceNumber",
    "start": 1,
    "format": "%02d"
  }
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e3f2fd', border: '1px solid #2196f3', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#1976d2', fontWeight: 'bold', mb: 1 }}>
                        📤 OUTPUT
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "PersonsWithSequence": [
    {
      "SequenceNumber": "01",
      "entity": {
        "data": {"name": "John Doe"}
      }
    },
    {
      "SequenceNumber": "02",
      "entity": {
        "data": {"name": "Jane Smith"}
      }
    }
  ]
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                </Grid2>
              </Box>
              
              <Box sx={{ mt: 3 }} id="sort-items">
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
                  7. Put Items in Order (Sort)
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                  Arrange your data alphabetically, by date, or by number. Sort witnesses by last name, incidents by date, or evidence by priority. Choose ascending (A-Z, 1-9) or descending (Z-A, 9-1) order.
                </Typography>
                <Grid2 container spacing={2}>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e8f5e8', border: '1px solid #4caf50', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#2e7d32', fontWeight: 'bold', mb: 1 }}>
                        📥 INPUT DATA
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "PersonEntities": [
    {
      "entity": {
        "data": {"age": 35, "name": "Bob"}
      }
    },
    {
      "entity": {
        "data": {"age": 25, "name": "Alice"}
      }
    },
    {
      "entity": {
        "data": {"age": 30, "name": "Charlie"}
      }
    }
  ]
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#fff3e0', border: '1px solid #ff9800', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#ef6c00', fontWeight: 'bold', mb: 1 }}>
                        ⚙️ MAPPING CONFIG
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "name": "sorted_by_age",
  "description": "Sort people by age (youngest first)",
  "input_path": "PersonEntities",
  "transformation": "sort",
  "output_path": "SortedByAge",
  "config": {
    "sort_field": "entity.data.age",
    "sort_order": "asc"
  }
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e3f2fd', border: '1px solid #2196f3', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#1976d2', fontWeight: 'bold', mb: 1 }}>
                        📤 OUTPUT
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "SortedByAge": [
    {
      "entity": {
        "data": {"age": 25, "name": "Alice"}
      }
    },
    {
      "entity": {
        "data": {"age": 30, "name": "Charlie"}
      }
    },
    {
      "entity": {
        "data": {"age": 35, "name": "Bob"}
      }
    }
  ]
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                </Grid2>
              </Box>
              
              <Box sx={{ mt: 3 }} id="group-items">
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
                  8. Organize by Category (Group By)
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                  Organize your data into categories automatically. Group all vehicles by type, all people by role (witness, suspect, victim), or all incidents by location. Makes large datasets easier to understand and process.
                </Typography>
                <Grid2 container spacing={2}>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e8f5e8', border: '1px solid #4caf50', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#2e7d32', fontWeight: 'bold', mb: 1 }}>
                        📥 INPUT DATA
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "entities": [
    {
      "entity": {
        "entity_type": "ENTITY_TYPE_PERSON",
        "data": {"name": "John"}
      }
    },
    {
      "entity": {
        "entity_type": "ENTITY_TYPE_VEHICLE",
        "data": {"make": "Toyota"}
      }
    },
    {
      "entity": {
        "entity_type": "ENTITY_TYPE_PERSON",
        "data": {"name": "Jane"}
      }
    }
  ]
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#fff3e0', border: '1px solid #ff9800', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#ef6c00', fontWeight: 'bold', mb: 1 }}>
                        ⚙️ MAPPING CONFIG
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "name": "grouped_by_type",
  "description": "Group entities by their type",
  "input_path": "entities",
  "transformation": "group_by",
  "output_path": "GroupedEntities",
  "config": {
    "group_field": "entity.entity_type"
  }
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e3f2fd', border: '1px solid #2196f3', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#1976d2', fontWeight: 'bold', mb: 1 }}>
                        📤 OUTPUT
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "GroupedEntities": {
    "ENTITY_TYPE_PERSON": [
      {
        "entity": {
          "entity_type": "ENTITY_TYPE_PERSON",
          "data": {"name": "John"}
        }
      },
      {
        "entity": {
          "entity_type": "ENTITY_TYPE_PERSON",
          "data": {"name": "Jane"}
        }
      }
    ],
    "ENTITY_TYPE_VEHICLE": [
      {
        "entity": {
          "entity_type": "ENTITY_TYPE_VEHICLE",
          "data": {"make": "Toyota"}
        }
      }
    ]
  }
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                </Grid2>
              </Box>
              
              
              <Box sx={{ mt: 3 }} id="path-syntax">
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
                  9. How to Navigate Your Data (Path Syntax)
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                  Learn how to tell the system where to find your data. Like giving directions: &quot;Go to reports, then the first one, then get the title.&quot; This section shows you how to access any piece of information in your data.
                </Typography>
                <Grid2 container spacing={2}>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e8f5e8', border: '1px solid #4caf50', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#2e7d32', fontWeight: 'bold', mb: 1 }}>
                        📊 BASIC PATHS
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`Nested data access:
reports[0].report.title
entities[0].entity.data.firstName

Array access:
First item: reports[0]
All items (wildcard): entities[*]
Specific field from all: entities[*].entity.data.name

JSONPath filters:
entities[?(@.entity.entity_type == 'ENTITY_TYPE_PERSON')]
entities[?(@.entity.data.age >= 18)]`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#fff3e0', border: '1px solid #ff9800', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#ef6c00', fontWeight: 'bold', mb: 1 }}>
                        🎯 PATH EXAMPLES
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`Get first report's title:
"input_path": "reports[0].report.title"

Get all entity names:
"input_path": "entities[*].entity.data.name"

Update all person genders in-place:
"input_path": "entities[*].entity.data.gender",
"output_path": "entities[*].entity.data.gender"`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e3f2fd', border: '1px solid #2196f3', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#1976d2', fontWeight: 'bold', mb: 1 }}>
                        🔄 IN-PLACE UPDATES
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`Use same path for input and output to update values without moving them:

{
  "input_path": "entities[*].entity.data.gender",
  "output_path": "entities[*].entity.data.gender",
  "transformation": "lookup",
  "config": {
    "lookup_table": "sex_codes"
  }
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                </Grid2>
              </Box>
              
              <Box sx={{ mt: 3 }} id="advanced-features">
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
                  10. Process Multiple Items at Once (Advanced Features)
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                  Save time by working with all items in a list simultaneously. Instead of processing each person one by one, transform all of them at once. Also learn how to filter data while accessing it.
                </Typography>
                <Grid2 container spacing={2}>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e8f5e8', border: '1px solid #4caf50', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#2e7d32', fontWeight: 'bold', mb: 1 }}>
                        🌟 PROCESS ALL ITEMS AT ONCE
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`Get all first names from your list:
"input_path": "entities[*].entity.data.firstName"
Result: ["John", "Jane", "Bob"]

Convert all gender codes to full text:
"input_path": "entities[*].entity.data.gender"
"output_path": "entities[*].entity.data.gender"
"transformation": "lookup"

The [*] means "all items in this list"`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#fff3e0', border: '1px solid #ff9800', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#ef6c00', fontWeight: 'bold', mb: 1 }}>
                        🔍 SMART FILTERING
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`Get only people (not vehicles/locations):
entities[?(@.entity.entity_type == 'ENTITY_TYPE_PERSON')]

Find only adults (18 or older):
entities[?(@.entity.data.age >= 18)]

Get race info for people only:
entities[?(@.entity.entity_type == 'ENTITY_TYPE_PERSON')].entity.data.race

The ?(@...) means "only items where..."`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e3f2fd', border: '1px solid #2196f3', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#1976d2', fontWeight: 'bold', mb: 1 }}>
                        🔗 STEP-BY-STEP PROCESSING
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`Build complex transformations step by step:

1. Find all people → Save as "PersonEntities"
2. From PersonEntities, find adults → Save as "AdultPersons"  
3. Number the AdultPersons → Save as "PersonsWithSequence"
4. Convert gender codes in PersonsWithSequence

Each step uses the result from the previous step!`}
                      </Typography>
                    </Paper>
                  </Grid2>
                </Grid2>
              </Box>
              
              <Box sx={{ mt: 3 }} id="filter-options">
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
                  Filter Options Guide
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                  All the different ways you can search and filter your data. Use these options with the Filter transformation to find exactly what you need.
                </Typography>
                <Grid2 container spacing={2}>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#f3e5f5', border: '1px solid #9c27b0', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#7b1fa2', fontWeight: 'bold', mb: 1 }}>
                        🔢 COMPARISON OPERATORS
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`equals
not_equals
greater_than
greater_than_or_equal
less_than
less_than_or_equal

Example:
{"field": "entity.data.age", "operator": "greater_than", "value": 18}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e8f5e8', border: '1px solid #4caf50', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#2e7d32', fontWeight: 'bold', mb: 1 }}>
                        📝 TEXT OPERATORS
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`contains
starts_with
ends_with

Examples:
{"field": "entity.data.name", "operator": "contains", "value": "Smith"}
{"field": "entity.data.email", "operator": "ends_with", "value": "@police.gov"}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#fff3e0', border: '1px solid #ff9800', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#ef6c00', fontWeight: 'bold', mb: 1 }}>
                        📋 LIST OPERATORS
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`in
not_in

Examples:
{"field": "entity.entity_type", "operator": "in", "value": ["ENTITY_TYPE_PERSON", "ENTITY_TYPE_VEHICLE"]}
{"field": "status", "operator": "not_in", "value": ["UNKNOWN", "ERROR"]}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                </Grid2>
              </Box>
            </>
          )}
          
          {/* Tab 2: Test Report Transformation */}
          {tabValue === 2 && (
            <>
              {/* Table of Contents */}
              <Box sx={{ mt: 2, mb: 4, p: 3, bgcolor: '#f8f9fa', borderRadius: 2, border: '1px solid #e0e0e0' }}>
                <Typography variant="h6" gutterBottom sx={{ color: '#212121', fontWeight: 600, mb: 2 }}>
                  📚 Quick Navigation - Click to Jump to Section
                </Typography>
                <Grid2 container spacing={2}>
                  <Grid2 size={6}>
                    <Typography variant="subtitle2" sx={{ color: '#666', fontWeight: 500, mb: 1 }}>
                      Template Basics
                    </Typography>
                    <Box component="ul" sx={{ m: 0, pl: 3, '& li': { fontSize: '0.875rem', mb: 0.5 } }}>
                      <li><Link href="#basic-template" underline="hover" sx={{ cursor: 'pointer' }} onClick={(e) => { e.preventDefault(); document.getElementById('basic-template')?.scrollIntoView({ behavior: 'smooth' }); }}>Create Your First XML Document</Link></li>
                      <li><Link href="#template-functions" underline="hover" sx={{ cursor: 'pointer' }} onClick={(e) => { e.preventDefault(); document.getElementById('template-functions')?.scrollIntoView({ behavior: 'smooth' }); }}>Make Text Look Right</Link></li>
                      <li><Link href="#conditionals" underline="hover" sx={{ cursor: 'pointer' }} onClick={(e) => { e.preventDefault(); document.getElementById('conditionals')?.scrollIntoView({ behavior: 'smooth' }); }}>Show Information Only When Needed</Link></li>
                      <li><Link href="#loops" underline="hover" sx={{ cursor: 'pointer' }} onClick={(e) => { e.preventDefault(); document.getElementById('loops')?.scrollIntoView({ behavior: 'smooth' }); }}>Create Lists Automatically</Link></li>
                      <li><Link href="#date-formatting" underline="hover" sx={{ cursor: 'pointer' }} onClick={(e) => { e.preventDefault(); document.getElementById('date-formatting')?.scrollIntoView({ behavior: 'smooth' }); }}>Format Dates and Times</Link></li>
                    </Box>
                  </Grid2>
                  <Grid2 size={6}>
                    <Typography variant="subtitle2" sx={{ color: '#666', fontWeight: 500, mb: 1 }}>
                      Advanced Features
                    </Typography>
                    <Box component="ul" sx={{ m: 0, pl: 3, '& li': { fontSize: '0.875rem', mb: 0.5 } }}>
                      <li><Link href="#with-statement" underline="hover" sx={{ cursor: 'pointer' }} onClick={(e) => { e.preventDefault(); document.getElementById('with-statement')?.scrollIntoView({ behavior: 'smooth' }); }}>Handle Missing Data Safely</Link></li>
                      <li><Link href="#index-function" underline="hover" sx={{ cursor: 'pointer' }} onClick={(e) => { e.preventDefault(); document.getElementById('index-function')?.scrollIntoView({ behavior: 'smooth' }); }}>Get Specific Items from Lists</Link></li>
                      <li><Link href="#clean-lists" underline="hover" sx={{ cursor: 'pointer' }} onClick={(e) => { e.preventDefault(); document.getElementById('clean-lists')?.scrollIntoView({ behavior: 'smooth' }); }}>Create Professional-Looking Lists</Link></li>
                      <li><Link href="#whitespace-control" underline="hover" sx={{ cursor: 'pointer' }} onClick={(e) => { e.preventDefault(); document.getElementById('whitespace-control')?.scrollIntoView({ behavior: 'smooth' }); }}>Remove Extra Spaces and Line Breaks</Link></li>
                    </Box>
                  </Grid2>
                  <Grid2 size={12}>
                    <Typography variant="subtitle2" sx={{ color: '#666', fontWeight: 500, mb: 1, mt: 2 }}>
                      Reference
                    </Typography>
                    <Box component="ul" sx={{ m: 0, pl: 3, '& li': { fontSize: '0.875rem', mb: 0.5 } }}>
                      <li><Link href="#functions-reference" underline="hover" sx={{ cursor: 'pointer' }} onClick={(e) => { e.preventDefault(); document.getElementById('functions-reference')?.scrollIntoView({ behavior: 'smooth' }); }}>All Available Functions Reference</Link></li>
                    </Box>
                  </Grid2>
                </Grid2>
              </Box>

              <Box sx={{ mt: 2 }} id="basic-template">
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
                  1. Create Your First XML Document (Basic Template)
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                  Start here to learn how templates work. Templates are like fill-in-the-blank forms where the system automatically fills in your data. This example shows the basics of creating an XML document from your data.
                </Typography>
                <Grid2 container spacing={2}>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e8f5e8', border: '1px solid #4caf50', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#2e7d32', fontWeight: 'bold', mb: 1 }}>
                        📥 INPUT DATA
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "report": {
    "id": "RPT-001",
    "title": "Incident Report",
    "date": "2024-07-14T10:30:00Z"
  }
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#fff3e0', border: '1px solid #ff9800', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#ef6c00', fontWeight: 'bold', mb: 1 }}>
                        📝 TEMPLATE
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`<?xml version="1.0" encoding="UTF-8"?>
<Report>
  <ID>{{.report.id}}</ID>
  <Title>{{.report.title}}</Title>
  <Date>{{.report.date}}</Date>
</Report>`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e3f2fd', border: '1px solid #2196f3', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#1976d2', fontWeight: 'bold', mb: 1 }}>
                        📤 OUTPUT
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`<?xml version="1.0" encoding="UTF-8"?>
<Report>
  <ID>RPT-001</ID>
  <Title>Incident Report</Title>
  <Date>2024-07-14T10:30:00Z</Date>
</Report>`}
                      </Typography>
                    </Paper>
                  </Grid2>
                </Grid2>
              </Box>
              
              <Box sx={{ mt: 3 }} id="template-functions">
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
                  2. Make Text Look Right (Template Functions)
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                  Change how your text appears in the final document. Make names UPPERCASE, provide default values for missing data, or clean up extra spaces. These functions help ensure your output looks professional.
                </Typography>
                <Grid2 container spacing={2}>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e8f5e8', border: '1px solid #4caf50', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#2e7d32', fontWeight: 'bold', mb: 1 }}>
                        📥 INPUT DATA
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "person": {
    "name": "  john doe  ",
    "age": "",
    "email": "<EMAIL>"
  }
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#fff3e0', border: '1px solid #ff9800', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#ef6c00', fontWeight: 'bold', mb: 1 }}>
                        📝 TEMPLATE
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`<Person>
  <Name>{{.person.name | trim | upper}}</Name>
  <Age>{{.person.age | default "Unknown"}}</Age>
  <Email>{{.person.email | lower}}</Email>
  <Generated>{{now | formatDate "2006-01-02 15:04:05"}}</Generated>
</Person>`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e3f2fd', border: '1px solid #2196f3', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#1976d2', fontWeight: 'bold', mb: 1 }}>
                        📤 OUTPUT
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`<Person>
  <Name>JOHN DOE</Name>
  <Age>Unknown</Age>
  <Email><EMAIL></Email>
  <Generated>2024-07-14 10:30:45</Generated>
</Person>`}
                      </Typography>
                    </Paper>
                  </Grid2>
                </Grid2>
              </Box>
              
              <Box sx={{ mt: 3 }} id="conditionals">
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
                  3. Show Information Only When Needed (Conditionals)
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                  Control what appears in your document based on your data. For example, only show a phone number if one exists, or display different text based on a status. Perfect for creating clean documents without empty fields.
                </Typography>
                <Grid2 container spacing={2}>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e8f5e8', border: '1px solid #4caf50', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#2e7d32', fontWeight: 'bold', mb: 1 }}>
                        📥 INPUT DATA
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "incident": {
    "severity": "high",
    "resolved": false
  }
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#fff3e0', border: '1px solid #ff9800', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#ef6c00', fontWeight: 'bold', mb: 1 }}>
                        📝 TEMPLATE
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`<Incident>
  <Priority>
    {{- if eq .incident.severity "high"}}
    <Code>1</Code>
    <Description>URGENT</Description>
    {{- else if eq .incident.severity "medium"}}
    <Code>2</Code>
    <Description>NORMAL</Description>
    {{- else}}
    <Code>3</Code>
    <Description>LOW</Description>
    {{- end}}
  </Priority>
  <Status>{{if .incident.resolved}}CLOSED{{else}}OPEN{{end}}</Status>
</Incident>`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e3f2fd', border: '1px solid #2196f3', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#1976d2', fontWeight: 'bold', mb: 1 }}>
                        📤 OUTPUT
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`<Incident>
  <Priority>
    <Code>1</Code>
    <Description>URGENT</Description>
  </Priority>
  <Status>OPEN</Status>
</Incident>`}
                      </Typography>
                    </Paper>
                  </Grid2>
                </Grid2>
              </Box>
              
              <Box sx={{ mt: 3 }} id="loops">
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
                  4. Create Lists Automatically (Loops)
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                  Turn a list of people, vehicles, or incidents into formatted XML entries automatically. Instead of manually creating each entry, let the system generate them all for you. Essential for witness lists, evidence inventories, and more.
                </Typography>
                <Grid2 container spacing={2}>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e8f5e8', border: '1px solid #4caf50', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#2e7d32', fontWeight: 'bold', mb: 1 }}>
                        📥 INPUT DATA
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "witnesses": [
    {"name": "Alice", "phone": "555-0001"},
    {"name": "Bob", "phone": "555-0002"}
  ]
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#fff3e0', border: '1px solid #ff9800', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#ef6c00', fontWeight: 'bold', mb: 1 }}>
                        📝 TEMPLATE
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`<WitnessList>
  {{- range $index, $witness := .witnesses}}
  <Witness sequence="{{add $index 1}}">
    <Name>{{$witness.name | upper}}</Name>
    <Phone>{{$witness.phone}}</Phone>
  </Witness>
  {{- end}}
</WitnessList>`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e3f2fd', border: '1px solid #2196f3', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#1976d2', fontWeight: 'bold', mb: 1 }}>
                        📤 OUTPUT
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`<WitnessList>
  <Witness sequence="1">
    <Name>ALICE</Name>
    <Phone>555-0001</Phone>
  </Witness>
  <Witness sequence="2">
    <Name>BOB</Name>
    <Phone>555-0002</Phone>
  </Witness>
</WitnessList>`}
                      </Typography>
                    </Paper>
                  </Grid2>
                </Grid2>
              </Box>
              
              <Box sx={{ mt: 3 }} id="date-formatting">
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
                  5. Format Dates and Times
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                  Convert dates and times to match your agency&apos;s requirements. Also learn how to add today&apos;s date automatically to your reports. Essential for compliance and proper documentation.
                </Typography>
                <Grid2 container spacing={2}>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e8f5e8', border: '1px solid #4caf50', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#2e7d32', fontWeight: 'bold', mb: 1 }}>
                        📥 INPUT DATA
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "incident": {
    "occurredAt": "2024-07-14T15:30:00Z"
  }
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#fff3e0', border: '1px solid #ff9800', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#ef6c00', fontWeight: 'bold', mb: 1 }}>
                        📝 TEMPLATE
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`<Incident>
  <Date>{{.incident.occurredAt | formatDate "2006-01-02"}}</Date>
  <Time>{{.incident.occurredAt | formatDate "15:04:05"}}</Time>
  <DateTime>{{.incident.occurredAt | formatDate "01/02/2006 3:04 PM"}}</DateTime>
  <Generated>{{now | formatDate "2006-01-02T15:04:05Z"}}</Generated>
</Incident>`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e3f2fd', border: '1px solid #2196f3', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#1976d2', fontWeight: 'bold', mb: 1 }}>
                        📤 OUTPUT
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`<Incident>
  <Date>2024-07-14</Date>
  <Time>15:30:00</Time>
  <DateTime>07/14/2024 3:30 PM</DateTime>
  <Generated>2024-07-14T10:30:45Z</Generated>
</Incident>`}
                      </Typography>
                    </Paper>
                  </Grid2>
                </Grid2>
              </Box>
              
              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
                  6. NIBRS XML Example
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                  Real-world example of NIBRS (National Incident-Based Reporting System) XML generation combining namespaces, loops, and formatting.
                </Typography>
                <Grid2 container spacing={2}>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e8f5e8', border: '1px solid #4caf50', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#2e7d32', fontWeight: 'bold', mb: 1 }}>
                        📥 INPUT DATA
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "agency": {"ori": "TX0000000"},
  "incident": {
    "number": "2024-001",
    "date": "2024-07-14T10:30:00Z"
  },
  "victims": [{
    "name": "John Doe",
    "age": 35,
    "sex": "M"
  }]
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#fff3e0', border: '1px solid #ff9800', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#ef6c00', fontWeight: 'bold', mb: 1 }}>
                        📝 TEMPLATE
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`<?xml version="1.0" encoding="UTF-8"?>
<nibrs:Submission xmlns:nibrs="http://fbi.gov/cjis/nibrs/4.1">
  <nibrs:ReportHeader>
    <nibrs:ORI>{{.agency.ori}}</nibrs:ORI>
    <nibrs:ReportDate>{{now | formatDate "2006-01-02"}}</nibrs:ReportDate>
  </nibrs:ReportHeader>
  <nibrs:Incident>
    <nibrs:IncidentNumber>{{.incident.number}}</nibrs:IncidentNumber>
    <nibrs:IncidentDate>{{.incident.date | formatDate "2006-01-02"}}</nibrs:IncidentDate>
    {{- range $index, $victim := .victims}}
    <nibrs:Victim sequence="{{printf "%02d" (add $index 1)}}">
      <nibrs:Name>{{$victim.name | upper}}</nibrs:Name>
      <nibrs:Age>{{$victim.age}}</nibrs:Age>
      <nibrs:Sex>{{$victim.sex}}</nibrs:Sex>
    </nibrs:Victim>
    {{- end}}
  </nibrs:Incident>
</nibrs:Submission>`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e3f2fd', border: '1px solid #2196f3', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#1976d2', fontWeight: 'bold', mb: 1 }}>
                        📤 OUTPUT
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`<?xml version="1.0" encoding="UTF-8"?>
<nibrs:Submission xmlns:nibrs="http://fbi.gov/cjis/nibrs/4.1">
  <nibrs:ReportHeader>
    <nibrs:ORI>TX0000000</nibrs:ORI>
    <nibrs:ReportDate>2024-07-14</nibrs:ReportDate>
  </nibrs:ReportHeader>
  <nibrs:Incident>
    <nibrs:IncidentNumber>2024-001</nibrs:IncidentNumber>
    <nibrs:IncidentDate>2024-07-14</nibrs:IncidentDate>
    <nibrs:Victim sequence="01">
      <nibrs:Name>JOHN DOE</nibrs:Name>
      <nibrs:Age>35</nibrs:Age>
      <nibrs:Sex>M</nibrs:Sex>
    </nibrs:Victim>
  </nibrs:Incident>
</nibrs:Submission>`}
                      </Typography>
                    </Paper>
                  </Grid2>
                </Grid2>
              </Box>
              
              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
                  7. Attributes
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                  Add attributes to XML elements dynamically. Supports conditional attributes based on data presence.
                </Typography>
                <Grid2 container spacing={2}>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e8f5e8', border: '1px solid #4caf50', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#2e7d32', fontWeight: 'bold', mb: 1 }}>
                        📥 INPUT DATA
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "report": {
    "id": "RPT-2024-001",
    "type": "incident",
    "priority": "high",
    "officer": {
      "id": "OFC-123",
      "name": "Det. Smith"
    }
  }
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#fff3e0', border: '1px solid #ff9800', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#ef6c00', fontWeight: 'bold', mb: 1 }}>
                        📝 TEMPLATE
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`<Report id="{{.report.id}}" type="{{.report.type}}" priority="{{.report.priority}}">
  <Officer id="{{.report.officer.id}}">
    <Name>{{.report.officer.name}}</Name>
  </Officer>
</Report>`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e3f2fd', border: '1px solid #2196f3', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#1976d2', fontWeight: 'bold', mb: 1 }}>
                        📤 OUTPUT
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`<Report id="RPT-2024-001" type="incident" priority="high">
  <Officer id="OFC-123">
    <Name>Det. Smith</Name>
  </Officer>
</Report>`}
                      </Typography>
                    </Paper>
                  </Grid2>
                </Grid2>
              </Box>
              
              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
                  8. Advanced Functions
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                  Additional powerful functions for complex template logic and data manipulation.
                </Typography>
                <Grid2 container spacing={2}>
                  <Grid2 size={6}>
                    <Paper sx={{ p: 2, bgcolor: '#f3e5f5', border: '1px solid #9c27b0', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#7b1fa2', fontWeight: 'bold', mb: 1 }}>
                        📊 len - Count Elements
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`INPUT:
{"witnesses": ["Alice", "Bob", "Charlie"], "evidence": []}

TEMPLATE:
<Summary>
  <WitnessCount>{{len .witnesses}}</WitnessCount>
  <EvidenceCount>{{len .evidence}}</EvidenceCount>
  {{- if gt (len .witnesses) 2}}
  <MultipleWitnesses>true</MultipleWitnesses>
  {{- end}}
</Summary>

OUTPUT:
<Summary>
  <WitnessCount>3</WitnessCount>
  <EvidenceCount>0</EvidenceCount>
  <MultipleWitnesses>true</MultipleWitnesses>
</Summary>`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={6}>
                    <Paper sx={{ p: 2, bgcolor: '#e8f5e8', border: '1px solid #4caf50', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#2e7d32', fontWeight: 'bold', mb: 1 }}>
                        📝 printf - Format Strings
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`INPUT:
{"caseNumber": 2024, "department": "Robbery", "percentage": 95.5}

TEMPLATE:
<Case>
  <CaseID>{{printf "CASE-%04d" .caseNumber}}</CaseID>
  <DeptCode>{{printf "%.3s" .department}}</DeptCode>
  <ClearanceRate>{{printf "%.1f%%" .percentage}}</ClearanceRate>
</Case>

OUTPUT:
<Case>
  <CaseID>CASE-2024</CaseID>
  <DeptCode>Rob</DeptCode>
  <ClearanceRate>95.5%</ClearanceRate>
</Case>`}
                      </Typography>
                    </Paper>
                  </Grid2>
                </Grid2>
              </Box>
              
              <Box sx={{ mt: 3 }} id="with-statement">
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
                  9. Handle Missing Data Safely (with Statement)
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                  Prevent errors when data might be missing. This feature checks if data exists before trying to use it, and can show alternative content when data is not available. Perfect for optional fields.
                </Typography>
                <Grid2 container spacing={2}>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e8f5e8', border: '1px solid #4caf50', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#2e7d32', fontWeight: 'bold', mb: 1 }}>
                        📥 INPUT DATA
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "reports": [
    {"report": {"id": "123", "title": "Incident"}},
    null
  ],
  "emptyReports": []
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#fff3e0', border: '1px solid #ff9800', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#ef6c00', fontWeight: 'bold', mb: 1 }}>
                        📝 TEMPLATE
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`<Document>
  {{- with index .reports 0}}
  <Report>
    <ID>{{.report.id}}</ID>
    <Title>{{.report.title}}</Title>
  </Report>
  {{- else}}
  <NoReportFound/>
  {{- end}}
  
  {{- with index .emptyReports 0}}
  <FirstEmpty>{{.}}</FirstEmpty>
  {{- else}}
  <EmptyArray/>
  {{- end}}
</Document>`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e3f2fd', border: '1px solid #2196f3', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#1976d2', fontWeight: 'bold', mb: 1 }}>
                        📤 OUTPUT
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`<Document>
  <Report>
    <ID>123</ID>
    <Title>Incident</Title>
  </Report>
  <EmptyArray/>
</Document>`}
                      </Typography>
                    </Paper>
                  </Grid2>
                </Grid2>
              </Box>
              
              <Box sx={{ mt: 3 }} id="index-function">
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
                  10. Get Specific Items from Lists (index Function)
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                  Pick out specific items from your lists by their position. Get the first witness, the lead officer, or any other specific item you need. Think of it like selecting item #1, #2, etc. from a numbered list.
                </Typography>
                <Grid2 container spacing={2}>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e8f5e8', border: '1px solid #4caf50', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#2e7d32', fontWeight: 'bold', mb: 1 }}>
                        📥 INPUT DATA
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "officers": ["Smith", "Johnson", "Williams"],
  "badges": [101, 102, 103]
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#fff3e0', border: '1px solid #ff9800', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#ef6c00', fontWeight: 'bold', mb: 1 }}>
                        📝 TEMPLATE
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`<Assignment>
  <LeadOfficer>{{index .officers 0}}</LeadOfficer>
  <SecondOfficer>{{index .officers 1}}</SecondOfficer>
  <LeadBadge>{{index .badges 0}}</LeadBadge>
</Assignment>`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e3f2fd', border: '1px solid #2196f3', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#1976d2', fontWeight: 'bold', mb: 1 }}>
                        📤 OUTPUT
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`<Assignment>
  <LeadOfficer>Smith</LeadOfficer>
  <SecondOfficer>Johnson</SecondOfficer>
  <LeadBadge>101</LeadBadge>
</Assignment>`}
                      </Typography>
                    </Paper>
                  </Grid2>
                </Grid2>
              </Box>
              
              <Box sx={{ mt: 3 }} id="clean-lists">
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
                  11. Create Professional-Looking Lists
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                  Turn multiple items into clean, formatted lists with proper separators. Create comma-separated tag lists, officer assignments with &quot;&amp;&quot; between names, or any other professional format you need.
                </Typography>
                <Grid2 container spacing={2}>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e8f5e8', border: '1px solid #4caf50', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#2e7d32', fontWeight: 'bold', mb: 1 }}>
                        📥 INPUT DATA
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`{
  "tags": ["urgent", "robbery", "armed"],
  "officers": ["Smith", "Johnson", "Williams"]
}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#fff3e0', border: '1px solid #ff9800', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#ef6c00', fontWeight: 'bold', mb: 1 }}>
                        📝 TEMPLATE
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`<Report>
  <Tags>{{range $i, $tag := .tags}}{{if $i}}, {{end}}{{$tag | upper}}{{end}}</Tags>
  <AssignedTo>{{range $i, $name := .officers}}{{if $i}} &amp; {{end}}{{$name}}{{end}}</AssignedTo>
</Report>`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={4}>
                    <Paper sx={{ p: 2, bgcolor: '#e3f2fd', border: '1px solid #2196f3', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#1976d2', fontWeight: 'bold', mb: 1 }}>
                        📤 OUTPUT
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`<Report>
  <Tags>URGENT, ROBBERY, ARMED</Tags>
  <AssignedTo>Smith &amp; Johnson &amp; Williams</AssignedTo>
</Report>`}
                      </Typography>
                    </Paper>
                  </Grid2>
                </Grid2>
              </Box>
              
              <Box sx={{ mt: 3 }} id="whitespace-control">
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
                  12. Remove Extra Spaces and Line Breaks (Whitespace Control)
                </Typography>
                <Typography variant="body2" sx={{ color: '#666', mb: 2 }}>
                  Make your XML output clean and professional by removing unwanted blank lines and spaces. This feature helps create compact, well-formatted documents that look exactly how you want them.
                </Typography>
                <Grid2 container spacing={2}>
                  <Grid2 size={6}>
                    <Paper sx={{ p: 2, bgcolor: '#fff3e0', border: '1px solid #ff9800', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#ef6c00', fontWeight: 'bold', mb: 1 }}>
                        ❌ WITHOUT WHITESPACE CONTROL
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`TEMPLATE:
<Persons>
  {{range .persons}}
  <Person>
    {{.name}}
  </Person>
  {{end}}
</Persons>

OUTPUT (extra blank lines):
<Persons>
  
  <Person>
    John
  </Person>
  
  <Person>
    Jane
  </Person>
  
</Persons>`}
                      </Typography>
                    </Paper>
                  </Grid2>
                  <Grid2 size={6}>
                    <Paper sx={{ p: 2, bgcolor: '#e8f5e8', border: '1px solid #4caf50', height: '100%' }}>
                      <Typography variant="subtitle2" sx={{ color: '#2e7d32', fontWeight: 'bold', mb: 1 }}>
                        ✅ WITH WHITESPACE CONTROL
                      </Typography>
                      <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap', fontSize: '0.75rem' }}>
{`TEMPLATE:
<Persons>
  {{- range .persons}}
  <Person>
    {{- .name -}}
  </Person>
  {{- end}}
</Persons>

OUTPUT (clean):
<Persons>
  <Person>John</Person>
  <Person>Jane</Person>
</Persons>

RULES:
• {{- removes whitespace before the tag
• -}} removes whitespace after the tag
• Use both: {{- .value -}}`}
                      </Typography>
                    </Paper>
                  </Grid2>
                </Grid2>
              </Box>
              
              <Box sx={{ mt: 3 }} id="functions-reference">
                <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
                  All Available Functions Reference
                </Typography>
                <Paper sx={{ p: 2, bgcolor: '#f5f5f5', border: '1px solid #e0e0e0' }}>
                  <Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242', whiteSpace: 'pre-wrap' }}>
{`BASIC FUNCTIONS:
• default      - Provide fallback: {{.field | default "N/A"}}
• upper        - Uppercase: {{.field | upper}}
• lower        - Lowercase: {{.field | lower}}
• trim         - Remove spaces: {{.field | trim}}
• formatDate   - Format date: {{.field | formatDate "2006-01-02"}}
• now          - Current time: {{now | formatDate "15:04:05"}}
• add          - Add numbers: {{add .number 1}}

ADVANCED FUNCTIONS:
• len          - Count items: {{len .array}}
• index        - Get array item: {{index .array 0}}
• printf       - Format output: {{printf "%04d" .number}}
                 Formats: %s (string), %d (int), %.0f (float no decimals),
                 %02d (2-digit), %04d (4-digit), %.2f (2 decimals)
• with         - Safe navigation: {{with .field}}...{{end}}
• range        - Loop arrays: {{range .items}}...{{end}}

COMPARISON FUNCTIONS:
• eq           - Equal: {{if eq .x .y}}
• ne           - Not equal: {{if ne .x .y}}
• gt           - Greater than: {{if gt .x .y}}
• ge           - Greater or equal: {{if ge .x .y}}
• lt           - Less than: {{if lt .x .y}}
• le           - Less or equal: {{if le .x .y}}

LOGICAL OPERATORS:
• and          - Logical AND: {{if and .x .y}}
• or           - Logical OR: {{if or .x .y}}
• not          - Logical NOT: {{if not .x}}

PRINTF FORMAT CODES:
• %s           - String
• %d           - Integer
• %04d         - Integer padded with zeros (0024)
• %f           - Float
• %.2f         - Float with 2 decimals
• %.3s         - First 3 characters of string
• %%           - Literal % sign

TIPS:
• Use {{- and -}} to control whitespace
• Chain functions: {{.field | trim | upper | default "UNKNOWN"}}
• Access parent context with $: {{$.parentField}}
• Handle missing data with default function
• Use printf for formatted numbers: {{printf "%02d" .number}}
• Special characters are automatically escaped in XML`}
                  </Typography>
                </Paper>
              </Box>
            </>
          )}
          
          <Box sx={{ mt: 3 }}>
            <Typography variant="subtitle1" gutterBottom sx={{ color: '#424242', fontWeight: 500 }}>
              Backend File Locations
            </Typography>
            <List dense>
              <ListItem>
                <ListItemText 
                  primary={<Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242' }}>services/workflow/internal/etl/templates/nibrs/reports/v2/nibrs.xml.tmpl</Typography>}
                  secondary="NIBRS XML template example"
                />
              </ListItem>
              <ListItem>
                <ListItemText 
                  primary={<Typography variant="body2" sx={{ fontFamily: 'monospace', color: '#424242' }}>services/workflow/internal/etl/templates/nibrs/reports/v2/nibrs_mapping_config.json</Typography>}
                  secondary="NIBRS mapping configuration example"
                />
              </ListItem>
            </List>
          </Box>
        </CardContent>
      </Card>
      </Container>
    </Box>
  );
}