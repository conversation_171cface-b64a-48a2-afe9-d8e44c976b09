"use client";

import { useUpdateEntity } from "@/app/apis/services/workflow/entity/hooks";
import { isArchivedStatus } from "@/app/utils/entityUtils";
import { colors } from "@/design-system/tokens";
import SettingsIcon from "@mui/icons-material/Settings";
import { Box, IconButton, Menu, MenuItem } from "@mui/material";
import { useQueryClient } from "@tanstack/react-query";
import { RecordStatus, UpdateEntityRequest } from "proto/hero/entity/v1/entity_pb";
import React, { useState } from "react";
import DeleteConfirmationDialog from "./DeleteConfirmationDialog";

interface EntityActionsMenuProps {
    onManage?: () => void;
    onDelete?: () => void;
    entityId?: string;
    entityName?: string;
    entityType?: string;
    entity?: any; // The full entity object for updating
}

export default function EntityActionsMenu({
    onManage,
    onDelete,
    entityId,
    entityName = "this entity",
    entityType = "entity",
    entity
}: EntityActionsMenuProps) {
    const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
    const [showDeleteDialog, setShowDeleteDialog] = useState(false);
    const queryClient = useQueryClient();
    const open = Boolean(anchorEl);

    // Determine if the entity is archived using utility function
    const isArchived = entity && isArchivedStatus(entity.status);

    // Use the updateEntity hook
    const updateEntityMutation = useUpdateEntity({
        onSuccess: (data) => {
            console.log("Entity status updated successfully:", data);
            setShowDeleteDialog(false);

            // Force a refetch of the entity data to update the UI
            queryClient.invalidateQueries({ queryKey: ["entity", data.id] });
            queryClient.refetchQueries({ queryKey: ["entity", data.id] });

            // Call the original onDelete callback if provided
            if (onDelete) {
                onDelete();
            }
        },
        onError: (error) => {
            console.error("Error updating entity status:", error);
            console.error("Error details:", {
                message: error.message,
                statusCode: (error as any).statusCode,
                stack: error.stack
            });
            // Show a more detailed error message
            alert(`Failed to update entity status: ${error.message}`);
        },
    });

    const handleClick = (event: React.MouseEvent<HTMLElement>) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleManage = () => {
        handleClose();
        if (onManage) {
            onManage();
        }
    };

    const handleDeleteClick = () => {
        handleClose();
        setShowDeleteDialog(true);
    };

    const handleUnarchive = () => {
        if (!entity) {
            console.error("No entity provided for unarchiving");
            return;
        }



        // Validate required fields
        if (!entity.id) {
            console.error("Entity ID is missing");
            alert("Entity ID is missing. Cannot unarchive entity.");
            return;
        }

        // Create the update request to unarchive the entity
        const unarchiveEntityRequest = {
            entity: {
                id: entity.id,
                status: RecordStatus.ACTIVE,
            },
        } as UpdateEntityRequest;



        // Call the mutation
        updateEntityMutation.mutate(unarchiveEntityRequest);
    };

    const handleDeleteConfirm = () => {
        if (!entity) {
            console.error("No entity provided for archiving");
            return;
        }



        // Validate required fields
        if (!entity.id) {
            console.error("Entity ID is missing");
            alert("Entity ID is missing. Cannot archive entity.");
            return;
        }

        if (!entity.entityType) {
            console.error("Entity type is missing");
            alert("Entity type is missing. Cannot archive entity.");
            return;
        }

        // Create the update request to archive the entity
        const archiveEntityRequest = {
            entity: {
                id: entity.id,
                status: RecordStatus.ARCHIVED,
            },
        } as UpdateEntityRequest;



        // Call the mutation
        updateEntityMutation.mutate(archiveEntityRequest);
    };

    const handleDeleteCancel = () => {
        setShowDeleteDialog(false);
    };

    return (
        <>
            <Box>
                <IconButton
                    onClick={handleClick}
                    size="small"
                    sx={{
                        color: colors.grey[600],
                        "&:hover": {
                            backgroundColor: colors.grey[100],
                        },
                    }}
                >
                    <SettingsIcon fontSize="small" />
                </IconButton>
                <Menu
                    anchorEl={anchorEl}
                    open={open}
                    onClose={handleClose}
                    anchorOrigin={{
                        vertical: "bottom",
                        horizontal: "right",
                    }}
                    transformOrigin={{
                        vertical: "top",
                        horizontal: "right",
                    }}
                    PaperProps={{
                        sx: {
                            mt: 1,
                            minWidth: 120,
                            boxShadow: "0px 4px 20px rgba(0, 0, 0, 0.15)",
                            borderRadius: "8px",
                            border: `1px solid ${colors.grey[200]}`,
                        },
                    }}
                >

                    {/* Show Delete if not archived, Unarchive if archived */}
                    {!isArchived ? (
                        <MenuItem
                            onClick={handleDeleteClick}
                            sx={{
                                padding: "8px 16px",
                                fontSize: "14px",
                                color: colors.rose[600],
                                "&:hover": {
                                    backgroundColor: colors.rose[50],
                                },
                            }}
                        >
                            Delete
                        </MenuItem>
                    ) : (
                        <MenuItem
                            onClick={handleUnarchive}
                            sx={{
                                padding: "8px 16px",
                                fontSize: "14px",
                                color: colors.blue[600],
                                "&:hover": {
                                    backgroundColor: colors.blue[50],
                                },
                            }}
                        >
                            Unarchive
                        </MenuItem>
                    )}
                </Menu>
            </Box>

            <DeleteConfirmationDialog
                open={showDeleteDialog}
                onClose={handleDeleteCancel}
                onConfirm={handleDeleteConfirm}
                entityName={entityName}
                entityType={entityType}
                isLoading={updateEntityMutation.isPending}
            />
        </>
    );
} 