"use client";

import { Button } from "@/design-system/components/Button";
import { colors } from "@/design-system/tokens";
import {
    Box,
    Dialog,
    DialogActions,
    DialogContent,
    DialogTitle,
    Typography as MuiTypography,
} from "@mui/material";

interface DeleteConfirmationDialogProps {
    open: boolean;
    onClose: () => void;
    onConfirm: () => void;
    entityName?: string;
    entityType?: string;
    isLoading?: boolean;
}

export default function DeleteConfirmationDialog({
    open,
    onClose,
    onConfirm,
    entityName = "this entity",
    entityType = "entity",
    isLoading = false,
}: DeleteConfirmationDialogProps) {
    return (
        <Dialog
            open={open}
            onClose={onClose}
            maxWidth="sm"
            fullWidth
            PaperProps={{
                sx: {
                    borderRadius: "12px",
                    boxShadow: "0px 8px 32px rgba(0, 0, 0, 0.12)",
                },
            }}
        >
            <DialogTitle
                sx={{
                    pb: 1,
                    px: 3,
                    pt: 3,
                }}
            >
                <Box
                    sx={{
                        fontFamily: "'Roboto', sans-serif",
                        fontSize: "20px",
                        fontWeight: "700",
                        letterSpacing: "0.25px",
                        lineHeight: "28px",
                        color: colors.grey[900],
                    }}
                >
                    Confirm Deletion
                </Box>
            </DialogTitle>

            <DialogContent sx={{ px: 3, pb: 2 }}>
                <MuiTypography
                    variant="body1"
                    sx={{
                        color: colors.grey[700],
                        lineHeight: 1.5,
                        mb: 1,
                    }}
                >
                    Are you sure you&apos;d like to delete {entityName}?
                </MuiTypography>
                <MuiTypography
                    variant="body2"
                    sx={{
                        color: colors.grey[600],
                        lineHeight: 1.4,
                    }}
                >
                    This will archive the {entityType} record. The data will be preserved for historical purposes but will no longer appear in active searches.
                </MuiTypography>
            </DialogContent>

            <DialogActions
                sx={{
                    px: 3,
                    pb: 3,
                    gap: 2,
                }}
            >
                <Button
                    onClick={onClose}
                    disabled={isLoading}
                    style="outline"
                    color="grey"
                    size="medium"
                    label="Cancel"
                />
                <Button
                    onClick={onConfirm}
                    disabled={isLoading}
                    style="filled"
                    color="rose"
                    size="medium"
                    label={isLoading ? "Archiving..." : "Confirm"}
                />
            </DialogActions>
        </Dialog>
    );
} 