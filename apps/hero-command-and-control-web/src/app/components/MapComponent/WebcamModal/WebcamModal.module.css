.webcamModal {
    position: fixed;
    top: 54px;
    right: 20px;
    width: 500px;
    background: #f7f7f7;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transform: translateX(120%);
    transition: transform 0.3s ease-in-out;
    z-index: 1000;
    border-radius: 8px;
}

.open {
    transform: translateX(0);
}

.closed {
    transform: translateX(120%);
}

.modalContent {
    display: flex;
    flex-direction: column;
    padding: 16px;
}

.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding: 0 4px;
}

.cameraName {
    font-weight: 600;
    font-size: 1.1rem;
    color: #333;
}

.closeButton {
    position: relative;
    background: white;
    color: #666;
    border: none;
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.closeButton:hover {
    background: white;
    transform: scale(1.1);
    color: #333;
}

.closeButton::after {
    content: '×';
    font-size: 24px;
    line-height: 1;
    margin-top: -2px;
}

.cameraPreview {
    width: 100%;
    overflow-y: auto;
    padding: 0;
    background: #f7f7f7;
    border-radius: 8px;
    border: 2px solid #000;
}

.cameraPlayer {
    position: relative;
    width: 100%;
    aspect-ratio: 16 / 9;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0;
    padding: 0;
}