"use client";
import React, { useEffect, useRef, useState } from "react";
import styles from "./WebcamModal.module.css";

type WebcamModalProps = {
    isOpen: boolean;
    onClose: () => void;
};

interface CameraDevice {
    deviceId: string;
    label: string;
}

export const WebcamModal: React.FC<WebcamModalProps> = ({
    isOpen,
    onClose,
}) => {
    const videoRef = useRef<HTMLVideoElement>(null);
    const streamRef = useRef<MediaStream | null>(null);
    const [availableCameras, setAvailableCameras] = useState<CameraDevice[]>([]);
    const [selectedCameraId, setSelectedCameraId] = useState<string>("");
    const [isLoading, setIsLoading] = useState(false);

    // Add refs to track async operations and prevent race conditions
    const currentRequestIdRef = useRef<number>(0);
    const isMountedRef = useRef(true);

    useEffect(() => {
        isMountedRef.current = true;

        if (isOpen) {
            loadAvailableCameras();
        } else {
            stopWebcam();
        }

        // Cleanup function to handle component unmount
        return () => {
            isMountedRef.current = false;
            stopWebcam();
        };
    }, [isOpen]);

    const loadAvailableCameras = async () => {
        try {
            const devices = await navigator.mediaDevices.enumerateDevices();
            const videoDevices = devices
                .filter(device => device.kind === 'videoinput')
                .map(device => ({
                    deviceId: device.deviceId,
                    label: device.label || `Camera ${device.deviceId.slice(0, 8)}...`
                }));

            // Check if component is still mounted before updating state
            if (!isMountedRef.current) return;

            setAvailableCameras(videoDevices);

            // Auto-select the first camera if available
            if (videoDevices.length > 0) {
                setSelectedCameraId(videoDevices[0].deviceId);
                await startWebcam(videoDevices[0].deviceId);
            }
        } catch (error) {
            console.error('Error loading cameras:', error);
            if (isMountedRef.current) {
                alert('Could not access camera devices. Please check permissions.');
                onClose();
            }
        }
    };

    const startWebcam = async (deviceId?: string) => {
        // Increment request ID to invalidate any previous requests
        const requestId = ++currentRequestIdRef.current;

        try {
            setIsLoading(true);

            // Stop any existing stream
            if (streamRef.current) {
                streamRef.current.getTracks().forEach(track => track.stop());
                streamRef.current = null;
            }

            const constraints = {
                video: deviceId ? { deviceId: { exact: deviceId } } : true,
                audio: false
            };

            const stream = await navigator.mediaDevices.getUserMedia(constraints);

            // Check if this request is still valid (not superseded by a newer request)
            if (requestId !== currentRequestIdRef.current || !isMountedRef.current) {
                // This request is outdated or component unmounted, stop the stream
                stream.getTracks().forEach(track => track.stop());
                return;
            }

            if (videoRef.current) {
                videoRef.current.srcObject = stream;
                streamRef.current = stream;
            }

            setIsLoading(false);
        } catch (error) {
            // Don't show error if request was superseded or component unmounted
            if (requestId !== currentRequestIdRef.current || !isMountedRef.current) {
                return;
            }

            console.error('Error accessing webcam:', error);

            if (isMountedRef.current) {
                setIsLoading(false);

                if (error instanceof Error && error.name === 'NotAllowedError') {
                    alert('Camera access denied. Please allow camera permissions and try again.');
                } else if (error instanceof Error && error.name === 'NotFoundError') {
                    alert('No camera found. Please check your camera connection.');
                } else {
                    alert('Could not access webcam. Please check your camera and permissions.');
                }
                onClose();
            }
        }
    };

    const stopWebcam = () => {
        // Invalidate any ongoing requests
        currentRequestIdRef.current++;

        // Stop the current stream
        if (streamRef.current) {
            streamRef.current.getTracks().forEach(track => track.stop());
            streamRef.current = null;
        }

        // Clear video element
        if (videoRef.current) {
            videoRef.current.srcObject = null;
        }
    };

    const handleCameraChange = async (deviceId: string) => {
        setSelectedCameraId(deviceId);
        await startWebcam(deviceId);
    };

    const handleClose = () => {
        stopWebcam();
        onClose();
    };

    return (
        <div
            className={`${styles.webcamModal} ${isOpen ? styles.open : styles.closed
                }`}
        >
            <div className={styles.modalContent}>
                <div className={styles.header}>
                    <div className={styles.cameraName}>
                        Local Webcam
                    </div>
                    <button className={styles.closeButton} onClick={handleClose} />
                </div>

                {/* Camera Selection Dropdown */}
                {availableCameras.length > 1 && (
                    <div style={{ marginBottom: '12px', padding: '0 4px' }}>
                        <select
                            value={selectedCameraId}
                            onChange={(e) => handleCameraChange(e.target.value)}
                            style={{
                                width: '100%',
                                padding: '8px 12px',
                                borderRadius: '4px',
                                border: '1px solid #ccc',
                                fontSize: '14px',
                                backgroundColor: 'white'
                            }}
                        >
                            {availableCameras.map((camera) => (
                                <option key={camera.deviceId} value={camera.deviceId}>
                                    {camera.label}
                                </option>
                            ))}
                        </select>
                    </div>
                )}

                <div className={styles.cameraPreview}>
                    <div className={styles.cameraPlayer}>
                        {isLoading && (
                            <div style={{
                                position: 'absolute',
                                top: '50%',
                                left: '50%',
                                transform: 'translate(-50%, -50%)',
                                color: '#666',
                                fontSize: '16px'
                            }}>
                                Loading camera...
                            </div>
                        )}
                        <video
                            ref={videoRef}
                            autoPlay
                            playsInline
                            style={{
                                width: '100%',
                                height: '100%',
                                objectFit: 'contain',
                                backgroundColor: '#000',
                                borderRadius: '4px',
                                opacity: isLoading ? 0.5 : 1
                            }}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
}; 