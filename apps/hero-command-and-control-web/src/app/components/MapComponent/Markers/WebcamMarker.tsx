import React from "react";
import { MdVideocam } from "react-icons/md";

const WebcamMarker: React.FC = () => {
    const mainColor = "#0EA8EF";
    const isSelected = false;
    const zoomLevel = 0;
    const showDetails = isSelected && zoomLevel > 13;
    const containerColor = isSelected ? `${mainColor}F2` : "#FFFFFF40";
    const iconColor = isSelected ? "#FFFFFFD1" : mainColor;
    const bottomCircleColor = mainColor;
    const containerBorderRadius = showDetails ? "25px" : "40px";
    const shortId = "WC-001";

    const containerTransition =
        "background-color 0.5s ease, border 0.5s ease, border-radius 0.5s ease";
    const textTransition = showDetails
        ? "margin-left 1s ease 1s, opacity 1s ease 1s, max-width 1s ease 1s, max-height 1s ease 1s"
        : "margin-left 1s ease 0s, opacity 1s ease 0s, max-width 1s ease 0s, max-height 1s ease 0s";
    const bottomTextTransition = "opacity 0.5s ease";

    return (
        <div
            style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                lineHeight: 1,
                cursor: "pointer",
            }}
        >
            <div
                style={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: showDetails ? "flex-start" : "center",
                    backgroundColor: containerColor,
                    border: `2px solid ${mainColor}`,
                    borderRadius: containerBorderRadius,
                    marginBottom: "4px",
                    padding: "8px",
                    paddingRight: showDetails ? "12px" : "8px",
                    transition: containerTransition,
                    backdropFilter: "blur(4px)",
                }}
            >
                <MdVideocam size={20} color={iconColor} />

                <div
                    style={{
                        display: "flex",
                        flexDirection: "column",
                        alignItems: "flex-start",
                        marginLeft: showDetails ? "8px" : "0px",
                        opacity: showDetails ? 1 : 0,
                        maxWidth: showDetails ? "200px" : "0px",
                        whiteSpace: "nowrap",
                        overflow: "hidden",
                        transition: textTransition,
                        maxHeight: isSelected && zoomLevel > 13 ? "70px" : "0px",
                    }}
                >
                    {isSelected && (
                        <div
                            style={{
                                color: "#FFFFFF",
                                fontWeight: 600,
                                fontSize: "12px",
                            }}
                        >
                            {shortId}
                        </div>
                    )}
                </div>
            </div>

            <div
                style={{
                    position: "relative",
                    width: "16px",
                    height: "16px",
                    marginBottom: "2px",
                }}
            >
                <div
                    style={{
                        width: "100%",
                        height: "100%",
                        borderRadius: "50%",
                        backgroundColor: "white",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                    }}
                >
                    <div
                        style={{
                            width: "10px",
                            height: "10px",
                            borderRadius: "50%",
                            backgroundColor: bottomCircleColor,
                        }}
                    />
                </div>
            </div>

            <div
                style={{
                    backgroundColor: "#FFFFFF",
                    color: mainColor,
                    padding: "2px 4px",
                    borderRadius: "3px",
                    fontSize: "10px",
                    fontWeight: 500,
                    fontFamily: "Roboto",
                    opacity: isSelected ? 0 : 1,
                    transition: bottomTextTransition,
                }}
            >
                {shortId}
            </div>
        </div>
    );
};

export default WebcamMarker; 