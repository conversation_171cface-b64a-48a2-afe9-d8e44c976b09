import { useListAssets } from "@/app/apis/services/workflow/assets/hooks";
import { useAddOrderUpdate, useCancelOrder, useCreateOrder } from "@/app/apis/services/workflow/orders/hooks";
import { useReport, useUpdateReport } from "@/app/apis/services/workflow/reports/v2/hooks";
import { useOrders } from "@/app/contexts/OrderContext";
import { useDispatcher } from "@/app/contexts/User/DispatcherContext";
import { Button } from "@/design-system/components/Button";
import { Dropdown } from "@/design-system/components/Dropdown";
import { InputType, TextInput } from "@/design-system/components/TextInput";
import { Typography as DSTypography } from "@/design-system/components/Typography";
import { colors } from "@/design-system/tokens";
import {
    Box,
    Dialog,
    DialogActions,
    DialogContent,
    Typography
} from "@mui/material";
import { AssetStatus, AssetType, ListAssetsRequest } from "proto/hero/assets/v2/assets_pb";
import { AddOrderUpdateRequest, CancelOrderRequest, CreateOrderRequest, Order, OrderUpdateEntry } from "proto/hero/orders/v2/orders_pb";
import { Report, ReportStatus, UpdateReportRequest } from "proto/hero/reports/v2/reports_pb";
import React, { useEffect, useState } from "react";

interface Asset {
    id: string;
    name: string;
}

interface ReassignReportPopupProps {
    open: boolean;
    onClose: () => void;
    order?: Order | undefined;
    report?: Report;
    reportId?: string; // Alternative to passing report object
    onSuccess?: () => void;
    isCreateMode?: boolean; // New prop for create mode
    onAssignReport?: (assigneeId: string) => Promise<void>; // New prop for create mode
}

export const ReassignReportPopup: React.FC<ReassignReportPopupProps> = ({
    open,
    onClose,
    order,
    report,
    reportId,
    onSuccess,
    isCreateMode = false,
    onAssignReport,
}) => {
    const [newAssigneeId, setNewAssigneeId] = useState<string>("");
    const [reason, setReason] = useState<string>("");
    const [errors, setErrors] = useState<{
        newAssignee?: string;
        order?: string;
        report?: string;
        cancel?: string;
        auditTrail?: string;
    }>({});
    // Track the original assignee name before reassignment
    const [originalAssigneeName, setOriginalAssigneeName] = useState<string>("");
    // Track reassignment state to ensure proper error handling
    const [reassignmentState, setReassignmentState] = useState<{
        reportUpdated: boolean;
        orderCanceled: boolean;
        orderCreated: boolean;
        auditTrailAdded: boolean;
    }>({
        reportUpdated: false,
        orderCanceled: false,
        orderCreated: false,
        auditTrailAdded: false,
    });
    // Track create mode loading state
    const [isCreatingReport, setIsCreatingReport] = useState(false);

    // Get current user's asset
    const { asset: currentUserAsset } = useDispatcher();

    // Get orders context for refreshing orders after reassignment
    const { refreshOrders } = useOrders();

    // Set default assignee to current user in create mode
    useEffect(() => {
        if (open && isCreateMode && currentUserAsset?.id && !newAssigneeId) {
            setNewAssigneeId(currentUserAsset.id);
        }
    }, [open, isCreateMode, currentUserAsset?.id]);

    // Determine which report ID to use
    const effectiveReportId = report?.id || reportId || order?.reportId;

    // Fetch report data if not provided directly
    const { data: fetchedReport } = useReport(
        effectiveReportId || "",
        undefined, // refetchInterval
        {
            enabled: !!effectiveReportId && !report && !isCreateMode,
        }
    );

    // Use provided report or fetched report
    const reportData = report || fetchedReport;

    // Get assets for reassignment
    const { data: assetsResponse } = useListAssets({
        pageSize: 100,
        pageToken: "",
        type: AssetType.UNSPECIFIED,
        status: AssetStatus.UNSPECIFIED,
        orderBy: "name",
    } as ListAssetsRequest);

    // Helper functions to get assignee names
    const getCurrentAssigneeName = () => {
        if (!reportData?.authorAssetId || !assetsResponse?.assets) return "Unassigned";
        const currentAssignee = assetsResponse.assets.find(asset => asset.id === reportData.authorAssetId);
        return currentAssignee?.name || "Unassigned";
    };

    const getNewAssigneeName = (assetId: string) => {
        if (!assetsResponse?.assets) return "Unassigned";
        const newAssignee = assetsResponse.assets.find(asset => asset.id === assetId);
        return newAssignee?.name || "Unassigned";
    };

    // Validation helper functions
    const validateReportState = () => {
        if (!reportData) return null;

        // Check if report can be reassigned
        if (reportData.status === ReportStatus.APPROVED) {
            return "Cannot reassign approved reports";
        }

        if (reportData.status === ReportStatus.REJECTED) {
            return "Cannot reassign rejected reports";
        }

        if (reportData.status === ReportStatus.CANCELLED) {
            return "Cannot reassign cancelled reports";
        }

        return null;
    };

    const validateOrderTypeForAsset = (assetId: string, orderType: any) => {
        // Basic validation - in a real implementation, you might check asset capabilities
        // For now, we'll assume all assets can receive report orders
        if (!assetId) return false;

        // You could add more sophisticated validation here based on asset type, permissions, etc.
        return true;
    };

    // Check if reassignment is fully complete
    const isReassignmentComplete = () => {
        if (!order?.id) {
            // No order to manage, only report update is needed
            return reassignmentState.reportUpdated;
        }
        // Full flow: report update + order cancel + order create + audit trail
        return reassignmentState.reportUpdated &&
            reassignmentState.orderCanceled &&
            reassignmentState.orderCreated &&
            reassignmentState.auditTrailAdded;
    };

    // Helper function to reset form and close popup
    const resetFormAndClose = () => {
        setNewAssigneeId("");
        setReason("");
        setErrors({});
        setOriginalAssigneeName("");
        setReassignmentState({
            reportUpdated: false,
            orderCanceled: false,
            orderCreated: false,
            auditTrailAdded: false,
        });
        onClose();
    };

    // Add order update mutation for audit trail
    const addOrderUpdateMutation = useAddOrderUpdate({
        onSuccess: () => {
            console.log("Audit trail added successfully");
            setReassignmentState(prev => {
                const newState = { ...prev, auditTrailAdded: true };

                // Check if reassignment is complete after updating state
                if (newState.reportUpdated && newState.orderCanceled && newState.orderCreated && newState.auditTrailAdded) {
                    // Refresh orders to immediately update the UI
                    refreshOrders();

                    // Reset form and close immediately since reassignment is complete
                    setNewAssigneeId("");
                    setReason("");
                    setErrors({});
                    setOriginalAssigneeName("");
                    setReassignmentState({
                        reportUpdated: false,
                        orderCanceled: false,
                        orderCreated: false,
                        auditTrailAdded: false,
                    });
                    onClose();
                    onSuccess?.();
                }

                return newState;
            });
        },
        onError: (error) => {
            console.error("Error adding order update entry:", error);
            setReassignmentState(prev => {
                const newState = { ...prev, auditTrailAdded: false };

                // If audit trail fails but order was created, we should still consider it a partial success
                // The order exists and is functional, just missing the audit trail
                if (newState.reportUpdated && newState.orderCanceled && newState.orderCreated) {
                    console.warn("Reassignment completed but audit trail failed. Order is functional but missing audit entry.");

                    // Refresh orders to immediately update the UI even though audit trail failed
                    refreshOrders();

                    // Still close the popup as the core functionality worked
                    setTimeout(() => {
                        resetFormAndClose();
                        onSuccess?.();
                    }, 2000); // Give user time to see the warning
                }

                return newState;
            });
            setErrors(prev => ({ ...prev, auditTrail: "Failed to add audit trail. Please try again." }));
        },
    });

    // Helper function to handle rollback on partial failures
    const handlePartialFailure = (failedStep: string, error: any) => {
        console.error(`Reassignment failed at step: ${failedStep}`, error);

        // Reset the state for the failed step
        setReassignmentState(prev => ({ ...prev, [failedStep]: false }));

        // Map failed steps to the correct error keys expected by the UI
        const errorKeyMapping: { [key: string]: string } = {
            reportUpdated: "report",
            orderCanceled: "cancel",
            orderCreated: "order",
            auditTrailAdded: "auditTrail"
        };

        // Set appropriate error message
        const errorMessages: { [key: string]: string } = {
            reportUpdated: "Failed to update report assignment. Please try again.",
            orderCanceled: "Failed to cancel existing order. Please try again.",
            orderCreated: "Failed to create new order. Please try again.",
            auditTrailAdded: "Failed to add audit trail. Please try again."
        };

        const errorKey = errorKeyMapping[failedStep] || failedStep.toLowerCase();
        setErrors(prev => ({ ...prev, [errorKey]: errorMessages[failedStep] || "An unexpected error occurred." }));

        // If we're in a partially completed state, we might want to attempt cleanup
        // For now, we'll just show the error and let the user retry
    };

    // Enhanced error handling for each mutation
    const updateReportMutation = useUpdateReport({
        onSuccess: (updatedReport) => {
            console.log("Report reassigned successfully:", updatedReport);
            setReassignmentState(prev => ({ ...prev, reportUpdated: true }));

            // If there's no order to manage, reassignment is complete
            if (!order?.id) {
                // Refresh orders to immediately update the UI
                refreshOrders();

                // Reset form and close immediately since no order management needed
                setNewAssigneeId("");
                setReason("");
                setErrors({});
                setOriginalAssigneeName("");
                setReassignmentState({
                    reportUpdated: false,
                    orderCanceled: false,
                    orderCreated: false,
                    auditTrailAdded: false,
                });
                onClose();
                onSuccess?.();
            } else {
                // If there's an associated order, cancel it and create a new one
                cancelOrderMutation.mutate({
                    id: order.id,
                    reason: `Report reassigned to ${getNewAssigneeName(newAssigneeId)} - ${reason || "No reason given"}`,
                } as CancelOrderRequest);
            }
        },
        onError: (error) => {
            handlePartialFailure("reportUpdated", error);
        },
    });

    // Cancel order mutation (to cancel the existing order)
    const cancelOrderMutation = useCancelOrder({
        onSuccess: (canceledOrder) => {
            console.log("Order canceled successfully:", canceledOrder);
            setReassignmentState(prev => ({ ...prev, orderCanceled: true }));

            // Create a new order for the new assignee
            if (order) {
                createOrderMutation.mutate({
                    order: {
                        situationId: order.situationId,
                        assetId: newAssigneeId,
                        reportId: reportData?.id,
                        type: order.type,
                        status: "ORDER_STATUS_CREATED",
                        instructions: order.instructions,
                        priority: order.priority,
                        additionalInfoJson: order.additionalInfoJson,
                        typeSpecificStatus: order.typeSpecificStatus,
                        permissions: order.permissions,
                        // Remove manual timestamp setting - let backend handle it
                    } as unknown as Order,
                } as CreateOrderRequest);
            }
        },
        onError: (error) => {
            handlePartialFailure("orderCanceled", error);
        },
    });

    // Create order mutation (to create a new order for the new assignee)
    const createOrderMutation = useCreateOrder({
        onSuccess: (createResponse) => {
            console.log("New order created successfully:", createResponse);
            if (createResponse.order?.id) {
                setReassignmentState(prev => ({ ...prev, orderCreated: true }));

                // Refresh orders to immediately update the UI with the new order
                refreshOrders();

                // Add audit trail entry to the new order
                addOrderUpdateMutation.mutate({
                    id: createResponse.order.id,
                    update: {
                        message: `Report ownership transferred from ${originalAssigneeName} to ${getNewAssigneeName(newAssigneeId)} - Reason: ${reason || "None given."}`,
                        timestamp: new Date().toISOString(),
                        updateSource: "UPDATE_SOURCE_HUMAN_OPERATOR",
                    } as unknown as OrderUpdateEntry,
                } as unknown as AddOrderUpdateRequest);
            } else {
                handlePartialFailure("orderCreated", new Error("Order creation response missing order ID"));
            }
        },
        onError: (error) => {
            handlePartialFailure("orderCreated", error);
        },
    });

    const handleReassign = async () => {
        // Validate required fields
        const newErrors: { newAssignee?: string; report?: string } = {};

        if (!newAssigneeId) {
            newErrors.newAssignee = "New assignee is required";
        }

        // Don't allow reassigning to the same person (only in reassign mode)
        if (!isCreateMode && newAssigneeId === reportData?.authorAssetId) {
            newErrors.newAssignee = "Cannot reassign to the current assignee";
        }

        // Validate report state
        const reportStateError = validateReportState();
        if (reportStateError) {
            newErrors.report = reportStateError;
        }

        // Validate order type for new assignee (only if there's an order)
        if (order && !validateOrderTypeForAsset(newAssigneeId, order.type)) {
            newErrors.newAssignee = "Selected assignee cannot receive this type of order";
        }

        setErrors(newErrors);

        if (Object.keys(newErrors).length === 0) {
            if (isCreateMode && onAssignReport) {
                // Create mode - call the parent's create and assign function
                try {
                    setIsCreatingReport(true);
                    await onAssignReport(newAssigneeId);
                    // Only close popup after successful completion
                    resetFormAndClose();
                } catch (error) {
                    console.error("Error creating and assigning report:", error);
                    // Don't close popup on error - let user see the error
                    // You might want to show an error message to the user here
                } finally {
                    setIsCreatingReport(false);
                }
            } else if (reportData?.id) {
                // Capture original assignee name before reassignment
                setOriginalAssigneeName(getCurrentAssigneeName());

                // Reassign mode - update the existing report
                updateReportMutation.mutate({
                    report: {
                        id: reportData.id,
                        authorAssetId: newAssigneeId,
                        assignedAt: new Date().toISOString(),
                    } as Report,
                } as UpdateReportRequest);
            }
        }
    };

    const handleClose = () => {
        resetFormAndClose();
    };

    // Transform assets for the popup
    const assets = assetsResponse?.assets?.map(asset => ({
        id: asset.id,
        name: asset.name,
    })) || [];

    const currentAssignee = getCurrentAssigneeName();
    const isLoading = updateReportMutation.isPending || cancelOrderMutation.isPending || createOrderMutation.isPending || addOrderUpdateMutation.isPending || isCreatingReport;

    return (
        <Dialog
            open={open}
            onClose={handleClose}
            maxWidth="sm"
            fullWidth
            PaperProps={{
                sx: {
                    borderRadius: "12px",
                    boxShadow: "0px 8px 32px rgba(0, 0, 0, 0.12)",
                },
            }}
        >
            <Box
                sx={{
                    borderBottom: `1px solid ${colors.grey[200]}`,
                    pb: 2,
                    px: 3,
                    pt: 3,
                }}
            >
                <DSTypography style="h2" color={colors.grey[900]}>
                    {isCreateMode ? "Assign New Report" : "Reassign Report"}
                </DSTypography>
            </Box>

            <DialogContent sx={{ pt: 3, pb: 2 }}>
                <Box sx={{ display: "flex", flexDirection: "column", gap: 3 }}>
                    {/* Current Assignee Section - only show in reassign mode */}
                    {!isCreateMode && (
                        <Box>
                            <TextInput
                                title="Current Assignee"
                                value={currentAssignee}
                                readOnly={true}
                            />
                        </Box>
                    )}

                    {/* New Assignee Section */}
                    <Box>
                        <div style={{ width: '300px' }}>
                            <Dropdown
                                enableSearch
                                onChange={(value) => {
                                    setNewAssigneeId(value || "");
                                    if (errors.newAssignee) {
                                        setErrors({ ...errors, newAssignee: undefined });
                                    }
                                }}
                                options={assets.map((asset) => ({
                                    label: asset.id === currentUserAsset?.id ? `${asset.name} (You)` : asset.name,
                                    value: asset.id
                                }))}
                                placeholder={isCreateMode ? "Select assignee" : "Select new assignee"}
                                title={isCreateMode ? "Assignee" : "New Assignee"}
                                value={newAssigneeId}
                            />
                        </div>
                        {errors.newAssignee && (
                            <Typography
                                variant="caption"
                                color={colors.rose[600]}
                                sx={{ mt: 0.5, display: "block" }}
                            >
                                {errors.newAssignee}
                            </Typography>
                        )}
                        {errors.report && (
                            <Typography
                                variant="caption"
                                color={colors.rose[600]}
                                sx={{ mt: 0.5, display: "block" }}
                            >
                                {errors.report}
                            </Typography>
                        )}
                        {errors.cancel && (
                            <Typography
                                variant="caption"
                                color={colors.rose[600]}
                                sx={{ mt: 0.5, display: "block" }}
                            >
                                {errors.cancel}
                            </Typography>
                        )}
                        {errors.order && (
                            <Typography
                                variant="caption"
                                color={colors.rose[600]}
                                sx={{ mt: 0.5, display: "block" }}
                            >
                                {errors.order}
                            </Typography>
                        )}
                        {errors.auditTrail && (
                            <Typography
                                variant="caption"
                                color={colors.rose[600]}
                                sx={{ mt: 0.5, display: "block" }}
                            >
                                {errors.auditTrail}
                            </Typography>
                        )}
                    </Box>

                    {/* Reason Section - only show in reassign mode */}
                    {!isCreateMode && (
                        <Box>
                            <TextInput
                                title="Reason for Reassignment"
                                placeholder="Provide a reason for reassignment (optional)"
                                value={reason}
                                onChange={(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => setReason(e.target.value)}
                                type={InputType.Multiline}
                            />
                        </Box>
                    )}
                </Box>
            </DialogContent>

            <DialogActions
                sx={{
                    borderTop: `1px solid ${colors.grey[200]}`,
                    pt: 2,
                    px: 3,
                    pb: 3,
                    gap: 1,
                }}
            >
                <Button
                    label="Cancel"
                    prominence={false}
                    onClick={handleClose}
                    disabled={isLoading}
                />
                <Button
                    label={isCreateMode ? "Assign" : "Reassign"}
                    prominence={true}
                    onClick={handleReassign}
                    isLoading={isLoading}
                    disabled={!newAssigneeId || isLoading}
                />
            </DialogActions>
        </Dialog>
    );
};