// hooks/useDispatcherQuery.ts
import { useQuery } from '@tanstack/react-query';
import { fetchAuthSession, getCurrentUser } from 'aws-amplify/auth';
import {
    Asset,
    AssetType,
    CreateAssetRequest,
    CreateAssetResponse,
    GetAssetByCognitoSubRequest,
    GetAssetByCognitoSubResponse,
} from 'proto/hero/assets/v2/assets_pb';
import { getTokensFromCookies, getTokensFromLocalStorage } from '../../../utils/auth';
import { createAsset, getAssetByCognitoSub } from '../../services/workflow/assets/endpoints';

// Define a type for the dispatcher asset (you can adjust this to match your actual asset shape)
export interface DispatcherAsset {
    id: string;
    name: string;
    type: AssetType;
    orgId: number;
    isInternal?: boolean;
    // Add other asset properties as needed
}

const isDevelopment = process.env.NODE_ENV === 'development';

/**
 * fetchDispatcherAsset follows these steps:
 * 1. Tries to get the username from cookies.
 * 2. If not found, tries localStorage.
 * 3. If not found, tries Amplify's current session.
 * 4. If found in any source, fetches the asset using getAssetByCognitoSub.
 * 5. In development:
 *    - If asset not found, creates a new one with the found username
 *    - If no username found, lists and returns first dispatcher
 * 6. In production:
 *    - Errors out if no asset found for authenticated user
 *    - Never falls back to listing or creating assets
 */
export const fetchDispatcherAsset = async (): Promise<DispatcherAsset> => {
    if (isDevelopment) {
        return fetchDispatcherAssetDevelopment();
    }
    return fetchDispatcherAssetProduction();
};

const fetchDispatcherAssetDevelopment = async (): Promise<DispatcherAsset> => {
    console.log('Starting fetchDispatcherAsset process in development mode...');
    let username: string | undefined;
    let authSource: string = '';
    let sub: string | undefined;

    const localTokens = getTokensFromLocalStorage();
    if (localTokens.username) {
        username = localTokens.username;
        sub = localTokens.username;
        authSource = 'localStorage';
    }


    if (!username) {
        console.warn('No authenticated user found in development mode');
        throw new Error('No authenticated user found in development mode');
    }

    console.log(`Authentication successful from ${authSource}`);
    console.log('Attempting to get asset by cognito sub...');

    const request: GetAssetByCognitoSubRequest = { cognitoJwtSub: sub } as GetAssetByCognitoSubRequest;
    let response: GetAssetByCognitoSubResponse;

    try {
        response = await getAssetByCognitoSub(request);
        console.log('Asset lookup completed');
    } catch (error: any) {
        if (error?.message && error.message.includes('asset not found')) {
            console.log('No existing asset found');
            response = { asset: undefined } as GetAssetByCognitoSubResponse;
        } else {
            console.error('Asset lookup failed');
            throw error;
        }
    }

    if (response.asset) {
        console.log('Existing asset found');
        return response.asset;
    }

    console.log('Creating new dispatcher asset...');
    const createRequest: CreateAssetRequest = {
        asset: {
            name: "Mona Sax",
            type: AssetType.DISPATCHER,
            cognitoJwtSub: username,
            orgId: 1
        } as Asset,
    } as CreateAssetRequest;

    let createResponse: CreateAssetResponse;

    try {
        createResponse = await createAsset(createRequest);
        console.log('Asset creation completed');
    } catch (error) {
        console.error('Asset creation failed');
        throw error;
    }

    if (createResponse.asset) {
        console.log('New asset created successfully');
        return createResponse.asset;
    }

    throw new Error('Asset creation failed');
};

const fetchDispatcherAssetProduction = async (): Promise<DispatcherAsset> => {
    let username: string | undefined;
    let authSource: string = '';
    let sub: string | undefined;

    // Try to get username from all possible sources
    const cookieTokens = getTokensFromCookies();
    if (cookieTokens.username) {
        username = cookieTokens.username;
        sub = cookieTokens.sub;
        authSource = 'cookies';
    } else {
        const localTokens = getTokensFromLocalStorage();
        if (localTokens.username) {
            username = localTokens.username;
            sub = localTokens.sub;
            authSource = 'localStorage';
        } else {
            const session = await fetchAuthSession();
            if (session.tokens?.accessToken) {
                const currentUser = await getCurrentUser();
                username = currentUser.username;
                sub = currentUser.userId;
                authSource = 'Amplify session';
            }
        }
    }

    if (!username) {
        console.error('Authentication required');
        throw new Error('Authentication required');
    }

    const request: GetAssetByCognitoSubRequest = { cognitoJwtSub: sub } as GetAssetByCognitoSubRequest;
    let response: GetAssetByCognitoSubResponse;

    try {
        response = await getAssetByCognitoSub(request);
    } catch (error: any) {
        if (error?.message && error.message.includes('asset not found')) {
            console.error('Asset not found');
            throw new Error('Asset not found');
        }
        console.error('Asset lookup failed');
        throw error;
    }

    if (response.asset) {
        return response.asset;
    }

    console.error('Asset not found');
    throw new Error('Asset not found');
};

/**
 * useDispatcherQuery is a React Query hook that runs fetchDispatcherAsset,
 * caching the result so that the dispatcher asset is accessible across the app.
 */
export const useDispatcherQuery = () => {
    return useQuery<DispatcherAsset, Error>({
        queryKey: ['dispatcherAsset'],
        queryFn: fetchDispatcherAsset,
        staleTime: 0, // disable caching
        gcTime: 0, // remove from cache immediately
    });
};
