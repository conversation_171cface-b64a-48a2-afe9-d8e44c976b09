import {
    useQuery,
    useMutation,
    useQueryClient,
    UseQueryOptions,
    UseMutationOptions
} from "@tanstack/react-query";
import {
    IsEnabledRequest,
    IsEnabledResponse,
    SetFeatureTargetRequest,
    SetFeatureTargetResponse,
    SetMultipleFeaturesRequest,
    SetMultipleFeaturesResponse,
    GetEnabledFeaturesRequest,
    GetEnabledFeaturesResponse,
} from "proto/hero/featureflags/v1/featureflags_pb";

import {
    isEnabled,
    setFeatureTarget,
    setMultipleFeatures,
    getEnabledFeatures,
} from "./endpoints";

// Cache key constants
const FEATURE_FLAGS_QUERY_KEY = "featureFlags";
const ENABLED_FEATURES_QUERY_KEY = "enabledFeatures";

// Check if a feature is enabled
export const useIsEnabled = (
    request: IsEnabledRequest,
    options?: Omit<UseQueryOptions<IsEnabledResponse, Error>, 'queryKey' | 'queryFn'>
) => {
    return useQuery<IsEnabledResponse, Error>({
        queryKey: [FEATURE_FLAGS_QUERY_KEY, "isEnabled", request],
        queryFn: () => isEnabled(request),
        ...options
    });
};

// Enable/disable feature for specific targets
export const useSetFeatureTarget = (
    options?: UseMutationOptions<SetFeatureTargetResponse, Error, SetFeatureTargetRequest>
) => {
    const queryClient = useQueryClient();
    
    return useMutation<SetFeatureTargetResponse, Error, SetFeatureTargetRequest>({
        mutationFn: (request) => setFeatureTarget(request),
        onSuccess: (data, variables, context) => {
            // Invalidate relevant queries to ensure fresh data
            queryClient.invalidateQueries({ queryKey: [FEATURE_FLAGS_QUERY_KEY] });
            queryClient.invalidateQueries({ queryKey: [ENABLED_FEATURES_QUERY_KEY] });
            
            // Call the original onSuccess if provided
            options?.onSuccess?.(data, variables, context);
        },
        ...options
    });
};

// Bulk enable/disable multiple features for a target
export const useSetMultipleFeatures = (
    options?: UseMutationOptions<SetMultipleFeaturesResponse, Error, SetMultipleFeaturesRequest>
) => {
    const queryClient = useQueryClient();
    
    return useMutation<SetMultipleFeaturesResponse, Error, SetMultipleFeaturesRequest>({
        mutationFn: (request) => setMultipleFeatures(request),
        onSuccess: (data, variables, context) => {
            // Invalidate relevant queries to ensure fresh data
            queryClient.invalidateQueries({ queryKey: [FEATURE_FLAGS_QUERY_KEY] });
            queryClient.invalidateQueries({ queryKey: [ENABLED_FEATURES_QUERY_KEY] });
            
            // Call the original onSuccess if provided
            options?.onSuccess?.(data, variables, context);
        },
        ...options
    });
};

// Get all enabled features for a target
export const useGetEnabledFeatures = (
    request: GetEnabledFeaturesRequest,
    options?: Omit<UseQueryOptions<GetEnabledFeaturesResponse, Error>, 'queryKey' | 'queryFn'>
) => {
    return useQuery<GetEnabledFeaturesResponse, Error>({
        queryKey: [ENABLED_FEATURES_QUERY_KEY, request],
        queryFn: () => getEnabledFeatures(request),
        ...options
    });
};