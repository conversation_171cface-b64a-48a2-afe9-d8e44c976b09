import { Feature } from "proto/hero/featureflags/v1/featureflags_pb";

// Feature enum to string mapping
export const featureToString = (feature: Feature): string => {
    switch (feature) {
        case Feature.UNSPECIFIED:
            return "Unspecified";
        case Feature.EXPERIMENTAL_CAMERA:
            return "Experimental Camera";
        case Feature.EXPERIMENTAL_PUSH_TO_TALK:
            return "Experimental Push to Talk";
        case Feature.EXPERIMENTAL_DEMO:
            return "Experimental Demo";
        default:
            return "Unknown";
    }
};

// String to Feature enum mapping
export const stringToFeature = (featureString: string): Feature => {
    const normalizedString = featureString.toUpperCase().replace(/\s+/g, '_');
    
    switch (normalizedString) {
        case "UNSPECIFIED":
        case "FEATURE_UNSPECIFIED":
            return Feature.UNSPECIFIED;
        case "EXPERIMENTAL_CAMERA":
        case "FEATURE_EXPERIMENTAL_CAMERA":
            return Feature.EXPERIMENTAL_CAMERA;
        case "EXPERIMENTAL_PUSH_TO_TALK":
        case "FEATURE_EXPERIMENTAL_PUSH_TO_TALK":
            return Feature.EXPERIMENTAL_PUSH_TO_TALK;
        case "EXPERIMENTAL_DEMO":
        case "FEATURE_EXPERIMENTAL_DEMO":
            return Feature.EXPERIMENTAL_DEMO;
        default:
            return Feature.UNSPECIFIED;
    }
};

// Get all features as an array
export const getAllFeatures = (): Array<{ value: Feature; label: string }> => {
    return [
        { value: Feature.EXPERIMENTAL_CAMERA, label: featureToString(Feature.EXPERIMENTAL_CAMERA) },
        { value: Feature.EXPERIMENTAL_PUSH_TO_TALK, label: featureToString(Feature.EXPERIMENTAL_PUSH_TO_TALK) },
        { value: Feature.EXPERIMENTAL_DEMO, label: featureToString(Feature.EXPERIMENTAL_DEMO) },
    ];
};