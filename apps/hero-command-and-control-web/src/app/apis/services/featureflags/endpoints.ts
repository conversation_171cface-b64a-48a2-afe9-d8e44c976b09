import axios, { AxiosResponse } from "axios";
import axiosInstance from "./axiosInstance";

import {
    IsEnabledRequest,
    IsEnabledResponse,
    SetFeatureTargetRequest,
    SetFeatureTargetResponse,
    SetMultipleFeaturesRequest,
    SetMultipleFeaturesResponse,
    GetEnabledFeaturesRequest,
    GetEnabledFeaturesResponse,
} from "proto/hero/featureflags/v1/featureflags_pb";

// Custom API Error class to include HTTP status codes
class APIError extends Error {
    public statusCode: number;
    constructor(statusCode: number, message: string) {
        super(message);
        this.statusCode = statusCode;
        Object.setPrototypeOf(this, APIError.prototype);
    }
}

// Generic helper function for POST requests with two type parameters:
// T for the response type and D for the request data type.
const postRequest = async <T, D>(url: string, data: D): Promise<T> => {
    try {
        const response: AxiosResponse<T> = await axiosInstance.post<T, AxiosResponse<T>, D>(
            url,
            data
        );
        return response.data;
    } catch (error: unknown) {
        if (axios.isAxiosError(error)) {
            const status = error.response?.status ?? 500;
            const message = error.response?.data?.message || error.message;
            throw new APIError(status, message);
        }
        throw error;
    }
};

// Check if a feature is enabled
export const isEnabled = async (request: IsEnabledRequest): Promise<IsEnabledResponse> => {
    return postRequest<IsEnabledResponse, IsEnabledRequest>(
        "/hero.featureflags.v1.FeatureFlagsService/IsEnabled",
        request
    );
};

// Enable/disable feature for specific targets
export const setFeatureTarget = async (request: SetFeatureTargetRequest): Promise<SetFeatureTargetResponse> => {
    return postRequest<SetFeatureTargetResponse, SetFeatureTargetRequest>(
        "/hero.featureflags.v1.FeatureFlagsService/SetFeatureTarget",
        request
    );
};

// Bulk enable/disable multiple features for a target
export const setMultipleFeatures = async (request: SetMultipleFeaturesRequest): Promise<SetMultipleFeaturesResponse> => {
    return postRequest<SetMultipleFeaturesResponse, SetMultipleFeaturesRequest>(
        "/hero.featureflags.v1.FeatureFlagsService/SetMultipleFeatures",
        request
    );
};

// Get all enabled features for a target
export const getEnabledFeatures = async (request: GetEnabledFeaturesRequest): Promise<GetEnabledFeaturesResponse> => {
    return postRequest<GetEnabledFeaturesResponse, GetEnabledFeaturesRequest>(
        "/hero.featureflags.v1.FeatureFlagsService/GetEnabledFeatures",
        request
    );
};