import { JobStatus, OutputFormat } from "proto/hero/etl/v1/etl_pb";

// Function to convert a string (e.g. "JOB_STATUS_PENDING")
// to the corresponding enum value (e.g. JobStatus.PENDING).
export function stringToJobStatus(value: string): JobStatus | undefined {
    const prefix = "JOB_STATUS_";
    if (!value.startsWith(prefix)) {
        return undefined;
    }
    // Remove the prefix to get the key matching the enum property name.
    const enumKey = value.substring(prefix.length) as keyof typeof JobStatus;
    // Ensure the key exists in the enum.
    if (enumKey in JobStatus) {
        return JobStatus[enumKey];
    }
    return undefined;
}

// Function to convert an enum value (e.g. JobStatus.PENDING)
// back to its string representation (e.g. "JOB_STATUS_PENDING").
export function jobStatusToString(jobStatus: JobStatus): string {
    // Using reverse mapping: JobStatus[jobStatus] returns the key as a string.
    const key = JobStatus[jobStatus];
    return "JOB_STATUS_" + key;
}

// For crazy reason when you use our hooks to get Job it gives you not the right Enum
// It gives you the string representation of the enum
// You can just string type cast it from JobStatus
export function hookJobStatusToString(jobStatus: JobStatus): string {
    return String(jobStatus);
}

// Function to convert a string (e.g. "OUTPUT_FORMAT_NIBRS_XML")
// to the corresponding enum value (e.g. OutputFormat.NIBRS_XML).
export function stringToOutputFormat(value: string): OutputFormat | undefined {
    const prefix = "OUTPUT_FORMAT_";
    if (!value.startsWith(prefix)) {
        return undefined;
    }
    // Remove the prefix to get the enum key.
    const enumKey = value.substring(prefix.length) as keyof typeof OutputFormat;
    if (enumKey in OutputFormat) {
        return OutputFormat[enumKey];
    }
    return undefined;
}

// Function to convert an enum value (e.g. OutputFormat.NIBRS_XML)
// back to its string representation (e.g. "OUTPUT_FORMAT_NIBRS_XML").
export function outputFormatToString(outputFormat: OutputFormat): string {
    // Using reverse mapping: OutputFormat[outputFormat] returns the key as a string.
    const key = OutputFormat[outputFormat];
    return "OUTPUT_FORMAT_" + key;
}

// For crazy reason when you use our hooks to get OutputFormat it gives you not the right Enum
// It gives you the string representation of the enum
// You can just string type cast it from OutputFormat
export function hookOutputFormatToString(outputFormat: OutputFormat): string {
    return String(outputFormat);
}