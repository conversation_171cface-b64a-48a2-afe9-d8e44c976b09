import {
    useQuery,
    useMutation,
    UseQueryOptions,
    UseMutationOptions
} from "@tanstack/react-query";

import {
    ExtractReportDataRequest,
    ExtractReportDataResponse,
    TestReportMappingRequest,
    TestReportMappingResponse,
    TestReportTransformationRequest,
    TestReportTransformationResponse
} from "proto/hero/etl/v1/etl_pb";

import {
    extractReportData,
    testReportMapping,
    testReportTransformation
} from "./endpoints";

// Cache key constants
const ETL_EXTRACT_QUERY_KEY = "etlExtractReportData";
const ETL_MAPPING_QUERY_KEY = "etlTestMapping";
const ETL_TRANSFORMATION_QUERY_KEY = "etlTestTransformation";

/**
 * Hook for extracting raw report data without transformation.
 * Useful for debugging and understanding what data is available.
 */
export function useExtractReportData(
    options?: UseMutationOptions<ExtractReportDataResponse, Error, ExtractReportDataRequest>
) {
    return useMutation({
        mutationFn: (request: ExtractReportDataRequest) => extractReportData(request),
        ...options,
    });
}

/**
 * Hook for testing report mapping configuration.
 * Shows the intermediate data after mapping but before template formatting.
 */
export function useTestReportMapping(
    options?: UseMutationOptions<TestReportMappingResponse, Error, TestReportMappingRequest>
) {
    return useMutation({
        mutationFn: (request: TestReportMappingRequest) => testReportMapping(request),
        ...options,
    });
}

/**
 * Hook for testing complete report transformation pipeline.
 * Extracts, maps, and formats the report according to the specified output format.
 */
export function useTestReportTransformation(
    options?: UseMutationOptions<TestReportTransformationResponse, Error, TestReportTransformationRequest>
) {
    return useMutation({
        mutationFn: (request: TestReportTransformationRequest) => testReportTransformation(request),
        ...options,
    });
}

/**
 * Hook for extracting report data with caching (query-based).
 * Use this when you want to cache extraction results.
 */
export function useExtractReportDataQuery(
    reportId: string,
    options?: UseQueryOptions<ExtractReportDataResponse, Error>
) {
    return useQuery({
        queryKey: [ETL_EXTRACT_QUERY_KEY, reportId],
        queryFn: () => extractReportData({ reportId } as ExtractReportDataRequest),
        enabled: !!reportId,
        staleTime: 5 * 60 * 1000, // 5 minutes
        retry: 2,
        ...options,
    });
}

/**
 * Hook for testing report mapping with caching (query-based).
 * Use this when you want to cache mapping results.
 */
export function useTestReportMappingQuery(
    request: TestReportMappingRequest,
    options?: UseQueryOptions<TestReportMappingResponse, Error>
) {
    return useQuery({
        queryKey: [ETL_MAPPING_QUERY_KEY, request],
        queryFn: () => testReportMapping(request),
        enabled: !!request.reportId,
        staleTime: 5 * 60 * 1000, // 5 minutes
        retry: 2,
        ...options,
    });
}

/**
 * Hook for testing report transformation with caching (query-based).
 * Use this when you want to cache transformation results.
 */
export function useTestReportTransformationQuery(
    request: TestReportTransformationRequest,
    options?: UseQueryOptions<TestReportTransformationResponse, Error>
) {
    return useQuery({
        queryKey: [ETL_TRANSFORMATION_QUERY_KEY, request],
        queryFn: () => testReportTransformation(request),
        enabled: !!request.reportId,
        staleTime: 5 * 60 * 1000, // 5 minutes
        retry: 2,
        ...options,
    });
}