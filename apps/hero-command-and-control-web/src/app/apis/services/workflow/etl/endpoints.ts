import axios, { AxiosResponse } from "axios";
import axiosInstance from "../axiosInstance";

import {
    ExtractReportDataRequest,
    ExtractReportDataResponse,
    TestReportMappingRequest,
    TestReportMappingResponse,
    TestReportTransformationRequest,
    TestReportTransformationResponse
} from "proto/hero/etl/v1/etl_pb";

// Custom API Error class to include HTTP status codes
class APIError extends Error {
    public statusCode: number;
    constructor(statusCode: number, message: string) {
        super(message);
        this.statusCode = statusCode;
        Object.setPrototypeOf(this, APIError.prototype);
    }
}

// Generic helper function for POST requests with two type parameters:
// T for the response type and D for the request data type.
const postRequest = async <T, D>(url: string, data: D): Promise<T> => {
    try {
        const response: AxiosResponse<T> = await axiosInstance.post<T, AxiosResponse<T>, D>(
            url,
            data
        );
        return response.data;
    } catch (error: unknown) {
        if (axios.isAxiosError(error)) {
            const status = error.response?.status ?? 500;
            const errorMsg = error.response?.data?.message || error.message;
            throw new APIError(status, `Request to ${url} failed: ${errorMsg}`);
        }
        throw error;
    }
};

// Extract Report Data
export const extractReportData = async (
    data: ExtractReportDataRequest
): Promise<ExtractReportDataResponse> => {
    return postRequest<ExtractReportDataResponse, ExtractReportDataRequest>(
        "/hero.etl.v1.ETLService/ExtractReportData",
        data
    );
};

// Test Report Mapping
export const testReportMapping = async (
    data: TestReportMappingRequest
): Promise<TestReportMappingResponse> => {
    return postRequest<TestReportMappingResponse, TestReportMappingRequest>(
        "/hero.etl.v1.ETLService/TestReportMapping",
        data
    );
};

// Test Report Transformation
export const testReportTransformation = async (
    data: TestReportTransformationRequest
): Promise<TestReportTransformationResponse> => {
    return postRequest<TestReportTransformationResponse, TestReportTransformationRequest>(
        "/hero.etl.v1.ETLService/TestReportTransformation",
        data
    );
};