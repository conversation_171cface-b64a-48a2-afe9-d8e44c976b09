import { JobStatus, OutputFormat } from "proto/hero/etl/v1/etl_pb";
import {
    stringToJobStatus,
    jobStatusToString,
    stringToOutputFormat,
    outputFormatToString
} from "./enumConverters";

describe("ETL Enum Converters", () => {
    describe("JobStatus converters", () => {
        it("should convert string to JobStatus enum", () => {
            expect(stringToJobStatus("JOB_STATUS_PENDING")).toBe(JobStatus.PENDING);
            expect(stringToJobStatus("JOB_STATUS_EXTRACTING")).toBe(JobStatus.EXTRACTING);
            expect(stringToJobStatus("JOB_STATUS_TRANSFORMING")).toBe(JobStatus.TRANSFORMING);
            expect(stringToJobStatus("JOB_STATUS_LOADING")).toBe(JobStatus.LOADING);
            expect(stringToJobStatus("JOB_STATUS_COMPLETED")).toBe(JobStatus.COMPLETED);
            expect(stringToJobStatus("JOB_STATUS_FAILED")).toBe(JobStatus.FAILED);
            expect(stringToJobStatus("JOB_STATUS_CANCELLED")).toBe(JobStatus.CANCELLED);
        });

        it("should return undefined for invalid JobStatus string", () => {
            expect(stringToJobStatus("INVALID_STATUS")).toBeUndefined();
            expect(stringToJobStatus("PENDING")).toBeUndefined();
            expect(stringToJobStatus("")).toBeUndefined();
        });

        it("should convert JobStatus enum to string", () => {
            expect(jobStatusToString(JobStatus.PENDING)).toBe("JOB_STATUS_PENDING");
            expect(jobStatusToString(JobStatus.EXTRACTING)).toBe("JOB_STATUS_EXTRACTING");
            expect(jobStatusToString(JobStatus.TRANSFORMING)).toBe("JOB_STATUS_TRANSFORMING");
            expect(jobStatusToString(JobStatus.LOADING)).toBe("JOB_STATUS_LOADING");
            expect(jobStatusToString(JobStatus.COMPLETED)).toBe("JOB_STATUS_COMPLETED");
            expect(jobStatusToString(JobStatus.FAILED)).toBe("JOB_STATUS_FAILED");
            expect(jobStatusToString(JobStatus.CANCELLED)).toBe("JOB_STATUS_CANCELLED");
        });
    });

    describe("OutputFormat converters", () => {
        it("should convert string to OutputFormat enum", () => {
            expect(stringToOutputFormat("OUTPUT_FORMAT_NIBRS_XML")).toBe(OutputFormat.NIBRS_XML);
        });

        it("should return undefined for invalid OutputFormat string", () => {
            expect(stringToOutputFormat("INVALID_FORMAT")).toBeUndefined();
            expect(stringToOutputFormat("NIBRS_XML")).toBeUndefined();
            expect(stringToOutputFormat("")).toBeUndefined();
        });

        it("should convert OutputFormat enum to string", () => {
            expect(outputFormatToString(OutputFormat.NIBRS_XML)).toBe("OUTPUT_FORMAT_NIBRS_XML");
        });
    });
});