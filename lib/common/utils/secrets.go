package utils

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
)

// GetSecret retrieves a secret from AWS Secrets Manager using the Lambda extension.
// It expects the secret to be a JSON object with string key-value pairs.
// must include PARAMETERS_SECRETS_EXTENSION_LOG_LEVEL, PARAMETERS_SECRETS_EXTENSION_CACHE_SIZE, SECRETS_MANAGER_TTL in the environment variables
// and the following in the paramsAndSecrets:
// paramsAndSecrets: lambda.ParamsAndSecretsLayerVersion.fromVersion(
// lambda.ParamsAndSecretsVersions.V1_0_103,
// {
// cacheSize: 10,
// logLevel: lambda.ParamsAndSecretsLogLevel.DEBUG,
// }
// ),
func GetSecret(secretARN string) (string, error) {
	port := os.Getenv("PARAMETERS_SECRETS_EXTENSION_HTTP_PORT")
	if port == "" {
		port = "2773"
	}

	url := fmt.Sprintf("http://localhost:%s/secretsmanager/get?secretId=%s", port, secretARN)

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return "", fmt.Errorf("error creating request: %w", err)
	}

	req.Header.Set("X-Aws-Parameters-Secrets-Token", os.Getenv("AWS_SESSION_TOKEN"))

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		bodyBytes, _ := io.ReadAll(resp.Body)
		return "", fmt.Errorf("HTTP error! status: %d, body: %s", resp.StatusCode, string(bodyBytes))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("error reading response: %w", err)
	}

	var secretData struct {
		SecretString string `json:"SecretString"`
	}
	if err := json.Unmarshal(body, &secretData); err != nil {
		return "", fmt.Errorf("error unmarshaling response: %w", err)
	}

	return secretData.SecretString, nil
}
