// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.3
// 	protoc        (unknown)
// source: hero/featureflags/v1/featureflags.proto

package featureflags

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Define all feature flags as an enum
type Feature int32

const (
	Feature_FEATURE_UNSPECIFIED               Feature = 0
	Feature_FEATURE_EXPERIMENTAL_CAMERA       Feature = 1
	Feature_FEATURE_EXPERIMENTAL_PUSH_TO_TALK Feature = 2
	Feature_FEATURE_EXPERIMENTAL_DEMO         Feature = 3 // Add new features here as needed
)

// Enum value maps for Feature.
var (
	Feature_name = map[int32]string{
		0: "FEATURE_UNSPECIFIED",
		1: "FEATURE_EXPERIMENTAL_CAMERA",
		2: "FEATURE_EXPERIMENTAL_PUSH_TO_TALK",
		3: "FEATURE_EXPERIMENTAL_DEMO",
	}
	Feature_value = map[string]int32{
		"FEATURE_UNSPECIFIED":               0,
		"FEATURE_EXPERIMENTAL_CAMERA":       1,
		"FEATURE_EXPERIMENTAL_PUSH_TO_TALK": 2,
		"FEATURE_EXPERIMENTAL_DEMO":         3,
	}
)

func (x Feature) Enum() *Feature {
	p := new(Feature)
	*p = x
	return p
}

func (x Feature) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Feature) Descriptor() protoreflect.EnumDescriptor {
	return file_hero_featureflags_v1_featureflags_proto_enumTypes[0].Descriptor()
}

func (Feature) Type() protoreflect.EnumType {
	return &file_hero_featureflags_v1_featureflags_proto_enumTypes[0]
}

func (x Feature) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Feature.Descriptor instead.
func (Feature) EnumDescriptor() ([]byte, []int) {
	return file_hero_featureflags_v1_featureflags_proto_rawDescGZIP(), []int{0}
}

// Request/Response messages
type IsEnabledRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrgId         int32                  `protobuf:"varint,1,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	Feature       Feature                `protobuf:"varint,2,opt,name=feature,proto3,enum=hero.featureflags.v1.Feature" json:"feature,omitempty"`
	AssetId       string                 `protobuf:"bytes,3,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"` // Optional: check for specific asset/user
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IsEnabledRequest) Reset() {
	*x = IsEnabledRequest{}
	mi := &file_hero_featureflags_v1_featureflags_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IsEnabledRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsEnabledRequest) ProtoMessage() {}

func (x *IsEnabledRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_featureflags_v1_featureflags_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsEnabledRequest.ProtoReflect.Descriptor instead.
func (*IsEnabledRequest) Descriptor() ([]byte, []int) {
	return file_hero_featureflags_v1_featureflags_proto_rawDescGZIP(), []int{0}
}

func (x *IsEnabledRequest) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *IsEnabledRequest) GetFeature() Feature {
	if x != nil {
		return x.Feature
	}
	return Feature_FEATURE_UNSPECIFIED
}

func (x *IsEnabledRequest) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

type IsEnabledResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Enabled       bool                   `protobuf:"varint,1,opt,name=enabled,proto3" json:"enabled,omitempty"`
	Evaluated     bool                   `protobuf:"varint,2,opt,name=evaluated,proto3" json:"evaluated,omitempty"` // Always true to indicate evaluation happened
	Reason        string                 `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`        // "default", "org_setting", "asset_setting"
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IsEnabledResponse) Reset() {
	*x = IsEnabledResponse{}
	mi := &file_hero_featureflags_v1_featureflags_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IsEnabledResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IsEnabledResponse) ProtoMessage() {}

func (x *IsEnabledResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_featureflags_v1_featureflags_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IsEnabledResponse.ProtoReflect.Descriptor instead.
func (*IsEnabledResponse) Descriptor() ([]byte, []int) {
	return file_hero_featureflags_v1_featureflags_proto_rawDescGZIP(), []int{1}
}

func (x *IsEnabledResponse) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

func (x *IsEnabledResponse) GetEvaluated() bool {
	if x != nil {
		return x.Evaluated
	}
	return false
}

func (x *IsEnabledResponse) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

type SetFeatureTargetRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrgId         int32                  `protobuf:"varint,1,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	Feature       Feature                `protobuf:"varint,2,opt,name=feature,proto3,enum=hero.featureflags.v1.Feature" json:"feature,omitempty"`
	AssetId       string                 `protobuf:"bytes,3,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"` // Empty string means org-wide setting
	Enabled       bool                   `protobuf:"varint,4,opt,name=enabled,proto3" json:"enabled,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetFeatureTargetRequest) Reset() {
	*x = SetFeatureTargetRequest{}
	mi := &file_hero_featureflags_v1_featureflags_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetFeatureTargetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetFeatureTargetRequest) ProtoMessage() {}

func (x *SetFeatureTargetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_featureflags_v1_featureflags_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetFeatureTargetRequest.ProtoReflect.Descriptor instead.
func (*SetFeatureTargetRequest) Descriptor() ([]byte, []int) {
	return file_hero_featureflags_v1_featureflags_proto_rawDescGZIP(), []int{2}
}

func (x *SetFeatureTargetRequest) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *SetFeatureTargetRequest) GetFeature() Feature {
	if x != nil {
		return x.Feature
	}
	return Feature_FEATURE_UNSPECIFIED
}

func (x *SetFeatureTargetRequest) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *SetFeatureTargetRequest) GetEnabled() bool {
	if x != nil {
		return x.Enabled
	}
	return false
}

type SetFeatureTargetResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetFeatureTargetResponse) Reset() {
	*x = SetFeatureTargetResponse{}
	mi := &file_hero_featureflags_v1_featureflags_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetFeatureTargetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetFeatureTargetResponse) ProtoMessage() {}

func (x *SetFeatureTargetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_featureflags_v1_featureflags_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetFeatureTargetResponse.ProtoReflect.Descriptor instead.
func (*SetFeatureTargetResponse) Descriptor() ([]byte, []int) {
	return file_hero_featureflags_v1_featureflags_proto_rawDescGZIP(), []int{3}
}

func (x *SetFeatureTargetResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type SetMultipleFeaturesRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	OrgId           int32                  `protobuf:"varint,1,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	AssetId         string                 `protobuf:"bytes,2,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"`                                                                   // Empty string means org-wide setting
	EnableFeatures  []Feature              `protobuf:"varint,3,rep,packed,name=enable_features,json=enableFeatures,proto3,enum=hero.featureflags.v1.Feature" json:"enable_features,omitempty"`    // Features to enable
	DisableFeatures []Feature              `protobuf:"varint,4,rep,packed,name=disable_features,json=disableFeatures,proto3,enum=hero.featureflags.v1.Feature" json:"disable_features,omitempty"` // Features to disable
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *SetMultipleFeaturesRequest) Reset() {
	*x = SetMultipleFeaturesRequest{}
	mi := &file_hero_featureflags_v1_featureflags_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetMultipleFeaturesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetMultipleFeaturesRequest) ProtoMessage() {}

func (x *SetMultipleFeaturesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_featureflags_v1_featureflags_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetMultipleFeaturesRequest.ProtoReflect.Descriptor instead.
func (*SetMultipleFeaturesRequest) Descriptor() ([]byte, []int) {
	return file_hero_featureflags_v1_featureflags_proto_rawDescGZIP(), []int{4}
}

func (x *SetMultipleFeaturesRequest) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *SetMultipleFeaturesRequest) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

func (x *SetMultipleFeaturesRequest) GetEnableFeatures() []Feature {
	if x != nil {
		return x.EnableFeatures
	}
	return nil
}

func (x *SetMultipleFeaturesRequest) GetDisableFeatures() []Feature {
	if x != nil {
		return x.DisableFeatures
	}
	return nil
}

type SetMultipleFeaturesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetMultipleFeaturesResponse) Reset() {
	*x = SetMultipleFeaturesResponse{}
	mi := &file_hero_featureflags_v1_featureflags_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetMultipleFeaturesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetMultipleFeaturesResponse) ProtoMessage() {}

func (x *SetMultipleFeaturesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_featureflags_v1_featureflags_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetMultipleFeaturesResponse.ProtoReflect.Descriptor instead.
func (*SetMultipleFeaturesResponse) Descriptor() ([]byte, []int) {
	return file_hero_featureflags_v1_featureflags_proto_rawDescGZIP(), []int{5}
}

func (x *SetMultipleFeaturesResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type GetEnabledFeaturesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	OrgId         int32                  `protobuf:"varint,1,opt,name=org_id,json=orgId,proto3" json:"org_id,omitempty"`
	AssetId       string                 `protobuf:"bytes,2,opt,name=asset_id,json=assetId,proto3" json:"asset_id,omitempty"` // Optional
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetEnabledFeaturesRequest) Reset() {
	*x = GetEnabledFeaturesRequest{}
	mi := &file_hero_featureflags_v1_featureflags_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEnabledFeaturesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEnabledFeaturesRequest) ProtoMessage() {}

func (x *GetEnabledFeaturesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_hero_featureflags_v1_featureflags_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEnabledFeaturesRequest.ProtoReflect.Descriptor instead.
func (*GetEnabledFeaturesRequest) Descriptor() ([]byte, []int) {
	return file_hero_featureflags_v1_featureflags_proto_rawDescGZIP(), []int{6}
}

func (x *GetEnabledFeaturesRequest) GetOrgId() int32 {
	if x != nil {
		return x.OrgId
	}
	return 0
}

func (x *GetEnabledFeaturesRequest) GetAssetId() string {
	if x != nil {
		return x.AssetId
	}
	return ""
}

type GetEnabledFeaturesResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	EnabledFeatures []Feature              `protobuf:"varint,1,rep,packed,name=enabled_features,json=enabledFeatures,proto3,enum=hero.featureflags.v1.Feature" json:"enabled_features,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetEnabledFeaturesResponse) Reset() {
	*x = GetEnabledFeaturesResponse{}
	mi := &file_hero_featureflags_v1_featureflags_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetEnabledFeaturesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEnabledFeaturesResponse) ProtoMessage() {}

func (x *GetEnabledFeaturesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_hero_featureflags_v1_featureflags_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEnabledFeaturesResponse.ProtoReflect.Descriptor instead.
func (*GetEnabledFeaturesResponse) Descriptor() ([]byte, []int) {
	return file_hero_featureflags_v1_featureflags_proto_rawDescGZIP(), []int{7}
}

func (x *GetEnabledFeaturesResponse) GetEnabledFeatures() []Feature {
	if x != nil {
		return x.EnabledFeatures
	}
	return nil
}

var File_hero_featureflags_v1_featureflags_proto protoreflect.FileDescriptor

var file_hero_featureflags_v1_featureflags_proto_rawDesc = []byte{
	0x0a, 0x27, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x66, 0x6c,
	0x61, 0x67, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x66, 0x6c,
	0x61, 0x67, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x68, 0x65, 0x72, 0x6f, 0x2e,
	0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x22,
	0x7d, 0x0a, 0x10, 0x49, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x07, 0x66, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x07, 0x66, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x22, 0x63,
	0x0a, 0x11, 0x49, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x09, 0x65, 0x76, 0x61, 0x6c, 0x75, 0x61, 0x74, 0x65, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x22, 0x9e, 0x01, 0x0a, 0x17, 0x53, 0x65, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x05, 0x6f, 0x72, 0x67, 0x49, 0x64, 0x12, 0x37, 0x0a, 0x07, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x66,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x46,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x07, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x65, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x22, 0x34, 0x0a, 0x18, 0x53, 0x65, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x07, 0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0xe0, 0x01, 0x0a, 0x1a, 0x53,
	0x65, 0x74, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x73, 0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x73, 0x73, 0x65, 0x74, 0x49, 0x64, 0x12, 0x46, 0x0a, 0x0f, 0x65,
	0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x66, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x52, 0x0e, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x65, 0x61, 0x74, 0x75,
	0x72, 0x65, 0x73, 0x12, 0x48, 0x0a, 0x10, 0x64, 0x69, 0x73, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x66,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1d, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x66, 0x6c, 0x61, 0x67,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x0f, 0x64, 0x69,
	0x73, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x22, 0x37, 0x0a,
	0x1b, 0x53, 0x65, 0x74, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65, 0x46, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x73, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x73,
	0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x22, 0x4d, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x61,
	0x62, 0x6c, 0x65, 0x64, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x15, 0x0a, 0x06, 0x6f, 0x72, 0x67, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x6f, 0x72, 0x67, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x73,
	0x73, 0x65, 0x74, 0x49, 0x64, 0x22, 0x66, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x48, 0x0a, 0x10, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x5f, 0x66,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1d, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x66, 0x6c, 0x61, 0x67,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x0f, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x64, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x2a, 0x89, 0x01,
	0x0a, 0x07, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x17, 0x0a, 0x13, 0x46, 0x45, 0x41,
	0x54, 0x55, 0x52, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x1f, 0x0a, 0x1b, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x45, 0x58,
	0x50, 0x45, 0x52, 0x49, 0x4d, 0x45, 0x4e, 0x54, 0x41, 0x4c, 0x5f, 0x43, 0x41, 0x4d, 0x45, 0x52,
	0x41, 0x10, 0x01, 0x12, 0x25, 0x0a, 0x21, 0x46, 0x45, 0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x45,
	0x58, 0x50, 0x45, 0x52, 0x49, 0x4d, 0x45, 0x4e, 0x54, 0x41, 0x4c, 0x5f, 0x50, 0x55, 0x53, 0x48,
	0x5f, 0x54, 0x4f, 0x5f, 0x54, 0x41, 0x4c, 0x4b, 0x10, 0x02, 0x12, 0x1d, 0x0a, 0x19, 0x46, 0x45,
	0x41, 0x54, 0x55, 0x52, 0x45, 0x5f, 0x45, 0x58, 0x50, 0x45, 0x52, 0x49, 0x4d, 0x45, 0x4e, 0x54,
	0x41, 0x4c, 0x5f, 0x44, 0x45, 0x4d, 0x4f, 0x10, 0x03, 0x32, 0xe3, 0x03, 0x0a, 0x13, 0x46, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x5e, 0x0a, 0x09, 0x49, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x26,
	0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x66, 0x6c, 0x61,
	0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x66, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x73,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x73, 0x0a, 0x10, 0x53, 0x65, 0x74, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x2d, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x66, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x66, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x46,
	0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7c, 0x0a, 0x13, 0x53, 0x65, 0x74, 0x4d, 0x75, 0x6c,
	0x74, 0x69, 0x70, 0x6c, 0x65, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x12, 0x30, 0x2e,
	0x68, 0x65, 0x72, 0x6f, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x66, 0x6c, 0x61, 0x67,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70, 0x6c, 0x65,
	0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x31, 0x2e, 0x68, 0x65, 0x72, 0x6f, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x66, 0x6c,
	0x61, 0x67, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x4d, 0x75, 0x6c, 0x74, 0x69, 0x70,
	0x6c, 0x65, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x79, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x46, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x73, 0x12, 0x2f, 0x2e, 0x68, 0x65, 0x72,
	0x6f, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x46, 0x65, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x68, 0x65,
	0x72, 0x6f, 0x2e, 0x66, 0x65, 0x61, 0x74, 0x75, 0x72, 0x65, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x46, 0x65, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42,
	0x29, 0x5a, 0x27, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x2f, 0x68, 0x65, 0x72, 0x6f, 0x2f, 0x66, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x66, 0x65,
	0x61, 0x74, 0x75, 0x72, 0x65, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_hero_featureflags_v1_featureflags_proto_rawDescOnce sync.Once
	file_hero_featureflags_v1_featureflags_proto_rawDescData = file_hero_featureflags_v1_featureflags_proto_rawDesc
)

func file_hero_featureflags_v1_featureflags_proto_rawDescGZIP() []byte {
	file_hero_featureflags_v1_featureflags_proto_rawDescOnce.Do(func() {
		file_hero_featureflags_v1_featureflags_proto_rawDescData = protoimpl.X.CompressGZIP(file_hero_featureflags_v1_featureflags_proto_rawDescData)
	})
	return file_hero_featureflags_v1_featureflags_proto_rawDescData
}

var file_hero_featureflags_v1_featureflags_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_hero_featureflags_v1_featureflags_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_hero_featureflags_v1_featureflags_proto_goTypes = []any{
	(Feature)(0),                        // 0: hero.featureflags.v1.Feature
	(*IsEnabledRequest)(nil),            // 1: hero.featureflags.v1.IsEnabledRequest
	(*IsEnabledResponse)(nil),           // 2: hero.featureflags.v1.IsEnabledResponse
	(*SetFeatureTargetRequest)(nil),     // 3: hero.featureflags.v1.SetFeatureTargetRequest
	(*SetFeatureTargetResponse)(nil),    // 4: hero.featureflags.v1.SetFeatureTargetResponse
	(*SetMultipleFeaturesRequest)(nil),  // 5: hero.featureflags.v1.SetMultipleFeaturesRequest
	(*SetMultipleFeaturesResponse)(nil), // 6: hero.featureflags.v1.SetMultipleFeaturesResponse
	(*GetEnabledFeaturesRequest)(nil),   // 7: hero.featureflags.v1.GetEnabledFeaturesRequest
	(*GetEnabledFeaturesResponse)(nil),  // 8: hero.featureflags.v1.GetEnabledFeaturesResponse
}
var file_hero_featureflags_v1_featureflags_proto_depIdxs = []int32{
	0, // 0: hero.featureflags.v1.IsEnabledRequest.feature:type_name -> hero.featureflags.v1.Feature
	0, // 1: hero.featureflags.v1.SetFeatureTargetRequest.feature:type_name -> hero.featureflags.v1.Feature
	0, // 2: hero.featureflags.v1.SetMultipleFeaturesRequest.enable_features:type_name -> hero.featureflags.v1.Feature
	0, // 3: hero.featureflags.v1.SetMultipleFeaturesRequest.disable_features:type_name -> hero.featureflags.v1.Feature
	0, // 4: hero.featureflags.v1.GetEnabledFeaturesResponse.enabled_features:type_name -> hero.featureflags.v1.Feature
	1, // 5: hero.featureflags.v1.FeatureFlagsService.IsEnabled:input_type -> hero.featureflags.v1.IsEnabledRequest
	3, // 6: hero.featureflags.v1.FeatureFlagsService.SetFeatureTarget:input_type -> hero.featureflags.v1.SetFeatureTargetRequest
	5, // 7: hero.featureflags.v1.FeatureFlagsService.SetMultipleFeatures:input_type -> hero.featureflags.v1.SetMultipleFeaturesRequest
	7, // 8: hero.featureflags.v1.FeatureFlagsService.GetEnabledFeatures:input_type -> hero.featureflags.v1.GetEnabledFeaturesRequest
	2, // 9: hero.featureflags.v1.FeatureFlagsService.IsEnabled:output_type -> hero.featureflags.v1.IsEnabledResponse
	4, // 10: hero.featureflags.v1.FeatureFlagsService.SetFeatureTarget:output_type -> hero.featureflags.v1.SetFeatureTargetResponse
	6, // 11: hero.featureflags.v1.FeatureFlagsService.SetMultipleFeatures:output_type -> hero.featureflags.v1.SetMultipleFeaturesResponse
	8, // 12: hero.featureflags.v1.FeatureFlagsService.GetEnabledFeatures:output_type -> hero.featureflags.v1.GetEnabledFeaturesResponse
	9, // [9:13] is the sub-list for method output_type
	5, // [5:9] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_hero_featureflags_v1_featureflags_proto_init() }
func file_hero_featureflags_v1_featureflags_proto_init() {
	if File_hero_featureflags_v1_featureflags_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_hero_featureflags_v1_featureflags_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_hero_featureflags_v1_featureflags_proto_goTypes,
		DependencyIndexes: file_hero_featureflags_v1_featureflags_proto_depIdxs,
		EnumInfos:         file_hero_featureflags_v1_featureflags_proto_enumTypes,
		MessageInfos:      file_hero_featureflags_v1_featureflags_proto_msgTypes,
	}.Build()
	File_hero_featureflags_v1_featureflags_proto = out.File
	file_hero_featureflags_v1_featureflags_proto_rawDesc = nil
	file_hero_featureflags_v1_featureflags_proto_goTypes = nil
	file_hero_featureflags_v1_featureflags_proto_depIdxs = nil
}
