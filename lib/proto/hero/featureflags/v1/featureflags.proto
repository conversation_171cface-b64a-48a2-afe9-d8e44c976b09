syntax = "proto3";

package hero.featureflags.v1;

option go_package = "proto/hero/featureflags/v1;featureflags";

service FeatureFlagsService {
    // Check if a feature is enabled
    rpc IsEnabled(IsEnabledRequest) returns (IsEnabledResponse) {}
    
    // Enable/disable feature for specific targets
    rpc SetFeatureTarget(SetFeatureTargetRequest) returns (SetFeatureTargetResponse) {}
    
    // Bulk enable/disable multiple features for a target
    rpc SetMultipleFeatures(SetMultipleFeaturesRequest) returns (SetMultipleFeaturesResponse) {}
    
    // Get all enabled features for a target
    rpc GetEnabledFeatures(GetEnabledFeaturesRequest) returns (GetEnabledFeaturesResponse) {}
}

// Define all feature flags as an enum
enum Feature {
    FEATURE_UNSPECIFIED = 0;
    FEATURE_EXPERIMENTAL_CAMERA = 1;
    FEATURE_EXPERIMENTAL_PUSH_TO_TALK = 2;
    FEATURE_EXPERIMENTAL_DEMO = 3;
    // Add new features here as needed
}

// Request/Response messages
message IsEnabledRequest {
    int32 org_id = 1;
    Feature feature = 2;
    string asset_id = 3;  // Optional: check for specific asset/user
}

message IsEnabledResponse {
    bool enabled = 1;
    bool evaluated = 2;        // Always true to indicate evaluation happened
    string reason = 3;         // "default", "org_setting", "asset_setting"
}

message SetFeatureTargetRequest {
    int32 org_id = 1;
    Feature feature = 2;
    string asset_id = 3;  // Empty string means org-wide setting
    bool enabled = 4;
}

message SetFeatureTargetResponse {
    bool success = 1;
}

message SetMultipleFeaturesRequest {
    int32 org_id = 1;
    string asset_id = 2;  // Empty string means org-wide setting
    repeated Feature enable_features = 3;   // Features to enable
    repeated Feature disable_features = 4;  // Features to disable
}

message SetMultipleFeaturesResponse {
    bool success = 1;
}

message GetEnabledFeaturesRequest {
    int32 org_id = 1;
    string asset_id = 2;  // Optional
}

message GetEnabledFeaturesResponse {
    repeated Feature enabled_features = 1;
}