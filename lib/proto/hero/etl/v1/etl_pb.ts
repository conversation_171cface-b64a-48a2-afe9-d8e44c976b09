// @generated by protoc-gen-es v2.6.2 with parameter "target=ts"
// @generated from file hero/etl/v1/etl.proto (package hero.etl.v1, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage, GenService } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import { file_google_protobuf_empty, file_google_protobuf_struct } from "@bufbuild/protobuf/wkt";
import type { JsonObject, Message } from "@bufbuild/protobuf";

/**
 * Describes the file hero/etl/v1/etl.proto.
 */
export const file_hero_etl_v1_etl: GenFile = /*@__PURE__*/
  fileDesc("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", [file_google_protobuf_empty, file_google_protobuf_struct]);

/**
 * ETL Job - tracks processing of reports for data standards compliance
 *
 * @generated from message hero.etl.v1.ETLJob
 */
export type ETLJob = Message<"hero.etl.v1.ETLJob"> & {
  /**
   * Unique job ID
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Organization ID for multi-tenant isolation
   *
   * @generated from field: int32 org_id = 2;
   */
  orgId: number;

  /**
   * Target agency/recipient (e.g., "FBI", "STATE_POLICE", "UNIVERSITY_POLICE")
   *
   * @generated from field: string agency_id = 3;
   */
  agencyId: string;

  /**
   * Data standard/format (NIBRS, etc.)
   *
   * @generated from field: hero.etl.v1.OutputFormat output_format = 4;
   */
  outputFormat: OutputFormat;

  /**
   * Current status
   *
   * @generated from field: hero.etl.v1.JobStatus status = 5;
   */
  status: JobStatus;

  /**
   * What's being processed
   *
   * Reports to process
   *
   * @generated from field: repeated string report_ids = 6;
   */
  reportIds: string[];

  /**
   * ISO8601 date range start (optional)
   *
   * @generated from field: string date_from = 7;
   */
  dateFrom: string;

  /**
   * ISO8601 date range end (optional)
   *
   * @generated from field: string date_to = 8;
   */
  dateTo: string;

  /**
   * Filter by case types (optional)
   *
   * @generated from field: repeated string case_types = 9;
   */
  caseTypes: string[];

  /**
   * Progress tracking
   *
   * Total reports to process
   *
   * @generated from field: int32 total_reports = 11;
   */
  totalReports: number;

  /**
   * Reports successfully processed
   *
   * @generated from field: int32 reports_processed = 12;
   */
  reportsProcessed: number;

  /**
   * Reports that failed processing
   *
   * @generated from field: int32 reports_failed = 13;
   */
  reportsFailed: number;

  /**
   * Reports skipped (e.g., invalid data)
   *
   * @generated from field: int32 reports_skipped = 14;
   */
  reportsSkipped: number;

  /**
   * Timestamps
   *
   * ISO8601 timestamp when job was created
   *
   * @generated from field: string created_at = 15;
   */
  createdAt: string;

  /**
   * ISO8601 timestamp when job started
   *
   * @generated from field: string started_at = 16;
   */
  startedAt: string;

  /**
   * ISO8601 timestamp when job completed
   *
   * @generated from field: string completed_at = 17;
   */
  completedAt: string;

  /**
   * ISO8601 timestamp of last status update
   *
   * @generated from field: string last_updated_at = 18;
   */
  lastUpdatedAt: string;

  /**
   * Results and content
   *
   * Primary error message if failed
   *
   * @generated from field: string error_message = 19;
   */
  errorMessage: string;

  /**
   * Detailed error list
   *
   * @generated from field: repeated hero.etl.v1.JobError errors = 20;
   */
  errors: JobError[];

  /**
   * Generated content for inspection
   *
   * @generated from field: bytes generated_content = 21;
   */
  generatedContent: Uint8Array;

  /**
   * MIME type of generated content
   *
   * @generated from field: string content_type = 22;
   */
  contentType: string;

  /**
   * Size of generated content
   *
   * @generated from field: int64 content_size_bytes = 23;
   */
  contentSizeBytes: bigint;

  /**
   * Submission details
   *
   * Agency's tracking ID (if submitted)
   *
   * @generated from field: string agency_submission_id = 24;
   */
  agencySubmissionId: string;

  /**
   * Agency's response message
   *
   * @generated from field: string submission_response = 25;
   */
  submissionResponse: string;

  /**
   * ISO8601 timestamp when submitted to agency
   *
   * @generated from field: string submitted_at = 26;
   */
  submittedAt: string;

  /**
   * Retry information
   *
   * Number of retry attempts made
   *
   * @generated from field: int32 retry_count = 27;
   */
  retryCount: number;

  /**
   * Maximum retry attempts allowed
   *
   * @generated from field: int32 max_retries = 28;
   */
  maxRetries: number;

  /**
   * ISO8601 timestamp of last retry attempt
   *
   * @generated from field: string last_retry_at = 29;
   */
  lastRetryAt: string;

  /**
   * Whether automatic retries are enabled
   *
   * @generated from field: bool auto_retry_enabled = 30;
   */
  autoRetryEnabled: boolean;

  /**
   * Metadata
   *
   * Who created the job
   *
   * @generated from field: string created_by_asset_id = 31;
   */
  createdByAssetId: string;

  /**
   * Generated but not sent to agency
   *
   * @generated from field: bool preview_only = 32;
   */
  previewOnly: boolean;

  /**
   * Optional human-readable job name
   *
   * @generated from field: string job_name = 33;
   */
  jobName: string;
};

/**
 * Describes the message hero.etl.v1.ETLJob.
 * Use `create(ETLJobSchema)` to create a new message.
 */
export const ETLJobSchema: GenMessage<ETLJob> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 0);

/**
 * Detailed error information for jobs
 *
 * @generated from message hero.etl.v1.JobError
 */
export type JobError = Message<"hero.etl.v1.JobError"> & {
  /**
   * Error code (e.g., "INVALID_DATA", "NETWORK_ERROR")
   *
   * @generated from field: string error_code = 1;
   */
  errorCode: string;

  /**
   * Human-readable error message
   *
   * @generated from field: string error_message = 2;
   */
  errorMessage: string;

  /**
   * Specific report ID that caused error (optional)
   *
   * @generated from field: string report_id = 3;
   */
  reportId: string;

  /**
   * Field path that caused error (optional)
   *
   * @generated from field: string field_path = 4;
   */
  fieldPath: string;

  /**
   * ISO8601 timestamp when error occurred
   *
   * @generated from field: string occurred_at = 5;
   */
  occurredAt: string;

  /**
   * Whether this error type is retryable
   *
   * @generated from field: bool is_retryable = 6;
   */
  isRetryable: boolean;

  /**
   * Which retry attempt this error occurred on (0 = initial)
   *
   * @generated from field: int32 retry_attempt = 7;
   */
  retryAttempt: number;
};

/**
 * Describes the message hero.etl.v1.JobError.
 * Use `create(JobErrorSchema)` to create a new message.
 */
export const JobErrorSchema: GenMessage<JobError> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 1);

/**
 * Process reports for compliance reporting (supports both specific reports and date ranges)
 *
 * @generated from message hero.etl.v1.ProcessReportsRequest
 */
export type ProcessReportsRequest = Message<"hero.etl.v1.ProcessReportsRequest"> & {
  /**
   * Target agency/recipient ID
   *
   * @generated from field: string agency_id = 1;
   */
  agencyId: string;

  /**
   * Data standard/format (NIBRS, etc.)
   *
   * @generated from field: hero.etl.v1.OutputFormat output_format = 2;
   */
  outputFormat: OutputFormat;

  /**
   * Generate but don't send to agency
   *
   * @generated from field: bool preview_only = 3;
   */
  previewOnly: boolean;

  /**
   * Optional human-readable job name
   *
   * @generated from field: string job_name = 4;
   */
  jobName: string;

  /**
   * EITHER specify reports OR date range (not both)
   *
   * Specific reports to process
   *
   * @generated from field: repeated string report_ids = 5;
   */
  reportIds: string[];

  /**
   * OR use date range with optional filters
   *
   * ISO8601 start date (optional)
   *
   * @generated from field: string date_from = 6;
   */
  dateFrom: string;

  /**
   * ISO8601 end date (optional)
   *
   * @generated from field: string date_to = 7;
   */
  dateTo: string;

  /**
   * Filter by case types (optional)
   *
   * @generated from field: repeated string case_types = 8;
   */
  caseTypes: string[];

  /**
   * Processing options
   *
   * Skip reports with validation errors
   *
   * @generated from field: bool skip_invalid_reports = 9;
   */
  skipInvalidReports: boolean;

  /**
   * Include draft/incomplete reports
   *
   * @generated from field: bool include_draft_reports = 10;
   */
  includeDraftReports: boolean;

  /**
   * Retry configuration
   *
   * Maximum retry attempts (default: 3)
   *
   * @generated from field: int32 max_retries = 11;
   */
  maxRetries: number;

  /**
   * Enable automatic retries on failure (default: true)
   *
   * @generated from field: bool auto_retry_enabled = 12;
   */
  autoRetryEnabled: boolean;
};

/**
 * Describes the message hero.etl.v1.ProcessReportsRequest.
 * Use `create(ProcessReportsRequestSchema)` to create a new message.
 */
export const ProcessReportsRequestSchema: GenMessage<ProcessReportsRequest> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 2);

/**
 * @generated from message hero.etl.v1.ProcessReportsResponse
 */
export type ProcessReportsResponse = Message<"hero.etl.v1.ProcessReportsResponse"> & {
  /**
   * Created job
   *
   * @generated from field: hero.etl.v1.ETLJob job = 1;
   */
  job?: ETLJob;

  /**
   * Estimated number of reports (if using date range)
   *
   * @generated from field: int32 estimated_report_count = 2;
   */
  estimatedReportCount: number;
};

/**
 * Describes the message hero.etl.v1.ProcessReportsResponse.
 * Use `create(ProcessReportsResponseSchema)` to create a new message.
 */
export const ProcessReportsResponseSchema: GenMessage<ProcessReportsResponse> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 3);

/**
 * Get job status and results
 *
 * @generated from message hero.etl.v1.GetJobRequest
 */
export type GetJobRequest = Message<"hero.etl.v1.GetJobRequest"> & {
  /**
   * Job ID
   *
   * @generated from field: string job_id = 1;
   */
  jobId: string;
};

/**
 * Describes the message hero.etl.v1.GetJobRequest.
 * Use `create(GetJobRequestSchema)` to create a new message.
 */
export const GetJobRequestSchema: GenMessage<GetJobRequest> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 4);

/**
 * @generated from message hero.etl.v1.GetJobResponse
 */
export type GetJobResponse = Message<"hero.etl.v1.GetJobResponse"> & {
  /**
   * Job details
   *
   * @generated from field: hero.etl.v1.ETLJob job = 1;
   */
  job?: ETLJob;
};

/**
 * Describes the message hero.etl.v1.GetJobResponse.
 * Use `create(GetJobResponseSchema)` to create a new message.
 */
export const GetJobResponseSchema: GenMessage<GetJobResponse> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 5);

/**
 * List jobs with filtering
 *
 * @generated from message hero.etl.v1.ListJobsRequest
 */
export type ListJobsRequest = Message<"hero.etl.v1.ListJobsRequest"> & {
  /**
   * Filter by agency (optional)
   *
   * @generated from field: string agency_id = 1;
   */
  agencyId: string;

  /**
   * Filter by status (optional)
   *
   * @generated from field: hero.etl.v1.JobStatus status = 2;
   */
  status: JobStatus;

  /**
   * ISO8601 start of creation date range (optional)
   *
   * @generated from field: string created_from = 3;
   */
  createdFrom: string;

  /**
   * ISO8601 end of creation date range (optional)
   *
   * @generated from field: string created_to = 4;
   */
  createdTo: string;

  /**
   * Max results per page
   *
   * @generated from field: int32 page_size = 5;
   */
  pageSize: number;

  /**
   * Pagination token
   *
   * @generated from field: string page_token = 6;
   */
  pageToken: string;
};

/**
 * Describes the message hero.etl.v1.ListJobsRequest.
 * Use `create(ListJobsRequestSchema)` to create a new message.
 */
export const ListJobsRequestSchema: GenMessage<ListJobsRequest> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 6);

/**
 * @generated from message hero.etl.v1.ListJobsResponse
 */
export type ListJobsResponse = Message<"hero.etl.v1.ListJobsResponse"> & {
  /**
   * Jobs
   *
   * @generated from field: repeated hero.etl.v1.ETLJob jobs = 1;
   */
  jobs: ETLJob[];

  /**
   * Next page token
   *
   * @generated from field: string next_page_token = 2;
   */
  nextPageToken: string;
};

/**
 * Describes the message hero.etl.v1.ListJobsResponse.
 * Use `create(ListJobsResponseSchema)` to create a new message.
 */
export const ListJobsResponseSchema: GenMessage<ListJobsResponse> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 7);

/**
 * Cancel a running job
 *
 * @generated from message hero.etl.v1.CancelJobRequest
 */
export type CancelJobRequest = Message<"hero.etl.v1.CancelJobRequest"> & {
  /**
   * Job to cancel
   *
   * @generated from field: string job_id = 1;
   */
  jobId: string;
};

/**
 * Describes the message hero.etl.v1.CancelJobRequest.
 * Use `create(CancelJobRequestSchema)` to create a new message.
 */
export const CancelJobRequestSchema: GenMessage<CancelJobRequest> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 8);

/**
 * @generated from message hero.etl.v1.CancelJobResponse
 */
export type CancelJobResponse = Message<"hero.etl.v1.CancelJobResponse"> & {
  /**
   * Updated job
   *
   * @generated from field: hero.etl.v1.ETLJob job = 1;
   */
  job?: ETLJob;
};

/**
 * Describes the message hero.etl.v1.CancelJobResponse.
 * Use `create(CancelJobResponseSchema)` to create a new message.
 */
export const CancelJobResponseSchema: GenMessage<CancelJobResponse> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 9);

/**
 * Retry a failed job
 *
 * @generated from message hero.etl.v1.RetryJobRequest
 */
export type RetryJobRequest = Message<"hero.etl.v1.RetryJobRequest"> & {
  /**
   * Job to retry
   *
   * @generated from field: string job_id = 1;
   */
  jobId: string;

  /**
   * Force retry even if max retries exceeded
   *
   * @generated from field: bool force_retry = 2;
   */
  forceRetry: boolean;

  /**
   * Override max retries for this attempt (optional)
   *
   * @generated from field: int32 override_max_retries = 3;
   */
  overrideMaxRetries: number;
};

/**
 * Describes the message hero.etl.v1.RetryJobRequest.
 * Use `create(RetryJobRequestSchema)` to create a new message.
 */
export const RetryJobRequestSchema: GenMessage<RetryJobRequest> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 10);

/**
 * @generated from message hero.etl.v1.RetryJobResponse
 */
export type RetryJobResponse = Message<"hero.etl.v1.RetryJobResponse"> & {
  /**
   * Updated job with retry information
   *
   * @generated from field: hero.etl.v1.ETLJob job = 1;
   */
  job?: ETLJob;

  /**
   * Whether retry was successfully scheduled
   *
   * @generated from field: bool retry_scheduled = 2;
   */
  retryScheduled: boolean;

  /**
   * Reason if retry was not scheduled
   *
   * @generated from field: string retry_reason = 3;
   */
  retryReason: string;
};

/**
 * Describes the message hero.etl.v1.RetryJobResponse.
 * Use `create(RetryJobResponseSchema)` to create a new message.
 */
export const RetryJobResponseSchema: GenMessage<RetryJobResponse> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 11);

/**
 * Download generated content
 *
 * @generated from message hero.etl.v1.DownloadJobContentRequest
 */
export type DownloadJobContentRequest = Message<"hero.etl.v1.DownloadJobContentRequest"> & {
  /**
   * Job ID
   *
   * @generated from field: string job_id = 1;
   */
  jobId: string;
};

/**
 * Describes the message hero.etl.v1.DownloadJobContentRequest.
 * Use `create(DownloadJobContentRequestSchema)` to create a new message.
 */
export const DownloadJobContentRequestSchema: GenMessage<DownloadJobContentRequest> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 12);

/**
 * @generated from message hero.etl.v1.DownloadJobContentResponse
 */
export type DownloadJobContentResponse = Message<"hero.etl.v1.DownloadJobContentResponse"> & {
  /**
   * Generated content
   *
   * @generated from field: bytes content = 1;
   */
  content: Uint8Array;

  /**
   * MIME type
   *
   * @generated from field: string content_type = 2;
   */
  contentType: string;

  /**
   * Suggested filename
   *
   * @generated from field: string filename = 3;
   */
  filename: string;

  /**
   * Content size
   *
   * @generated from field: int64 size_bytes = 4;
   */
  sizeBytes: bigint;
};

/**
 * Describes the message hero.etl.v1.DownloadJobContentResponse.
 * Use `create(DownloadJobContentResponseSchema)` to create a new message.
 */
export const DownloadJobContentResponseSchema: GenMessage<DownloadJobContentResponse> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 13);

/**
 * Get list of available agencies
 *
 * No parameters needed
 *
 * @generated from message hero.etl.v1.ListAgenciesRequest
 */
export type ListAgenciesRequest = Message<"hero.etl.v1.ListAgenciesRequest"> & {
};

/**
 * Describes the message hero.etl.v1.ListAgenciesRequest.
 * Use `create(ListAgenciesRequestSchema)` to create a new message.
 */
export const ListAgenciesRequestSchema: GenMessage<ListAgenciesRequest> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 14);

/**
 * @generated from message hero.etl.v1.ListAgenciesResponse
 */
export type ListAgenciesResponse = Message<"hero.etl.v1.ListAgenciesResponse"> & {
  /**
   * Available agencies
   *
   * @generated from field: repeated hero.etl.v1.AgencyInfo agencies = 1;
   */
  agencies: AgencyInfo[];
};

/**
 * Describes the message hero.etl.v1.ListAgenciesResponse.
 * Use `create(ListAgenciesResponseSchema)` to create a new message.
 */
export const ListAgenciesResponseSchema: GenMessage<ListAgenciesResponse> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 15);

/**
 * Agency/recipient information with supported data standards
 *
 * @generated from message hero.etl.v1.AgencyInfo
 */
export type AgencyInfo = Message<"hero.etl.v1.AgencyInfo"> & {
  /**
   * Agency ID (e.g., "FBI", "STATE_POLICE", "UNIVERSITY_POLICE")
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * Display name
   *
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * Supported data standards (NIBRS, etc.)
   *
   * @generated from field: repeated hero.etl.v1.OutputFormat supported_formats = 3;
   */
  supportedFormats: OutputFormat[];

  /**
   * Whether agency is enabled
   *
   * @generated from field: bool enabled = 4;
   */
  enabled: boolean;

  /**
   * Type: "FEDERAL", "STATE", "LOCAL", "UNIVERSITY"
   *
   * @generated from field: string agency_type = 5;
   */
  agencyType: string;
};

/**
 * Describes the message hero.etl.v1.AgencyInfo.
 * Use `create(AgencyInfoSchema)` to create a new message.
 */
export const AgencyInfoSchema: GenMessage<AgencyInfo> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 16);

/**
 * Extract raw report data without transformation
 *
 * @generated from message hero.etl.v1.ExtractReportDataRequest
 */
export type ExtractReportDataRequest = Message<"hero.etl.v1.ExtractReportDataRequest"> & {
  /**
   * Single report to extract data from
   *
   * @generated from field: string report_id = 1;
   */
  reportId: string;
};

/**
 * Describes the message hero.etl.v1.ExtractReportDataRequest.
 * Use `create(ExtractReportDataRequestSchema)` to create a new message.
 */
export const ExtractReportDataRequestSchema: GenMessage<ExtractReportDataRequest> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 17);

/**
 * @generated from message hero.etl.v1.ExtractReportDataResponse
 */
export type ExtractReportDataResponse = Message<"hero.etl.v1.ExtractReportDataResponse"> & {
  /**
   * Complete extracted data as structured object
   *
   * @generated from field: google.protobuf.Struct extracted_data = 1;
   */
  extractedData?: JsonObject;

  /**
   * ISO8601 timestamp when extraction was done
   *
   * @generated from field: string extraction_time = 2;
   */
  extractionTime: string;

  /**
   * Extraction warnings
   *
   * @generated from field: repeated string warnings = 3;
   */
  warnings: string[];

  /**
   * Whether extraction succeeded
   *
   * @generated from field: bool success = 4;
   */
  success: boolean;

  /**
   * Error if failed
   *
   * @generated from field: string error_message = 5;
   */
  errorMessage: string;

  /**
   * Number of data sets extracted
   *
   * @generated from field: int32 data_sets_count = 6;
   */
  dataSetsCount: number;

  /**
   * Names of extracted data sets
   *
   * @generated from field: repeated string data_set_names = 7;
   */
  dataSetNames: string[];
};

/**
 * Describes the message hero.etl.v1.ExtractReportDataResponse.
 * Use `create(ExtractReportDataResponseSchema)` to create a new message.
 */
export const ExtractReportDataResponseSchema: GenMessage<ExtractReportDataResponse> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 18);

/**
 * Test report transformation - complete transformation pipeline with formatting
 *
 * @generated from message hero.etl.v1.TestReportTransformationRequest
 */
export type TestReportTransformationRequest = Message<"hero.etl.v1.TestReportTransformationRequest"> & {
  /**
   * Single report to test
   *
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * Target format (NIBRS_XML, etc.)
   *
   * @generated from field: hero.etl.v1.OutputFormat output_format = 2;
   */
  outputFormat: OutputFormat;

  /**
   * Run validation on output (optional)
   *
   * @generated from field: bool validate = 3;
   */
  validate: boolean;

  /**
   * Optional: Override mapping config (JSON string)
   *
   * @generated from field: string mapping_config_json = 4;
   */
  mappingConfigJson: string;

  /**
   * Optional: Override template content
   *
   * @generated from field: string template_content = 5;
   */
  templateContent: string;
};

/**
 * Describes the message hero.etl.v1.TestReportTransformationRequest.
 * Use `create(TestReportTransformationRequestSchema)` to create a new message.
 */
export const TestReportTransformationRequestSchema: GenMessage<TestReportTransformationRequest> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 19);

/**
 * @generated from message hero.etl.v1.TestReportTransformationResponse
 */
export type TestReportTransformationResponse = Message<"hero.etl.v1.TestReportTransformationResponse"> & {
  /**
   * Generated content
   *
   * @generated from field: bytes transformed_content = 1;
   */
  transformedContent: Uint8Array;

  /**
   * MIME type
   *
   * @generated from field: string content_type = 2;
   */
  contentType: string;

  /**
   * ISO8601 timestamp when transformation was done
   *
   * @generated from field: string transformation_time = 3;
   */
  transformationTime: string;

  /**
   * Transformation warnings
   *
   * @generated from field: repeated string warnings = 4;
   */
  warnings: string[];

  /**
   * Whether transformation succeeded
   *
   * @generated from field: bool success = 5;
   */
  success: boolean;

  /**
   * Error if failed
   *
   * @generated from field: string error_message = 6;
   */
  errorMessage: string;

  /**
   * Detailed validation errors
   *
   * @generated from field: repeated hero.etl.v1.ValidationError validation_errors = 7;
   */
  validationErrors: ValidationError[];

  /**
   * Human-readable version of transformed_content (for debugging)
   *
   * @generated from field: string readable_content = 8;
   */
  readableContent: string;

  /**
   * Mapping config file path used
   *
   * @generated from field: string mapping_config_used = 9;
   */
  mappingConfigUsed: string;

  /**
   * Template file path used for formatting
   *
   * @generated from field: string template_used = 10;
   */
  templateUsed: string;
};

/**
 * Describes the message hero.etl.v1.TestReportTransformationResponse.
 * Use `create(TestReportTransformationResponseSchema)` to create a new message.
 */
export const TestReportTransformationResponseSchema: GenMessage<TestReportTransformationResponse> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 20);

/**
 * Test mapping configuration transformation without template formatting
 *
 * @generated from message hero.etl.v1.TestReportMappingRequest
 */
export type TestReportMappingRequest = Message<"hero.etl.v1.TestReportMappingRequest"> & {
  /**
   * Single report to test
   *
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * Output format/standard (NIBRS, etc.)
   *
   * @generated from field: hero.etl.v1.OutputFormat output_format = 2;
   */
  outputFormat: OutputFormat;

  /**
   * Optional: Override mapping config (JSON string)
   *
   * @generated from field: string mapping_config_json = 3;
   */
  mappingConfigJson: string;
};

/**
 * Describes the message hero.etl.v1.TestReportMappingRequest.
 * Use `create(TestReportMappingRequestSchema)` to create a new message.
 */
export const TestReportMappingRequestSchema: GenMessage<TestReportMappingRequest> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 21);

/**
 * @generated from message hero.etl.v1.TestReportMappingResponse
 */
export type TestReportMappingResponse = Message<"hero.etl.v1.TestReportMappingResponse"> & {
  /**
   * Config-mapped data as structured object
   *
   * @generated from field: google.protobuf.Struct mapped_data = 1;
   */
  mappedData?: JsonObject;

  /**
   * MIME type (application/json)
   *
   * @generated from field: string content_type = 2;
   */
  contentType: string;

  /**
   * Config file path used
   *
   * @generated from field: string mapping_config_used = 3;
   */
  mappingConfigUsed: string;

  /**
   * ISO8601 timestamp when mapping was done
   *
   * @generated from field: string transformation_time = 4;
   */
  transformationTime: string;

  /**
   * Mapping warnings
   *
   * @generated from field: repeated string warnings = 5;
   */
  warnings: string[];

  /**
   * Whether mapping succeeded
   *
   * @generated from field: bool success = 6;
   */
  success: boolean;

  /**
   * Error if failed
   *
   * @generated from field: string error_message = 7;
   */
  errorMessage: string;
};

/**
 * Describes the message hero.etl.v1.TestReportMappingResponse.
 * Use `create(TestReportMappingResponseSchema)` to create a new message.
 */
export const TestReportMappingResponseSchema: GenMessage<TestReportMappingResponse> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 22);

/**
 * Validation error details
 *
 * @generated from message hero.etl.v1.ValidationError
 */
export type ValidationError = Message<"hero.etl.v1.ValidationError"> & {
  /**
   * Field path that failed validation
   *
   * @generated from field: string field_path = 1;
   */
  fieldPath: string;

  /**
   * Error code (e.g., "REQUIRED_FIELD", "INVALID_FORMAT")
   *
   * @generated from field: string error_code = 2;
   */
  errorCode: string;

  /**
   * Human-readable error message
   *
   * @generated from field: string error_message = 3;
   */
  errorMessage: string;

  /**
   * Expected value or format (optional)
   *
   * @generated from field: string expected_value = 4;
   */
  expectedValue: string;

  /**
   * Actual value that failed (optional)
   *
   * @generated from field: string actual_value = 5;
   */
  actualValue: string;
};

/**
 * Describes the message hero.etl.v1.ValidationError.
 * Use `create(ValidationErrorSchema)` to create a new message.
 */
export const ValidationErrorSchema: GenMessage<ValidationError> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 23);

/**
 * Get job statistics
 *
 * @generated from message hero.etl.v1.GetJobStatsRequest
 */
export type GetJobStatsRequest = Message<"hero.etl.v1.GetJobStatsRequest"> & {
  /**
   * Filter by agency (optional)
   *
   * @generated from field: string agency_id = 1;
   */
  agencyId: string;

  /**
   * Filter by format (optional)
   *
   * @generated from field: hero.etl.v1.OutputFormat output_format = 2;
   */
  outputFormat: OutputFormat;

  /**
   * ISO8601 start date (optional)
   *
   * @generated from field: string date_from = 3;
   */
  dateFrom: string;

  /**
   * ISO8601 end date (optional)
   *
   * @generated from field: string date_to = 4;
   */
  dateTo: string;
};

/**
 * Describes the message hero.etl.v1.GetJobStatsRequest.
 * Use `create(GetJobStatsRequestSchema)` to create a new message.
 */
export const GetJobStatsRequestSchema: GenMessage<GetJobStatsRequest> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 24);

/**
 * @generated from message hero.etl.v1.GetJobStatsResponse
 */
export type GetJobStatsResponse = Message<"hero.etl.v1.GetJobStatsResponse"> & {
  /**
   * Total jobs in period
   *
   * @generated from field: int32 total_jobs = 1;
   */
  totalJobs: number;

  /**
   * Successfully completed jobs
   *
   * @generated from field: int32 completed_jobs = 2;
   */
  completedJobs: number;

  /**
   * Failed jobs
   *
   * @generated from field: int32 failed_jobs = 3;
   */
  failedJobs: number;

  /**
   * Cancelled jobs
   *
   * @generated from field: int32 cancelled_jobs = 4;
   */
  cancelledJobs: number;

  /**
   * Currently running jobs
   *
   * @generated from field: int32 running_jobs = 5;
   */
  runningJobs: number;

  /**
   * Total reports processed
   *
   * @generated from field: int32 total_reports_processed = 6;
   */
  totalReportsProcessed: number;

  /**
   * Total content generated
   *
   * @generated from field: int64 total_content_size_bytes = 7;
   */
  totalContentSizeBytes: bigint;

  /**
   * Per-agency statistics
   *
   * @generated from field: repeated hero.etl.v1.AgencyStats agency_stats = 8;
   */
  agencyStats: AgencyStats[];
};

/**
 * Describes the message hero.etl.v1.GetJobStatsResponse.
 * Use `create(GetJobStatsResponseSchema)` to create a new message.
 */
export const GetJobStatsResponseSchema: GenMessage<GetJobStatsResponse> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 25);

/**
 * Per-agency statistics
 *
 * @generated from message hero.etl.v1.AgencyStats
 */
export type AgencyStats = Message<"hero.etl.v1.AgencyStats"> & {
  /**
   * Agency ID
   *
   * @generated from field: string agency_id = 1;
   */
  agencyId: string;

  /**
   * Agency name
   *
   * @generated from field: string agency_name = 2;
   */
  agencyName: string;

  /**
   * Number of jobs for this agency
   *
   * @generated from field: int32 job_count = 3;
   */
  jobCount: number;

  /**
   * Number of reports processed
   *
   * @generated from field: int32 report_count = 4;
   */
  reportCount: number;

  /**
   * Total content size for agency
   *
   * @generated from field: int64 content_size_bytes = 5;
   */
  contentSizeBytes: bigint;

  /**
   * ISO8601 timestamp of last submission
   *
   * @generated from field: string last_submission_at = 6;
   */
  lastSubmissionAt: string;
};

/**
 * Describes the message hero.etl.v1.AgencyStats.
 * Use `create(AgencyStatsSchema)` to create a new message.
 */
export const AgencyStatsSchema: GenMessage<AgencyStats> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 26);

/**
 * Validate reports before processing
 *
 * @generated from message hero.etl.v1.ValidateReportsRequest
 */
export type ValidateReportsRequest = Message<"hero.etl.v1.ValidateReportsRequest"> & {
  /**
   * Reports to validate
   *
   * @generated from field: repeated string report_ids = 1;
   */
  reportIds: string[];

  /**
   * Target data standard
   *
   * @generated from field: hero.etl.v1.OutputFormat output_format = 2;
   */
  outputFormat: OutputFormat;

  /**
   * Target agency (optional)
   *
   * @generated from field: string agency_id = 3;
   */
  agencyId: string;
};

/**
 * Describes the message hero.etl.v1.ValidateReportsRequest.
 * Use `create(ValidateReportsRequestSchema)` to create a new message.
 */
export const ValidateReportsRequestSchema: GenMessage<ValidateReportsRequest> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 27);

/**
 * @generated from message hero.etl.v1.ValidateReportsResponse
 */
export type ValidateReportsResponse = Message<"hero.etl.v1.ValidateReportsResponse"> & {
  /**
   * Validation results per report
   *
   * @generated from field: repeated hero.etl.v1.ReportValidationResult results = 1;
   */
  results: ReportValidationResult[];

  /**
   * Number of valid reports
   *
   * @generated from field: int32 valid_reports = 2;
   */
  validReports: number;

  /**
   * Number of invalid reports
   *
   * @generated from field: int32 invalid_reports = 3;
   */
  invalidReports: number;

  /**
   * Whether all reports are valid
   *
   * @generated from field: bool all_valid = 4;
   */
  allValid: boolean;
};

/**
 * Describes the message hero.etl.v1.ValidateReportsResponse.
 * Use `create(ValidateReportsResponseSchema)` to create a new message.
 */
export const ValidateReportsResponseSchema: GenMessage<ValidateReportsResponse> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 28);

/**
 * Validation result for a single report
 *
 * @generated from message hero.etl.v1.ReportValidationResult
 */
export type ReportValidationResult = Message<"hero.etl.v1.ReportValidationResult"> & {
  /**
   * Report ID
   *
   * @generated from field: string report_id = 1;
   */
  reportId: string;

  /**
   * Whether report is valid
   *
   * @generated from field: bool valid = 2;
   */
  valid: boolean;

  /**
   * Validation errors
   *
   * @generated from field: repeated hero.etl.v1.ValidationError errors = 3;
   */
  errors: ValidationError[];

  /**
   * Validation warnings
   *
   * @generated from field: repeated string warnings = 4;
   */
  warnings: string[];
};

/**
 * Describes the message hero.etl.v1.ReportValidationResult.
 * Use `create(ReportValidationResultSchema)` to create a new message.
 */
export const ReportValidationResultSchema: GenMessage<ReportValidationResult> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 29);

/**
 * Update job progress counters
 *
 * @generated from message hero.etl.v1.UpdateJobProgressRequest
 */
export type UpdateJobProgressRequest = Message<"hero.etl.v1.UpdateJobProgressRequest"> & {
  /**
   * Job ID to update
   *
   * @generated from field: string job_id = 1;
   */
  jobId: string;

  /**
   * Total reports to process
   *
   * @generated from field: int32 total_reports = 2;
   */
  totalReports: number;

  /**
   * Reports successfully processed
   *
   * @generated from field: int32 reports_processed = 3;
   */
  reportsProcessed: number;

  /**
   * Reports that failed processing
   *
   * @generated from field: int32 reports_failed = 4;
   */
  reportsFailed: number;

  /**
   * Reports skipped (e.g., invalid data)
   *
   * @generated from field: int32 reports_skipped = 5;
   */
  reportsSkipped: number;

  /**
   * Optional progress note or status message
   *
   * @generated from field: string progress_note = 6;
   */
  progressNote: string;
};

/**
 * Describes the message hero.etl.v1.UpdateJobProgressRequest.
 * Use `create(UpdateJobProgressRequestSchema)` to create a new message.
 */
export const UpdateJobProgressRequestSchema: GenMessage<UpdateJobProgressRequest> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 30);

/**
 * @generated from message hero.etl.v1.UpdateJobProgressResponse
 */
export type UpdateJobProgressResponse = Message<"hero.etl.v1.UpdateJobProgressResponse"> & {
  /**
   * Updated job with new progress
   *
   * @generated from field: hero.etl.v1.ETLJob job = 1;
   */
  job?: ETLJob;

  /**
   * Whether progress was successfully updated
   *
   * @generated from field: bool progress_updated = 2;
   */
  progressUpdated: boolean;
};

/**
 * Describes the message hero.etl.v1.UpdateJobProgressResponse.
 * Use `create(UpdateJobProgressResponseSchema)` to create a new message.
 */
export const UpdateJobProgressResponseSchema: GenMessage<UpdateJobProgressResponse> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 31);

/**
 * Get statistics for a specific agency
 *
 * @generated from message hero.etl.v1.GetAgencyStatsRequest
 */
export type GetAgencyStatsRequest = Message<"hero.etl.v1.GetAgencyStatsRequest"> & {
  /**
   * Agency ID (required)
   *
   * @generated from field: string agency_id = 1;
   */
  agencyId: string;

  /**
   * ISO8601 start date (optional)
   *
   * @generated from field: string date_from = 2;
   */
  dateFrom: string;

  /**
   * ISO8601 end date (optional)
   *
   * @generated from field: string date_to = 3;
   */
  dateTo: string;

  /**
   * Include detailed job breakdown (optional)
   *
   * @generated from field: bool include_job_details = 4;
   */
  includeJobDetails: boolean;
};

/**
 * Describes the message hero.etl.v1.GetAgencyStatsRequest.
 * Use `create(GetAgencyStatsRequestSchema)` to create a new message.
 */
export const GetAgencyStatsRequestSchema: GenMessage<GetAgencyStatsRequest> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 32);

/**
 * @generated from message hero.etl.v1.GetAgencyStatsResponse
 */
export type GetAgencyStatsResponse = Message<"hero.etl.v1.GetAgencyStatsResponse"> & {
  /**
   * Agency statistics
   *
   * @generated from field: hero.etl.v1.AgencyStats agency_stats = 1;
   */
  agencyStats?: AgencyStats;

  /**
   * Recent jobs for this agency (if include_job_details=true)
   *
   * @generated from field: repeated hero.etl.v1.JobSummary recent_jobs = 2;
   */
  recentJobs: JobSummary[];

  /**
   * ISO8601 timestamp when stats were generated
   *
   * @generated from field: string generated_at = 3;
   */
  generatedAt: string;
};

/**
 * Describes the message hero.etl.v1.GetAgencyStatsResponse.
 * Use `create(GetAgencyStatsResponseSchema)` to create a new message.
 */
export const GetAgencyStatsResponseSchema: GenMessage<GetAgencyStatsResponse> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 33);

/**
 * Summary information for a job (used in agency stats)
 *
 * @generated from message hero.etl.v1.JobSummary
 */
export type JobSummary = Message<"hero.etl.v1.JobSummary"> & {
  /**
   * Job ID
   *
   * @generated from field: string job_id = 1;
   */
  jobId: string;

  /**
   * Job status
   *
   * @generated from field: hero.etl.v1.JobStatus status = 2;
   */
  status: JobStatus;

  /**
   * Output format
   *
   * @generated from field: hero.etl.v1.OutputFormat output_format = 3;
   */
  outputFormat: OutputFormat;

  /**
   * Number of reports processed
   *
   * @generated from field: int32 reports_processed = 4;
   */
  reportsProcessed: number;

  /**
   * ISO8601 timestamp when job was created
   *
   * @generated from field: string created_at = 5;
   */
  createdAt: string;

  /**
   * ISO8601 timestamp when job completed (if applicable)
   *
   * @generated from field: string completed_at = 6;
   */
  completedAt: string;

  /**
   * Size of generated content
   *
   * @generated from field: int64 content_size_bytes = 7;
   */
  contentSizeBytes: bigint;
};

/**
 * Describes the message hero.etl.v1.JobSummary.
 * Use `create(JobSummarySchema)` to create a new message.
 */
export const JobSummarySchema: GenMessage<JobSummary> = /*@__PURE__*/
  messageDesc(file_hero_etl_v1_etl, 34);

/**
 * ETL Job status
 *
 * @generated from enum hero.etl.v1.JobStatus
 */
export enum JobStatus {
  /**
   * @generated from enum value: JOB_STATUS_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * Job queued, waiting to start
   *
   * @generated from enum value: JOB_STATUS_PENDING = 1;
   */
  PENDING = 1,

  /**
   * Extracting report data from services
   *
   * @generated from enum value: JOB_STATUS_EXTRACTING = 2;
   */
  EXTRACTING = 2,

  /**
   * Transforming to agency format
   *
   * @generated from enum value: JOB_STATUS_TRANSFORMING = 3;
   */
  TRANSFORMING = 3,

  /**
   * Sending/loading data to agency
   *
   * @generated from enum value: JOB_STATUS_LOADING = 4;
   */
  LOADING = 4,

  /**
   * Successfully completed
   *
   * @generated from enum value: JOB_STATUS_COMPLETED = 5;
   */
  COMPLETED = 5,

  /**
   * Failed permanently
   *
   * @generated from enum value: JOB_STATUS_FAILED = 6;
   */
  FAILED = 6,

  /**
   * Cancelled by user
   *
   * @generated from enum value: JOB_STATUS_CANCELLED = 7;
   */
  CANCELLED = 7,
}

/**
 * Describes the enum hero.etl.v1.JobStatus.
 */
export const JobStatusSchema: GenEnum<JobStatus> = /*@__PURE__*/
  enumDesc(file_hero_etl_v1_etl, 0);

/**
 * Data standards/formats for law enforcement reporting
 *
 * @generated from enum hero.etl.v1.OutputFormat
 */
export enum OutputFormat {
  /**
   * @generated from enum value: OUTPUT_FORMAT_UNSPECIFIED = 0;
   */
  UNSPECIFIED = 0,

  /**
   * FBI NIBRS XML format
   *
   * @generated from enum value: OUTPUT_FORMAT_NIBRS_XML = 1;
   */
  NIBRS_XML = 1,
}

/**
 * Describes the enum hero.etl.v1.OutputFormat.
 */
export const OutputFormatSchema: GenEnum<OutputFormat> = /*@__PURE__*/
  enumDesc(file_hero_etl_v1_etl, 1);

/**
 * Core ETL operations
 *
 * @generated from service hero.etl.v1.ETLService
 */
export const ETLService: GenService<{
  /**
   * ProcessReports: Transform reports to compliance data standards and submit to agencies
   * Supports both specific reports and date range queries with filtering
   * Use cases:
   *   - NIBRS reporting: "Send these 3 incident reports to FBI in NIBRS XML format"
   *   - Campus reporting: "Send all campus crimes from last month in NIBRS XML"
   *   - State reporting: "Send all crimes from this quarter to State Police in NIBRS format"
   * Creates ETL job that extracts complete report data and transforms using data standard strategy
   *
   * @generated from rpc hero.etl.v1.ETLService.ProcessReports
   */
  processReports: {
    methodKind: "unary";
    input: typeof ProcessReportsRequestSchema;
    output: typeof ProcessReportsResponseSchema;
  },
  /**
   * ExtractReportData: Extract raw report data without transformation
   * Use cases:
   *   - "Show me all the raw data extracted from this report"
   *   - "Debug what data sets are being extracted from report XYZ"
   *   - "Inspect extracted entities, assets, and relationships"
   * Returns complete extracted data as structured object for inspection and debugging
   * Critical for understanding what data the extractor is finding
   *
   * @generated from rpc hero.etl.v1.ETLService.ExtractReportData
   */
  extractReportData: {
    methodKind: "unary";
    input: typeof ExtractReportDataRequestSchema;
    output: typeof ExtractReportDataResponseSchema;
  },
  /**
   * TestReportMapping: Preview mapping configuration transformation without template formatting
   * Use cases:
   *   - "Show me the data structure after mapping config transformation"
   *   - "Debug what fields are available for template development"
   *   - "Inspect intermediate transformation state before format rendering"
   * Returns the structured output from mapping transformation for template development
   *
   * @generated from rpc hero.etl.v1.ETLService.TestReportMapping
   */
  testReportMapping: {
    methodKind: "unary";
    input: typeof TestReportMappingRequestSchema;
    output: typeof TestReportMappingResponseSchema;
  },
  /**
   * TestReportTransformation: Complete transformation pipeline - extract, map, and format
   * Use cases: 
   *   - "Show me what this report looks like in NIBRS XML format before sending"
   *   - "Preview this campus incident in NIBRS format"
   *   - "Convert report to final XML format"
   * Internally calls TestReportMapping first, then applies template formatting
   * Critical for debugging field mappings and data standard compliance
   * Returns the formatted content ready for submission
   *
   * @generated from rpc hero.etl.v1.ETLService.TestReportTransformation
   */
  testReportTransformation: {
    methodKind: "unary";
    input: typeof TestReportTransformationRequestSchema;
    output: typeof TestReportTransformationResponseSchema;
  },
  /**
   * GetJob: Get current status and details of an ETL job
   * Use case: "What's the status of job XYZ? Did it complete successfully?"
   * Returns job status, progress, errors, and generated content for inspection
   *
   * @generated from rpc hero.etl.v1.ETLService.GetJob
   */
  getJob: {
    methodKind: "unary";
    input: typeof GetJobRequestSchema;
    output: typeof GetJobResponseSchema;
  },
  /**
   * ListJobs: List ETL jobs with filtering and pagination
   * Use cases: 
   *   - "Show me all failed FBI jobs from this week"
   *   - "List all completed NIBRS jobs from January 1-15"
   *   - "Show me all jobs for State Police created yesterday"
   * Supports filtering by agency, status, and creation date range
   *
   * @generated from rpc hero.etl.v1.ETLService.ListJobs
   */
  listJobs: {
    methodKind: "unary";
    input: typeof ListJobsRequestSchema;
    output: typeof ListJobsResponseSchema;
  },
  /**
   * CancelJob: Cancel a running or pending ETL job
   * Use case: "Stop that bulk job, I need to fix the data first"
   * Prevents unnecessary processing or sending of incorrect data
   *
   * @generated from rpc hero.etl.v1.ETLService.CancelJob
   */
  cancelJob: {
    methodKind: "unary";
    input: typeof CancelJobRequestSchema;
    output: typeof CancelJobResponseSchema;
  },
  /**
   * RetryJob: Retry a failed ETL job
   * Use cases:
   *   - "Retry that failed FBI submission after fixing the network issue"
   *   - "Force retry this job even though it exceeded max retries"
   *   - "Retry with increased retry limit for this complex job"
   * Automatically handles retry logic with exponential backoff and error classification
   *
   * @generated from rpc hero.etl.v1.ETLService.RetryJob
   */
  retryJob: {
    methodKind: "unary";
    input: typeof RetryJobRequestSchema;
    output: typeof RetryJobResponseSchema;
  },
  /**
   * DownloadJobContent: Download the generated content from a completed job
   * Use case: "Let me download the NIBRS XML that was generated for review"
   * Essential for content inspection, compliance review, and manual submission
   *
   * @generated from rpc hero.etl.v1.ETLService.DownloadJobContent
   */
  downloadJobContent: {
    methodKind: "unary";
    input: typeof DownloadJobContentRequestSchema;
    output: typeof DownloadJobContentResponseSchema;
  },
  /**
   * ListAgencies: Get list of available agencies and their supported formats
   * Use case: "What agencies can I send reports to and what formats do they support?"
   * Returns agency info including supported output formats (NIBRS, etc.)
   *
   * @generated from rpc hero.etl.v1.ETLService.ListAgencies
   */
  listAgencies: {
    methodKind: "unary";
    input: typeof ListAgenciesRequestSchema;
    output: typeof ListAgenciesResponseSchema;
  },
  /**
   * GetJobStats: Get statistical overview of ETL jobs
   * Use cases:
   *   - "Show me ETL job statistics for this month"
   *   - "How many NIBRS jobs have we completed this quarter?"
   *   - "What's our success rate for FBI submissions?"
   * Returns job counts, success rates, and per-agency statistics
   *
   * @generated from rpc hero.etl.v1.ETLService.GetJobStats
   */
  getJobStats: {
    methodKind: "unary";
    input: typeof GetJobStatsRequestSchema;
    output: typeof GetJobStatsResponseSchema;
  },
  /**
   * ValidateReports: Validate reports against data standard before processing
   * Use cases:
   *   - "Check if these reports are valid for NIBRS before submitting"
   *   - "Validate campus incidents against data standard requirements"
   * Returns detailed validation results and error information
   *
   * @generated from rpc hero.etl.v1.ETLService.ValidateReports
   */
  validateReports: {
    methodKind: "unary";
    input: typeof ValidateReportsRequestSchema;
    output: typeof ValidateReportsResponseSchema;
  },
  /**
   * UpdateJobProgress: Update job progress counters
   * Use cases:
   *   - "External job processor updating progress: 50 out of 100 reports processed"
   *   - "Distributed ETL system reporting partial completion"
   *   - "Real-time progress updates for long-running jobs"
   * Allows external systems to update job progress for monitoring and status tracking
   *
   * @generated from rpc hero.etl.v1.ETLService.UpdateJobProgress
   */
  updateJobProgress: {
    methodKind: "unary";
    input: typeof UpdateJobProgressRequestSchema;
    output: typeof UpdateJobProgressResponseSchema;
  },
  /**
   * GetAgencyStats: Get detailed statistics for a specific agency
   * Use cases:
   *   - "Show me FBI submission statistics for this quarter"
   *   - "Generate agency-specific dashboard data"
   *   - "Track performance metrics per agency"
   * Returns detailed statistics for a single agency including submission history
   *
   * @generated from rpc hero.etl.v1.ETLService.GetAgencyStats
   */
  getAgencyStats: {
    methodKind: "unary";
    input: typeof GetAgencyStatsRequestSchema;
    output: typeof GetAgencyStatsResponseSchema;
  },
}> = /*@__PURE__*/
  serviceDesc(file_hero_etl_v1_etl, 0);

