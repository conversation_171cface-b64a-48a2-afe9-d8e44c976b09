// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: hero/etl/v1/etl.proto

package etlconnect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	http "net/http"
	v1 "proto/hero/etl/v1"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// ETLServiceName is the fully-qualified name of the ETLService service.
	ETLServiceName = "hero.etl.v1.ETLService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// ETLServiceProcessReportsProcedure is the fully-qualified name of the ETLService's ProcessReports
	// RPC.
	ETLServiceProcessReportsProcedure = "/hero.etl.v1.ETLService/ProcessReports"
	// ETLServiceExtractReportDataProcedure is the fully-qualified name of the ETLService's
	// ExtractReportData RPC.
	ETLServiceExtractReportDataProcedure = "/hero.etl.v1.ETLService/ExtractReportData"
	// ETLServiceTestReportMappingProcedure is the fully-qualified name of the ETLService's
	// TestReportMapping RPC.
	ETLServiceTestReportMappingProcedure = "/hero.etl.v1.ETLService/TestReportMapping"
	// ETLServiceTestReportTransformationProcedure is the fully-qualified name of the ETLService's
	// TestReportTransformation RPC.
	ETLServiceTestReportTransformationProcedure = "/hero.etl.v1.ETLService/TestReportTransformation"
	// ETLServiceGetJobProcedure is the fully-qualified name of the ETLService's GetJob RPC.
	ETLServiceGetJobProcedure = "/hero.etl.v1.ETLService/GetJob"
	// ETLServiceListJobsProcedure is the fully-qualified name of the ETLService's ListJobs RPC.
	ETLServiceListJobsProcedure = "/hero.etl.v1.ETLService/ListJobs"
	// ETLServiceCancelJobProcedure is the fully-qualified name of the ETLService's CancelJob RPC.
	ETLServiceCancelJobProcedure = "/hero.etl.v1.ETLService/CancelJob"
	// ETLServiceRetryJobProcedure is the fully-qualified name of the ETLService's RetryJob RPC.
	ETLServiceRetryJobProcedure = "/hero.etl.v1.ETLService/RetryJob"
	// ETLServiceDownloadJobContentProcedure is the fully-qualified name of the ETLService's
	// DownloadJobContent RPC.
	ETLServiceDownloadJobContentProcedure = "/hero.etl.v1.ETLService/DownloadJobContent"
	// ETLServiceListAgenciesProcedure is the fully-qualified name of the ETLService's ListAgencies RPC.
	ETLServiceListAgenciesProcedure = "/hero.etl.v1.ETLService/ListAgencies"
	// ETLServiceGetJobStatsProcedure is the fully-qualified name of the ETLService's GetJobStats RPC.
	ETLServiceGetJobStatsProcedure = "/hero.etl.v1.ETLService/GetJobStats"
	// ETLServiceValidateReportsProcedure is the fully-qualified name of the ETLService's
	// ValidateReports RPC.
	ETLServiceValidateReportsProcedure = "/hero.etl.v1.ETLService/ValidateReports"
	// ETLServiceUpdateJobProgressProcedure is the fully-qualified name of the ETLService's
	// UpdateJobProgress RPC.
	ETLServiceUpdateJobProgressProcedure = "/hero.etl.v1.ETLService/UpdateJobProgress"
	// ETLServiceGetAgencyStatsProcedure is the fully-qualified name of the ETLService's GetAgencyStats
	// RPC.
	ETLServiceGetAgencyStatsProcedure = "/hero.etl.v1.ETLService/GetAgencyStats"
)

// ETLServiceClient is a client for the hero.etl.v1.ETLService service.
type ETLServiceClient interface {
	// ProcessReports: Transform reports to compliance data standards and submit to agencies
	// Supports both specific reports and date range queries with filtering
	// Use cases:
	//   - NIBRS reporting: "Send these 3 incident reports to FBI in NIBRS XML format"
	//   - Campus reporting: "Send all campus crimes from last month in NIBRS XML"
	//   - State reporting: "Send all crimes from this quarter to State Police in NIBRS format"
	//
	// Creates ETL job that extracts complete report data and transforms using data standard strategy
	ProcessReports(context.Context, *connect.Request[v1.ProcessReportsRequest]) (*connect.Response[v1.ProcessReportsResponse], error)
	// ExtractReportData: Extract raw report data without transformation
	// Use cases:
	//   - "Show me all the raw data extracted from this report"
	//   - "Debug what data sets are being extracted from report XYZ"
	//   - "Inspect extracted entities, assets, and relationships"
	//
	// Returns complete extracted data as structured object for inspection and debugging
	// Critical for understanding what data the extractor is finding
	ExtractReportData(context.Context, *connect.Request[v1.ExtractReportDataRequest]) (*connect.Response[v1.ExtractReportDataResponse], error)
	// TestReportMapping: Preview mapping configuration transformation without template formatting
	// Use cases:
	//   - "Show me the data structure after mapping config transformation"
	//   - "Debug what fields are available for template development"
	//   - "Inspect intermediate transformation state before format rendering"
	//
	// Returns the structured output from mapping transformation for template development
	TestReportMapping(context.Context, *connect.Request[v1.TestReportMappingRequest]) (*connect.Response[v1.TestReportMappingResponse], error)
	// TestReportTransformation: Complete transformation pipeline - extract, map, and format
	// Use cases:
	//   - "Show me what this report looks like in NIBRS XML format before sending"
	//   - "Preview this campus incident in NIBRS format"
	//   - "Convert report to final XML format"
	//
	// Internally calls TestReportMapping first, then applies template formatting
	// Critical for debugging field mappings and data standard compliance
	// Returns the formatted content ready for submission
	TestReportTransformation(context.Context, *connect.Request[v1.TestReportTransformationRequest]) (*connect.Response[v1.TestReportTransformationResponse], error)
	// GetJob: Get current status and details of an ETL job
	// Use case: "What's the status of job XYZ? Did it complete successfully?"
	// Returns job status, progress, errors, and generated content for inspection
	GetJob(context.Context, *connect.Request[v1.GetJobRequest]) (*connect.Response[v1.GetJobResponse], error)
	// ListJobs: List ETL jobs with filtering and pagination
	// Use cases:
	//   - "Show me all failed FBI jobs from this week"
	//   - "List all completed NIBRS jobs from January 1-15"
	//   - "Show me all jobs for State Police created yesterday"
	//
	// Supports filtering by agency, status, and creation date range
	ListJobs(context.Context, *connect.Request[v1.ListJobsRequest]) (*connect.Response[v1.ListJobsResponse], error)
	// CancelJob: Cancel a running or pending ETL job
	// Use case: "Stop that bulk job, I need to fix the data first"
	// Prevents unnecessary processing or sending of incorrect data
	CancelJob(context.Context, *connect.Request[v1.CancelJobRequest]) (*connect.Response[v1.CancelJobResponse], error)
	// RetryJob: Retry a failed ETL job
	// Use cases:
	//   - "Retry that failed FBI submission after fixing the network issue"
	//   - "Force retry this job even though it exceeded max retries"
	//   - "Retry with increased retry limit for this complex job"
	//
	// Automatically handles retry logic with exponential backoff and error classification
	RetryJob(context.Context, *connect.Request[v1.RetryJobRequest]) (*connect.Response[v1.RetryJobResponse], error)
	// DownloadJobContent: Download the generated content from a completed job
	// Use case: "Let me download the NIBRS XML that was generated for review"
	// Essential for content inspection, compliance review, and manual submission
	DownloadJobContent(context.Context, *connect.Request[v1.DownloadJobContentRequest]) (*connect.Response[v1.DownloadJobContentResponse], error)
	// ListAgencies: Get list of available agencies and their supported formats
	// Use case: "What agencies can I send reports to and what formats do they support?"
	// Returns agency info including supported output formats (NIBRS, etc.)
	ListAgencies(context.Context, *connect.Request[v1.ListAgenciesRequest]) (*connect.Response[v1.ListAgenciesResponse], error)
	// GetJobStats: Get statistical overview of ETL jobs
	// Use cases:
	//   - "Show me ETL job statistics for this month"
	//   - "How many NIBRS jobs have we completed this quarter?"
	//   - "What's our success rate for FBI submissions?"
	//
	// Returns job counts, success rates, and per-agency statistics
	GetJobStats(context.Context, *connect.Request[v1.GetJobStatsRequest]) (*connect.Response[v1.GetJobStatsResponse], error)
	// ValidateReports: Validate reports against data standard before processing
	// Use cases:
	//   - "Check if these reports are valid for NIBRS before submitting"
	//   - "Validate campus incidents against data standard requirements"
	//
	// Returns detailed validation results and error information
	ValidateReports(context.Context, *connect.Request[v1.ValidateReportsRequest]) (*connect.Response[v1.ValidateReportsResponse], error)
	// UpdateJobProgress: Update job progress counters
	// Use cases:
	//   - "External job processor updating progress: 50 out of 100 reports processed"
	//   - "Distributed ETL system reporting partial completion"
	//   - "Real-time progress updates for long-running jobs"
	//
	// Allows external systems to update job progress for monitoring and status tracking
	UpdateJobProgress(context.Context, *connect.Request[v1.UpdateJobProgressRequest]) (*connect.Response[v1.UpdateJobProgressResponse], error)
	// GetAgencyStats: Get detailed statistics for a specific agency
	// Use cases:
	//   - "Show me FBI submission statistics for this quarter"
	//   - "Generate agency-specific dashboard data"
	//   - "Track performance metrics per agency"
	//
	// Returns detailed statistics for a single agency including submission history
	GetAgencyStats(context.Context, *connect.Request[v1.GetAgencyStatsRequest]) (*connect.Response[v1.GetAgencyStatsResponse], error)
}

// NewETLServiceClient constructs a client for the hero.etl.v1.ETLService service. By default, it
// uses the Connect protocol with the binary Protobuf Codec, asks for gzipped responses, and sends
// uncompressed requests. To use the gRPC or gRPC-Web protocols, supply the connect.WithGRPC() or
// connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewETLServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) ETLServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	eTLServiceMethods := v1.File_hero_etl_v1_etl_proto.Services().ByName("ETLService").Methods()
	return &eTLServiceClient{
		processReports: connect.NewClient[v1.ProcessReportsRequest, v1.ProcessReportsResponse](
			httpClient,
			baseURL+ETLServiceProcessReportsProcedure,
			connect.WithSchema(eTLServiceMethods.ByName("ProcessReports")),
			connect.WithClientOptions(opts...),
		),
		extractReportData: connect.NewClient[v1.ExtractReportDataRequest, v1.ExtractReportDataResponse](
			httpClient,
			baseURL+ETLServiceExtractReportDataProcedure,
			connect.WithSchema(eTLServiceMethods.ByName("ExtractReportData")),
			connect.WithClientOptions(opts...),
		),
		testReportMapping: connect.NewClient[v1.TestReportMappingRequest, v1.TestReportMappingResponse](
			httpClient,
			baseURL+ETLServiceTestReportMappingProcedure,
			connect.WithSchema(eTLServiceMethods.ByName("TestReportMapping")),
			connect.WithClientOptions(opts...),
		),
		testReportTransformation: connect.NewClient[v1.TestReportTransformationRequest, v1.TestReportTransformationResponse](
			httpClient,
			baseURL+ETLServiceTestReportTransformationProcedure,
			connect.WithSchema(eTLServiceMethods.ByName("TestReportTransformation")),
			connect.WithClientOptions(opts...),
		),
		getJob: connect.NewClient[v1.GetJobRequest, v1.GetJobResponse](
			httpClient,
			baseURL+ETLServiceGetJobProcedure,
			connect.WithSchema(eTLServiceMethods.ByName("GetJob")),
			connect.WithClientOptions(opts...),
		),
		listJobs: connect.NewClient[v1.ListJobsRequest, v1.ListJobsResponse](
			httpClient,
			baseURL+ETLServiceListJobsProcedure,
			connect.WithSchema(eTLServiceMethods.ByName("ListJobs")),
			connect.WithClientOptions(opts...),
		),
		cancelJob: connect.NewClient[v1.CancelJobRequest, v1.CancelJobResponse](
			httpClient,
			baseURL+ETLServiceCancelJobProcedure,
			connect.WithSchema(eTLServiceMethods.ByName("CancelJob")),
			connect.WithClientOptions(opts...),
		),
		retryJob: connect.NewClient[v1.RetryJobRequest, v1.RetryJobResponse](
			httpClient,
			baseURL+ETLServiceRetryJobProcedure,
			connect.WithSchema(eTLServiceMethods.ByName("RetryJob")),
			connect.WithClientOptions(opts...),
		),
		downloadJobContent: connect.NewClient[v1.DownloadJobContentRequest, v1.DownloadJobContentResponse](
			httpClient,
			baseURL+ETLServiceDownloadJobContentProcedure,
			connect.WithSchema(eTLServiceMethods.ByName("DownloadJobContent")),
			connect.WithClientOptions(opts...),
		),
		listAgencies: connect.NewClient[v1.ListAgenciesRequest, v1.ListAgenciesResponse](
			httpClient,
			baseURL+ETLServiceListAgenciesProcedure,
			connect.WithSchema(eTLServiceMethods.ByName("ListAgencies")),
			connect.WithClientOptions(opts...),
		),
		getJobStats: connect.NewClient[v1.GetJobStatsRequest, v1.GetJobStatsResponse](
			httpClient,
			baseURL+ETLServiceGetJobStatsProcedure,
			connect.WithSchema(eTLServiceMethods.ByName("GetJobStats")),
			connect.WithClientOptions(opts...),
		),
		validateReports: connect.NewClient[v1.ValidateReportsRequest, v1.ValidateReportsResponse](
			httpClient,
			baseURL+ETLServiceValidateReportsProcedure,
			connect.WithSchema(eTLServiceMethods.ByName("ValidateReports")),
			connect.WithClientOptions(opts...),
		),
		updateJobProgress: connect.NewClient[v1.UpdateJobProgressRequest, v1.UpdateJobProgressResponse](
			httpClient,
			baseURL+ETLServiceUpdateJobProgressProcedure,
			connect.WithSchema(eTLServiceMethods.ByName("UpdateJobProgress")),
			connect.WithClientOptions(opts...),
		),
		getAgencyStats: connect.NewClient[v1.GetAgencyStatsRequest, v1.GetAgencyStatsResponse](
			httpClient,
			baseURL+ETLServiceGetAgencyStatsProcedure,
			connect.WithSchema(eTLServiceMethods.ByName("GetAgencyStats")),
			connect.WithClientOptions(opts...),
		),
	}
}

// eTLServiceClient implements ETLServiceClient.
type eTLServiceClient struct {
	processReports           *connect.Client[v1.ProcessReportsRequest, v1.ProcessReportsResponse]
	extractReportData        *connect.Client[v1.ExtractReportDataRequest, v1.ExtractReportDataResponse]
	testReportMapping        *connect.Client[v1.TestReportMappingRequest, v1.TestReportMappingResponse]
	testReportTransformation *connect.Client[v1.TestReportTransformationRequest, v1.TestReportTransformationResponse]
	getJob                   *connect.Client[v1.GetJobRequest, v1.GetJobResponse]
	listJobs                 *connect.Client[v1.ListJobsRequest, v1.ListJobsResponse]
	cancelJob                *connect.Client[v1.CancelJobRequest, v1.CancelJobResponse]
	retryJob                 *connect.Client[v1.RetryJobRequest, v1.RetryJobResponse]
	downloadJobContent       *connect.Client[v1.DownloadJobContentRequest, v1.DownloadJobContentResponse]
	listAgencies             *connect.Client[v1.ListAgenciesRequest, v1.ListAgenciesResponse]
	getJobStats              *connect.Client[v1.GetJobStatsRequest, v1.GetJobStatsResponse]
	validateReports          *connect.Client[v1.ValidateReportsRequest, v1.ValidateReportsResponse]
	updateJobProgress        *connect.Client[v1.UpdateJobProgressRequest, v1.UpdateJobProgressResponse]
	getAgencyStats           *connect.Client[v1.GetAgencyStatsRequest, v1.GetAgencyStatsResponse]
}

// ProcessReports calls hero.etl.v1.ETLService.ProcessReports.
func (c *eTLServiceClient) ProcessReports(ctx context.Context, req *connect.Request[v1.ProcessReportsRequest]) (*connect.Response[v1.ProcessReportsResponse], error) {
	return c.processReports.CallUnary(ctx, req)
}

// ExtractReportData calls hero.etl.v1.ETLService.ExtractReportData.
func (c *eTLServiceClient) ExtractReportData(ctx context.Context, req *connect.Request[v1.ExtractReportDataRequest]) (*connect.Response[v1.ExtractReportDataResponse], error) {
	return c.extractReportData.CallUnary(ctx, req)
}

// TestReportMapping calls hero.etl.v1.ETLService.TestReportMapping.
func (c *eTLServiceClient) TestReportMapping(ctx context.Context, req *connect.Request[v1.TestReportMappingRequest]) (*connect.Response[v1.TestReportMappingResponse], error) {
	return c.testReportMapping.CallUnary(ctx, req)
}

// TestReportTransformation calls hero.etl.v1.ETLService.TestReportTransformation.
func (c *eTLServiceClient) TestReportTransformation(ctx context.Context, req *connect.Request[v1.TestReportTransformationRequest]) (*connect.Response[v1.TestReportTransformationResponse], error) {
	return c.testReportTransformation.CallUnary(ctx, req)
}

// GetJob calls hero.etl.v1.ETLService.GetJob.
func (c *eTLServiceClient) GetJob(ctx context.Context, req *connect.Request[v1.GetJobRequest]) (*connect.Response[v1.GetJobResponse], error) {
	return c.getJob.CallUnary(ctx, req)
}

// ListJobs calls hero.etl.v1.ETLService.ListJobs.
func (c *eTLServiceClient) ListJobs(ctx context.Context, req *connect.Request[v1.ListJobsRequest]) (*connect.Response[v1.ListJobsResponse], error) {
	return c.listJobs.CallUnary(ctx, req)
}

// CancelJob calls hero.etl.v1.ETLService.CancelJob.
func (c *eTLServiceClient) CancelJob(ctx context.Context, req *connect.Request[v1.CancelJobRequest]) (*connect.Response[v1.CancelJobResponse], error) {
	return c.cancelJob.CallUnary(ctx, req)
}

// RetryJob calls hero.etl.v1.ETLService.RetryJob.
func (c *eTLServiceClient) RetryJob(ctx context.Context, req *connect.Request[v1.RetryJobRequest]) (*connect.Response[v1.RetryJobResponse], error) {
	return c.retryJob.CallUnary(ctx, req)
}

// DownloadJobContent calls hero.etl.v1.ETLService.DownloadJobContent.
func (c *eTLServiceClient) DownloadJobContent(ctx context.Context, req *connect.Request[v1.DownloadJobContentRequest]) (*connect.Response[v1.DownloadJobContentResponse], error) {
	return c.downloadJobContent.CallUnary(ctx, req)
}

// ListAgencies calls hero.etl.v1.ETLService.ListAgencies.
func (c *eTLServiceClient) ListAgencies(ctx context.Context, req *connect.Request[v1.ListAgenciesRequest]) (*connect.Response[v1.ListAgenciesResponse], error) {
	return c.listAgencies.CallUnary(ctx, req)
}

// GetJobStats calls hero.etl.v1.ETLService.GetJobStats.
func (c *eTLServiceClient) GetJobStats(ctx context.Context, req *connect.Request[v1.GetJobStatsRequest]) (*connect.Response[v1.GetJobStatsResponse], error) {
	return c.getJobStats.CallUnary(ctx, req)
}

// ValidateReports calls hero.etl.v1.ETLService.ValidateReports.
func (c *eTLServiceClient) ValidateReports(ctx context.Context, req *connect.Request[v1.ValidateReportsRequest]) (*connect.Response[v1.ValidateReportsResponse], error) {
	return c.validateReports.CallUnary(ctx, req)
}

// UpdateJobProgress calls hero.etl.v1.ETLService.UpdateJobProgress.
func (c *eTLServiceClient) UpdateJobProgress(ctx context.Context, req *connect.Request[v1.UpdateJobProgressRequest]) (*connect.Response[v1.UpdateJobProgressResponse], error) {
	return c.updateJobProgress.CallUnary(ctx, req)
}

// GetAgencyStats calls hero.etl.v1.ETLService.GetAgencyStats.
func (c *eTLServiceClient) GetAgencyStats(ctx context.Context, req *connect.Request[v1.GetAgencyStatsRequest]) (*connect.Response[v1.GetAgencyStatsResponse], error) {
	return c.getAgencyStats.CallUnary(ctx, req)
}

// ETLServiceHandler is an implementation of the hero.etl.v1.ETLService service.
type ETLServiceHandler interface {
	// ProcessReports: Transform reports to compliance data standards and submit to agencies
	// Supports both specific reports and date range queries with filtering
	// Use cases:
	//   - NIBRS reporting: "Send these 3 incident reports to FBI in NIBRS XML format"
	//   - Campus reporting: "Send all campus crimes from last month in NIBRS XML"
	//   - State reporting: "Send all crimes from this quarter to State Police in NIBRS format"
	//
	// Creates ETL job that extracts complete report data and transforms using data standard strategy
	ProcessReports(context.Context, *connect.Request[v1.ProcessReportsRequest]) (*connect.Response[v1.ProcessReportsResponse], error)
	// ExtractReportData: Extract raw report data without transformation
	// Use cases:
	//   - "Show me all the raw data extracted from this report"
	//   - "Debug what data sets are being extracted from report XYZ"
	//   - "Inspect extracted entities, assets, and relationships"
	//
	// Returns complete extracted data as structured object for inspection and debugging
	// Critical for understanding what data the extractor is finding
	ExtractReportData(context.Context, *connect.Request[v1.ExtractReportDataRequest]) (*connect.Response[v1.ExtractReportDataResponse], error)
	// TestReportMapping: Preview mapping configuration transformation without template formatting
	// Use cases:
	//   - "Show me the data structure after mapping config transformation"
	//   - "Debug what fields are available for template development"
	//   - "Inspect intermediate transformation state before format rendering"
	//
	// Returns the structured output from mapping transformation for template development
	TestReportMapping(context.Context, *connect.Request[v1.TestReportMappingRequest]) (*connect.Response[v1.TestReportMappingResponse], error)
	// TestReportTransformation: Complete transformation pipeline - extract, map, and format
	// Use cases:
	//   - "Show me what this report looks like in NIBRS XML format before sending"
	//   - "Preview this campus incident in NIBRS format"
	//   - "Convert report to final XML format"
	//
	// Internally calls TestReportMapping first, then applies template formatting
	// Critical for debugging field mappings and data standard compliance
	// Returns the formatted content ready for submission
	TestReportTransformation(context.Context, *connect.Request[v1.TestReportTransformationRequest]) (*connect.Response[v1.TestReportTransformationResponse], error)
	// GetJob: Get current status and details of an ETL job
	// Use case: "What's the status of job XYZ? Did it complete successfully?"
	// Returns job status, progress, errors, and generated content for inspection
	GetJob(context.Context, *connect.Request[v1.GetJobRequest]) (*connect.Response[v1.GetJobResponse], error)
	// ListJobs: List ETL jobs with filtering and pagination
	// Use cases:
	//   - "Show me all failed FBI jobs from this week"
	//   - "List all completed NIBRS jobs from January 1-15"
	//   - "Show me all jobs for State Police created yesterday"
	//
	// Supports filtering by agency, status, and creation date range
	ListJobs(context.Context, *connect.Request[v1.ListJobsRequest]) (*connect.Response[v1.ListJobsResponse], error)
	// CancelJob: Cancel a running or pending ETL job
	// Use case: "Stop that bulk job, I need to fix the data first"
	// Prevents unnecessary processing or sending of incorrect data
	CancelJob(context.Context, *connect.Request[v1.CancelJobRequest]) (*connect.Response[v1.CancelJobResponse], error)
	// RetryJob: Retry a failed ETL job
	// Use cases:
	//   - "Retry that failed FBI submission after fixing the network issue"
	//   - "Force retry this job even though it exceeded max retries"
	//   - "Retry with increased retry limit for this complex job"
	//
	// Automatically handles retry logic with exponential backoff and error classification
	RetryJob(context.Context, *connect.Request[v1.RetryJobRequest]) (*connect.Response[v1.RetryJobResponse], error)
	// DownloadJobContent: Download the generated content from a completed job
	// Use case: "Let me download the NIBRS XML that was generated for review"
	// Essential for content inspection, compliance review, and manual submission
	DownloadJobContent(context.Context, *connect.Request[v1.DownloadJobContentRequest]) (*connect.Response[v1.DownloadJobContentResponse], error)
	// ListAgencies: Get list of available agencies and their supported formats
	// Use case: "What agencies can I send reports to and what formats do they support?"
	// Returns agency info including supported output formats (NIBRS, etc.)
	ListAgencies(context.Context, *connect.Request[v1.ListAgenciesRequest]) (*connect.Response[v1.ListAgenciesResponse], error)
	// GetJobStats: Get statistical overview of ETL jobs
	// Use cases:
	//   - "Show me ETL job statistics for this month"
	//   - "How many NIBRS jobs have we completed this quarter?"
	//   - "What's our success rate for FBI submissions?"
	//
	// Returns job counts, success rates, and per-agency statistics
	GetJobStats(context.Context, *connect.Request[v1.GetJobStatsRequest]) (*connect.Response[v1.GetJobStatsResponse], error)
	// ValidateReports: Validate reports against data standard before processing
	// Use cases:
	//   - "Check if these reports are valid for NIBRS before submitting"
	//   - "Validate campus incidents against data standard requirements"
	//
	// Returns detailed validation results and error information
	ValidateReports(context.Context, *connect.Request[v1.ValidateReportsRequest]) (*connect.Response[v1.ValidateReportsResponse], error)
	// UpdateJobProgress: Update job progress counters
	// Use cases:
	//   - "External job processor updating progress: 50 out of 100 reports processed"
	//   - "Distributed ETL system reporting partial completion"
	//   - "Real-time progress updates for long-running jobs"
	//
	// Allows external systems to update job progress for monitoring and status tracking
	UpdateJobProgress(context.Context, *connect.Request[v1.UpdateJobProgressRequest]) (*connect.Response[v1.UpdateJobProgressResponse], error)
	// GetAgencyStats: Get detailed statistics for a specific agency
	// Use cases:
	//   - "Show me FBI submission statistics for this quarter"
	//   - "Generate agency-specific dashboard data"
	//   - "Track performance metrics per agency"
	//
	// Returns detailed statistics for a single agency including submission history
	GetAgencyStats(context.Context, *connect.Request[v1.GetAgencyStatsRequest]) (*connect.Response[v1.GetAgencyStatsResponse], error)
}

// NewETLServiceHandler builds an HTTP handler from the service implementation. It returns the path
// on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewETLServiceHandler(svc ETLServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	eTLServiceMethods := v1.File_hero_etl_v1_etl_proto.Services().ByName("ETLService").Methods()
	eTLServiceProcessReportsHandler := connect.NewUnaryHandler(
		ETLServiceProcessReportsProcedure,
		svc.ProcessReports,
		connect.WithSchema(eTLServiceMethods.ByName("ProcessReports")),
		connect.WithHandlerOptions(opts...),
	)
	eTLServiceExtractReportDataHandler := connect.NewUnaryHandler(
		ETLServiceExtractReportDataProcedure,
		svc.ExtractReportData,
		connect.WithSchema(eTLServiceMethods.ByName("ExtractReportData")),
		connect.WithHandlerOptions(opts...),
	)
	eTLServiceTestReportMappingHandler := connect.NewUnaryHandler(
		ETLServiceTestReportMappingProcedure,
		svc.TestReportMapping,
		connect.WithSchema(eTLServiceMethods.ByName("TestReportMapping")),
		connect.WithHandlerOptions(opts...),
	)
	eTLServiceTestReportTransformationHandler := connect.NewUnaryHandler(
		ETLServiceTestReportTransformationProcedure,
		svc.TestReportTransformation,
		connect.WithSchema(eTLServiceMethods.ByName("TestReportTransformation")),
		connect.WithHandlerOptions(opts...),
	)
	eTLServiceGetJobHandler := connect.NewUnaryHandler(
		ETLServiceGetJobProcedure,
		svc.GetJob,
		connect.WithSchema(eTLServiceMethods.ByName("GetJob")),
		connect.WithHandlerOptions(opts...),
	)
	eTLServiceListJobsHandler := connect.NewUnaryHandler(
		ETLServiceListJobsProcedure,
		svc.ListJobs,
		connect.WithSchema(eTLServiceMethods.ByName("ListJobs")),
		connect.WithHandlerOptions(opts...),
	)
	eTLServiceCancelJobHandler := connect.NewUnaryHandler(
		ETLServiceCancelJobProcedure,
		svc.CancelJob,
		connect.WithSchema(eTLServiceMethods.ByName("CancelJob")),
		connect.WithHandlerOptions(opts...),
	)
	eTLServiceRetryJobHandler := connect.NewUnaryHandler(
		ETLServiceRetryJobProcedure,
		svc.RetryJob,
		connect.WithSchema(eTLServiceMethods.ByName("RetryJob")),
		connect.WithHandlerOptions(opts...),
	)
	eTLServiceDownloadJobContentHandler := connect.NewUnaryHandler(
		ETLServiceDownloadJobContentProcedure,
		svc.DownloadJobContent,
		connect.WithSchema(eTLServiceMethods.ByName("DownloadJobContent")),
		connect.WithHandlerOptions(opts...),
	)
	eTLServiceListAgenciesHandler := connect.NewUnaryHandler(
		ETLServiceListAgenciesProcedure,
		svc.ListAgencies,
		connect.WithSchema(eTLServiceMethods.ByName("ListAgencies")),
		connect.WithHandlerOptions(opts...),
	)
	eTLServiceGetJobStatsHandler := connect.NewUnaryHandler(
		ETLServiceGetJobStatsProcedure,
		svc.GetJobStats,
		connect.WithSchema(eTLServiceMethods.ByName("GetJobStats")),
		connect.WithHandlerOptions(opts...),
	)
	eTLServiceValidateReportsHandler := connect.NewUnaryHandler(
		ETLServiceValidateReportsProcedure,
		svc.ValidateReports,
		connect.WithSchema(eTLServiceMethods.ByName("ValidateReports")),
		connect.WithHandlerOptions(opts...),
	)
	eTLServiceUpdateJobProgressHandler := connect.NewUnaryHandler(
		ETLServiceUpdateJobProgressProcedure,
		svc.UpdateJobProgress,
		connect.WithSchema(eTLServiceMethods.ByName("UpdateJobProgress")),
		connect.WithHandlerOptions(opts...),
	)
	eTLServiceGetAgencyStatsHandler := connect.NewUnaryHandler(
		ETLServiceGetAgencyStatsProcedure,
		svc.GetAgencyStats,
		connect.WithSchema(eTLServiceMethods.ByName("GetAgencyStats")),
		connect.WithHandlerOptions(opts...),
	)
	return "/hero.etl.v1.ETLService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case ETLServiceProcessReportsProcedure:
			eTLServiceProcessReportsHandler.ServeHTTP(w, r)
		case ETLServiceExtractReportDataProcedure:
			eTLServiceExtractReportDataHandler.ServeHTTP(w, r)
		case ETLServiceTestReportMappingProcedure:
			eTLServiceTestReportMappingHandler.ServeHTTP(w, r)
		case ETLServiceTestReportTransformationProcedure:
			eTLServiceTestReportTransformationHandler.ServeHTTP(w, r)
		case ETLServiceGetJobProcedure:
			eTLServiceGetJobHandler.ServeHTTP(w, r)
		case ETLServiceListJobsProcedure:
			eTLServiceListJobsHandler.ServeHTTP(w, r)
		case ETLServiceCancelJobProcedure:
			eTLServiceCancelJobHandler.ServeHTTP(w, r)
		case ETLServiceRetryJobProcedure:
			eTLServiceRetryJobHandler.ServeHTTP(w, r)
		case ETLServiceDownloadJobContentProcedure:
			eTLServiceDownloadJobContentHandler.ServeHTTP(w, r)
		case ETLServiceListAgenciesProcedure:
			eTLServiceListAgenciesHandler.ServeHTTP(w, r)
		case ETLServiceGetJobStatsProcedure:
			eTLServiceGetJobStatsHandler.ServeHTTP(w, r)
		case ETLServiceValidateReportsProcedure:
			eTLServiceValidateReportsHandler.ServeHTTP(w, r)
		case ETLServiceUpdateJobProgressProcedure:
			eTLServiceUpdateJobProgressHandler.ServeHTTP(w, r)
		case ETLServiceGetAgencyStatsProcedure:
			eTLServiceGetAgencyStatsHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedETLServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedETLServiceHandler struct{}

func (UnimplementedETLServiceHandler) ProcessReports(context.Context, *connect.Request[v1.ProcessReportsRequest]) (*connect.Response[v1.ProcessReportsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.etl.v1.ETLService.ProcessReports is not implemented"))
}

func (UnimplementedETLServiceHandler) ExtractReportData(context.Context, *connect.Request[v1.ExtractReportDataRequest]) (*connect.Response[v1.ExtractReportDataResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.etl.v1.ETLService.ExtractReportData is not implemented"))
}

func (UnimplementedETLServiceHandler) TestReportMapping(context.Context, *connect.Request[v1.TestReportMappingRequest]) (*connect.Response[v1.TestReportMappingResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.etl.v1.ETLService.TestReportMapping is not implemented"))
}

func (UnimplementedETLServiceHandler) TestReportTransformation(context.Context, *connect.Request[v1.TestReportTransformationRequest]) (*connect.Response[v1.TestReportTransformationResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.etl.v1.ETLService.TestReportTransformation is not implemented"))
}

func (UnimplementedETLServiceHandler) GetJob(context.Context, *connect.Request[v1.GetJobRequest]) (*connect.Response[v1.GetJobResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.etl.v1.ETLService.GetJob is not implemented"))
}

func (UnimplementedETLServiceHandler) ListJobs(context.Context, *connect.Request[v1.ListJobsRequest]) (*connect.Response[v1.ListJobsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.etl.v1.ETLService.ListJobs is not implemented"))
}

func (UnimplementedETLServiceHandler) CancelJob(context.Context, *connect.Request[v1.CancelJobRequest]) (*connect.Response[v1.CancelJobResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.etl.v1.ETLService.CancelJob is not implemented"))
}

func (UnimplementedETLServiceHandler) RetryJob(context.Context, *connect.Request[v1.RetryJobRequest]) (*connect.Response[v1.RetryJobResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.etl.v1.ETLService.RetryJob is not implemented"))
}

func (UnimplementedETLServiceHandler) DownloadJobContent(context.Context, *connect.Request[v1.DownloadJobContentRequest]) (*connect.Response[v1.DownloadJobContentResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.etl.v1.ETLService.DownloadJobContent is not implemented"))
}

func (UnimplementedETLServiceHandler) ListAgencies(context.Context, *connect.Request[v1.ListAgenciesRequest]) (*connect.Response[v1.ListAgenciesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.etl.v1.ETLService.ListAgencies is not implemented"))
}

func (UnimplementedETLServiceHandler) GetJobStats(context.Context, *connect.Request[v1.GetJobStatsRequest]) (*connect.Response[v1.GetJobStatsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.etl.v1.ETLService.GetJobStats is not implemented"))
}

func (UnimplementedETLServiceHandler) ValidateReports(context.Context, *connect.Request[v1.ValidateReportsRequest]) (*connect.Response[v1.ValidateReportsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.etl.v1.ETLService.ValidateReports is not implemented"))
}

func (UnimplementedETLServiceHandler) UpdateJobProgress(context.Context, *connect.Request[v1.UpdateJobProgressRequest]) (*connect.Response[v1.UpdateJobProgressResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.etl.v1.ETLService.UpdateJobProgress is not implemented"))
}

func (UnimplementedETLServiceHandler) GetAgencyStats(context.Context, *connect.Request[v1.GetAgencyStatsRequest]) (*connect.Response[v1.GetAgencyStatsResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("hero.etl.v1.ETLService.GetAgencyStats is not implemented"))
}
