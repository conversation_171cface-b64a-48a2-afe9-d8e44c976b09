syntax = "proto3";

package hero.etl.v1;

option go_package = "proto/hero/etl/v1;etl";

import "google/protobuf/empty.proto";
import "google/protobuf/struct.proto";

// -----------------------------------------------------------------------------
// CORE ETL OPERATIONS 
// -----------------------------------------------------------------------------

// ETL Job status
enum JobStatus {
    JOB_STATUS_UNSPECIFIED = 0;
    JOB_STATUS_PENDING      = 1;  // Job queued, waiting to start
    JOB_STATUS_EXTRACTING   = 2;  // Extracting report data from services
    JOB_STATUS_TRANSFORMING = 3;  // Transforming to agency format
    JOB_STATUS_LOADING      = 4;  // Sending/loading data to agency
    JOB_STATUS_COMPLETED    = 5;  // Successfully completed
    JOB_STATUS_FAILED       = 6;  // Failed permanently
    JOB_STATUS_CANCELLED    = 7;  // Cancelled by user
}

// Data standards/formats for law enforcement reporting
enum OutputFormat {
    OUTPUT_FORMAT_UNSPECIFIED = 0;
    OUTPUT_FORMAT_NIBRS_XML   = 1;  // FBI NIBRS XML format
}

// ETL Job - tracks processing of reports for data standards compliance
message ETLJob {
    string id                    = 1;  // Unique job ID
    int32 org_id                 = 2;  // Organization ID for multi-tenant isolation
    string agency_id             = 3;  // Target agency/recipient (e.g., "FBI", "STATE_POLICE", "UNIVERSITY_POLICE")
    OutputFormat output_format   = 4;  // Data standard/format (NIBRS, etc.)
    JobStatus status             = 5;  // Current status
    
    // What's being processed
    repeated string report_ids   = 6;  // Reports to process
    string date_from             = 7;  // ISO8601 date range start (optional)
    string date_to               = 8;  // ISO8601 date range end (optional)
    repeated string case_types   = 9;  // Filter by case types (optional)
    
    // Progress tracking
    int32 total_reports          = 11; // Total reports to process
    int32 reports_processed      = 12; // Reports successfully processed
    int32 reports_failed         = 13; // Reports that failed processing
    int32 reports_skipped        = 14; // Reports skipped (e.g., invalid data)
    
    // Timestamps
    string created_at            = 15; // ISO8601 timestamp when job was created
    string started_at            = 16; // ISO8601 timestamp when job started
    string completed_at          = 17; // ISO8601 timestamp when job completed
    string last_updated_at       = 18; // ISO8601 timestamp of last status update
    
    // Results and content
    string error_message         = 19; // Primary error message if failed
    repeated JobError errors     = 20; // Detailed error list
    bytes generated_content      = 21; // Generated content for inspection
    string content_type          = 22; // MIME type of generated content
    int64 content_size_bytes     = 23; // Size of generated content
    
    // Submission details
    string agency_submission_id  = 24; // Agency's tracking ID (if submitted)
    string submission_response   = 25; // Agency's response message
    string submitted_at          = 26; // ISO8601 timestamp when submitted to agency
    
    // Retry information
    int32 retry_count            = 27; // Number of retry attempts made
    int32 max_retries            = 28; // Maximum retry attempts allowed
    string last_retry_at         = 29; // ISO8601 timestamp of last retry attempt
    bool auto_retry_enabled      = 30; // Whether automatic retries are enabled
    
    // Metadata
    string created_by_asset_id   = 31; // Who created the job
    bool preview_only            = 32; // Generated but not sent to agency
    string job_name              = 33; // Optional human-readable job name
}

// Detailed error information for jobs
message JobError {
    string error_code            = 1;  // Error code (e.g., "INVALID_DATA", "NETWORK_ERROR")
    string error_message         = 2;  // Human-readable error message
    string report_id             = 3;  // Specific report ID that caused error (optional)
    string field_path            = 4;  // Field path that caused error (optional)
    string occurred_at           = 5;  // ISO8601 timestamp when error occurred
    bool is_retryable            = 6;  // Whether this error type is retryable
    int32 retry_attempt          = 7;  // Which retry attempt this error occurred on (0 = initial)
}

// -----------------------------------------------------------------------------
// API REQUESTS/RESPONSES
// -----------------------------------------------------------------------------

// Process reports for compliance reporting (supports both specific reports and date ranges)
message ProcessReportsRequest {
    string agency_id             = 1;  // Target agency/recipient ID
    OutputFormat output_format   = 2;  // Data standard/format (NIBRS, etc.)
    bool preview_only            = 3;  // Generate but don't send to agency
    string job_name              = 4;  // Optional human-readable job name
    
    // EITHER specify reports OR date range (not both)
    repeated string report_ids   = 5;  // Specific reports to process
    
    // OR use date range with optional filters
    string date_from             = 6;  // ISO8601 start date (optional)
    string date_to               = 7;  // ISO8601 end date (optional)
    repeated string case_types   = 8;  // Filter by case types (optional)
    
    // Processing options
    bool skip_invalid_reports    = 9; // Skip reports with validation errors
    bool include_draft_reports   = 10; // Include draft/incomplete reports
    
    // Retry configuration
    int32 max_retries            = 11; // Maximum retry attempts (default: 3)
    bool auto_retry_enabled      = 12; // Enable automatic retries on failure (default: true)
}

message ProcessReportsResponse {
    ETLJob job                   = 1;  // Created job
    int32 estimated_report_count = 2;  // Estimated number of reports (if using date range)
}

// Get job status and results
message GetJobRequest {
    string job_id                = 1;  // Job ID
}

message GetJobResponse {
    ETLJob job                   = 1;  // Job details
}

// List jobs with filtering
message ListJobsRequest {
    string agency_id             = 1;  // Filter by agency (optional)
    JobStatus status             = 2;  // Filter by status (optional)
    string created_from          = 3;  // ISO8601 start of creation date range (optional)
    string created_to            = 4;  // ISO8601 end of creation date range (optional)
    int32 page_size              = 5;  // Max results per page
    string page_token            = 6;  // Pagination token
}

message ListJobsResponse {
    repeated ETLJob jobs         = 1;  // Jobs
    string next_page_token       = 2;  // Next page token
}

// Cancel a running job
message CancelJobRequest {
    string job_id                = 1;  // Job to cancel
}

message CancelJobResponse {
    ETLJob job                   = 1;  // Updated job
}

// Retry a failed job
message RetryJobRequest {
    string job_id                = 1;  // Job to retry
    bool force_retry             = 2;  // Force retry even if max retries exceeded
    int32 override_max_retries   = 3;  // Override max retries for this attempt (optional)
}

message RetryJobResponse {
    ETLJob job                   = 1;  // Updated job with retry information
    bool retry_scheduled         = 2;  // Whether retry was successfully scheduled
    string retry_reason          = 3;  // Reason if retry was not scheduled
}

// Download generated content
message DownloadJobContentRequest {
    string job_id                = 1;  // Job ID
}

message DownloadJobContentResponse {
    bytes content                = 1;  // Generated content
    string content_type          = 2;  // MIME type
    string filename              = 3;  // Suggested filename
    int64 size_bytes             = 4;  // Content size
}

// Get list of available agencies
message ListAgenciesRequest {
    // No parameters needed
}

message ListAgenciesResponse {
    repeated AgencyInfo agencies = 1;  // Available agencies
}

// Agency/recipient information with supported data standards
message AgencyInfo {
    string id                    = 1;  // Agency ID (e.g., "FBI", "STATE_POLICE", "UNIVERSITY_POLICE")
    string name                  = 2;  // Display name
    repeated OutputFormat supported_formats = 3;  // Supported data standards (NIBRS, etc.)
    bool enabled                 = 4;  // Whether agency is enabled
    string agency_type           = 5;  // Type: "FEDERAL", "STATE", "LOCAL", "UNIVERSITY"
}

// Extract raw report data without transformation
message ExtractReportDataRequest {
    string report_id             = 1;  // Single report to extract data from
}

message ExtractReportDataResponse {
    google.protobuf.Struct extracted_data = 1;  // Complete extracted data as structured object
    string extraction_time       = 2;  // ISO8601 timestamp when extraction was done
    repeated string warnings     = 3;  // Extraction warnings
    bool success                 = 4;  // Whether extraction succeeded
    string error_message         = 5;  // Error if failed
    int32 data_sets_count        = 6;  // Number of data sets extracted
    repeated string data_set_names = 7; // Names of extracted data sets
}

// Test report transformation - complete transformation pipeline with formatting
message TestReportTransformationRequest {
    string report_id             = 1;  // Single report to test
    OutputFormat output_format   = 2;  // Target format (NIBRS_XML, etc.)
    bool validate                = 3;  // Run validation on output (optional)
    string mapping_config_json   = 4;  // Optional: Override mapping config (JSON string)
    string template_content      = 5;  // Optional: Override template content
}

message TestReportTransformationResponse {
    bytes transformed_content    = 1;  // Generated content
    string content_type          = 2;  // MIME type
    string transformation_time   = 3;  // ISO8601 timestamp when transformation was done
    repeated string warnings     = 4;  // Transformation warnings
    bool success                 = 5;  // Whether transformation succeeded
    string error_message         = 6;  // Error if failed
    repeated ValidationError validation_errors = 7; // Detailed validation errors
    string readable_content      = 8;  // Human-readable version of transformed_content (for debugging)
    string mapping_config_used   = 9;  // Mapping config file path used
    string template_used         = 10; // Template file path used for formatting
}

// Test mapping configuration transformation without template formatting
message TestReportMappingRequest {
    string report_id             = 1;  // Single report to test
    OutputFormat output_format   = 2;  // Output format/standard (NIBRS, etc.)
    string mapping_config_json   = 3;  // Optional: Override mapping config (JSON string)
}

message TestReportMappingResponse {
    google.protobuf.Struct mapped_data = 1;  // Config-mapped data as structured object
    string content_type          = 2;  // MIME type (application/json)
    string mapping_config_used   = 3;  // Config file path used
    string transformation_time   = 4;  // ISO8601 timestamp when mapping was done
    repeated string warnings     = 5;  // Mapping warnings
    bool success                 = 6;  // Whether mapping succeeded
    string error_message         = 7;  // Error if failed
}

// Validation error details
message ValidationError {
    string field_path            = 1;  // Field path that failed validation
    string error_code            = 2;  // Error code (e.g., "REQUIRED_FIELD", "INVALID_FORMAT")
    string error_message         = 3;  // Human-readable error message
    string expected_value        = 4;  // Expected value or format (optional)
    string actual_value          = 5;  // Actual value that failed (optional)
}

// Get job statistics
message GetJobStatsRequest {
    string agency_id             = 1;  // Filter by agency (optional)
    OutputFormat output_format   = 2;  // Filter by format (optional)
    string date_from             = 3;  // ISO8601 start date (optional)
    string date_to               = 4;  // ISO8601 end date (optional)
}

message GetJobStatsResponse {
    int32 total_jobs             = 1;  // Total jobs in period
    int32 completed_jobs         = 2;  // Successfully completed jobs
    int32 failed_jobs            = 3;  // Failed jobs
    int32 cancelled_jobs         = 4;  // Cancelled jobs
    int32 running_jobs           = 5;  // Currently running jobs
    int32 total_reports_processed = 6; // Total reports processed
    int64 total_content_size_bytes = 7; // Total content generated
    repeated AgencyStats agency_stats = 8; // Per-agency statistics
}

// Per-agency statistics
message AgencyStats {
    string agency_id             = 1;  // Agency ID
    string agency_name           = 2;  // Agency name
    int32 job_count              = 3;  // Number of jobs for this agency
    int32 report_count           = 4;  // Number of reports processed
    int64 content_size_bytes     = 5;  // Total content size for agency
    string last_submission_at    = 6;  // ISO8601 timestamp of last submission
}

// Validate reports before processing
message ValidateReportsRequest {
    repeated string report_ids   = 1;  // Reports to validate
    OutputFormat output_format   = 2;  // Target data standard
    string agency_id             = 3;  // Target agency (optional)
}

message ValidateReportsResponse {
    repeated ReportValidationResult results = 1; // Validation results per report
    int32 valid_reports          = 2;  // Number of valid reports
    int32 invalid_reports        = 3;  // Number of invalid reports
    bool all_valid               = 4;  // Whether all reports are valid
}

// Validation result for a single report
message ReportValidationResult {
    string report_id             = 1;  // Report ID
    bool valid                   = 2;  // Whether report is valid
    repeated ValidationError errors = 3; // Validation errors
    repeated string warnings     = 4;  // Validation warnings
}

// Update job progress counters
message UpdateJobProgressRequest {
    string job_id                = 1;  // Job ID to update
    int32 total_reports          = 2;  // Total reports to process
    int32 reports_processed      = 3;  // Reports successfully processed
    int32 reports_failed         = 4;  // Reports that failed processing
    int32 reports_skipped        = 5;  // Reports skipped (e.g., invalid data)
    string progress_note         = 6;  // Optional progress note or status message
}

message UpdateJobProgressResponse {
    ETLJob job                   = 1;  // Updated job with new progress
    bool progress_updated        = 2;  // Whether progress was successfully updated
}

// Get statistics for a specific agency
message GetAgencyStatsRequest {
    string agency_id             = 1;  // Agency ID (required)
    string date_from             = 2;  // ISO8601 start date (optional)
    string date_to               = 3;  // ISO8601 end date (optional)
    bool include_job_details     = 4;  // Include detailed job breakdown (optional)
}

message GetAgencyStatsResponse {
    AgencyStats agency_stats     = 1;  // Agency statistics
    repeated JobSummary recent_jobs = 2; // Recent jobs for this agency (if include_job_details=true)
    string generated_at          = 3;  // ISO8601 timestamp when stats were generated
}

// Summary information for a job (used in agency stats)
message JobSummary {
    string job_id                = 1;  // Job ID
    JobStatus status             = 2;  // Job status
    OutputFormat output_format   = 3;  // Output format
    int32 reports_processed      = 4;  // Number of reports processed
    string created_at            = 5;  // ISO8601 timestamp when job was created
    string completed_at          = 6;  // ISO8601 timestamp when job completed (if applicable)
    int64 content_size_bytes     = 7;  // Size of generated content
}

// -----------------------------------------------------------------------------
// ETL SERVICE
// -----------------------------------------------------------------------------

service ETLService {
    // Core ETL operations
    
    // ProcessReports: Transform reports to compliance data standards and submit to agencies
    // Supports both specific reports and date range queries with filtering
    // Use cases:
    //   - NIBRS reporting: "Send these 3 incident reports to FBI in NIBRS XML format"
    //   - Campus reporting: "Send all campus crimes from last month in NIBRS XML"
    //   - State reporting: "Send all crimes from this quarter to State Police in NIBRS format"
    // Creates ETL job that extracts complete report data and transforms using data standard strategy
    rpc ProcessReports(ProcessReportsRequest) returns (ProcessReportsResponse);
    
    // ExtractReportData: Extract raw report data without transformation
    // Use cases:
    //   - "Show me all the raw data extracted from this report"
    //   - "Debug what data sets are being extracted from report XYZ"
    //   - "Inspect extracted entities, assets, and relationships"
    // Returns complete extracted data as structured object for inspection and debugging
    // Critical for understanding what data the extractor is finding
    rpc ExtractReportData(ExtractReportDataRequest) returns (ExtractReportDataResponse);
    
    // TestReportMapping: Preview mapping configuration transformation without template formatting
    // Use cases:
    //   - "Show me the data structure after mapping config transformation"
    //   - "Debug what fields are available for template development"
    //   - "Inspect intermediate transformation state before format rendering"
    // Returns the structured output from mapping transformation for template development
    rpc TestReportMapping(TestReportMappingRequest) returns (TestReportMappingResponse);
    
    // TestReportTransformation: Complete transformation pipeline - extract, map, and format
    // Use cases: 
    //   - "Show me what this report looks like in NIBRS XML format before sending"
    //   - "Preview this campus incident in NIBRS format"
    //   - "Convert report to final XML format"
    // Internally calls TestReportMapping first, then applies template formatting
    // Critical for debugging field mappings and data standard compliance
    // Returns the formatted content ready for submission
    rpc TestReportTransformation(TestReportTransformationRequest) returns (TestReportTransformationResponse);
    
    // Job management
    
    // GetJob: Get current status and details of an ETL job
    // Use case: "What's the status of job XYZ? Did it complete successfully?"
    // Returns job status, progress, errors, and generated content for inspection
    rpc GetJob(GetJobRequest) returns (GetJobResponse);
    
    // ListJobs: List ETL jobs with filtering and pagination
    // Use cases: 
    //   - "Show me all failed FBI jobs from this week"
    //   - "List all completed NIBRS jobs from January 1-15"
    //   - "Show me all jobs for State Police created yesterday"
    // Supports filtering by agency, status, and creation date range
    rpc ListJobs(ListJobsRequest) returns (ListJobsResponse);
    
    // CancelJob: Cancel a running or pending ETL job
    // Use case: "Stop that bulk job, I need to fix the data first"
    // Prevents unnecessary processing or sending of incorrect data
    rpc CancelJob(CancelJobRequest) returns (CancelJobResponse);
    
    // RetryJob: Retry a failed ETL job
    // Use cases:
    //   - "Retry that failed FBI submission after fixing the network issue"
    //   - "Force retry this job even though it exceeded max retries"
    //   - "Retry with increased retry limit for this complex job"
    // Automatically handles retry logic with exponential backoff and error classification
    rpc RetryJob(RetryJobRequest) returns (RetryJobResponse);
    
    // DownloadJobContent: Download the generated content from a completed job
    // Use case: "Let me download the NIBRS XML that was generated for review"
    // Essential for content inspection, compliance review, and manual submission
    rpc DownloadJobContent(DownloadJobContentRequest) returns (DownloadJobContentResponse);
    
    // Utilities
    
    // ListAgencies: Get list of available agencies and their supported formats
    // Use case: "What agencies can I send reports to and what formats do they support?"
    // Returns agency info including supported output formats (NIBRS, etc.)
    rpc ListAgencies(ListAgenciesRequest) returns (ListAgenciesResponse);
    
    // GetJobStats: Get statistical overview of ETL jobs
    // Use cases:
    //   - "Show me ETL job statistics for this month"
    //   - "How many NIBRS jobs have we completed this quarter?"
    //   - "What's our success rate for FBI submissions?"
    // Returns job counts, success rates, and per-agency statistics
    rpc GetJobStats(GetJobStatsRequest) returns (GetJobStatsResponse);
    
    // ValidateReports: Validate reports against data standard before processing
    // Use cases:
    //   - "Check if these reports are valid for NIBRS before submitting"
    //   - "Validate campus incidents against data standard requirements"
    // Returns detailed validation results and error information
    rpc ValidateReports(ValidateReportsRequest) returns (ValidateReportsResponse);
    
    // UpdateJobProgress: Update job progress counters
    // Use cases:
    //   - "External job processor updating progress: 50 out of 100 reports processed"
    //   - "Distributed ETL system reporting partial completion"
    //   - "Real-time progress updates for long-running jobs"
    // Allows external systems to update job progress for monitoring and status tracking
    rpc UpdateJobProgress(UpdateJobProgressRequest) returns (UpdateJobProgressResponse);
    
    // GetAgencyStats: Get detailed statistics for a specific agency
    // Use cases:
    //   - "Show me FBI submission statistics for this quarter"
    //   - "Generate agency-specific dashboard data"
    //   - "Track performance metrics per agency"
    // Returns detailed statistics for a single agency including submission history
    rpc GetAgencyStats(GetAgencyStatsRequest) returns (GetAgencyStatsResponse);
}