import * as cdk from 'aws-cdk-lib';
import * as ecr from 'aws-cdk-lib/aws-ecr';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Construct } from 'constructs';

export interface ECRStackProps extends cdk.StackProps {
  pullAccountIds?: string[];
}

export class ECRStack extends cdk.Stack {
  public readonly repository: ecr.IRepository;
  constructor(scope: Construct, id: string, props?: ECRStackProps) {
    super(scope, 'ECRStack-' + id, props);

    // Create an ECR repository
    this.repository = new ecr.Repository(this, id + 'ServiceRepo', {
      repositoryName: id.toLocaleLowerCase() + '-service',
      removalPolicy: cdk.RemovalPolicy.RETAIN, // Retain repo when stack is deleted
      imageTagMutability: ecr.TagMutability.MUTABLE,
      imageScanOnPush: true,
      encryption: ecr.RepositoryEncryption.KMS,
      lifecycleRules: [
        {
          rulePriority: 1,
          tagStatus: ecr.TagStatus.UNTAGGED,
          maxImageCount: 10, // Keep only the latest 10 untagged images
        },
      ],
    });

    if (props?.pullAccountIds) {
      this.repository.grantPull(new iam.AccountPrincipal(cdk.Aws.ACCOUNT_ID));
      for (const accountId of props.pullAccountIds) {
        this.repository.grantPull(new iam.AccountPrincipal(accountId));
      }
    }

    // Output the repository URI
    new cdk.CfnOutput(this, id + 'ServiceRepoUri', {
      value: this.repository.repositoryUri,
      description: 'URI of the ' + id + 'Service ECR Repository',
    });
  }
}
