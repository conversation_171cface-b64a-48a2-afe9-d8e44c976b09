import * as cdk from 'aws-cdk-lib';
import * as iam from 'aws-cdk-lib/aws-iam';
import { Construct } from 'constructs';

export interface GithubActionsRoleStackProps extends cdk.StackProps {
    githubOrg: string;
    githubRepo: string;
    githubBranch: string;
}

export class GithubActionsRoleStack extends cdk.Stack {
    constructor(scope: Construct, id: string, props: GithubActionsRoleStackProps) {
        super(scope, id, props);

        const { githubOrg, githubRepo, githubBranch } = props;

        const githubProvider = new iam.OpenIdConnectProvider(this, 'GitHubOidcProvider', {
            url: 'https://token.actions.githubusercontent.com',
            thumbprints: ['6938fd4d98bab03faadb97b34396831e3780aea1'],
            clientIds: ['sts.amazonaws.com'],
        });

        // allowed only for pushes to main branch
        const federatedPrincipal = new iam.FederatedPrincipal(
            githubProvider.openIdConnectProviderArn,
            {
                StringLike: {
                    'token.actions.githubusercontent.com:sub': `repo:${githubOrg}/${githubRepo}:ref:refs/heads/${githubBranch}`,
                },
                StringEquals: {
                    'token.actions.githubusercontent.com:aud': 'sts.amazonaws.com',
                },
            },
            'sts:AssumeRoleWithWebIdentity',
        );

        const role = new iam.Role(this, 'GitHubActionsDeployRole', {
            roleName: 'GitHubActionsDeployRole',
            assumedBy: federatedPrincipal,
        });

        role.addToPolicy(
            new iam.PolicyStatement({
                sid: 'AllowCDKDeploy',
                effect: iam.Effect.ALLOW,
                actions: ['sts:AssumeRole'],
                resources: ['arn:aws:iam::*:role/cdk-*'],
            }),
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                sid: 'AllowEcrAuth',
                effect: iam.Effect.ALLOW,
                actions: ['ecr:GetAuthorizationToken'],
                resources: ['*'],
            }),
        );

        role.addToPolicy(
            new iam.PolicyStatement({
                sid: 'AllowEcrReadWrite',
                effect: iam.Effect.ALLOW,
                actions: [
                    'ecr:BatchCheckLayerAvailability',
                    'ecr:BatchGetImage',
                    'ecr:CompleteLayerUpload',
                    'ecr:GetDownloadUrlForLayer',
                    'ecr:InitiateLayerUpload',
                    'ecr:PutImage',
                    'ecr:UploadLayerPart',
                    'ecr:DescribeImages',
                    'ecr:DescribeRepositories',
                    'ecr:ListImages',
                    'ecr:GetRepositoryPolicy',
                    'ecr:SetRepositoryPolicy',
                    'ecr:DeleteRepositoryPolicy',
                    'ecr:BatchDeleteImage',
                ],
                resources: [`arn:aws:ecr:${cdk.Aws.REGION}:${cdk.Aws.ACCOUNT_ID}:repository/*`],
            }),
        );

        // allowed for pull requests
        const pullFederatedPrincipal = new iam.FederatedPrincipal(
            githubProvider.openIdConnectProviderArn,
            {
                StringLike: {
                    'token.actions.githubusercontent.com:sub': `repo:${githubOrg}/${githubRepo}:*`,
                },
                StringEquals: {
                    'token.actions.githubusercontent.com:aud': 'sts.amazonaws.com',
                },
            },
            'sts:AssumeRoleWithWebIdentity',
        );

        const pullRole = new iam.Role(this, 'GitHubEcrPullRole', {
            roleName: 'GitHubEcrPullRole',
            assumedBy: pullFederatedPrincipal,
        });

        pullRole.addToPolicy(
            new iam.PolicyStatement({
                sid: 'AllowEcrAuth',
                effect: iam.Effect.ALLOW,
                actions: ['ecr:GetAuthorizationToken'],
                resources: ['*'],
            }),
        );

        pullRole.addToPolicy(
            new iam.PolicyStatement({
                sid: 'AllowEcrPull',
                effect: iam.Effect.ALLOW,
                actions: [
                    'ecr:BatchCheckLayerAvailability',
                    'ecr:BatchGetImage',
                    'ecr:GetDownloadUrlForLayer',
                    'ecr:DescribeImages',
                    'ecr:DescribeRepositories',
                    'ecr:ListImages',
                    'ecr:GetRepositoryPolicy',
                ],
                resources: [`arn:aws:ecr:${cdk.Aws.REGION}:${cdk.Aws.ACCOUNT_ID}:repository/*`],
            }),
        );
    }
} 