#!/usr/bin/env node
import * as cdk from 'aws-cdk-lib';
import * as fs from 'fs';
import * as path from 'path';
import { AppConfig, Environment } from '../cloud-config/config';
import { EntryStack } from '../cloud-shared/entry-stack';


const app = new cdk.App();

async function loadDefaultConfig(): Promise<AppConfig> {
    const configModule = await import(`../cloud-config/default/config`);
    const config = configModule.default;
    return config;
}

async function loadEnvConfigs(): Promise<AppConfig[]> {
    const configs: AppConfig[] = [];
    const envsPath = path.join(__dirname, '../cloud-config/envs');

    // Get all environment directories
    const envDirs = fs.readdirSync(envsPath).filter((d: string) => {
        const envPath = path.join(envsPath, d);
        return fs.statSync(envPath).isDirectory() &&
            fs.existsSync(path.join(envPath, 'config.ts'));
    });

    // Load each environment config
    for (const envDir of envDirs) {
        try {
            const configModule = await import(`../cloud-config/envs/${envDir}/config`);
            const config = configModule.default as AppConfig;
            configs.push(config);
            console.log(`Loaded config for environment: ${config.environment.envName}`);
        } catch (error) {
            console.error(`Failed to load config for environment ${envDir}:`, error);
        }
    }

    return configs;
}

function extractEnvironmentInfo(configs: AppConfig[]): Environment[] {
    const environments: Environment[] = [];

    for (const config of configs) {
        environments.push(config.environment);
    }

    return environments;
}

async function deploy() {
    try {
        const defaultConfig = await loadDefaultConfig();
        const envConfigs = await loadEnvConfigs();

        const sharedAccount = defaultConfig.sharedEnvironment.accountId;
        const sharedRegion = defaultConfig.sharedEnvironment.ecrRegion;

        const environments = extractEnvironmentInfo(envConfigs);

        console.log(`Shared Account: ${sharedAccount}`);
        console.log(`Environments:`, environments);

        new EntryStack(app, 'CDKSharedEntryStack', {
            env: {
                account: sharedAccount,
                region: sharedRegion,
            },
            environments: environments,
        });

        app.synth();
    } catch (error) {
        console.error('Deployment failed:', error);
        process.exit(1);
    }
}

deploy();