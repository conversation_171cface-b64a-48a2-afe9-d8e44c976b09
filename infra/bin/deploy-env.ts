#!/usr/bin/env node
import * as cdk from 'aws-cdk-lib';
import { Tags } from 'aws-cdk-lib';
import * as fs from 'fs';
import * as path from 'path';
import { AppConfig } from '../cloud-config/config';
import { EntryStack } from '../cloud/entry-stack';

const app = new cdk.App();

const env = process.env.CDK_ENV;
if (!env || env === 'default') {
  throw new Error('CDK_ENV is not set to a valid environment');
}

let config: AppConfig;

const configPath = path.join(__dirname, `../cloud-config/envs/${env}/config.ts`);
if (!fs.existsSync(configPath)) {
  const availableEnvs = fs.readdirSync(path.join(__dirname, '../cloud-config/envs')).filter((d: string) => fs.statSync(path.join(__dirname, `../cloud-config/envs/${d}`)).isDirectory() && d !== 'default' && fs.existsSync(path.join(__dirname, `../cloud-config/envs/${d}/config.ts`)));
  throw new Error(`Unknown environment: ${env}. Available environments are: ${availableEnvs.join(', ')}`);
}

import(`../cloud-config/envs/${env}/config`).then(({ default: configModule }) => {
  config = configModule;

  if (!config.environment.accountId) {
    throw new Error('AWS account is not set in the config');
  } else {
    console.log(`AWS account: ${config.environment.accountId}`);
  }


  Tags.of(app).add('env', config.tags.env);

  new EntryStack(app, `CDKEntryStack-${config.environment.envName}`, {
    env: {
      account: config.environment.accountId,
      region: config.environment.primaryRegion,
    },
    config,
  });

  app.synth();

}).catch(err => {
  console.error(err);
  process.exit(1);
});