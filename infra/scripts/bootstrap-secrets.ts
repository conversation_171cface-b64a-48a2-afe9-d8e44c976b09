import { CreateSecretCommand, DescribeSecretCommand, GetSecretValueCommand, ResourceNotFoundException, SecretsManagerClient } from "@aws-sdk/client-secrets-manager";
import * as fs from 'fs';
import * as path from 'path';
import * as readline from 'readline';
import { ServerConfig } from "../cloud-config/server-config";

const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

const askQuestion = (query: string): Promise<string> => {
    return new Promise(resolve => rl.question(query, ans => {
        resolve(ans);
    }));
};

const main = async (): Promise<Set<string>> => {
    const region = process.env.AWS_REGION || 'us-west-2';
    const secretsManagerClient = new SecretsManagerClient({ region });

    const serverConfigs: ServerConfig[] = [];
    const serversDir = path.resolve(__dirname, '../cloud-config/default/servers');
    const files = fs.readdirSync(serversDir);

    for (const file of files) {
        if (file.endsWith('.ts') && !file.endsWith('.d.ts')) {
            const modulePath = path.join(serversDir, file);
            const module = await import(modulePath);
            for (const key in module) {
                const exportedItem = module[key];
                if (exportedItem && typeof exportedItem === 'object' && 'serviceName' in exportedItem) {
                    serverConfigs.push(exportedItem as ServerConfig);
                }
            }
        }
    }

    const allSecrets = new Set<string>();
    serverConfigs.forEach(config => {
        config.secrets?.forEach(secret => {
            allSecrets.add(secret.secretName);
        });
    });

    console.log("Checking for missing secrets...");

    for (const secretName of allSecrets) {
        try {
            await secretsManagerClient.send(new DescribeSecretCommand({ SecretId: secretName }));
            console.log(`Secret ${secretName} already exists.`);
        } catch (error) {
            if (error instanceof ResourceNotFoundException) {
                console.log(`Secret ${secretName} not found.`);
                const secretValue = await askQuestion(`Please enter the value for secret "${secretName}": `);
                if (secretValue) {
                    await secretsManagerClient.send(new CreateSecretCommand({
                        Name: secretName,
                        SecretString: secretValue,
                    }));
                    console.log(`Secret ${secretName} created.`);
                } else {
                    console.log(`Skipping creation of secret ${secretName} due to empty value.`);
                }
            } else {
                console.error(`Error checking secret ${secretName}:`, error);
            }
        }
    }

    console.log("Secret check complete.");
    return allSecrets;
};

const createDerivedSecrets = async (allSecrets: Set<string>) => {
    const region = process.env.AWS_REGION || 'us-west-2';
    const secretsManagerClient = new SecretsManagerClient({ region });

    const dbUserSecretMap: { [key: string]: string } = {
        'hero': 'ServerUserSecret',
        'perms': 'PermsUserSecret'
    };

    for (const secretName of allSecrets) {
        const match = secretName.match(/^Postgres(.+)DBUrl$/);
        if (match) {
            const dbName = match[1].toLowerCase();
            const userSecretName = dbUserSecretMap[dbName];

            if (!userSecretName) {
                console.log(`No user secret mapping found for ${dbName}, skipping derived secret ${secretName}`);
                continue;
            }

            try {
                await secretsManagerClient.send(new DescribeSecretCommand({ SecretId: secretName }));
                console.log(`Derived secret ${secretName} already exists.`);
            } catch (error) {
                if (error instanceof ResourceNotFoundException) {
                    console.log(`Derived secret ${secretName} not found. Attempting to create it.`);
                    try {
                        const userSecretValue = await secretsManagerClient.send(new GetSecretValueCommand({ SecretId: userSecretName }));
                        if (userSecretValue.SecretString) {
                            const userSecret = JSON.parse(userSecretValue.SecretString);
                            const dbUrl = `postgres://${userSecret.username}:${userSecret.password}@${userSecret.host}:${userSecret.port}/${userSecret.dbname}`;

                            await secretsManagerClient.send(new CreateSecretCommand({
                                Name: secretName,
                                SecretString: dbUrl,
                            }));
                            console.log(`Derived secret ${secretName} created.`);
                        }
                    } catch (getSecretError) {
                        console.error(`Failed to create derived secret ${secretName}. Could not retrieve source secret ${userSecretName}.`, getSecretError);
                    }
                } else {
                    console.error(`Error checking derived secret ${secretName}:`, error);
                }
            }
        }
    }
    rl.close();
};

main()
    .then(createDerivedSecrets)
    .catch(error => {
        console.error("An error occurred:", error);
        rl.close();
        process.exit(1);
    }); 