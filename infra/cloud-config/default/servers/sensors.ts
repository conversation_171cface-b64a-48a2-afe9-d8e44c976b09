import { ServerConfig } from '../../server-config';

export const sensorsServerConfig: ServerConfig = {
    serviceName: "Sensors",
    enableDbAccess: true,
    taskRolePolicies: [
        {
            actions: ["kms:Encrypt", "kms:Decrypt", "kms:ReEncrypt*", "kms:GenerateDataKey*", "kms:DescribeKey"],
            resources: ["${KMS_KEY_ARN}"]
        },
        {
            actions: ["kinesisvideo:*"],
            resources: ["*"]
        }
    ],
    secrets: []
}; 