import { ServerConfig } from '../../server-config';

export const featureFlagsServerConfig: ServerConfig = {
    serviceName: "FeatureFlags",
    enableDbAccess: true,
    taskRolePolicies: [
        {
            actions: ["kms:Encrypt", "kms:Decrypt", "kms:ReEncrypt*", "kms:GenerateDataKey*", "kms:DescribeKey"],
            resources: ["${KMS_KEY_ARN}"]
        },
    ],
    env: {
        "REPO_TYPE": "postgres",
    },
    secrets: []
}; 