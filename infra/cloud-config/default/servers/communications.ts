import { ServerConfig } from '../../server-config';

export const communicationsServerConfig: ServerConfig = {
    serviceName: "Communications",
    enableBasicAuth: true,
    enableDbAccess: true,
    taskRolePolicies: [
        {
            actions: ["kms:Encrypt", "kms:Decrypt", "kms:ReEncrypt*", "kms:GenerateDataKey*", "kms:DescribeKey"],
            resources: ["${KMS_KEY_ARN}"]
        }
    ],
    secrets: [
        {
            secretName: "agora/chatOrgName",
            envVarName: "AGORA_CHAT_ORG_NAME",
        },
        {
            secretName: "agora/chatAppName",
            envVarName: "AGORA_CHAT_APP_NAME",
        },
        {
            secretName: "agora/chatHostURL",
            envVarName: "AGORA_CHAT_HOST_URL",
        },
        {
            secretName: "agora/chatAppId",
            envVarName: "AGORA_CHAT_APP_ID",
        },
        {
            secretName: "agora/appCertificate",
            envVarName: "AGORA_APP_CERTIFICATE",
        },
        {
            secretName: "agora/appId",
            envVarName: "AGORA_APP_ID",
        },
        {
            secretName: "twilio/account_sid",
            envVarName: "TWILIO_ACCOUNT_SID",
        },
        {
            secretName: "twilio/auth_token",
            envVarName: "TWILIO_AUTH_TOKEN",
        },
        {
            secretName: "twilio/api_key_sid",
            envVarName: "TWILIO_API_KEY_SID",
        },
        {
            secretName: "twilio/api_key_secret",
            envVarName: "TWILIO_API_KEY_SECRET",
        },
        {
            secretName: "zello/admin_user",
            envVarName: "ZELLO_ADMIN_USER",
        },
        {
            secretName: "zello/admin_user_password",
            envVarName: "ZELLO_ADMIN_USER_PASSWORD",
        },
        {
            secretName: "zello/api_key",
            envVarName: "ZELLO_API_KEY",
        },
    ],
    env:
    {
        "REPO_TYPE": "postgres",
        "COMMS_SERVER_PUBLIC_DOMAIN": "communications.basic.api.${ROOT_DOMAIN}",
    },
}; 