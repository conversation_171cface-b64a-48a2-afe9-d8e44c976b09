import { ServerConfig } from '../../server-config';

export const permsServerConfig: ServerConfig = {
    serviceName: "Perms",
    enableDbAccess: true,
    env:
    {
        "REPO_TYPE": "postgres",
        "FGA_API_URL": "https://openfga.lb.api.${ROOT_DOMAIN}",
    },
    taskRolePolicies: [
        {
            actions: ["kms:Encrypt", "kms:Decrypt", "kms:ReEncrypt*", "kms:GenerateDataKey*", "kms:DescribeKey"],
            resources: ["${KMS_KEY_ARN}"]
        }
    ],
    secrets: [
        {
            secretName: "bots/bot-post-confirmation-lambda-secret",
            envVarName: "BOT_POST_CONFIRMATION_LAMBDA_SECRET",
        },
        {
            secretName: "bots/bot-pre-signup-lambda-secret",
            envVarName: "BOT_PRE_SIGNUP_LAMBDA_SECRET",
        },
        {
            secretName: "bots/bot-basic-auth-lambda-secret",
            envVarName: "BOT_BASIC_AUTH_LAMBDA_SECRET",
        },
        {
            secretName: "bots/bot-camera-listener-lambda-secret",
            envVarName: "BOT_CAMERA_LISTENER_LAMBDA_SECRET",
        },
    ]
}; 