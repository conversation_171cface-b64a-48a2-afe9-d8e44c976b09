import { ServerConfig } from "../../server-config";
import { commandServerConfig } from "./command";
import { communicationsServerConfig } from "./communications";
import { featureFlagsServerConfig } from "./featureflags";
import { filerepositoryServerConfig } from "./filerepository";
import { orgsServerConfig } from "./orgs";
import { permsServerConfig } from "./perms";
import { sensorsServerConfig } from "./sensors";
import { workflowServerConfig } from "./workflow";

export const servers: Record<string, ServerConfig> = {
    perms: permsServerConfig,
    orgs: orgsServerConfig,
    workflow: workflowServerConfig,
    command: commandServerConfig,
    sensors: sensorsServerConfig,
    communications: communicationsServerConfig,
    filerepository: filerepositoryServerConfig,
    featureFlags: featureFlagsServerConfig,
}