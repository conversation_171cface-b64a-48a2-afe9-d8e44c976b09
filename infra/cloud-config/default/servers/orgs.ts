import { ServerConfig } from '../../server-config';

export const orgsServerConfig: ServerConfig = {
    serviceName: "Orgs",
    enableDbAccess: true,
    env:
    {
        "REPO_TYPE": "postgres",
        "COMMS_SERVER_PUBLIC_DOMAIN": "communications.basic.api.${ROOT_DOMAIN}",
    },
    taskRolePolicies: [
        {
            actions: ["kms:Encrypt", "kms:Decrypt", "kms:ReEncrypt*", "kms:GenerateDataKey*", "kms:DescribeKey"],
            resources: ["${KMS_KEY_ARN}"]
        },
        {
            actions: [
                "cognito-idp:CreateGroup", "cognito-idp:GetGroup", "cognito-idp:ListGroups",
                "cognito-idp:UpdateGroup", "cognito-idp:DeleteGroup", "cognito-idp:AdminAddUserToGroup",
                "cognito-idp:AdminRemoveUserFromGroup", "cognito-idp:ListUsersInGroup",
                "cognito-idp:AdminCreateUser", "cognito-idp:AdminSetUserPassword"
            ],
            resources: ["${COGNITO_USER_POOL_ARN}"]
        },
    ],
    secrets: [
        {
            secretName: "bots/bot-post-confirmation-lambda-secret",
            envVarName: "BOT_POST_CONFIRMATION_LAMBDA_SECRET",
        },
        {
            secretName: "bots/bot-pre-signup-lambda-secret",
            envVarName: "BOT_PRE_SIGNUP_LAMBDA_SECRET",
        },
        {
            secretName: "bots/bot-basic-auth-lambda-secret",
            envVarName: "BOT_BASIC_AUTH_LAMBDA_SECRET",
        },
        {
            secretName: "zello/admin_user",
            envVarName: "ZELLO_ADMIN_USER",
        },
        {
            secretName: "zello/admin_user_password",
            envVarName: "ZELLO_ADMIN_USER_PASSWORD",
        },
        {
            secretName: "zello/api_key",
            envVarName: "ZELLO_API_KEY",
        },
        {
            secretName: "twilio/account_sid",
            envVarName: "TWILIO_ACCOUNT_SID",
        },
        {
            secretName: "twilio/auth_token",
            envVarName: "TWILIO_AUTH_TOKEN",
        },
        {
            secretName: "twilio/api_key_sid",
            envVarName: "TWILIO_API_KEY_SID",
        },
        {
            secretName: "twilio/api_key_secret",
            envVarName: "TWILIO_API_KEY_SECRET",
        },
    ]
}; 