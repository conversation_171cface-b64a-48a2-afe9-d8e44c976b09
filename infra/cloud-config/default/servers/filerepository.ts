import { ServerConfig } from '../../server-config';

export const filerepositoryServerConfig: ServerConfig = {
    serviceName: "FileRepository",
    enableDbAccess: true,
    taskRolePolicies: [
        {
            actions: ["kms:Encrypt", "kms:Decrypt", "kms:ReEncrypt*", "kms:GenerateDataKey*", "kms:DescribeKey"],
            resources: ["${KMS_KEY_ARN}"]
        },
        {
            actions: [
                "s3:GetObject", "s3:PutObject", "s3:DeleteObject", "s3:ListBucket", "s3:GetObjectVersion",
                "s3:PutObjectAcl", "s3:GetObjectAcl", "s3:GetBucketLocation", "s3:GetBucketVersioning"
            ],
            resources: ["arn:aws:s3:::*", "arn:aws:s3:::*/*"]
        }
    ],
    env: {
        "REPO_TYPE": "postgres",
        "AWS_S3_BUCKET_NAME": "${ENVIRONMENT}-hero-file-repository",
    },
    secrets: []
}; 