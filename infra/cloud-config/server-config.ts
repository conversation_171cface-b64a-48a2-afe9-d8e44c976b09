export interface IAMPolicyStatement {
    effect?: 'Allow' | 'Deny';
    actions: string[];
    resources: string[];
}

export interface Secret {
    secretName: string;
    arn?: string;
    envVarName: string;
    maxCapacity?: number;
}

export interface ServerConfig {
    serviceName: string;
    ecrOnly?: boolean;
    secrets?: Secret[];
    enableBasicAuth?: boolean;
    env?: Record<string, string>;
    dockerImageName?: string;
    cpu?: number;
    memoryLimitMiB?: number;
    desiredCount?: number;
    minCapacity?: number;
    maxCapacity?: number;
    taskRolePolicies?: IAMPolicyStatement[];
    enableDbAccess?: boolean;
}

export interface ServersConfig {
    perms: Partial<ServerConfig>;
    orgs: Partial<ServerConfig>;
    workflow: Partial<ServerConfig>;
    command: Partial<ServerConfig>;
    sensors: Partial<ServerConfig>;
    communications: Partial<ServerConfig>;
    filerepository: Partial<ServerConfig>;
}