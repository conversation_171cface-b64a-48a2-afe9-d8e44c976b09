# Environment Configuration

This directory contains the configuration for the different environments (e.g., `dev`, `prod`). 

## Configuration Layers

The configuration is structured in layers. Each environment inherits and can override settings from the default configuration.

1.  **Default Configuration**: `infra/cloud-config/default/config.ts` provides a base configuration that is inherited by all environments. It defines common settings for services, S3 buckets, and other resources.

2.  **Environment-Specific Configuration**: Each environment (e.g., `dev`, `prod`) has its own directory under `infra/cloud-config/envs/`. The `config.ts` file in an environment's directory contains settings that override the default configuration.

The `merge.ts` script handles the merging of these configurations.

## Server Configuration

Each service has a configuration file in the `servers` directory (e.g., `infra/env/prod/servers/command.ts`). The structure of this configuration is defined in `infra/env/server-config.ts`.

### CPU and Memory

You can specify the CPU and memory for each service in its configuration file.

-   `cpu`: The number of CPU units to reserve for the container.
-   `memoryLimitMiB`: The amount of memory (in MiB) to reserve for the container.

If not specified, the system will use the default values specified in our fargate stack.

### IAM Task Role Policies

The `taskRolePolicies` array in a service's configuration allows you to add IAM policies to the task role for that service. This grants the service permissions to interact with other AWS resources.

#### Dynamic Resource ARNs

For some resources, the ARN is not known until deployment time. In these cases, you can use placeholders in the `resources` array of an IAM policy. These placeholders will be automatically replaced with the correct ARNs during the deployment process.

The following placeholders are supported:

-   `${KMS_KEY_ARN}`: The ARN of the KMS key for the service.
-   `${COGNITO_USER_POOL_ARN}`: The ARN of the Cognito User Pool for the environment.

**Example:**

```typescript
{
    actions: ["kms:Encrypt", "kms:Decrypt"],
    resources: ["${KMS_KEY_ARN}"]
}
```

### S3 Bucket Configuration

You can define S3 buckets in the `s3buckets` property of a configuration file. The key is the logical name of the bucket, and the value is an `S3BucketConfig` object.

The structure of this configuration is defined in `infra/cloud-config/s3-config.ts`.

**Example:**

```typescript
// in infra/cloud-config/default/config.ts
s3buckets: {
    fileRepository: {
        bucketName: 'hero-file-repository',
        description: 'Main file repository for Hero application',
        versioned: true,
        // ... other options
    }
}
```

### Secrets Management

The `secrets` array in a service's configuration allows you to inject secrets from AWS Secrets Manager into the service's container as environment variables.

Each secret object in the array should contain:

-   `secretName`: The name of the secret in AWS Secrets Manager.
-   `arn`: The ARN of the secret.
-   `envVarName`: The name of the environment variable to create in the container with the secret's value.

**Example:**

```typescript
{
    secretName: "PostgresDBCredentials",
    arn: "arn:aws:secretsmanager:us-west-2:816069150268:secret:PostgresDBCredentials-tqW9hm",
    envVarName: "DB_CREDENTIALS",
}
```

### Environment Variable Placeholders

You can use placeholders in environment variable values. These placeholders will be automatically replaced with the correct values during deployment.

The following placeholders are supported:

-   `${ROOT_DOMAIN}`: The root domain for the environment (e.g., `gethero.com`).

**Example:**

```typescript
// in infra/env/dev/servers/myservice.ts
env: {
    MY_SERVICE_URL: "https://myservice.api.${ROOT_DOMAIN}"
}
```

### Other Configuration

-   `serviceName`: The name of the service.
-   `ecrOnly`: If true, only an ECR repository will be created for the service, and no Fargate service will be deployed. Typically only used for the inital deployment to bootstrap a new service. 
-   `enableBasicAuth`: If true, basic authentication will be enabled for the service.
-   `enableDbAccess`: If true, injects the database credentials (`DB_CREDENTIALS` and `DB_USER_SECRET`) as environment variables.
-   `env`: A map of environment variables to set in the container.
-   `dockerImageName`: The name of the Docker image to use for the service.
-   `desiredCount`: The desired number of tasks to run for the service.
-   `minCapacity`: The minimum number of tasks to run for the service.
-   `maxCapacity`: The maximum number of tasks to run for the service. 