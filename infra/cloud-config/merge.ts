export function isObject(item: any): item is Record<string, any> {
    return item && typeof item === 'object' && !Array.isArray(item);
}

export function mergeDeep<T extends Record<string, any>>(...sources: Partial<T>[]): T {
    const result: Record<string, any> = {};
    for (const source of sources) {
        if (isObject(source)) {
            for (const key in source) {
                const sourceValue = source[key];
                if (isObject(sourceValue) && isObject(result[key])) {
                    result[key] = mergeDeep(result[key] as Record<string, any>, sourceValue as Record<string, any>);
                } else if (isObject(sourceValue)) {
                    result[key] = mergeDeep({}, sourceValue as Record<string, any>);
                } else {
                    result[key] = sourceValue;
                }
            }
        }
    }
    return result as T;
} 