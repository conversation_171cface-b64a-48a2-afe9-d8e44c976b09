package main

import (
	"context"
	"database/sql"
	"embed"
	"encoding/json"
	"fmt"
	"io/fs"
	"log"
	"os"

	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/secretsmanager"
	_ "github.com/lib/pq"
	"github.com/pressly/goose/v3"
)

//go:embed all:migrations
var embedMigrations embed.FS

type DBSecret struct {
	Username string `json:"username"`
	Password string `json:"password"`
	Host     string `json:"host"`
	Port     int    `json:"port"`
	DBName   string `json:"dbname"`
}

type Response struct {
	Message string `json:"message"`
}

func handler(ctx context.Context) (Response, error) {
	migrationsFS, err := fs.Sub(embedMigrations, "migrations")
	if err != nil {
		return Response{}, fmt.Errorf("failed to get migrations subdirectory: %w", err)
	}
	goose.SetBaseFS(migrationsFS)

	secretName := os.Getenv("DB_SECRET_NAME")
	if secretName == "" {
		return Response{}, fmt.Errorf("DB_SECRET_NAME environment variable not set")
	}

	sess, err := session.NewSession()
	if err != nil {
		return Response{}, fmt.Errorf("failed to create AWS session: %w", err)
	}

	svc := secretsmanager.New(sess)
	input := &secretsmanager.GetSecretValueInput{
		SecretId: aws.String(secretName),
	}

	result, err := svc.GetSecretValue(input)
	if err != nil {
		return Response{}, fmt.Errorf("failed to get secret value: %w", err)
	}

	if result.SecretString == nil {
		return Response{}, fmt.Errorf("retrieved secret string is empty")
	}

	var dbSecret DBSecret
	if err := json.Unmarshal([]byte(*result.SecretString), &dbSecret); err != nil {
		return Response{}, fmt.Errorf("failed to unmarshal db secret: %w", err)
	}

	dbConnectionString := fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=require",
		dbSecret.Username,
		dbSecret.Password,
		dbSecret.Host,
		dbSecret.Port,
		dbSecret.DBName,
	)

	db, err := sql.Open("postgres", dbConnectionString)
	if err != nil {
		return Response{}, fmt.Errorf("failed to open database connection: %w", err)
	}
	defer db.Close()

	if err := goose.SetDialect("postgres"); err != nil {
		return Response{}, fmt.Errorf("failed to set goose dialect: %w", err)
	}

	log.Println("Running migrations...")
	if err := goose.Up(db, "."); err != nil {
		log.Printf("goose up failed: %v", err)
		return Response{}, fmt.Errorf("failed to run migrations: %w", err)
	}

	return Response{Message: "Migrations ran successfully!"}, nil
}

func main() {
	lambda.Start(handler)
}
