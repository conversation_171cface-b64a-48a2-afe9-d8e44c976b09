package handlers

import (
	"bytes"
	"context"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"twilio-monitor/twilio"
)

// SentryHandler sends alerts to Sentry
type SentryHandler struct {
	dsn    string
	client *http.Client
}

// NewSentryHandler creates a new Sentry alert handler
func NewSentryHandler() *SentryHandler {
	return &SentryHandler{
		dsn: os.Getenv("SENTRY_DSN"),
		client: &http.Client{
			Timeout: 10 * time.Second,
		},
	}
}

// SendAlert sends an alert to Sentry using the modern envelope format
func (h *SentryHandler) SendAlert(ctx context.Context, errorData *twilio.TwilioError) error {
	if h.dsn == "" {
		return fmt.Errorf("SENTRY_DSN environment variable not set")
	}

	// Parse DSN to get project info
	parsedDSN, err := url.Parse(h.dsn)
	if err != nil {
		return fmt.Errorf("invalid Sentry DSN: %w", err)
	}

	// Extract project ID from path
	pathParts := strings.Split(strings.Trim(parsedDSN.Path, "/"), "/")
	if len(pathParts) < 1 {
		return fmt.Errorf("invalid Sentry DSN format")
	}
	projectID := pathParts[len(pathParts)-1]

	// Use modern Sentry envelope endpoint
	sentryURL := fmt.Sprintf("%s://%s/api/%s/envelope/", parsedDSN.Scheme, parsedDSN.Host, projectID)

	// Generate event ID
	eventID := generateEventID()

	// Create envelope headers
	envelopeHeaders := map[string]interface{}{
		"event_id": eventID,
		"dsn":      h.dsn,
	}

	// Get environment context
	environment := getEnvironmentFromAccountSid(errorData.AccountSid)

	// Create actionable alert title for PagerDuty/on-call
	serviceContext := extractServiceContext(errorData.URL)
	alertTitle := fmt.Sprintf("[%s] Twilio %s Error %d - %s",
		strings.ToUpper(environment),
		strings.Title(errorData.Category()),
		errorData.ErrorCode,
		serviceContext)

	// Create event payload
	eventPayload := map[string]interface{}{
		"event_id":  eventID,
		"timestamp": errorData.Timestamp.Format(time.RFC3339),
		"platform":  "other",
		"level":     mapSeverityToSentry(errorData.Severity()),
		"logger":    "twilio-monitor",
		"message": map[string]interface{}{
			"formatted": alertTitle,
		},
		"exception": map[string]interface{}{
			"values": []map[string]interface{}{
				{
					"type":  alertTitle,
					"value": errorData.ErrorMessage,
				},
			},
		},
		"tags": map[string]string{
			"error_code":   fmt.Sprintf("%d", errorData.ErrorCode),
			"call_sid":     errorData.CallSid,
			"direction":    errorData.Direction,
			"category":     errorData.Category(),
			"severity":     errorData.Severity(),
			"environment":  environment,
			"service_type": serviceContext,
		},
		"extra": map[string]interface{}{
			"account_sid":      errorData.AccountSid,
			"from":             errorData.From,
			"to":               errorData.To,
			"status":           errorData.Status,
			"duration":         errorData.Duration,
			"url":              errorData.URL,
			"raw_payload":      errorData.RawPayload,                         // Critical for debugging
			"response_details": extractResponseDetails(errorData.RawPayload), // Clean response parsing
		},
		"fingerprint": []string{
			"twilio-error",
			errorData.CallSid,
			fmt.Sprintf("%d", errorData.ErrorCode),
		},
	}

	// Marshal payloads
	envelopeHeadersJSON, err := json.Marshal(envelopeHeaders)
	if err != nil {
		return fmt.Errorf("failed to marshal envelope headers: %w", err)
	}

	eventPayloadJSON, err := json.Marshal(eventPayload)
	if err != nil {
		return fmt.Errorf("failed to marshal event payload: %w", err)
	}

	// Create item headers
	itemHeaders := map[string]interface{}{
		"type":         "event",
		"length":       len(eventPayloadJSON),
		"content_type": "application/json",
	}

	itemHeadersJSON, err := json.Marshal(itemHeaders)
	if err != nil {
		return fmt.Errorf("failed to marshal item headers: %w", err)
	}

	// Build envelope (headers + item headers + payload, separated by newlines)
	envelope := fmt.Sprintf("%s\n%s\n%s",
		string(envelopeHeadersJSON),
		string(itemHeadersJSON),
		string(eventPayloadJSON))

	req, err := http.NewRequestWithContext(ctx, "POST", sentryURL, bytes.NewBuffer([]byte(envelope)))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	// Extract auth key from DSN
	if parsedDSN.User != nil {
		authKey := parsedDSN.User.Username()
		req.Header.Set("X-Sentry-Auth", fmt.Sprintf("Sentry sentry_version=7,sentry_key=%s", authKey))
	}

	req.Header.Set("Content-Type", "application/x-sentry-envelope")

	resp, err := h.client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request to Sentry: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("sentry API returned status %d", resp.StatusCode)
	}

	return nil
}

// mapSeverityToSentry maps internal severity to Sentry severity levels
func mapSeverityToSentry(severity string) string {
	switch severity {
	case "high":
		return "error"
	case "low":
		return "warning"
	default:
		return "info"
	}
}

// getEnvironmentFromAccountSid maps Twilio AccountSid to environment name
func getEnvironmentFromAccountSid(accountSid string) string {
	// Use the environment name from CDK config
	heroEnv := os.Getenv("ENVIRONMENT")
	expectedAccountSid := os.Getenv("TWILIO_ACCOUNT_SID")

	// Verify the AccountSid matches what we expect for this environment
	if accountSid == expectedAccountSid && heroEnv != "" {
		return heroEnv
	}

	// For cross-environment webhooks, use a generic identifier
	if len(accountSid) >= 4 {
		return fmt.Sprintf("unknown-%s", accountSid[len(accountSid)-4:])
	}
	return "unknown"
}

// extractServiceContext extracts actionable service info without exposing credentials
func extractServiceContext(webhookURL string) string {
	if webhookURL == "" {
		return "Unknown Service"
	}

	// Parse URL to extract meaningful parts
	parsedURL, err := url.Parse(webhookURL)
	if err != nil {
		return "Invalid URL"
	}

	// Determine service type from path
	var serviceType string
	if strings.Contains(parsedURL.Path, "/voice") {
		serviceType = "/voice"
	} else if strings.Contains(parsedURL.Path, "/callstatus") {
		serviceType = "/callstatus"
	} else {
		serviceType = "webhook"
	}

	// Determine environment/host type
	if strings.Contains(parsedURL.Host, "ngrok") {
		return fmt.Sprintf("🔧 Dev %s", serviceType)
	} else if strings.Contains(parsedURL.Host, "gethero.com") {
		return fmt.Sprintf("🚨 Prod %s", serviceType)
	}

	return fmt.Sprintf("External %s", serviceType)
}

// extractResponseDetails parses webhook response data for debugging context
func extractResponseDetails(rawPayload map[string]string) map[string]interface{} {
	details := map[string]interface{}{}

	// Parse the JSON payload if it exists
	payloadStr, exists := rawPayload["Payload"]
	if !exists || payloadStr == "" {
		return details
	}

	var payload map[string]interface{}
	if err := json.Unmarshal([]byte(payloadStr), &payload); err != nil {
		details["parse_error"] = "Failed to parse Payload JSON"
		return details
	}

	// Extract webhook response info
	if webhook, ok := payload["webhook"].(map[string]interface{}); ok {
		if response, ok := webhook["response"].(map[string]interface{}); ok {

			// Extract response body with length limit
			if body, ok := response["body"].(string); ok && body != "" {
				if len(body) > 1000 {
					details["response_body"] = body[:1000] + "... [truncated]"
					details["response_body_length"] = len(body)
				} else {
					details["response_body"] = body
				}
			}

			// Extract useful response headers
			if headers, ok := response["headers"].(map[string]interface{}); ok {
				usefulHeaders := map[string]interface{}{}

				// AWS-specific headers
				if val, exists := headers["x-amzn-errortype"]; exists {
					usefulHeaders["amzn_error_type"] = val // Renamed to avoid Sentry scrubbing
				}
				if val, exists := headers["x-amzn-requestid"]; exists {
					usefulHeaders["aws_request_id"] = val
				}

				// Envoy/Load balancer headers
				if val, exists := headers["x-envoy-upstream-service-time"]; exists {
					usefulHeaders["upstream_service_time_ms"] = val
				}
				if val, exists := headers["Server"]; exists {
					usefulHeaders["server"] = val
				}

				// Twilio debugging headers
				if val, exists := headers["x-twilio-webhookinsights"]; exists {
					usefulHeaders["twilio_timing"] = val
				}
				if val, exists := headers["x-twilio-webhookretriable"]; exists {
					usefulHeaders["twilio_retriable"] = val
				}
				if val, exists := headers["x-twilio-webhookattempt"]; exists {
					usefulHeaders["twilio_attempt"] = val
				}

				if len(usefulHeaders) > 0 {
					details["response_headers"] = usefulHeaders
				}
			}

			// Status code
			if statusCode, ok := response["status_code"]; ok && statusCode != nil {
				details["response_status"] = statusCode
			}
		}
	}

	return details
}

// generateEventID generates a 32-character lowercase hexadecimal UUID for Sentry
func generateEventID() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}
