package twilio

import (
	"encoding/json"
	"fmt"
	"net/url"
	"strconv"
	"strings"
	"time"
)

// TwilioError represents a parsed Twilio error event
type TwilioError struct {
	// Core identifiers
	CallSid    string `json:"call_sid"`
	AccountSid string `json:"account_sid"`

	// Error details
	ErrorCode    int    `json:"error_code"`
	ErrorMessage string `json:"error_message"`
	Level        string `json:"level,omitempty"` // Twilio's ERROR/Warning classification

	// Call context
	From      string `json:"from"`
	To        string `json:"to"`
	Direction string `json:"direction"`
	Status    string `json:"status"`

	// Timing
	Timestamp time.Time `json:"timestamp"`
	Duration  int       `json:"duration,omitempty"`

	// Additional context
	URL        string            `json:"url,omitempty"`
	RawPayload map[string]string `json:"raw_payload"`
}

// Severity returns the error severity based on <PERSON><PERSON><PERSON>'s classification and context.
// Business logic: Use <PERSON>wilio's ERROR/Warning levels, with context-based overrides.
func (te *TwilioError) Severity() string {
	// Lower severity for development/ngrok tunnel errors
	if strings.Contains(te.URL, "ngrok") {
		return "low"
	}

	// Lower severity for callstatus errors - they usually follow voice failures
	if strings.Contains(te.URL, "/callstatus") {
		return "low"
	}

	// Use Twilio's official classification if available
	if te.Level != "" {
		switch strings.ToLower(te.Level) {
		case "error":
			return "high"
		case "warning":
			return "low"
		}
	}

	// Fallback for webhooks without Level field - minimal maintenance approach
	return te.fallbackSeverity()
}

// fallbackSeverity provides simple classification for webhooks without Level field
// This should rarely be used since Twilio provides Level in modern webhooks
func (te *TwilioError) fallbackSeverity() string {
	switch {
	// Only page for clearly critical account/auth issues
	case te.ErrorCode >= 10000 && te.ErrorCode < 11000: // Account suspended/inactive
		return "high"
	case te.ErrorCode >= 20000 && te.ErrorCode < 21000: // Billing issues
		return "high"
	case te.ErrorCode >= 40000 && te.ErrorCode < 50000: // Auth failures
		return "high"

	// Everything else - default to low, let Twilio's Level field do the work
	default:
		return "low"
	}
}

// Category returns the error category for routing
func (te *TwilioError) Category() string {
	switch {
	// Webhook/HTTP integration errors
	case te.ErrorCode >= 11000 && te.ErrorCode < 20000:
		return "webhook"

	// Account/billing errors
	case te.ErrorCode >= 20000 && te.ErrorCode < 21000:
		return "account"

	// SMS/messaging errors
	case te.ErrorCode >= 21000 && te.ErrorCode < 30000:
		return "messaging"

	// Call setup failures
	case te.ErrorCode >= 30000 && te.ErrorCode < 40000:
		return "call_setup"

	// Authentication/authorization errors
	case te.ErrorCode >= 40000 && te.ErrorCode < 50000:
		return "authentication"

	// Media/network issues
	case te.ErrorCode >= 50000 && te.ErrorCode < 60000:
		return "media"

	// Unknown/other errors
	default:
		return "other"
	}
}

// DebuggerPayload represents the JSON payload in Twilio Debugger webhooks
type DebuggerPayload struct {
	ResourceSid string `json:"resource_sid"`
	ServiceSid  string `json:"service_sid"`
	ErrorCode   string `json:"error_code"`
	MoreInfo    struct {
		Msg       string `json:"Msg"`
		ErrorCode string `json:"ErrorCode"`
		LogLevel  string `json:"LogLevel"`
	} `json:"more_info"`
	Webhook struct {
		Type    string `json:"type"`
		Request struct {
			URL        string            `json:"url"`
			Method     string            `json:"method"`
			Parameters map[string]string `json:"parameters"`
		} `json:"request"`
		Response struct {
			StatusCode interface{}       `json:"status_code"`
			Headers    map[string]string `json:"headers"`
			Body       string            `json:"body"`
		} `json:"response"`
	} `json:"webhook"`
}

// ParseTwilioWebhook parses Twilio webhook form data into TwilioError.
// Handles two webhook formats:
// 1. Debugger webhooks: contain JSON Payload field with error details
// 2. Direct error webhooks: error fields directly in form data
//
// formData contains URL-encoded form fields from Twilio's webhook POST request.
// See: https://www.twilio.com/docs/usage/webhooks/debugger-webhooks
func ParseTwilioWebhook(formData url.Values) (*TwilioError, error) {
	// Check if this is a Twilio Debugger webhook (has Payload field)
	payloadStr := formData.Get("Payload")
	if payloadStr != "" {
		return parseDebuggerWebhook(formData, payloadStr)
	}

	// Fallback to direct error webhook format
	return parseDirectErrorWebhook(formData)
}

// parseDebuggerWebhook handles Twilio Debugger notification format.
// payloadStr contains JSON with structure: {"resource_sid": "CAxx", "error_code": "12345", "more_info": {...}, "webhook": {...}}
func parseDebuggerWebhook(formData url.Values, payloadStr string) (*TwilioError, error) {
	var payload DebuggerPayload
	if err := json.Unmarshal([]byte(payloadStr), &payload); err != nil {
		return nil, fmt.Errorf("failed to parse JSON payload: %w", err)
	}

	// Parse error code from payload
	errorCode, err := strconv.Atoi(payload.ErrorCode)
	if err != nil {
		return nil, fmt.Errorf("invalid error code in payload: %w", err)
	}

	// Parse timestamp
	timestamp, _ := time.Parse(time.RFC3339, formData.Get("Timestamp"))
	if timestamp.IsZero() {
		timestamp = time.Now()
	}

	// Create raw payload map for debugging
	rawPayload := make(map[string]string)
	for key, values := range formData {
		if len(values) > 0 {
			rawPayload[key] = values[0]
		}
	}

	// Extract call details from webhook parameters if available
	callSid := payload.ResourceSid
	if callSid == "" && payload.Webhook.Request.Parameters != nil {
		callSid = payload.Webhook.Request.Parameters["CallSid"]
	}

	var from, to, direction, status string
	if payload.Webhook.Request.Parameters != nil {
		from = payload.Webhook.Request.Parameters["From"]
		to = payload.Webhook.Request.Parameters["To"]
		direction = payload.Webhook.Request.Parameters["Direction"]
		status = payload.Webhook.Request.Parameters["CallStatus"]
	}

	// Extract error message with fallback logic
	errorMessage := payload.MoreInfo.Msg
	if errorMessage == "" {
		// Fallback to generating message from available data
		if payload.Webhook.Response.StatusCode != nil {
			statusCode := fmt.Sprintf("%v", payload.Webhook.Response.StatusCode)
			errorMessage = fmt.Sprintf("Got HTTP %s response to %s", statusCode, payload.Webhook.Request.URL)
		} else {
			errorMessage = "Webhook request failed"
		}
	}

	twilioError := &TwilioError{
		CallSid:      callSid,
		AccountSid:   formData.Get("AccountSid"),
		ErrorCode:    errorCode,
		ErrorMessage: errorMessage,
		Level:        formData.Get("Level"), // Capture Twilio's ERROR/Warning classification
		From:         from,
		To:           to,
		Direction:    direction,
		Status:       status,
		Timestamp:    timestamp,
		Duration:     0, // Not available in debugger webhooks
		URL:          payload.Webhook.Request.URL,
		RawPayload:   rawPayload,
	}

	return twilioError, nil
}

// parseDirectErrorWebhook handles direct Twilio error webhook format
func parseDirectErrorWebhook(formData url.Values) (*TwilioError, error) {
	errorCode, err := strconv.Atoi(formData.Get("ErrorCode"))
	if err != nil {
		return nil, fmt.Errorf("invalid error code: %w", err)
	}

	// Parse duration if present
	var duration int
	if durationStr := formData.Get("CallDuration"); durationStr != "" {
		duration, _ = strconv.Atoi(durationStr)
	}

	// Create raw payload map for debugging
	rawPayload := make(map[string]string)
	for key, values := range formData {
		if len(values) > 0 {
			rawPayload[key] = values[0]
		}
	}

	twilioError := &TwilioError{
		CallSid:      formData.Get("CallSid"),
		AccountSid:   formData.Get("AccountSid"),
		ErrorCode:    errorCode,
		ErrorMessage: formData.Get("ErrorMessage"),
		Level:        formData.Get("Level"), // Capture Twilio's ERROR/Warning classification
		From:         formData.Get("From"),
		To:           formData.Get("To"),
		Direction:    formData.Get("Direction"),
		Status:       formData.Get("CallStatus"),
		Timestamp:    time.Now(),
		Duration:     duration,
		URL:          formData.Get("Url"),
		RawPayload:   rawPayload,
	}

	return twilioError, nil
}
