package main

import (
	"context"
	"encoding/base64"
	"fmt"
	"log"
	"net/url"
	"strings"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"

	"twilio-monitor/handlers"
	"twilio-monitor/twilio"
)

func handler(ctx context.Context, event events.LambdaFunctionURLRequest) (events.LambdaFunctionURLResponse, error) {
	log.Printf("Received webhook: Method=%s, Headers=%+v", event.RequestContext.HTTP.Method, event.Headers)
	log.Printf("IsBase64Encoded: %t", event.IsBase64Encoded)

	// Only accept POST requests
	if event.RequestContext.HTTP.Method != "POST" {
		return events.LambdaFunctionURLResponse{
			StatusCode: 405,
			Body:       "Method not allowed",
			Headers: map[string]string{
				"Content-Type": "text/plain",
			},
		}, nil
	}

	// Handle potential base64 encoding
	body := event.Body
	if event.IsBase64Encoded {
		log.Printf("Body is base64 encoded, decoding...")
		decodedBytes, err := base64.StdEncoding.DecodeString(event.Body)
		if err != nil {
			log.Printf("Error decoding base64 body: %v", err)
			return events.LambdaFunctionURLResponse{
				StatusCode: 400,
				Body:       "Error decoding body",
				Headers: map[string]string{
					"Content-Type": "text/plain",
				},
			}, nil
		}
		body = string(decodedBytes)
		log.Printf("Decoded body: %s", body)
	}

	// Parse form data from request body
	formData, err := url.ParseQuery(body)
	if err != nil {
		log.Printf("Error parsing form data: %v", err)
		log.Printf("Body that failed to parse: %s", body)
		return events.LambdaFunctionURLResponse{
			StatusCode: 400,
			Body:       "Invalid form data",
			Headers: map[string]string{
				"Content-Type": "text/plain",
			},
		}, nil
	}

	// Validate that this looks like a Twilio error webhook
	if !isValidTwilioErrorWebhook(formData) {
		log.Printf("Invalid Twilio webhook data: %+v", formData)
		return events.LambdaFunctionURLResponse{
			StatusCode: 400,
			Body:       "Invalid Twilio webhook data",
			Headers: map[string]string{
				"Content-Type": "text/plain",
			},
		}, nil
	}

	// Parse Twilio error data
	twilioError, err := twilio.ParseTwilioWebhook(formData)
	if err != nil {
		log.Printf("Error parsing Twilio webhook: %v", err)
		return events.LambdaFunctionURLResponse{
			StatusCode: 400,
			Body:       fmt.Sprintf("Error parsing webhook: %v", err),
			Headers: map[string]string{
				"Content-Type": "text/plain",
			},
		}, nil
	}

	log.Printf("Parsed Twilio error: CallSid=%s, ErrorCode=%d, ErrorMessage=%s, Severity=%s",
		twilioError.CallSid, twilioError.ErrorCode, twilioError.ErrorMessage, twilioError.Severity())

	// Create Sentry alert handler
	alertHandler := handlers.NewSentryHandler()

	// Send the alert
	if err := alertHandler.SendAlert(ctx, twilioError); err != nil {
		log.Printf("Error sending alert: %v", err)
		return events.LambdaFunctionURLResponse{
			StatusCode: 500,
			Body:       fmt.Sprintf("Error sending alert: %v", err),
			Headers: map[string]string{
				"Content-Type": "text/plain",
			},
		}, nil
	}

	log.Printf("Successfully processed Twilio error webhook for CallSid=%s", twilioError.CallSid)

	// Return success response
	return events.LambdaFunctionURLResponse{
		StatusCode: 200,
		Body:       "OK",
		Headers: map[string]string{
			"Content-Type": "text/plain",
		},
	}, nil
}

// isValidTwilioErrorWebhook performs basic validation to ensure this is a Twilio error webhook
func isValidTwilioErrorWebhook(formData url.Values) bool {
	// Check for Twilio Debugger webhook format (has Payload field)
	payload := formData.Get("Payload")
	if payload != "" {
		// Must have AccountSid and Level=ERROR or WARNING for debugger webhooks
		accountSid := formData.Get("AccountSid")
		level := formData.Get("Level")

		if accountSid == "" || (level != "ERROR" && level != "WARNING") {
			return false
		}

		// Validate AccountSid format (starts with AC)
		if !strings.HasPrefix(accountSid, "AC") {
			return false
		}

		return true
	}

	// Fallback to direct error webhook validation
	// Must have ErrorCode and ErrorMessage
	errorCode := formData.Get("ErrorCode")
	errorMessage := formData.Get("ErrorMessage")

	if errorCode == "" || errorMessage == "" {
		return false
	}

	// Optional: Check for AccountSid format (starts with AC)
	accountSid := formData.Get("AccountSid")
	if accountSid != "" && !strings.HasPrefix(accountSid, "AC") {
		return false
	}

	// Optional: Check for CallSid format (starts with CA)
	callSid := formData.Get("CallSid")
	if callSid != "" && !strings.HasPrefix(callSid, "CA") {
		return false
	}

	return true
}

func main() {
	lambda.Start(handler)
}
