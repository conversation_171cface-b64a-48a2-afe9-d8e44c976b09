import * as cdk from 'aws-cdk-lib';
import * as ssm from 'aws-cdk-lib/aws-ssm';
import { Construct } from 'constructs';
import { AppConfig } from '../../cloud-config/config';

export interface SSMParametersStackProps extends cdk.StackProps {
    config: AppConfig;
    userPoolId: string;
    internalUserPoolClientId: string;
    internalIdentityPoolId: string;
}

export class SSMParametersStack extends cdk.Stack {
    constructor(scope: Construct, id: string, props: SSMParametersStackProps) {
        super(scope, id, props);

        const { config, userPoolId, internalUserPoolClientId, internalIdentityPoolId } = props;

        const envName = config.environment.envName;

        // Create SSM parameters in the global region
        new ssm.StringParameter(this, 'UserPoolIdParameter', {
            parameterName: `/hero/${envName}/cognito/user-pool-id`,
            stringValue: userPoolId,
        });

        new ssm.StringParameter(this, 'InternalUserPoolClientIdParameter', {
            parameterName: `/hero/${envName}/cognito/internal-user-pool-client-id`,
            stringValue: internalUserPoolClientId,
        });

        new ssm.StringParameter(this, 'InternalIdentityPoolIdParameter', {
            parameterName: `/hero/${envName}/cognito/internal-identity-pool-id`,
            stringValue: internalIdentityPoolId,
        });
    }
} 