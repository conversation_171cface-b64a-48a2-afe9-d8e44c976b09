import * as cdk from 'aws-cdk-lib';
import * as secretsmanager from 'aws-cdk-lib/aws-secretsmanager';
import { Construct } from 'constructs';
import { AppConfig } from '../../cloud-config/config';

export interface PasswordStackProps extends cdk.StackProps {
    config: AppConfig;
}

export class PasswordStack extends cdk.Stack {
    public readonly rootAdminPasswordSecret: secretsmanager.ISecret;

    constructor(scope: Construct, id: string, props: PasswordStackProps) {
        super(scope, id, props);

        this.rootAdminPasswordSecret = new secretsmanager.Secret(this, 'RootAdminPassword', {
            secretName: `root-admin-password`,
            generateSecretString: {
                secretStringTemplate: JSON.stringify({ username: '<EMAIL>' }),
                generateStringKey: 'password',
                passwordLength: 32,
                excludePunctuation: false,
            },
        });
    }
} 