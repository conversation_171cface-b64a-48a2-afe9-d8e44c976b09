# Hero AWS Infrastructure Source Map

## Overview
This document provides a comprehensive mapping of all AWS infrastructure stacks, services, and components in the Hero ecosystem. The infrastructure is built using AWS CDK (Cloud Development Kit) with TypeScript.

## Infrastructure Architecture

### Deployment Structure
- **Shared Services Account**: `************` (us-west-2)
- **Demo Environment**: `************` (us-west-2) - demo-1.gethero.com
- **Production Environment**: `************` (us-east-2) - tcu.gethero.com

## Core Infrastructure Stacks

### 1. Entry Stacks
- **Main Entry Stack**: `infra/cloud/entry-stack.ts`
  - Orchestrates all environment-specific infrastructure
  - Stack Name: `CDKEntryStack-{envName}`
  
- **Shared Entry Stack**: `infra/cloud-shared/entry-stack.ts`
  - Manages shared resources across environments
  - Stack Name: `CDKSharedEntryStack`

### 2. Shared Infrastructure Stacks

#### VPC & Networking
- **VPCStack**: `infra/cloud/shared/vpc-stack.ts`
  - Creates VPC with public/private subnets
  - ECS cluster for Fargate services
  - NAT gateways and internet gateways

#### Security & Authentication
- **CognitoStack**: `infra/cloud/shared/cognito-stack/cognito-stack.ts`
  - User pools and identity providers
  - SAML integration with Okta
  - Post-confirmation and pre-signup Lambda triggers

- **GlobalCertsStack**: `infra/cloud/shared/global-certs-stack.ts`
  - SSL certificates for CloudFront (us-east-1)
  - Route53 hosted zones

- **LocalCertsStack**: `infra/cloud/shared/local-certs-stack.ts`
  - Regional SSL certificates
  - Wildcard certificates for API domains

#### Database Infrastructure
- **PostgresRdsStack**: `infra/cloud/shared/db-stack.ts`
  - Two separate RDS instances:
    - Hero database (main application data)
    - Perms database (permissions/authorization)
  - PostgreSQL 17 with encryption and backups

#### Storage & Messaging
- **S3Stack**: `infra/cloud/s3bucket/s3-stack.ts`
  - File repository buckets
  - Camera stream storage
  - Website hosting buckets

- **SnsStack**: `infra/cloud/shared/sns/sns-stack.ts`
  - Camera stream object detection alerts
  - Event-driven architecture messaging

#### Utilities & Management
- **BastionStack**: `infra/cloud/shared/bastion-stack.ts`
  - EC2 bastion host for secure database access

- **PasswordStack**: `infra/cloud/shared/password-stack.ts`
  - Secrets Manager for database credentials

- **SSMParametersStack**: `infra/cloud/shared/ssm-parameters-stack.ts`
  - System configuration parameters

### 3. Application Stacks

#### Frontend Applications
- **ReactFrontendCloudFrontStack**: `infra/cloud/apps/react-frontend-stack.ts`
  - CloudFront distribution for Command & Control web app
  - Lambda@Edge authorization
  - S3 bucket deployment
  - Stack Name: `CommandUIStack`

#### Microservices (Fargate)
All services use the **FargateServiceStack** pattern: `infra/cloud/servers/fargate-service-stack.ts`

**Core Services:**
1. **OrgsService** - Organization management
2. **PermsService** - Permissions and authorization  
3. **WorkflowService** - Business process workflows
4. **CommandService** - Command and control operations
5. **SensorsService** - IoT sensor data processing
6. **CommunicationsService** - Voice calls, PTT, messaging
7. **FileRepositoryService** - File storage and management
8. **FeatureFlagsService** - Feature flag management

**Service Configuration**: `infra/cloud-config/default/servers/`
- Each service has dedicated configuration files
- Auto-scaling, load balancing, and health checks
- Multiple API endpoints (Cognito, IAM, Basic Auth)

#### Specialized Services
- **OpenFgaFargateServiceStack**: `infra/cloud/openfga/openfga-stack.ts`
  - Fine-grained authorization service
  - Connected to permissions database

### 4. Lambda Functions

#### Core Lambda Stack
- **LambdasStack**: `infra/cloud/lambdas/lambdas-stack.ts`
  - Camera event processing lambdas:
    - SNS-to-S3: Video clip capture and storage
    - SNS-to-DB: Event metadata persistence  
    - SNS-to-FieldReport: Alert processing

#### Migration & Maintenance
- **MigrationRunnerStack**: `infra/cloud/lambdas/migration-runner-stack.ts`
  - Database migration execution
  - VPC-enabled for secure database access

#### Shared Services Lambdas
- **DiscoveryApiStack**: `infra/cloud-shared/lambdas/discovery-api-stack.ts`
  - Email-to-environment discovery for mobile apps
  - Maps domains to Cognito configurations

#### Authentication Lambdas
- **PostConfirmationLambda**: Cognito user setup
- **PreSignUpLambda**: User registration validation
- **AuthorizationLambda**: CloudFront edge authorization

### 5. IoT & Edge Computing
- **GreengrassSetupStack**: `infra/cloud/shared/greengrass-stack.ts`
  - IoT Core thing groups and policies
  - Greengrass v2 token exchange roles
  - Edge device management

### 6. CI/CD & DevOps
- **GithubActionsRoleStack**: Cross-account deployment roles
- **ECRStack**: Container image repositories for each service

## Environment Configurations

### Configuration Structure
- **Base Config**: `infra/cloud-config/default/config.ts`
- **Environment Overrides**: `infra/cloud-config/envs/{env}/config.ts`

### Supported Environments
1. **demo-1**: Development/demo environment
2. **prod-1**: Production environment (TCU)
3. **shared**: Shared services account

## Deployment Scripts & Tools

### Key Scripts
- `infra/scripts/deploy-services.sh` - Service deployment
- `infra/scripts/prepare-cdk.sh` - Lambda function builds
- `infra/scripts/bootstrap.sh` - Environment bootstrapping

### CDK Entry Points
- `infra/bin/deploy.ts` - Main deployment router
- `infra/bin/deploy-env.ts` - Environment-specific deployment
- `infra/bin/deploy-shared.ts` - Shared services deployment

## Service Domains & Endpoints

### Domain Structure
Each service gets multiple API endpoints:
- `{service}.api.{domain}` - Cognito-authorized API
- `{service}.iam.api.{domain}` - IAM-authorized API  
- `{service}.basic.api.{domain}` - Basic auth API
- `{service}.lb.api.{domain}` - Load balancer (VPC internal)

### Example (demo-1 environment):
- `orgs.api.demo-1.gethero.com`
- `communications.basic.api.demo-1.gethero.com`
- `sensors.lb.api.demo-1.gethero.com`

## Resource Tagging Strategy
- `env`: Environment name
- `Service`: hero-core
- `Component`: Service-specific component name

## Security & Access Control
- VPC isolation with private subnets
- Security groups for service communication
- IAM roles with least-privilege access
- Secrets Manager for sensitive configuration
- KMS encryption for data at rest
- SSL/TLS for data in transit

## Monitoring & Observability
- CloudWatch logs for all services
- Sentry integration for error tracking
- Health check endpoints for all services
- Auto-scaling based on CPU utilization
