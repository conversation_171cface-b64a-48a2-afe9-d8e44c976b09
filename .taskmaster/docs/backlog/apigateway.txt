Overview
Our goal is to expose the Discovery Service—currently a Lambda Function URL—through a predictable, secure hostname discovery.api.<rootDomain>, backed by an HTTP API Gateway, Route 53 A-record, and the existing *.api.<rootDomain> ACM wildcard certificate.

Problem solved Mobile apps must decide which Cognito User Pool to direct a user to, but today they call an opaque Lambda URL protected by a hard-coded API key.

Target users iOS/Android clients (Hero Responder App) making sign-in decisions at runtime.

Why valuable Eliminates leaked secrets, centralises rate-limiting & logging, and aligns with the API naming convention already used by Command/Org services.

Core Features
Feature	What it does	Why it’s important	High-level mechanics
1. Discovery API (POST / on discovery.api.*)`	Returns { userPoolUrl, clientId } for a given email domain.	Removes secret key, gives predictable host, enables WAF & CloudWatch metrics.	API Gateway (HTTP API) → Lambda proxy (shared-discovery-lambda in infra/cloud-shared/lambdas).
2. Health probe (GET /health)	Zero-logic 200 OK for uptime checks.	Allows external monitors without incurring Lambda cost.	API Gateway mock integration, no authorizer.
3. Rate-limiting / WAF	Caps abusive clients (e.g. 200 req/5 min/IP).	Prevents DoS & scrapers; no key required.	WAFv2 WebACL attached to HTTP API stage.
4. Observability & logs	Per-route access logs in CloudWatch.	Faster debugging & anomaly detection.	CfnStage.accessLogSettings similar to Fargate stack.
5. Re-usable CDK construct	Re-uses wildcard ACM and Route 53 pattern from Fargate stack.	Keeps infra consistent; one cert to manage.	Code added to infra/cloud-shared/lambdas/lambdas-stack.ts.

User Experience
Personas
Mobile end-user – Just wants to sign in; discovery call must be invisible and < 200 ms.

Mobile engineer – Needs a single constant DISCOVERY_URL and no secrets.

Infra/Ops – Wants CloudWatch alarms, WAF rate-limit, and quick rollback.

Key Flows
App launch → discovery POST with { email }.

App receives { userPoolUrl, clientId } → opens Cognito Hosted UI.

On success, tokens used for downstream *.api calls (unchanged).

UI / UX Considerations
No UI surface change—only config code changes:

ts
Copy
Edit
export const DISCOVERY_URL = "https://discovery.api.gethero.com";
Handle 429 (rate-limit) gracefully with retry/backoff toast.

</context> <PRD>
Technical Architecture
System components

HTTP API Gateway (regional)

Lambda: shared-discovery-lambda (Go 1.22, same binary)

WAFv2 WebACL (single shared instance; rate-limit rule only)

Route 53 A-record → API custom domain

ACM wildcard cert: LocalCertsStack.wildcardApiCert (*.api.<rootDomain>)

Data model – unchanged:

json5
Copy
Edit
// discovery request
{ "email": "<EMAIL>" }

// discovery response
{ "userPoolUrl": "...", "clientId": "..." }
API surface

Method	Path	Auth	Body	Response
POST	/	None	{ email }	200 + discovery JSON
GET	/health	None	–	200 {}

Infrastructure requirements

CDK additions in infra/cloud-shared/lambdas/lambdas-stack.ts.

Props passed from entry stack: rootDomain, wildcardApiCert, hostedZone.

WAF association (CfnWebACLAssociation).

Development Roadmap
Phase 0 – Scaffold
Import wildcard cert & hosted zone into LambdasStack props.

Pull shared-discovery-lambda code artefact path via existing build pipeline.

Phase 1 – MVP (usable)
Create HTTP API with default Lambda integration (POST /).

Add custom domain discovery.api.<rootDomain> using wildcard cert.

Route 53 A-record for hostname.

Mock /health route.

CloudWatch access log group.

Mobile patch: switch to new URL, keep old Function URL as fallback. We should eliminate the need for the function URL. 

Phase 2 – Hardening
Create WAFv2 WebACL with IP rate-limit rule, associate to API stage.

Alarm on 5xx, high latency, WAF blocks.

Remove Function URL + Secrets Manager API key; delete mobile fallback.

Phase 3 – Enhancements
Usage-plan + optional API key distributed via Remote-Config (if abuse persists).

Add JSON Schema validation in Lambda for stricter input.

Add Prometheus metrics export via CloudWatch EMF.

Logical Dependency Chain
Wildcard cert & hosted zone exist (already in LocalCertsStack).

HTTP API + custom domain → must exist before DNS record.

Route 53 A-record depends on custom domain’s regional ALIAS targets.

WAF association attaches after API exists.

Mobile build upgrades only after endpoint live.

Cleanup (Function URL deletion).

Appendix
Reference code

Existing load-balancer API pattern: infra/cloud/servers/fargate-service-stack.ts

Discovery Lambda path: infra/cloud-shared/lambdas/discovery-lambda/main.go

Proposed edits live in: infra/cloud-shared/lambdas/lambdas-stack.ts

ACM wildcard cert ARN printed by LocalCertsStack at deploy-time.