# Task ID: 14
# Title: Configure Lambda function artifact path integration
# Status: pending
# Dependencies: 13
# Priority: high
# Description: Set up the shared-discovery-lambda Go binary artifact path using existing build pipeline integration
# Details:
Reference existing Lambda function creation pattern in lambdas-stack.ts. Use aws-cdk-lib/aws-lambda Function construct with Go 1.22 runtime (Runtime.GO_1_X). Set code path to infra/cloud-shared/lambdas/discovery-lambda/main.go compiled binary. Configure handler as 'main', timeout 10 seconds, memory 256MB. Environment variables: LOG_LEVEL=INFO. Use existing build pipeline artifact resolution pattern from other Lambda functions in the stack.

# Test Strategy:
Verify Lambda function deploys successfully with correct Go runtime and handler. Test function invocation locally using AWS CLI or CDK testing utilities.

# Subtasks:
## 1. Review Existing Lambda Function Integration Patterns [pending]
### Dependencies: None
### Description: Analyze the current Lambda function creation and artifact resolution patterns in lambdas-stack.ts to ensure consistency with existing build pipeline integration.
### Details:
Identify how other Lambda functions in the stack resolve their Go binary artifact paths and how the build pipeline outputs are referenced in the CDK stack.

## 2. Compile shared-discovery-lambda Go Binary [pending]
### Dependencies: 14.1
### Description: Set up the build process to compile infra/cloud-shared/lambdas/discovery-lambda/main.go into a Go binary suitable for Lambda deployment.
### Details:
Ensure the build pipeline produces the Go binary in the expected output directory, matching the artifact resolution pattern used by other Lambda functions.

## 3. Integrate Compiled Artifact Path in CDK Stack [pending]
### Dependencies: 14.2
### Description: Update lambdas-stack.ts to reference the compiled shared-discovery-lambda Go binary artifact path using the aws-cdk-lib/aws-lambda Function construct.
### Details:
Set the code property to the compiled binary path, configure the handler as 'main', runtime as Go 1.22 (Runtime.GO_1_X), timeout to 10 seconds, memory to 256MB, and environment variable LOG_LEVEL=INFO.

## 4. Validate Build Pipeline Artifact Resolution [pending]
### Dependencies: 14.3
### Description: Ensure the build pipeline correctly resolves and provides the Lambda artifact for deployment, following the established pattern for other Lambda functions.
### Details:
Check that the artifact path used in the CDK stack matches the output from the build pipeline and that any required pipeline steps are present.

## 5. Test Lambda Deployment and Invocation [pending]
### Dependencies: 14.4
### Description: Verify the deployed Lambda function operates as expected with the integrated artifact path and configuration.
### Details:
Test deployment success, correct runtime and handler, environment variable presence, and function invocation using AWS CLI or CDK testing utilities.

