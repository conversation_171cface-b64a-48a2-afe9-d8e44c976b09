# Task ID: 20
# Title: Create WAFv2 WebACL with rate limiting rules
# Status: pending
# Dependencies: 19
# Priority: high
# Description: Implement WAFv2 WebACL with IP-based rate limiting to prevent abuse and DoS attacks
# Details:
Create CfnWebACL using aws-cdk-lib/aws-wafv2. Configure scope: 'REGIONAL', rules: [{ name: 'RateLimitRule', priority: 1, statement: { rateBasedStatement: { limit: 200, aggregateKeyType: 'IP' } }, action: { block: {} } }]. Set defaultAction: { allow: {} }. Add CloudWatch metrics enabled: true. Use 5-minute evaluation window (300 seconds) matching requirement of 200 req/5 min/IP. Consider adding geo-blocking rule if needed for security.

# Test Strategy:
Test rate limiting by sending >200 requests from single IP within 5 minutes and verify 429 responses. Monitor WAF metrics in CloudWatch. Verify legitimate traffic under limit passes through successfully.

# Subtasks:
## 1. Define Rate Limiting Requirements and Patterns [pending]
### Dependencies: None
### Description: Specify the IP-based rate limiting threshold (200 requests per 5 minutes per IP) and determine if any URL path or resource-specific regex patterns are needed for targeted protection.
### Details:
Review application endpoints and security requirements. Decide if rate limiting should apply globally or to specific API paths using regex pattern sets. Document the required evaluation window and request limit.

## 2. Implement WAFv2 WebACL with Rate-Based Rule in AWS CDK [pending]
### Dependencies: 20.1
### Description: Create a WAFv2 WebACL using aws-cdk-lib/aws-wafv2, configuring a rate-based rule with IP aggregation and a 5-minute (300 seconds) evaluation window.
### Details:
Use the CfnWebACL construct to define the WebACL. Set scope to 'REGIONAL', add a rule named 'RateLimitRule' with priority 1, and configure the rateBasedStatement with limit: 200 and aggregateKeyType: 'IP'. Set defaultAction to allow.

## 3. Enable CloudWatch Metrics and Logging for WebACL [pending]
### Dependencies: 20.2
### Description: Configure the WebACL to enable CloudWatch metrics and logging for monitoring rule matches and rate limiting events.
### Details:
Set CloudWatchMetricsEnabled to true in the WebACL configuration. Optionally, configure logging destinations for detailed request logs.

## 4. Associate WebACL with Target AWS Resource [pending]
### Dependencies: 20.3
### Description: Attach the configured WebACL to the appropriate AWS resource (e.g., API Gateway or Application Load Balancer) to enforce rate limiting.
### Details:
Use the AWS CDK or AWS Console to associate the WebACL with the intended resource. Ensure the association is active and covers all relevant endpoints.

## 5. Test and Validate Rate Limiting and Optional Geo-Blocking [pending]
### Dependencies: 20.4
### Description: Perform functional testing to ensure the rate limiting rule blocks requests exceeding the threshold and consider adding a geo-blocking rule if required.
### Details:
Simulate traffic exceeding 200 requests per 5 minutes from a single IP and verify 429 responses. If geo-blocking is needed, add a rule to restrict traffic from specific countries.

