# Task ID: 21
# Title: Associate WAF WebACL with API Gateway stage
# Status: pending
# Dependencies: 20
# Priority: high
# Description: Link the WAFv2 WebACL to the HTTP API Gateway stage for traffic filtering
# Details:
Create CfnWebACLAssociation using aws-cdk-lib/aws-wafv2. Configure resourceArn: httpApi.defaultStage.stageArn, webAclArn: webAcl.attrArn. Ensure association is created after both API Gateway stage and WebACL exist. Add dependency using addDependsOn() if needed. This enables WAF filtering on all API requests before they reach Lambda.

# Test Strategy:
Verify WAF association is active by checking AWS console or CLI. Test that rate-limited requests return 403/429 status codes. Confirm WAF metrics appear in CloudWatch for blocked and allowed requests.

# Subtasks:
## 1. Retrieve ARNs for API Gateway Stage and WAFv2 WebACL [pending]
### Dependencies: None
### Description: Obtain the Amazon Resource Names (ARNs) for both the API Gateway stage and the WAFv2 WebACL to be associated.
### Details:
Use CDK constructs or CloudFormation outputs to get httpApi.defaultStage.stageArn and webAcl.attrArn as required for association.

## 2. Create CfnWebACLAssociation Resource in CDK [pending]
### Dependencies: 21.1
### Description: Define a new aws_wafv2.CfnWebACLAssociation resource in the AWS CDK stack to link the WebACL to the API Gateway stage.
### Details:
Configure the CfnWebACLAssociation with resourceArn set to the API Gateway stage ARN and webAclArn set to the WebACL ARN.

## 3. Ensure Proper Resource Dependencies [pending]
### Dependencies: 21.2
### Description: Guarantee that the WebACL association is created only after both the API Gateway stage and the WebACL exist.
### Details:
Use addDependsOn() or CDK dependency constructs to explicitly set dependencies between the association, API Gateway stage, and WebACL resources.

## 4. Deploy and Validate WAF Association [pending]
### Dependencies: 21.3
### Description: Deploy the CDK stack and confirm that the WAFv2 WebACL is successfully associated with the API Gateway stage.
### Details:
Deploy using CDK CLI. After deployment, verify the association in the AWS Console or via AWS CLI using 'get-web-acl-for-resource'.

## 5. Test WAF Filtering on API Gateway [pending]
### Dependencies: 21.4
### Description: Test that the WAFv2 WebACL is actively filtering requests to the API Gateway stage as intended.
### Details:
Send requests that should be blocked (e.g., exceeding rate limits) and verify 403/429 responses. Confirm allowed requests pass through. Monitor CloudWatch for WAF metrics.

