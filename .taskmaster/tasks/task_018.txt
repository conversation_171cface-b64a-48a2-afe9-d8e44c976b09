# Task ID: 18
# Title: Create Route 53 A-record for custom domain
# Status: pending
# Dependencies: 17
# Priority: high
# Description: Add Route 53 A-record pointing discovery.api.<rootDomain> to API Gateway custom domain
# Details:
Use ARecord construct from aws-cdk-lib/aws-route53. Configure recordName: 'discovery.api', zone: props.hostedZone, target: RecordTarget.fromAlias(new ApiGatewayv2DomainProperties(customDomain.regionalDomainName, customDomain.regionalHostedZoneId)). Import ApiGatewayv2DomainProperties from aws-cdk-lib/aws-route53-targets. Set TTL to 300 seconds for faster DNS propagation during deployments.

# Test Strategy:
Verify DNS record is created and propagates correctly using dig or nslookup. Test domain resolution from multiple geographic locations. Validate A-record points to correct API Gateway regional endpoint.

# Subtasks:
## 1. Import Required CDK Constructs and Properties [pending]
### Dependencies: None
### Description: Import the ARecord construct from aws-cdk-lib/aws-route53 and ApiGatewayv2DomainProperties from aws-cdk-lib/aws-route53-targets for use in the stack.
### Details:
Ensure the CDK stack file includes imports for ARecord, RecordTarget, and ApiGatewayv2DomainProperties to enable creation of the Route 53 alias record targeting the API Gateway custom domain.

## 2. Reference Hosted Zone and API Gateway Custom Domain [pending]
### Dependencies: 18.1
### Description: Retrieve or reference the existing Route 53 hosted zone and the API Gateway custom domain properties (regionalDomainName and regionalHostedZoneId).
### Details:
Use HostedZone.fromLookup or similar method to get the hosted zone object. Ensure the customDomain object exposes regionalDomainName and regionalHostedZoneId for use in the ARecord target.

## 3. Create Route 53 A-Record for discovery.api Subdomain [pending]
### Dependencies: 18.2
### Description: Define a new ARecord in Route 53 with recordName 'discovery.api', pointing to the API Gateway custom domain using RecordTarget.fromAlias and ApiGatewayv2DomainProperties.
### Details:
Instantiate the ARecord construct with the specified recordName, zone, and target. Use RecordTarget.fromAlias(new ApiGatewayv2DomainProperties(customDomain.regionalDomainName, customDomain.regionalHostedZoneId)).

## 4. Configure TTL for DNS Propagation [pending]
### Dependencies: 18.3
### Description: Set the TTL (time-to-live) for the A-record to 300 seconds to enable faster DNS propagation during deployments.
### Details:
Specify the ttl property as Duration.seconds(300) or equivalent in the ARecord construct configuration.

## 5. Validate DNS Record Creation and Propagation [pending]
### Dependencies: 18.4
### Description: Test that the discovery.api.<rootDomain> A-record resolves to the correct API Gateway endpoint from multiple locations and propagates as expected.
### Details:
Use tools like dig or nslookup to query the DNS record after deployment. Confirm that the record resolves to the API Gateway regional endpoint and that propagation occurs within the expected TTL window.

