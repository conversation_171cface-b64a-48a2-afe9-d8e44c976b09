# Task ID: 23
# Title: Update mobile app configuration for new endpoint
# Status: pending
# Dependencies: 22
# Priority: high
# Description: Modify mobile app configuration to use new discovery.api.<rootDomain> endpoint while maintaining Function URL fallback
# Details:
Update mobile app constants: export const DISCOVERY_URL = 'https://discovery.api.gethero.com'; Implement fallback logic: try new endpoint first, on failure (network error, 5xx) retry with old Function URL. Add exponential backoff for 429 responses: initial delay 1s, max delay 30s, max retries 3. Use fetch API with timeout: 10 seconds. Handle errors gracefully with user-friendly messages. Consider feature flag for gradual rollout.

# Test Strategy:
Test mobile app with new endpoint in staging environment. Verify fallback works when new endpoint is unavailable. Test rate limiting handling with proper retry behavior. Validate error handling and user experience.

# Subtasks:
## 1. Update Mobile App Constants for New Endpoint [pending]
### Dependencies: None
### Description: Modify the mobile app configuration to set DISCOVERY_URL to the new endpoint (https://discovery.api.<rootDomain>).
### Details:
Update the relevant constants or configuration files in the codebase to reference the new discovery API endpoint as the primary URL.

## 2. Implement Endpoint Fallback Logic [pending]
### Dependencies: 23.1
### Description: Add logic to attempt the new endpoint first and, on network error or 5xx response, retry with the old Function URL.
### Details:
Use fetch API to try the new endpoint; if a network error or server error (5xx) occurs, automatically retry the request using the previous Function URL as a fallback.

## 3. Add Exponential Backoff for 429 Responses [pending]
### Dependencies: 23.2
### Description: Implement exponential backoff logic for handling HTTP 429 (Too Many Requests) responses, starting with a 1s delay, doubling up to 30s, with a maximum of 3 retries.
### Details:
Detect 429 responses and apply exponential backoff: initial delay 1 second, double each retry, cap at 30 seconds, and stop after 3 attempts.

## 4. Integrate Fetch API Timeout and Error Handling [pending]
### Dependencies: 23.3
### Description: Ensure all fetch requests use a 10-second timeout and handle errors gracefully with user-friendly messages.
### Details:
Wrap fetch calls with timeout logic; display clear, actionable error messages to users for timeouts, network errors, and other failures.

## 5. Implement Feature Flag for Gradual Rollout [pending]
### Dependencies: 23.4
### Description: Add a feature flag to control rollout of the new endpoint configuration, allowing gradual enablement for users.
### Details:
Integrate a feature flag system to toggle the use of the new endpoint, enabling staged deployment and rollback if needed.

