# Task ID: 25
# Title: Remove Function URL and cleanup legacy resources
# Status: pending
# Dependencies: None
# Priority: medium
# Description: Eliminate the old Lambda Function URL and associated Secrets Manager API key after successful migration
# Details:
Remove Function URL configuration from shared-discovery-lambda. Delete associated Secrets Manager secret containing API key. Update Lambda function to remove API key validation logic if present. Remove mobile app fallback code after confirming new endpoint stability. Update documentation and deployment scripts. Consider gradual rollout: disable Function URL creation for new deployments first, then remove from existing deployments.

# Test Strategy:
Verify Function URL is no longer accessible and returns 404/403. Confirm Secrets Manager secret is deleted. Test mobile app works without fallback code. Monitor error rates during transition period.

# Subtasks:
## 1. Remove Lambda Function URL configuration [pending]
### Dependencies: None
### Description: Delete the Function URL configuration from the shared-discovery-lambda to eliminate the public HTTP endpoint.
### Details:
Use the AWS Lambda console or AWS CLI 'delete-function-url-config' command to remove the Function URL from the Lambda function. Confirm that the function is no longer accessible via its previous URL.

## 2. Delete associated Secrets Manager API key [pending]
### Dependencies: 25.1
### Description: Remove the API key stored in AWS Secrets Manager that was used for the legacy Function URL.
### Details:
Identify the secret associated with the Lambda function's API key in AWS Secrets Manager and delete it. Ensure no other resources depend on this secret before deletion.

## 3. Update Lambda function to remove API key validation logic [pending]
### Dependencies: 25.2
### Description: Modify the Lambda function code to eliminate any logic that validates or requires the legacy API key.
### Details:
Review the Lambda function codebase for API key validation checks and remove them. Deploy the updated function and ensure it operates correctly without the API key.

## 4. Remove mobile app fallback code and confirm new endpoint stability [pending]
### Dependencies: 25.3
### Description: Delete any fallback logic in the mobile app that references the old Function URL, after verifying the new endpoint is stable.
### Details:
Coordinate with mobile app developers to remove fallback code. Monitor the new endpoint for stability before and after the change.

## 5. Update documentation and deployment scripts [pending]
### Dependencies: 25.4
### Description: Revise all relevant documentation and deployment scripts to reflect the removal of the Function URL and legacy API key.
### Details:
Update internal and external documentation, deployment guides, and infrastructure-as-code scripts to remove references to the Function URL and API key. Communicate changes to stakeholders.

