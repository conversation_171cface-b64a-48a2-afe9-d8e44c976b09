# Task ID: 13
# Title: Import wildcard certificate and hosted zone into LambdasStack
# Status: pending
# Dependencies: None
# Priority: high
# Description: Modify LambdasStack constructor to accept rootDomain, wildcardApiCert, and hostedZone props from the entry stack
# Details:
Update infra/cloud-shared/lambdas/lambdas-stack.ts constructor to accept props: { rootDomain: string, wildcardApiCert: ICertificate, hostedZone: IHostedZone }. Reference existing LocalCertsStack.wildcardApiCert pattern from fargate-service-stack.ts. Use AWS CDK v2 latest stable (2.110.x+) with @aws-cdk/aws-apigatewayv2-alpha for HTTP API support. Import statements: import { HttpApi, HttpMethod, HttpRoute } from '@aws-cdk/aws-apigatewayv2-alpha'; import { HttpLambdaIntegration } from '@aws-cdk/aws-apigatewayv2-integrations-alpha';

# Test Strategy:
Verify props are correctly passed from entry stack and accessible in LambdasStack. Test CDK synth to ensure no compilation errors and props are properly typed.

# Subtasks:
## 1. Update LambdasStack constructor to accept new props [pending]
### Dependencies: None
### Description: Modify the constructor of LambdasStack in infra/cloud-shared/lambdas/lambdas-stack.ts to accept rootDomain (string), wildcardApiCert (ICertificate), and hostedZone (IHostedZone) as props.
### Details:
Edit the TypeScript interface for LambdasStack props and update the constructor signature to include the new properties. Ensure type safety and compatibility with AWS CDK v2.

## 2. Reference wildcard certificate and hosted zone from entry stack [pending]
### Dependencies: 13.1
### Description: Update the entry stack to pass the wildcardApiCert and hostedZone resources to LambdasStack, following the pattern used in fargate-service-stack.ts for LocalCertsStack.wildcardApiCert.
### Details:
Locate the entry stack where LambdasStack is instantiated. Import or reference the existing ACM certificate and Route53 hosted zone, and pass them as props to LambdasStack.

## 3. Update import statements for API Gateway v2 alpha modules [pending]
### Dependencies: 13.1
### Description: Ensure lambdas-stack.ts imports the required modules for HTTP API and Lambda integration from @aws-cdk/aws-apigatewayv2-alpha and @aws-cdk/aws-apigatewayv2-integrations-alpha.
### Details:
Add or update import statements for HttpApi, HttpMethod, HttpRoute, and HttpLambdaIntegration as specified in the implementation details.

## 4. Refactor LambdasStack resource definitions to use new props [pending]
### Dependencies: 13.1, 13.2
### Description: Update all resource definitions within LambdasStack that require rootDomain, wildcardApiCert, or hostedZone to use the new props instead of hardcoded or locally defined values.
### Details:
Replace any direct references to certificates, domains, or hosted zones with references to the corresponding props. Ensure all constructs (e.g., API Gateway custom domains) use the injected resources.

## 5. Validate stack integration and deployment [pending]
### Dependencies: 13.4
### Description: Test the integration by synthesizing and deploying the updated LambdasStack to ensure all resources are created with the correct certificate and hosted zone.
### Details:
Perform a full CDK synth and deploy in a test environment. Check that the stack deploys without errors and that the resources reference the correct ACM certificate and Route53 hosted zone.

