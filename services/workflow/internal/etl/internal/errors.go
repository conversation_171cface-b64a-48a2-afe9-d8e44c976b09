package internal

import "errors"

// ETL Service Error Codes - following the established pattern in the workflow services

// Job Management Errors
var (
	ErrJobNotFound           = errors.New("ETL job not found")
	ErrJobAlreadyCompleted   = errors.New("ETL job already completed")
	ErrJobCancelled          = errors.New("ETL job was cancelled")
	ErrJobMaxRetriesExceeded = errors.New("ETL job exceeded maximum retry attempts")
	ErrJobInvalidStatus      = errors.New("ETL job has invalid status for this operation")
)

// Data Extraction Errors
var (
	ErrReportNotFound       = errors.New("report not found for ETL processing")
	ErrReportInvalidData    = errors.New("report contains invalid data for ETL processing")
	ErrReportExtractionFail = errors.New("failed to extract report data")
	ErrEntityNotFound       = errors.New("related entity not found")
	ErrSituationNotFound    = errors.New("related situation not found")
	ErrAssetNotFound        = errors.New("related asset not found")
)

// Data Transformation Errors
var (
	ErrTransformationFailed    = errors.New("data transformation failed")
	ErrTemplateNotFound        = errors.New("transformation template not found")
	ErrTemplateInvalid         = errors.New("transformation template is invalid")
	ErrValidationFailed        = errors.New("transformed data validation failed")
	ErrUnsupportedOutputFormat = errors.New("unsupported output format")
	ErrMissingRequiredField    = errors.New("missing required field for transformation")
	ErrInvalidFieldValue       = errors.New("invalid field value for transformation")
)

// Data Loading/Submission Errors
var (
	ErrAgencyNotFound     = errors.New("target agency not found")
	ErrAgencyUnavailable  = errors.New("target agency is unavailable")
	ErrSubmissionFailed   = errors.New("submission to agency failed")
	ErrInvalidCredentials = errors.New("invalid credentials for agency submission")
	ErrNetworkError       = errors.New("network error during submission")
	ErrAgencyRejected     = errors.New("submission rejected by agency")
	ErrContentTooLarge    = errors.New("generated content exceeds size limits")
)

// Configuration Errors
var (
	ErrInvalidConfiguration = errors.New("invalid ETL configuration")
	ErrMissingConfiguration = errors.New("missing required ETL configuration")
	ErrConfigurationChanged = errors.New("ETL configuration changed during processing")
)

// Orchestration Errors
var (
	ErrPipelineTimeout     = errors.New("ETL pipeline execution timeout")
	ErrResourceUnavailable = errors.New("required resource is unavailable")
	ErrConcurrencyLimit    = errors.New("ETL concurrency limit exceeded")
	ErrDependencyFailed    = errors.New("ETL dependency failed")
)

// Validation Errors
var (
	ErrInvalidRequest   = errors.New("invalid ETL request")
	ErrInvalidJobID     = errors.New("invalid ETL job ID")
	ErrInvalidAgencyID  = errors.New("invalid agency ID")
	ErrInvalidDateRange = errors.New("invalid date range for ETL processing")
	ErrEmptyReportList  = errors.New("no reports specified for ETL processing")
	ErrInvalidPageToken = errors.New("invalid pagination token")
)

// Error codes for detailed error tracking (used in JobError.ErrorCode)
const (
	// Extract phase error codes
	ErrorCodeReportNotFound          = "REPORT_NOT_FOUND"
	ErrorCodeReportInvalidData       = "REPORT_INVALID_DATA"
	ErrorCodeEntityExtractionFail    = "ENTITY_EXTRACTION_FAIL"
	ErrorCodeSituationExtractionFail = "SITUATION_EXTRACTION_FAIL"
	ErrorCodeAssetExtractionFail     = "ASSET_EXTRACTION_FAIL"
	ErrorCodeDatabaseError           = "DATABASE_ERROR"

	// Transform phase error codes
	ErrorCodeTransformationFailed = "TRANSFORMATION_FAILED"
	ErrorCodeTemplateNotFound     = "TEMPLATE_NOT_FOUND"
	ErrorCodeTemplateInvalid      = "TEMPLATE_INVALID"
	ErrorCodeValidationFailed     = "VALIDATION_FAILED"
	ErrorCodeMissingRequiredField = "MISSING_REQUIRED_FIELD"
	ErrorCodeInvalidFieldValue    = "INVALID_FIELD_VALUE"
	ErrorCodeCodeMappingFailed    = "CODE_MAPPING_FAILED"

	// Load phase error codes
	ErrorCodeAgencyNotFound     = "AGENCY_NOT_FOUND"
	ErrorCodeAgencyUnavailable  = "AGENCY_UNAVAILABLE"
	ErrorCodeSubmissionFailed   = "SUBMISSION_FAILED"
	ErrorCodeInvalidCredentials = "INVALID_CREDENTIALS" // #nosec G101 -- This is an error code constant, not a hardcoded credential
	ErrorCodeNetworkError       = "NETWORK_ERROR"
	ErrorCodeAgencyRejected     = "AGENCY_REJECTED"
	ErrorCodeContentTooLarge    = "CONTENT_TOO_LARGE"
	ErrorCodeTimeoutError       = "TIMEOUT_ERROR"

	// Configuration error codes
	ErrorCodeInvalidConfiguration = "INVALID_CONFIGURATION"
	ErrorCodeMissingConfiguration = "MISSING_CONFIGURATION"
	ErrorCodeConfigurationChanged = "CONFIGURATION_CHANGED"

	// System error codes
	ErrorCodeResourceUnavailable = "RESOURCE_UNAVAILABLE"
	ErrorCodeConcurrencyLimit    = "CONCURRENCY_LIMIT"
	ErrorCodeDependencyFailed    = "DEPENDENCY_FAILED"
	ErrorCodeUnknownError        = "UNKNOWN_ERROR"
)

// Retryable error codes - these errors allow automatic retries
var RetryableErrorCodes = map[string]bool{
	ErrorCodeNetworkError:        true,
	ErrorCodeTimeoutError:        true,
	ErrorCodeAgencyUnavailable:   true,
	ErrorCodeResourceUnavailable: true,
	ErrorCodeDatabaseError:       true,
	ErrorCodeSubmissionFailed:    true, // May be retryable depending on specific failure
}

// IsRetryableError checks if an error code allows for automatic retries
func IsRetryableError(errorCode string) bool {
	return RetryableErrorCodes[errorCode]
}

// GetErrorMessage returns a human-readable error message for an error code
func GetErrorMessage(errorCode string) string {
	switch errorCode {
	// Extract phase
	case ErrorCodeReportNotFound:
		return "The specified report could not be found"
	case ErrorCodeReportInvalidData:
		return "The report contains invalid or incomplete data"
	case ErrorCodeEntityExtractionFail:
		return "Failed to extract entity data from the report"
	case ErrorCodeSituationExtractionFail:
		return "Failed to extract situation data from the report"
	case ErrorCodeAssetExtractionFail:
		return "Failed to extract asset data from the report"
	case ErrorCodeDatabaseError:
		return "Database error occurred during processing"

	// Transform phase
	case ErrorCodeTransformationFailed:
		return "Data transformation to target format failed"
	case ErrorCodeTemplateNotFound:
		return "Transformation template not found for the target format"
	case ErrorCodeTemplateInvalid:
		return "Transformation template is invalid or corrupted"
	case ErrorCodeValidationFailed:
		return "Transformed data failed validation checks"
	case ErrorCodeMissingRequiredField:
		return "Required field is missing for the target format"
	case ErrorCodeInvalidFieldValue:
		return "Field value is invalid for the target format"
	case ErrorCodeCodeMappingFailed:
		return "Failed to map internal codes to target format codes"

	// Load phase
	case ErrorCodeAgencyNotFound:
		return "Target agency not found or not configured"
	case ErrorCodeAgencyUnavailable:
		return "Target agency is temporarily unavailable"
	case ErrorCodeSubmissionFailed:
		return "Failed to submit data to the target agency"
	case ErrorCodeInvalidCredentials:
		return "Invalid credentials for agency submission"
	case ErrorCodeNetworkError:
		return "Network error occurred during submission"
	case ErrorCodeAgencyRejected:
		return "Submission was rejected by the target agency"
	case ErrorCodeContentTooLarge:
		return "Generated content exceeds size limits"
	case ErrorCodeTimeoutError:
		return "Operation timed out"

	// Configuration
	case ErrorCodeInvalidConfiguration:
		return "ETL configuration is invalid"
	case ErrorCodeMissingConfiguration:
		return "Required ETL configuration is missing"
	case ErrorCodeConfigurationChanged:
		return "ETL configuration changed during processing"

	// System
	case ErrorCodeResourceUnavailable:
		return "Required resource is unavailable"
	case ErrorCodeConcurrencyLimit:
		return "Maximum number of concurrent ETL jobs exceeded"
	case ErrorCodeDependencyFailed:
		return "Required dependency failed"
	case ErrorCodeUnknownError:
		return "An unknown error occurred"

	default:
		return "Unknown error code: " + errorCode
	}
}
