package repository

import (
	"context"
	"database/sql"
	"fmt"
	"strconv"
	"strings"
	"time"

	cmncontext "common/context"
	"common/database"
	workflowUtils "workflow/internal/common/utils"

	etl "proto/hero/etl/v1"

	"github.com/google/uuid"
	"github.com/lib/pq"
)

// Default pagination size
const defaultPageSize = 50

// PostgresETLRepository implements ETLRepository using PostgreSQL
type PostgresETLRepository struct {
	db *sql.DB
}

// NewPostgresETLRepository creates a new PostgreSQL ETL repository
func NewPostgresETLRepository(db *sql.DB) *PostgresETLRepository {
	return &PostgresETLRepository{db: db}
}

// nullIfEmpty returns nil for empty strings so ExecContext writes SQL NULL
func nullIfEmpty(inputStr string) interface{} {
	if strings.TrimSpace(inputStr) == "" {
		return nil
	}
	return inputStr
}

// <PERSON><PERSON><PERSON><PERSON> creates a new ETL job
func (repo *PostgresETLRepository) CreateJob(ctx context.Context, tx *sql.Tx, job *etl.ETLJob) (*etl.ETLJob, error) {
	return database.WithSession(repo.db, ctx, tx, func(sessionTx *sql.Tx) (*etl.ETLJob, error) {
		if job.Id == "" {
			job.Id = uuid.New().String()
		}

		orgId := cmncontext.GetOrgId(ctx)
		job.OrgId = orgId

		callerAssetId, err := workflowUtils.GetAssetIDFromContextUsingCognitoSubWithTx(ctx, sessionTx)
		if err != nil {
			callerAssetId = ""
		}
		if job.CreatedByAssetId == "" {
			job.CreatedByAssetId = callerAssetId
		}

		// Set timestamps
		currentTime := time.Now().UTC()
		currentTimeStr := currentTime.Format(time.RFC3339Nano)
		job.CreatedAt = currentTimeStr
		job.LastUpdatedAt = currentTimeStr

		// Set defaults
		if job.Status == etl.JobStatus_JOB_STATUS_UNSPECIFIED {
			job.Status = etl.JobStatus_JOB_STATUS_PENDING
		}
		if job.MaxRetries == 0 {
			job.MaxRetries = 3
		}

		query := `
			INSERT INTO etl_jobs (
				id, org_id, agency_id, output_format, status,
				report_ids, date_from, date_to, case_types,
				total_reports, reports_processed, reports_failed, reports_skipped,
				created_at, last_updated_at,
				error_message, content_type, content_size_bytes,
				retry_count, max_retries, auto_retry_enabled,
				created_by_asset_id, preview_only, job_name
			) VALUES (
				$1, $2, $3, $4, $5,
				$6, $7, $8, $9,
				$10, $11, $12, $13,
				$14, $15,
				$16, $17, $18,
				$19, $20, $21,
				$22, $23, $24
			)
		`

		_, err = sessionTx.ExecContext(ctx, query,
			job.Id, orgId, job.AgencyId, int(job.OutputFormat), int(job.Status),
			pq.Array(job.ReportIds), nullIfEmpty(job.DateFrom), nullIfEmpty(job.DateTo), pq.Array(job.CaseTypes),
			job.TotalReports, job.ReportsProcessed, job.ReportsFailed, job.ReportsSkipped,
			currentTime, currentTime,
			nullIfEmpty(job.ErrorMessage), nullIfEmpty(job.ContentType), job.ContentSizeBytes,
			job.RetryCount, job.MaxRetries, job.AutoRetryEnabled,
			nullIfEmpty(job.CreatedByAssetId), job.PreviewOnly, nullIfEmpty(job.JobName),
		)

		if err != nil {
			return nil, fmt.Errorf("failed to create ETL job: %w", err)
		}

		return job, nil
	})
}

// GetJob retrieves an ETL job by ID
func (repo *PostgresETLRepository) GetJob(ctx context.Context, tx *sql.Tx, jobID string) (*etl.ETLJob, error) {
	return database.WithSession(repo.db, ctx, tx, func(sessionTx *sql.Tx) (*etl.ETLJob, error) {
		query := `
			SELECT 
				id, org_id, agency_id, output_format, status,
				report_ids, date_from, date_to, case_types,
				total_reports, reports_processed, reports_failed, reports_skipped,
				created_at, started_at, completed_at, last_updated_at,
				error_message, generated_content, content_type, content_size_bytes,
				agency_submission_id, submission_response, submitted_at,
				retry_count, max_retries, last_retry_at, auto_retry_enabled,
				created_by_asset_id, preview_only, job_name
			FROM etl_jobs 
			WHERE id = $1
		`

		job := &etl.ETLJob{}
		var reportIds pq.StringArray
		var caseTypes pq.StringArray
		var dateFrom, dateTo sql.NullString
		var startedAt, completedAt, submittedAt, lastRetryAt sql.NullTime
		var errorMessage, contentType, agencySubmissionID, submissionResponse, createdByAssetID, jobName sql.NullString
		var generatedContent []byte
		var createdAtTime, lastUpdatedAtTime time.Time

		err := sessionTx.QueryRowContext(ctx, query, jobID).Scan(
			&job.Id, &job.OrgId, &job.AgencyId, &job.OutputFormat, &job.Status,
			&reportIds, &dateFrom, &dateTo, &caseTypes,
			&job.TotalReports, &job.ReportsProcessed, &job.ReportsFailed, &job.ReportsSkipped,
			&createdAtTime, &startedAt, &completedAt, &lastUpdatedAtTime,
			&errorMessage, &generatedContent, &contentType, &job.ContentSizeBytes,
			&agencySubmissionID, &submissionResponse, &submittedAt,
			&job.RetryCount, &job.MaxRetries, &lastRetryAt, &job.AutoRetryEnabled,
			&createdByAssetID, &job.PreviewOnly, &jobName,
		)

		if err != nil {
			if err == sql.ErrNoRows {
				return nil, ErrETLJobNotFound
			}
			return nil, fmt.Errorf("failed to get ETL job: %w", err)
		}

		// Convert arrays and nullable fields
		job.ReportIds = reportIds
		job.CaseTypes = caseTypes
		job.CreatedAt = createdAtTime.Format(time.RFC3339Nano)
		job.LastUpdatedAt = lastUpdatedAtTime.Format(time.RFC3339Nano)

		if dateFrom.Valid {
			job.DateFrom = dateFrom.String
		}
		if dateTo.Valid {
			job.DateTo = dateTo.String
		}
		if startedAt.Valid {
			job.StartedAt = startedAt.Time.Format(time.RFC3339Nano)
		}
		if completedAt.Valid {
			job.CompletedAt = completedAt.Time.Format(time.RFC3339Nano)
		}
		if submittedAt.Valid {
			job.SubmittedAt = submittedAt.Time.Format(time.RFC3339Nano)
		}
		if lastRetryAt.Valid {
			job.LastRetryAt = lastRetryAt.Time.Format(time.RFC3339Nano)
		}
		if errorMessage.Valid {
			job.ErrorMessage = errorMessage.String
		}
		if contentType.Valid {
			job.ContentType = contentType.String
		}
		if agencySubmissionID.Valid {
			job.AgencySubmissionId = agencySubmissionID.String
		}
		if submissionResponse.Valid {
			job.SubmissionResponse = submissionResponse.String
		}
		if createdByAssetID.Valid {
			job.CreatedByAssetId = createdByAssetID.String
		}
		if jobName.Valid {
			job.JobName = jobName.String
		}
		if generatedContent != nil {
			job.GeneratedContent = generatedContent
		}

		// Load job errors
		errors, err := repo.ListJobErrors(ctx, sessionTx, jobID)
		if err != nil {
			return nil, fmt.Errorf("failed to load job errors: %w", err)
		}
		job.Errors = errors

		return job, nil
	})
}

// UpdateJob updates an existing ETL job
func (repo *PostgresETLRepository) UpdateJob(ctx context.Context, tx *sql.Tx, job *etl.ETLJob) (*etl.ETLJob, error) {
	return database.WithSession(repo.db, ctx, tx, func(sessionTx *sql.Tx) (*etl.ETLJob, error) {
		// Update timestamp
		currentTime := time.Now().UTC()
		job.LastUpdatedAt = currentTime.Format(time.RFC3339Nano)

		query := `
			UPDATE etl_jobs SET
				agency_id = $2, output_format = $3, status = $4,
				report_ids = $5, date_from = $6, date_to = $7, case_types = $8,
				total_reports = $9, reports_processed = $10, reports_failed = $11, reports_skipped = $12,
				started_at = $13, completed_at = $14, last_updated_at = $15,
				error_message = $16, generated_content = $17, content_type = $18, content_size_bytes = $19,
				agency_submission_id = $20, submission_response = $21, submitted_at = $22,
				retry_count = $23, max_retries = $24, last_retry_at = $25, auto_retry_enabled = $26,
				created_by_asset_id = $27, preview_only = $28, job_name = $29
			WHERE id = $1
		`

		var startedAt, completedAt, submittedAt, lastRetryAt interface{}
		if job.StartedAt != "" {
			if t, err := time.Parse(time.RFC3339Nano, job.StartedAt); err == nil {
				startedAt = t
			}
		}
		if job.CompletedAt != "" {
			if t, err := time.Parse(time.RFC3339Nano, job.CompletedAt); err == nil {
				completedAt = t
			}
		}
		if job.SubmittedAt != "" {
			if t, err := time.Parse(time.RFC3339Nano, job.SubmittedAt); err == nil {
				submittedAt = t
			}
		}
		if job.LastRetryAt != "" {
			if t, err := time.Parse(time.RFC3339Nano, job.LastRetryAt); err == nil {
				lastRetryAt = t
			}
		}

		_, err := sessionTx.ExecContext(ctx, query,
			job.Id, job.AgencyId, int(job.OutputFormat), int(job.Status),
			pq.Array(job.ReportIds), nullIfEmpty(job.DateFrom), nullIfEmpty(job.DateTo), pq.Array(job.CaseTypes),
			job.TotalReports, job.ReportsProcessed, job.ReportsFailed, job.ReportsSkipped,
			startedAt, completedAt, currentTime,
			nullIfEmpty(job.ErrorMessage), job.GeneratedContent, nullIfEmpty(job.ContentType), job.ContentSizeBytes,
			nullIfEmpty(job.AgencySubmissionId), nullIfEmpty(job.SubmissionResponse), submittedAt,
			job.RetryCount, job.MaxRetries, lastRetryAt, job.AutoRetryEnabled,
			nullIfEmpty(job.CreatedByAssetId), job.PreviewOnly, nullIfEmpty(job.JobName),
		)

		if err != nil {
			return nil, fmt.Errorf("failed to update ETL job: %w", err)
		}

		return job, nil
	})
}

// UpdateJobStatus updates the status of an ETL job
func (repo *PostgresETLRepository) UpdateJobStatus(ctx context.Context, tx *sql.Tx, jobID string, status etl.JobStatus) (*etl.ETLJob, error) {
	return database.WithSession(repo.db, ctx, tx, func(sessionTx *sql.Tx) (*etl.ETLJob, error) {
		currentTime := time.Now().UTC()

		query := `
			UPDATE etl_jobs SET 
				status = $2, 
				last_updated_at = $3,
				started_at = CASE WHEN $2 = 2 AND started_at IS NULL THEN $3 ELSE started_at END,
				completed_at = CASE WHEN $2 IN (5, 6, 7) AND completed_at IS NULL THEN $3 ELSE completed_at END
			WHERE id = $1
		`

		_, err := sessionTx.ExecContext(ctx, query, jobID, int(status), currentTime)
		if err != nil {
			return nil, fmt.Errorf("failed to update job status: %w", err)
		}

		return repo.GetJob(ctx, sessionTx, jobID)
	})
}

// UpdateJobProgress updates job progress counters
func (repo *PostgresETLRepository) UpdateJobProgress(ctx context.Context, tx *sql.Tx, jobID string, totalReports, processed, failed, skipped int32) error {
	return database.WithSessionErr(repo.db, ctx, tx, func(sessionTx *sql.Tx) error {
		query := `
			UPDATE etl_jobs SET 
				total_reports = $2,
				reports_processed = $3,
				reports_failed = $4,
				reports_skipped = $5,
				last_updated_at = $6
			WHERE id = $1
		`

		result, err := sessionTx.ExecContext(ctx, query, jobID, totalReports, processed, failed, skipped, time.Now().UTC())
		if err != nil {
			return fmt.Errorf("failed to update job progress: %w", err)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return fmt.Errorf("failed to get rows affected: %w", err)
		}

		if rowsAffected == 0 {
			return ErrETLJobNotFound
		}

		return nil
	})
}

// UpdateJobContent updates job generated content
func (repo *PostgresETLRepository) UpdateJobContent(ctx context.Context, tx *sql.Tx, jobID string, content []byte, contentType string, sizeBytes int64) error {
	return database.WithSessionErr(repo.db, ctx, tx, func(sessionTx *sql.Tx) error {
		query := `
			UPDATE etl_jobs SET 
				generated_content = $2,
				content_type = $3,
				content_size_bytes = $4,
				last_updated_at = $5
			WHERE id = $1
		`

		result, err := sessionTx.ExecContext(ctx, query, jobID, content, contentType, sizeBytes, time.Now().UTC())
		if err != nil {
			return fmt.Errorf("failed to update job content: %w", err)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return fmt.Errorf("failed to get rows affected: %w", err)
		}

		if rowsAffected == 0 {
			return ErrETLJobNotFound
		}

		return nil
	})
}

// UpdateJobSubmission updates job submission details
func (repo *PostgresETLRepository) UpdateJobSubmission(ctx context.Context, tx *sql.Tx, jobID string, agencySubmissionID, submissionResponse string) error {
	return database.WithSessionErr(repo.db, ctx, tx, func(sessionTx *sql.Tx) error {
		query := `
			UPDATE etl_jobs SET 
				agency_submission_id = $2,
				submission_response = $3,
				submitted_at = $4,
				last_updated_at = $4
			WHERE id = $1
		`

		currentTime := time.Now().UTC()
		result, err := sessionTx.ExecContext(ctx, query, jobID, nullIfEmpty(agencySubmissionID), nullIfEmpty(submissionResponse), currentTime)
		if err != nil {
			return fmt.Errorf("failed to update job submission: %w", err)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return fmt.Errorf("failed to get rows affected: %w", err)
		}

		if rowsAffected == 0 {
			return ErrETLJobNotFound
		}

		return nil
	})
}

// ListJobs lists ETL jobs with filtering and pagination
func (repo *PostgresETLRepository) ListJobs(ctx context.Context, tx *sql.Tx, pageSize int, pageToken string, agencyID string, status etl.JobStatus, createdFrom, createdTo time.Time) (*etl.ListJobsResponse, error) {
	return database.WithSession(repo.db, ctx, tx, func(sessionTx *sql.Tx) (*etl.ListJobsResponse, error) {
		var whereClause []string
		var args []interface{}
		argIndex := 1

		// Add agency filter
		if agencyID != "" {
			whereClause = append(whereClause, fmt.Sprintf("agency_id = $%d", argIndex))
			args = append(args, agencyID)
			argIndex++
		}

		// Add status filter
		if status != etl.JobStatus_JOB_STATUS_UNSPECIFIED {
			whereClause = append(whereClause, fmt.Sprintf("status = $%d", argIndex))
			args = append(args, int(status))
			argIndex++
		}

		// Add date range filters
		if !createdFrom.IsZero() {
			whereClause = append(whereClause, fmt.Sprintf("created_at >= $%d", argIndex))
			args = append(args, createdFrom)
			argIndex++
		}

		if !createdTo.IsZero() {
			whereClause = append(whereClause, fmt.Sprintf("created_at <= $%d", argIndex))
			args = append(args, createdTo)
			argIndex++
		}

		// Pagination
		offset := 0
		if pageToken != "" {
			if parsedOffset, err := strconv.Atoi(pageToken); err == nil {
				offset = parsedOffset
			}
		}

		if pageSize <= 0 {
			pageSize = defaultPageSize
		}

		whereClauseStr := ""
		if len(whereClause) > 0 {
			whereClauseStr = "WHERE " + strings.Join(whereClause, " AND ")
		}

		query := fmt.Sprintf(`
			SELECT 
				id, org_id, agency_id, output_format, status,
				report_ids, date_from, date_to, case_types,
				total_reports, reports_processed, reports_failed, reports_skipped,
				created_at, started_at, completed_at, last_updated_at,
				error_message, content_type, content_size_bytes,
				agency_submission_id, submission_response, submitted_at,
				retry_count, max_retries, last_retry_at, auto_retry_enabled,
				created_by_asset_id, preview_only, job_name
			FROM etl_jobs 
			%s
			ORDER BY created_at DESC
			LIMIT $%d OFFSET $%d
		`, whereClauseStr, argIndex, argIndex+1) // #nosec G201 -- whereClauseStr is safely built from parameterized conditions, argIndex values are safe integers

		args = append(args, pageSize+1, offset) // +1 to check if there are more results

		rows, err := sessionTx.QueryContext(ctx, query, args...)
		if err != nil {
			return nil, fmt.Errorf("failed to list ETL jobs: %w", err)
		}
		defer rows.Close()

		var jobs []*etl.ETLJob
		for rows.Next() {
			job := &etl.ETLJob{}
			var reportIds pq.StringArray
			var caseTypes pq.StringArray
			var dateFrom, dateTo sql.NullString
			var startedAt, completedAt, submittedAt, lastRetryAt sql.NullTime
			var errorMessage, contentType, agencySubmissionID, submissionResponse, createdByAssetID, jobName sql.NullString
			var createdAtTime, lastUpdatedAtTime time.Time

			err := rows.Scan(
				&job.Id, &job.OrgId, &job.AgencyId, &job.OutputFormat, &job.Status,
				&reportIds, &dateFrom, &dateTo, &caseTypes,
				&job.TotalReports, &job.ReportsProcessed, &job.ReportsFailed, &job.ReportsSkipped,
				&createdAtTime, &startedAt, &completedAt, &lastUpdatedAtTime,
				&errorMessage, &contentType, &job.ContentSizeBytes,
				&agencySubmissionID, &submissionResponse, &submittedAt,
				&job.RetryCount, &job.MaxRetries, &lastRetryAt, &job.AutoRetryEnabled,
				&createdByAssetID, &job.PreviewOnly, &jobName,
			)

			if err != nil {
				return nil, fmt.Errorf("failed to scan ETL job: %w", err)
			}

			// Convert arrays and nullable fields
			job.ReportIds = reportIds
			job.CaseTypes = caseTypes
			job.CreatedAt = createdAtTime.Format(time.RFC3339Nano)
			job.LastUpdatedAt = lastUpdatedAtTime.Format(time.RFC3339Nano)

			if dateFrom.Valid {
				job.DateFrom = dateFrom.String
			}
			if dateTo.Valid {
				job.DateTo = dateTo.String
			}
			if startedAt.Valid {
				job.StartedAt = startedAt.Time.Format(time.RFC3339Nano)
			}
			if completedAt.Valid {
				job.CompletedAt = completedAt.Time.Format(time.RFC3339Nano)
			}
			if submittedAt.Valid {
				job.SubmittedAt = submittedAt.Time.Format(time.RFC3339Nano)
			}
			if lastRetryAt.Valid {
				job.LastRetryAt = lastRetryAt.Time.Format(time.RFC3339Nano)
			}
			if errorMessage.Valid {
				job.ErrorMessage = errorMessage.String
			}
			if contentType.Valid {
				job.ContentType = contentType.String
			}
			if agencySubmissionID.Valid {
				job.AgencySubmissionId = agencySubmissionID.String
			}
			if submissionResponse.Valid {
				job.SubmissionResponse = submissionResponse.String
			}
			if createdByAssetID.Valid {
				job.CreatedByAssetId = createdByAssetID.String
			}
			if jobName.Valid {
				job.JobName = jobName.String
			}

			jobs = append(jobs, job)
		}

		if err := rows.Err(); err != nil {
			return nil, fmt.Errorf("failed to iterate ETL jobs: %w", err)
		}

		// Check if there are more results
		var nextPageToken string
		if len(jobs) > pageSize {
			jobs = jobs[:pageSize] // Remove the extra job
			nextPageToken = strconv.Itoa(offset + pageSize)
		}

		return &etl.ListJobsResponse{
			Jobs:          jobs,
			NextPageToken: nextPageToken,
		}, nil
	})
}

// CancelJob cancels an ETL job
func (repo *PostgresETLRepository) CancelJob(ctx context.Context, tx *sql.Tx, jobID string) (*etl.ETLJob, error) {
	return repo.UpdateJobStatus(ctx, tx, jobID, etl.JobStatus_JOB_STATUS_CANCELLED)
}

// CreateJobError creates a new ETL job error
func (repo *PostgresETLRepository) CreateJobError(ctx context.Context, tx *sql.Tx, jobID string, jobError *etl.JobError) (*etl.JobError, error) {
	return database.WithSession(repo.db, ctx, tx, func(sessionTx *sql.Tx) (*etl.JobError, error) {
		query := `
			INSERT INTO etl_job_errors (
				job_id, error_code, error_message, report_id, field_path,
				occurred_at, is_retryable, retry_attempt
			) VALUES (
				$1, $2, $3, $4, $5, $6, $7, $8
			)
			RETURNING occurred_at
		`

		currentTime := time.Now().UTC()
		var occurredAtTime time.Time

		err := sessionTx.QueryRowContext(ctx, query,
			jobID, jobError.ErrorCode, jobError.ErrorMessage,
			nullIfEmpty(jobError.ReportId), nullIfEmpty(jobError.FieldPath),
			currentTime, jobError.IsRetryable, jobError.RetryAttempt,
		).Scan(&occurredAtTime)

		if err != nil {
			return nil, fmt.Errorf("failed to create ETL job error: %w", err)
		}

		jobError.OccurredAt = occurredAtTime.Format(time.RFC3339Nano)
		return jobError, nil
	})
}

// ListJobErrors lists errors for an ETL job
func (repo *PostgresETLRepository) ListJobErrors(ctx context.Context, tx *sql.Tx, jobID string) ([]*etl.JobError, error) {
	query := `
		SELECT error_code, error_message, report_id, field_path, occurred_at, is_retryable, retry_attempt
		FROM etl_job_errors 
		WHERE job_id = $1
		ORDER BY occurred_at DESC
	`

	rows, err := tx.QueryContext(ctx, query, jobID)
	if err != nil {
		return nil, fmt.Errorf("failed to list ETL job errors: %w", err)
	}
	defer rows.Close()

	var errors []*etl.JobError
	for rows.Next() {
		jobError := &etl.JobError{}
		var reportID, fieldPath sql.NullString
		var occurredAtTime time.Time

		err := rows.Scan(
			&jobError.ErrorCode, &jobError.ErrorMessage,
			&reportID, &fieldPath, &occurredAtTime,
			&jobError.IsRetryable, &jobError.RetryAttempt,
		)

		if err != nil {
			return nil, fmt.Errorf("failed to scan ETL job error: %w", err)
		}

		if reportID.Valid {
			jobError.ReportId = reportID.String
		}
		if fieldPath.Valid {
			jobError.FieldPath = fieldPath.String
		}
		jobError.OccurredAt = occurredAtTime.Format(time.RFC3339Nano)

		errors = append(errors, jobError)
	}

	if err := rows.Err(); err != nil {
		return nil, fmt.Errorf("failed to iterate ETL job errors: %w", err)
	}

	return errors, nil
}

// GetJobStats retrieves job statistics
func (repo *PostgresETLRepository) GetJobStats(ctx context.Context, tx *sql.Tx, agencyID string, outputFormat etl.OutputFormat, dateFrom, dateTo time.Time) (*etl.GetJobStatsResponse, error) {
	return database.WithSession(repo.db, ctx, tx, func(sessionTx *sql.Tx) (*etl.GetJobStatsResponse, error) {
		var whereClause []string
		var args []interface{}
		argIndex := 1

		if agencyID != "" {
			whereClause = append(whereClause, fmt.Sprintf("agency_id = $%d", argIndex))
			args = append(args, agencyID)
			argIndex++
		}

		if outputFormat != etl.OutputFormat_OUTPUT_FORMAT_UNSPECIFIED {
			whereClause = append(whereClause, fmt.Sprintf("output_format = $%d", argIndex))
			args = append(args, int(outputFormat))
			argIndex++
		}

		if !dateFrom.IsZero() {
			whereClause = append(whereClause, fmt.Sprintf("created_at >= $%d", argIndex))
			args = append(args, dateFrom)
			argIndex++
		}

		if !dateTo.IsZero() {
			whereClause = append(whereClause, fmt.Sprintf("created_at <= $%d", argIndex))
			args = append(args, dateTo)
		}

		whereClauseStr := ""
		if len(whereClause) > 0 {
			whereClauseStr = "WHERE " + strings.Join(whereClause, " AND ")
		}

		query := fmt.Sprintf(`
			SELECT 
				COUNT(*) as total_jobs,
				COUNT(CASE WHEN status = 5 THEN 1 END) as completed_jobs,
				COUNT(CASE WHEN status = 6 THEN 1 END) as failed_jobs,
				COUNT(CASE WHEN status = 7 THEN 1 END) as cancelled_jobs,
				COUNT(CASE WHEN status IN (2, 3, 4) THEN 1 END) as running_jobs,
				COALESCE(SUM(reports_processed), 0) as total_reports_processed,
				COALESCE(SUM(content_size_bytes), 0) as total_content_size_bytes
			FROM etl_jobs 
			%s
		`, whereClauseStr) // #nosec G201 -- whereClauseStr is safely built from parameterized conditions

		stats := &etl.GetJobStatsResponse{}
		err := sessionTx.QueryRowContext(ctx, query, args...).Scan(
			&stats.TotalJobs, &stats.CompletedJobs, &stats.FailedJobs,
			&stats.CancelledJobs, &stats.RunningJobs,
			&stats.TotalReportsProcessed, &stats.TotalContentSizeBytes,
		)

		if err != nil {
			return nil, fmt.Errorf("failed to get job stats: %w", err)
		}

		return stats, nil
	})
}

// GetAgencyStats retrieves statistics for a specific agency
func (repo *PostgresETLRepository) GetAgencyStats(ctx context.Context, tx *sql.Tx, agencyID string, dateFrom, dateTo time.Time, includeJobDetails bool) (*etl.GetAgencyStatsResponse, error) {
	return database.WithSession(repo.db, ctx, tx, func(sessionTx *sql.Tx) (*etl.GetAgencyStatsResponse, error) {
		var whereClause []string
		var args []interface{}
		argIndex := 1

		whereClause = append(whereClause, fmt.Sprintf("agency_id = $%d", argIndex))
		args = append(args, agencyID)
		argIndex++

		if !dateFrom.IsZero() {
			whereClause = append(whereClause, fmt.Sprintf("created_at >= $%d", argIndex))
			args = append(args, dateFrom)
			argIndex++
		}

		if !dateTo.IsZero() {
			whereClause = append(whereClause, fmt.Sprintf("created_at <= $%d", argIndex))
			args = append(args, dateTo)
		}

		whereClauseStr := "WHERE " + strings.Join(whereClause, " AND ")

		// Get basic agency stats
		statsQuery := fmt.Sprintf(`
			SELECT 
				COUNT(*) as job_count,
				COALESCE(SUM(reports_processed), 0) as report_count,
				COALESCE(SUM(content_size_bytes), 0) as content_size_bytes,
				MAX(submitted_at) as last_submission_at
			FROM etl_jobs 
			%s
		`, whereClauseStr) // #nosec G201 -- whereClauseStr is safely built from parameterized conditions

		agencyStats := &etl.AgencyStats{
			AgencyId: agencyID,
		}

		var lastSubmissionAt sql.NullTime
		err := sessionTx.QueryRowContext(ctx, statsQuery, args...).Scan(
			&agencyStats.JobCount, &agencyStats.ReportCount,
			&agencyStats.ContentSizeBytes, &lastSubmissionAt,
		)

		if err != nil {
			return nil, fmt.Errorf("failed to get agency stats: %w", err)
		}

		if lastSubmissionAt.Valid {
			agencyStats.LastSubmissionAt = lastSubmissionAt.Time.Format(time.RFC3339Nano)
		}

		// Get agency name from predefined list
		agency, err := repo.GetAgency(ctx, sessionTx, agencyID)
		if err == nil {
			agencyStats.AgencyName = agency.Name
		}

		response := &etl.GetAgencyStatsResponse{
			AgencyStats: agencyStats,
			GeneratedAt: time.Now().Format(time.RFC3339Nano),
		}

		// Include recent jobs if requested
		if includeJobDetails {
			jobsQuery := fmt.Sprintf(`
				SELECT 
					id, status, output_format, reports_processed,
					created_at, completed_at, content_size_bytes
				FROM etl_jobs 
				%s
				ORDER BY created_at DESC
				LIMIT 10
			`, whereClauseStr) // #nosec G201 -- whereClauseStr is safely built from parameterized conditions

			rows, err := sessionTx.QueryContext(ctx, jobsQuery, args...)
			if err != nil {
				return nil, fmt.Errorf("failed to get recent jobs: %w", err)
			}
			defer rows.Close()

			var recentJobs []*etl.JobSummary
			for rows.Next() {
				job := &etl.JobSummary{}
				var createdAtTime, completedAtTime sql.NullTime

				err := rows.Scan(
					&job.JobId, &job.Status, &job.OutputFormat, &job.ReportsProcessed,
					&createdAtTime, &completedAtTime, &job.ContentSizeBytes,
				)
				if err != nil {
					return nil, fmt.Errorf("failed to scan job summary: %w", err)
				}

				if createdAtTime.Valid {
					job.CreatedAt = createdAtTime.Time.Format(time.RFC3339Nano)
				}
				if completedAtTime.Valid {
					job.CompletedAt = completedAtTime.Time.Format(time.RFC3339Nano)
				}

				recentJobs = append(recentJobs, job)
			}

			if err := rows.Err(); err != nil {
				return nil, fmt.Errorf("failed to iterate job summaries: %w", err)
			}

			response.RecentJobs = recentJobs
		}

		return response, nil
	})
}

// ListAgencies lists available agencies (placeholder - would be configured)
func (repo *PostgresETLRepository) ListAgencies(ctx context.Context, tx *sql.Tx) ([]*etl.AgencyInfo, error) {
	// For now, return hardcoded agencies - in production this would come from configuration
	return []*etl.AgencyInfo{
		{
			Id:               "FBI",
			Name:             "Federal Bureau of Investigation",
			SupportedFormats: []etl.OutputFormat{etl.OutputFormat_OUTPUT_FORMAT_NIBRS_XML},
			Enabled:          true,
			AgencyType:       "FEDERAL",
		},
		{
			Id:               "STATE_POLICE",
			Name:             "State Police",
			SupportedFormats: []etl.OutputFormat{etl.OutputFormat_OUTPUT_FORMAT_NIBRS_XML},
			Enabled:          true,
			AgencyType:       "STATE",
		},
		{
			Id:               "UNIVERSITY_POLICE",
			Name:             "University Police",
			SupportedFormats: []etl.OutputFormat{etl.OutputFormat_OUTPUT_FORMAT_NIBRS_XML},
			Enabled:          true,
			AgencyType:       "UNIVERSITY",
		},
	}, nil
}

// GetAgency retrieves a specific agency by ID
func (repo *PostgresETLRepository) GetAgency(ctx context.Context, tx *sql.Tx, agencyID string) (*etl.AgencyInfo, error) {
	agencies, err := repo.ListAgencies(ctx, tx)
	if err != nil {
		return nil, err
	}

	for _, agency := range agencies {
		if agency.Id == agencyID {
			return agency, nil
		}
	}

	return nil, fmt.Errorf("agency not found: %s", agencyID)
}

// IncrementRetryCount increments the retry count for a job
func (repo *PostgresETLRepository) IncrementRetryCount(ctx context.Context, tx *sql.Tx, jobID string) error {
	return database.WithSessionErr(repo.db, ctx, tx, func(sessionTx *sql.Tx) error {
		query := `
			UPDATE etl_jobs SET 
				retry_count = retry_count + 1,
				last_retry_at = $2,
				last_updated_at = $2
			WHERE id = $1
		`

		currentTime := time.Now().UTC()
		result, err := sessionTx.ExecContext(ctx, query, jobID, currentTime)
		if err != nil {
			return fmt.Errorf("failed to increment retry count: %w", err)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return fmt.Errorf("failed to get rows affected: %w", err)
		}

		if rowsAffected == 0 {
			return ErrETLJobNotFound
		}

		return nil
	})
}

// ResetRetryCount resets the retry count for a job
func (repo *PostgresETLRepository) ResetRetryCount(ctx context.Context, tx *sql.Tx, jobID string) error {
	return database.WithSessionErr(repo.db, ctx, tx, func(sessionTx *sql.Tx) error {
		query := `
			UPDATE etl_jobs SET 
				retry_count = 0,
				last_updated_at = $2
			WHERE id = $1
		`

		currentTime := time.Now().UTC()
		result, err := sessionTx.ExecContext(ctx, query, jobID, currentTime)
		if err != nil {
			return fmt.Errorf("failed to reset retry count: %w", err)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return fmt.Errorf("failed to get rows affected: %w", err)
		}

		if rowsAffected == 0 {
			return ErrETLJobNotFound
		}

		return nil
	})
}

// UpdateLastRetryAt updates the last retry timestamp
func (repo *PostgresETLRepository) UpdateLastRetryAt(ctx context.Context, tx *sql.Tx, jobID string, retryAt time.Time) error {
	return database.WithSessionErr(repo.db, ctx, tx, func(sessionTx *sql.Tx) error {
		query := `
			UPDATE etl_jobs SET 
				last_retry_at = $2,
				last_updated_at = $2
			WHERE id = $1
		`

		result, err := sessionTx.ExecContext(ctx, query, jobID, retryAt)
		if err != nil {
			return fmt.Errorf("failed to update last retry at: %w", err)
		}

		rowsAffected, err := result.RowsAffected()
		if err != nil {
			return fmt.Errorf("failed to get rows affected: %w", err)
		}

		if rowsAffected == 0 {
			return ErrETLJobNotFound
		}

		return nil
	})
}
