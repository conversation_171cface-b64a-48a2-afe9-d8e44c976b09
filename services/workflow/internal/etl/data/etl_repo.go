package repository

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"time"

	etl "proto/hero/etl/v1"
)

// ErrETLJobNotFound indicates a missing ETL job in the database.
var ErrETLJobNotFound = fmt.Errorf("ETL job not found")

// ErrETLJobErrorNotFound indicates a missing ETL job error in the database.
var ErrETLJobErrorNotFound = fmt.Errorf("ETL job error not found")

// Fixed resource type constants
const (
	FixedResourceTypeETLJob      = "ETL_JOB"
	FixedResourceTypeETLJobError = "ETL_JOB_ERROR"
)

// ETLRepository defines operations for managing ETL jobs and related entities.
type ETLRepository interface {
	// Job CRUD operations
	CreateJob(ctx context.Context, tx *sql.Tx, job *etl.ETLJob) (*etl.ETLJob, error)
	GetJob(ctx context.Context, tx *sql.Tx, jobID string) (*etl.ETLJob, error)
	UpdateJob(ctx context.Context, tx *sql.Tx, job *etl.ETLJob) (*etl.ETLJob, error)
	UpdateJobStatus(ctx context.Context, tx *sql.Tx, jobID string, status etl.JobStatus) (*etl.ETLJob, error)
	UpdateJobProgress(ctx context.Context, tx *sql.Tx, jobID string, totalReports, processed, failed, skipped int32) error
	UpdateJobContent(ctx context.Context, tx *sql.Tx, jobID string, content []byte, contentType string, sizeBytes int64) error
	UpdateJobSubmission(ctx context.Context, tx *sql.Tx, jobID string, agencySubmissionID, submissionResponse string) error
	ListJobs(ctx context.Context, tx *sql.Tx, pageSize int, pageToken string, agencyID string, status etl.JobStatus, createdFrom, createdTo time.Time) (*etl.ListJobsResponse, error)
	CancelJob(ctx context.Context, tx *sql.Tx, jobID string) (*etl.ETLJob, error)

	// Job error operations
	CreateJobError(ctx context.Context, tx *sql.Tx, jobID string, jobError *etl.JobError) (*etl.JobError, error)
	ListJobErrors(ctx context.Context, tx *sql.Tx, jobID string) ([]*etl.JobError, error)

	// Statistics operations
	GetJobStats(ctx context.Context, tx *sql.Tx, agencyID string, outputFormat etl.OutputFormat, dateFrom, dateTo time.Time) (*etl.GetJobStatsResponse, error)
	GetAgencyStats(ctx context.Context, tx *sql.Tx, agencyID string, dateFrom, dateTo time.Time, includeJobDetails bool) (*etl.GetAgencyStatsResponse, error)

	// Agency configuration
	ListAgencies(ctx context.Context, tx *sql.Tx) ([]*etl.AgencyInfo, error)
	GetAgency(ctx context.Context, tx *sql.Tx, agencyID string) (*etl.AgencyInfo, error)

	// Retry operations
	IncrementRetryCount(ctx context.Context, tx *sql.Tx, jobID string) error
	ResetRetryCount(ctx context.Context, tx *sql.Tx, jobID string) error
	UpdateLastRetryAt(ctx context.Context, tx *sql.Tx, jobID string, retryAt time.Time) error
}

// NewETLRepository returns a Postgres-backed implementation.
func NewETLRepository(db *sql.DB) (ETLRepository, *sql.DB, error) {
	if db == nil {
		return nil, nil, errors.New("database is nil: cannot initialize ETLRepository")
	}
	return NewPostgresETLRepository(db), db, nil
}
