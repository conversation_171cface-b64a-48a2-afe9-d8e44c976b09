<?xml version="1.0" encoding="UTF-8"?>
<nibrs:Submission xmlns:nibrs="http://fbi.gov/cjis/nibrs/2023.0" 
	xmlns:cjis="http://fbi.gov/cjis/2.1" 
	xmlns:j="http://release.niem.gov/niem/domains/jxdm/5.2/" 
	xmlns:nc="http://release.niem.gov/niem/niem-core/3.0/"
	xmlns:s="http://release.niem.gov/niem/structures/3.0/"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xmlns:xsd="http://www.w3.org/2001/XMLSchema" 
	xmlns:nibrscodes="http://fbi.gov/cjis/nibrs/2023.0/nibrs-codes" 
	xsi:schemaLocation="http://fbi.gov/cjis/nibrs/2023.0 ../base-xsd/nibrs/2023.0/nibrs.xsd">
	<cjis:MessageMetadata>
		<!-- Message Timestamp -->
		<cjis:MessageDateTime>2017-03-23T03:00:00</cjis:MessageDateTime>
		<!-- Message ID -->
		<cjis:MessageIdentification>
			<nc:IdentificationID>123456</nc:IdentificationID>
		</cjis:MessageIdentification>
		<!-- NIBRS IEPD Version -->
		<cjis:MessageImplementationVersion>2023.0</cjis:MessageImplementationVersion>
		<cjis:MessageSubmittingOrganization>
			<j:OrganizationAugmentation>
				<j:OrganizationORIIdentification>
					<!-- Submitting Agency ORI -->
					<nc:IdentificationID>FBI111111</nc:IdentificationID>
				</j:OrganizationORIIdentification>
			</j:OrganizationAugmentation>
		</cjis:MessageSubmittingOrganization>
	</cjis:MessageMetadata>
	<nibrs:Report>
		<nibrs:ReportHeader>
			<!-- Submission Type -->
			<nibrs:NIBRSReportCategoryCode>GROUP A INCIDENT REPORT</nibrs:NIBRSReportCategoryCode>
			<!-- Submission Action Type -->
			<nibrs:ReportActionCategoryCode>I</nibrs:ReportActionCategoryCode>
			<!-- Year/Month Of Report -->
			<nibrs:ReportDate>
				<nc:YearMonthDate>2016-02</nc:YearMonthDate>
			</nibrs:ReportDate>
			<nibrs:ReportingAgency>
				<j:OrganizationAugmentation>
					<j:OrganizationORIIdentification>
						<!-- Element 1, ORI Code, Owning Agency ORI -->
						<nc:IdentificationID>WVNDX0100</nc:IdentificationID>
					</j:OrganizationORIIdentification>
				</j:OrganizationAugmentation>
			</nibrs:ReportingAgency>
		</nibrs:ReportHeader>
		<nc:Incident>
			<nc:ActivityIdentification>
				<!-- Element 2, Incident Number -->
				<nc:IdentificationID>54236732</nc:IdentificationID>
			</nc:ActivityIdentification>
			<nc:ActivityDate>
			    <nc:YearMonthDate>1999-01</nc:YearMonthDate>
				<!-- Element 3, Incident Date and Hour-->
				<nc:DateTime>2016-02-19T10:00:00</nc:DateTime>
				<!-- Element 3, Incident Date if Hour is Unknown-->
				<nc:Date>2016-02-19</nc:Date>
			</nc:ActivityDate>
			<cjis:IncidentAugmentation>
				<!-- Element 3, IncidentReportDateIndicator is true to designate that the ActivityDate is the Report Date rather than the Incident Date -->
				<cjis:IncidentReportDateIndicator>false</cjis:IncidentReportDateIndicator>
				<!-- Element 2A, Cargo Theft Indicator: True/False-->
				<j:OffenseCargoTheftIndicator>true</j:OffenseCargoTheftIndicator>
			</cjis:IncidentAugmentation>
			<j:IncidentAugmentation>
				<!-- Element 4, Cleared Exceptionally -->
				<j:IncidentExceptionalClearanceCode>A</j:IncidentExceptionalClearanceCode>
				<!-- Element 5, Exceptional Clearance Date -->
				<j:IncidentExceptionalClearanceDate>
					<nc:Date>2016-02-25</nc:Date>
				</j:IncidentExceptionalClearanceDate>
			</j:IncidentAugmentation>
		</nc:Incident>
		<j:Offense s:id="Offense1">
			<!-- Element 6, Offense Code -->
			<!--<nibrs:OffenseUCRCode>HUMAN_TRAFFICKING-COMMERCIAL_SEX_ACTS</nibrs:OffenseUCRCode>-->
		    <nibrs:OffenseUCRCode>MOTOR_VEHICLE_THEFT</nibrs:OffenseUCRCode>
			<!-- Element 12, Type Criminal Activity/Gang Information -->
			<nibrs:CriminalActivityCategoryCode>N</nibrs:CriminalActivityCategoryCode>
			<!-- Element 8A, Bias Motivation -->
			<nibrs:NIBRSBiasMotivationCode>ANTIAMERICAN INDIAN_ALASKAN NATIVE</nibrs:NIBRSBiasMotivationCode>
			<!-- Element 10, Number Of Premises Entered -->
			<j:OffenseStructuresEnteredQuantity>1</j:OffenseStructuresEnteredQuantity>
			<!-- Element 8, Offender(s) Suspected Of Using -->
			<j:OffenseFactor>
				<j:OffenseFactorCode>N</j:OffenseFactorCode>
			</j:OffenseFactor>
			<!-- Element 11, Method Of Entry -->
			<j:OffenseEntryPoint>
				<j:PassagePointMethodCode>F</j:PassagePointMethodCode>
			</j:OffenseEntryPoint>
			<!-- Element 13, Type Weapon/Force Involved -->
			<j:OffenseForce>
				<j:ForceCategoryCode>11A</j:ForceCategoryCode>
			</j:OffenseForce>
			<!-- Element 7, Attempted/Completed -->
			<j:OffenseAttemptedIndicator>true</j:OffenseAttemptedIndicator>
		</j:Offense>
		<!-- Begin Property -->
		<!-- Element 9, Location Type -->
		<nc:Location s:id="Location1">
			<nibrs:LocationCategoryCode>58</nibrs:LocationCategoryCode>
			<nc:LocationLocale>
				<cjis:JudicialDistrictCode>ALABAMA_MIDDLE</cjis:JudicialDistrictCode>
			</nc:LocationLocale>
		</nc:Location>
	    <nc:Item>
	        <!-- Element 14, Type Property Loss/etc Substituted for nc:ItemStatus -->
	        <nc:ItemStatus>
	            <cjis:ItemStatusCode>NONE</cjis:ItemStatusCode>
	        </nc:ItemStatus>
	        <!-- Element 16, Value of Property in US Dollars -->
	        <nc:ItemValue>
	            <nc:ItemValueAmount>
	                <nc:Amount>12000</nc:Amount>
	            </nc:ItemValueAmount>
	            <!-- Element 17, Date Recovered -->
	            <nc:ItemValueDate>
	                <nc:Date>2016-02-24</nc:Date>
	            </nc:ItemValueDate>
	        </nc:ItemValue>
	        <!-- Element 15, Property Description -->
	        <j:ItemCategoryNIBRSPropertyCategoryCode>01</j:ItemCategoryNIBRSPropertyCategoryCode>
	        <!-- Element 19, Number of Recovered Motor Vehicles, if Status is Recovered -->
	        <!-- Element 18, Number of Stolen Motor Vehicles, if Status is Stolen -->
	        <nc:ItemQuantity>1</nc:ItemQuantity>
	    </nc:Item>
	    <nc:Item>
	        <!-- Element 14, Type Property Loss/etc Substituted for nc:ItemStatus -->
	        <nc:ItemStatus>
	            <cjis:ItemStatusCode>BURNED</cjis:ItemStatusCode>
	        </nc:ItemStatus>
	        <!-- Element 16, Value of Property in US Dollars -->
	        <nc:ItemValue>
	            <nc:ItemValueAmount>
	                <nc:Amount>12000</nc:Amount>
	            </nc:ItemValueAmount>
	            <!-- Element 17, Date Recovered -->
	            <nc:ItemValueDate>
	                <nc:Date>2016-02-24</nc:Date>
	                <nc:DateTime>2017-03-23T03:00:00</nc:DateTime>
	            </nc:ItemValueDate>
	        </nc:ItemValue>
	        <!-- Element 15, Property Description -->
	        <j:ItemCategoryNIBRSPropertyCategoryCode>01</j:ItemCategoryNIBRSPropertyCategoryCode>
	        <!-- Element 19, Number of Recovered Motor Vehicles, if Status is Recovered -->
	        <!-- Element 18, Number of Stolen Motor Vehicles, if Status is Stolen -->
	        <nc:ItemQuantity>1</nc:ItemQuantity>
	    </nc:Item>
	    <nc:Item>
	        <!-- Element 14, Type Property Loss/etc Substituted for nc:ItemStatus -->
	        <nc:ItemStatus>
	            <cjis:ItemStatusCode>COUNTERFEITED</cjis:ItemStatusCode>
	        </nc:ItemStatus>
	        <!-- Element 16, Value of Property in US Dollars -->
	        <nc:ItemValue>
	            <nc:ItemValueAmount>
	                <nc:Amount>12000</nc:Amount>
	            </nc:ItemValueAmount>
	            <!-- Element 17, Date Recovered -->
	            <nc:ItemValueDate>
	                <nc:Date>2016-02-24</nc:Date>
	            </nc:ItemValueDate>
	        </nc:ItemValue>
	        <!-- Element 15, Property Description -->
	        <j:ItemCategoryNIBRSPropertyCategoryCode>01</j:ItemCategoryNIBRSPropertyCategoryCode>
	        <!-- Element 19, Number of Recovered Motor Vehicles, if Status is Recovered -->
	        <!-- Element 18, Number of Stolen Motor Vehicles, if Status is Stolen -->
	        <nc:ItemQuantity>1</nc:ItemQuantity>
	    </nc:Item>
	    <nc:Item>
	        <!-- Element 14, Type Property Loss/etc Substituted for nc:ItemStatus -->
	        <nc:ItemStatus>
	            <cjis:ItemStatusCode>DESTROYED_DAMAGED_VANDALIZED</cjis:ItemStatusCode>
	        </nc:ItemStatus>
	        <!-- Element 16, Value of Property in US Dollars -->
	        <nc:ItemValue>
	            <nc:ItemValueAmount>
	                <nc:Amount>12000</nc:Amount>
	            </nc:ItemValueAmount>
	            <!-- Element 17, Date Recovered -->
	            <nc:ItemValueDate>
	                <nc:Date>2016-02-24</nc:Date>
	            </nc:ItemValueDate>
	        </nc:ItemValue>
	        <!-- Element 15, Property Description -->
	        <j:ItemCategoryNIBRSPropertyCategoryCode>01</j:ItemCategoryNIBRSPropertyCategoryCode>
	        <!-- Element 19, Number of Recovered Motor Vehicles, if Status is Recovered -->
	        <!-- Element 18, Number of Stolen Motor Vehicles, if Status is Stolen -->
	        <nc:ItemQuantity>1</nc:ItemQuantity>
	    </nc:Item>
		<nc:Item>
			<!-- Element 14, Type Property Loss/etc Substituted for nc:ItemStatus -->
			<nc:ItemStatus>
				<cjis:ItemStatusCode>RECOVERED</cjis:ItemStatusCode>
			</nc:ItemStatus>
			<!-- Element 16, Value of Property in US Dollars -->
			<nc:ItemValue>
				<nc:ItemValueAmount>
					<nc:Amount>12000</nc:Amount>
				</nc:ItemValueAmount>
				<!-- Element 17, Date Recovered -->
				<nc:ItemValueDate>
					<nc:Date>2016-02-24</nc:Date>
				</nc:ItemValueDate>
			</nc:ItemValue>
			<!-- Element 15, Property Description -->
			<j:ItemCategoryNIBRSPropertyCategoryCode>01</j:ItemCategoryNIBRSPropertyCategoryCode>
		    <!-- Element 19, Number of Recovered Motor Vehicles, if Status is Recovered -->
			<!-- Element 18, Number of Stolen Motor Vehicles, if Status is Stolen -->
			<nc:ItemQuantity>1</nc:ItemQuantity>
		</nc:Item>
	    <nc:Item>
	        <!-- Element 14, Type Property Loss/etc Substituted for nc:ItemStatus -->
	        <nc:ItemStatus>
	            <cjis:ItemStatusCode>RECOVERED</cjis:ItemStatusCode>
	        </nc:ItemStatus>
	        <!-- Element 16, Value of Property in US Dollars -->
	        <nc:ItemValue>
	            <nc:ItemValueAmount>
	                <nc:Amount>12000</nc:Amount>
	            </nc:ItemValueAmount>
	            <!-- Element 17, Date Recovered -->
	            <nc:ItemValueDate>
	                <nc:Date>2016-02-24</nc:Date>
	            </nc:ItemValueDate>
	        </nc:ItemValue>
	        <!-- Element 15, Property Description -->
	        <j:ItemCategoryNIBRSPropertyCategoryCode>01</j:ItemCategoryNIBRSPropertyCategoryCode>
	        <!-- Element 19, Number of Recovered Motor Vehicles, if Status is Recovered -->
	        <!-- Element 18, Number of Stolen Motor Vehicles, if Status is Stolen -->
	        <nc:ItemQuantity>4</nc:ItemQuantity>
	    </nc:Item>
	    <nc:Item>
	        <!-- Element 14, Type Property Loss/etc Substituted for nc:ItemStatus -->
	        <nc:ItemStatus>
	            <cjis:ItemStatusCode>RECOVERED</cjis:ItemStatusCode>
	        </nc:ItemStatus>
	        <!-- Element 16, Value of Property in US Dollars -->
	        <nc:ItemValue>
	            <nc:ItemValueAmount>
	                <nc:Amount>12000</nc:Amount>
	            </nc:ItemValueAmount>
	            <!-- Element 17, Date Recovered -->
	            <nc:ItemValueDate>
	                <nc:Date>2016-02-24</nc:Date>
	            </nc:ItemValueDate>
	        </nc:ItemValue>
	        <!-- Element 15, Property Description -->
	        <j:ItemCategoryNIBRSPropertyCategoryCode>03</j:ItemCategoryNIBRSPropertyCategoryCode>
	        <!-- Element 19, Number of Recovered Motor Vehicles, if Status is Recovered -->
	        <!-- Element 18, Number of Stolen Motor Vehicles, if Status is Stolen -->
	        <nc:ItemQuantity>41</nc:ItemQuantity>
	    </nc:Item>
		<nc:Substance>
			<!-- Element 14, Type Property Loss/etc Substituted for nc:ItemStatus -->
			<nc:ItemStatus>
				<cjis:ItemStatusCode>RECOVERED</cjis:ItemStatusCode>
			</nc:ItemStatus>
			<!-- Element 16, Value of Property in US Dollars -->
			<nc:ItemValue>
				<nc:ItemValueAmount>
					<nc:Amount>12000</nc:Amount>
				</nc:ItemValueAmount>
				<!-- Element 17, Date Recovered -->
				<nc:ItemValueDate>
					<nc:Date>2016-02-24</nc:Date>
				</nc:ItemValueDate>
			</nc:ItemValue>
			<!-- Element 15, Property Description -->
			<j:ItemCategoryNIBRSPropertyCategoryCode>10</j:ItemCategoryNIBRSPropertyCategoryCode>
			<!-- Element 20, Suspected Involved Drug Type -->
			<j:DrugCategoryCode>X</j:DrugCategoryCode>
			<nc:SubstanceQuantityMeasure>
				<!-- Element 21/22, Estimated Quantity/Fraction -->
				<nc:MeasureDecimalValue>1.5</nc:MeasureDecimalValue>
				<j:SubstanceUnitCode>XX</j:SubstanceUnitCode>
			</nc:SubstanceQuantityMeasure>
		</nc:Substance>
		<!--End Property-->
		<nc:Person s:id="PersonVictim1">
			<!-- Element 26, Age of Victim (only one would be included per victim)-->
			<nc:PersonAgeMeasure>
				<nc:MeasureIntegerValue>32</nc:MeasureIntegerValue>
			</nc:PersonAgeMeasure>
			<!-- Element 29, Ethnicity of Victim -->
			<j:PersonEthnicityCode>N</j:PersonEthnicityCode>
			<!-- Element 28, Race of Victim -->
			<j:PersonRaceNDExCode>B</j:PersonRaceNDExCode>
			<!-- Element 30, Resident Status -->
			<j:PersonResidentCode>R</j:PersonResidentCode>
			<!-- Element 27, Sex of Victim -->
			<j:PersonSexCode>M</j:PersonSexCode>
		</nc:Person>
		<nc:Person s:id="PersonVictim2">
			<!-- Element 26, Age of Victim (only one would be included per victim)-->
			<!--Values: NEONATAL, NEWBORN, BABY, UNKNOWN-->
			<nc:PersonAgeMeasure>
				<nc:MeasureValueText>BABY</nc:MeasureValueText>
			</nc:PersonAgeMeasure>
			<!-- Element 29, Ethnicity of Victim -->
			<j:PersonEthnicityCode>U</j:PersonEthnicityCode>
			<!-- Element 28, Race of Victim -->
			<j:PersonRaceNDExCode>W</j:PersonRaceNDExCode>
			<!-- Element 30, Resident Status -->
			<j:PersonResidentCode>R</j:PersonResidentCode>
			<!-- Element 27, Sex of Victim -->
			<j:PersonSexCode>M</j:PersonSexCode>
		</nc:Person>
		<nc:Person s:id="PersonSubject1">
			<!-- Element 37, Age of Subject (only one would be included per subject)-->
			<nc:PersonAgeMeasure>
				<nc:MeasureIntegerRange>
					<nc:RangeMaximumIntegerValue>30</nc:RangeMaximumIntegerValue>
					<nc:RangeMinimumIntegerValue>25</nc:RangeMinimumIntegerValue>
				</nc:MeasureIntegerRange>
			</nc:PersonAgeMeasure>
			<!-- Element 39A, Ethnicity of Subject -->
			<j:PersonEthnicityCode>N</j:PersonEthnicityCode>
			<!-- Element 39, Race of Subject -->
			<j:PersonRaceNDExCode>W</j:PersonRaceNDExCode>
			<!-- Element 38, Sex of Subject -->
			<j:PersonSexCode>F</j:PersonSexCode>
		</nc:Person>
		<nc:Person s:id="PersonArrestee1">
			<!-- Element 47, Age of Arrestee (only one would be included per victim)-->
			<nc:PersonAgeMeasure>
				<nc:MeasureIntegerValue>25</nc:MeasureIntegerValue>
			</nc:PersonAgeMeasure>
			<!-- Element 50, Ethnicity of Arrestee -->
			<j:PersonEthnicityCode>N</j:PersonEthnicityCode>
			<!-- Element 49, Race of Arrestee -->
			<j:PersonRaceNDExCode>W</j:PersonRaceNDExCode>
			<!-- Element 51, Resident Status -->
			<j:PersonResidentCode>R</j:PersonResidentCode>
			<!-- Element 48, Sex of Arrestee -->
			<j:PersonSexCode>F</j:PersonSexCode>
		</nc:Person>
		<j:EnforcementOfficial>
			<nc:RoleOfPerson s:ref="PersonVictim1"/>
			<!-- Element 25A, Type of Activity (Officer)/ Circumstance -->
			<j:EnforcementOfficialActivityCategoryCode>10</j:EnforcementOfficialActivityCategoryCode>
			<!-- Element 25B, Assignment Type (Officer) -->
			<j:EnforcementOfficialAssignmentCategoryCode>G</j:EnforcementOfficialAssignmentCategoryCode>
			<j:EnforcementOfficialUnit>
				<j:OrganizationAugmentation>
					<j:OrganizationORIIdentification>
						<!-- Element 25C, ORI-Other Jurisdiction (Officer) -->
						<nc:IdentificationID>WVNDX01</nc:IdentificationID>
					</j:OrganizationORIIdentification>
				</j:OrganizationAugmentation>
			</j:EnforcementOfficialUnit>
		</j:EnforcementOfficial>
		<j:Victim s:id="Victim1">
			<nc:RoleOfPerson s:ref="PersonVictim1"/>
			<!-- Element 23, Victim Sequence Number -->
			<j:VictimSequenceNumberText>1</j:VictimSequenceNumberText>
			<!-- Element 33, Type Injury -->
			<j:VictimInjury>
				<j:InjuryCategoryCode>N</j:InjuryCategoryCode>
			</j:VictimInjury>
			<!-- Element 25, Type of Victim -->
			<j:VictimCategoryCode>L</j:VictimCategoryCode>
			<!-- Element 31, Aggravated Assault/Homicide Circumstances -->
			<j:VictimAggravatedAssaultHomicideFactorCode>01</j:VictimAggravatedAssaultHomicideFactorCode>
			<!-- Element 32, Additional Justifiable Homicide Circumstances -->
			<j:VictimJustifiableHomicideFactorCode>C</j:VictimJustifiableHomicideFactorCode>
		</j:Victim>
		<j:Victim s:id="Victim2">
			<nc:RoleOfPerson s:ref="PersonVictim2"/>
			<!-- Element 23, Victim Sequence Number -->
			<j:VictimSequenceNumberText>2</j:VictimSequenceNumberText>
			<!-- Element 33, Type Injury -->
			<j:VictimInjury>
				<j:InjuryCategoryCode>N</j:InjuryCategoryCode>
			</j:VictimInjury>
			<!-- Element 25, Type of Victim -->
			<j:VictimCategoryCode>I</j:VictimCategoryCode>
			<!-- Element 31, Aggravated Assault/Homicide Circumstances -->
			<j:VictimAggravatedAssaultHomicideFactorCode>10</j:VictimAggravatedAssaultHomicideFactorCode>
			<!-- Element 32, Additional Justifiable Homicide Circumstances -->
			<j:VictimJustifiableHomicideFactorCode>G</j:VictimJustifiableHomicideFactorCode>
		</j:Victim>
		<j:Subject s:id="Subject1">
			<nc:RoleOfPerson s:ref="PersonSubject1"/>
			<!-- Element 36, Offender Sequence Number -->
			<j:SubjectSequenceNumberText>1</j:SubjectSequenceNumberText>
		</j:Subject>
		<j:Arrestee s:id="Arrestee1">
			<nc:RoleOfPerson s:ref="PersonArrestee1"/>
			<!-- Element 40, Arrestee Sequence Number -->
			<j:ArrestSequenceID>1</j:ArrestSequenceID>
			<!-- Element 46, Arrestee Was Armed With -->
			<j:ArresteeArmedWithCode>12</j:ArresteeArmedWithCode>
		    <j:ArresteeArmedWithCode>13A</j:ArresteeArmedWithCode>
			<!-- Element 52, Disposition of Arrestee Under 18 -->
			<j:ArresteeJuvenileDispositionCode>H</j:ArresteeJuvenileDispositionCode>
			<!-- Element 44, Multiple Arrestee Segments Indicator -->
			<j:ArrestSubjectCountCode>N</j:ArrestSubjectCountCode>
		</j:Arrestee>
		<j:Arrest s:id="Arrest1">
			<!-- Element 41, Arrest Transaction Number -->
			<nc:ActivityIdentification>
				<nc:IdentificationID>12345</nc:IdentificationID>
			</nc:ActivityIdentification>
			<!-- Element 42, Arrest Date -->
			<nc:ActivityDate>
				<nc:Date>2016-02-28</nc:Date>
			</nc:ActivityDate>
			<!-- Element 45, UCR Arrest Offense Code -->
			<j:ArrestCharge>
				<nibrs:ChargeUCRCode>HUMAN_TRAFFICKING-COMMERCIAL_SEX_ACTS</nibrs:ChargeUCRCode>
			</j:ArrestCharge>
			<!-- Element 43, Type Of Arrest -->
			<j:ArrestCategoryCode>O</j:ArrestCategoryCode>
		</j:Arrest>
		<!-- Associations ================================================== -->
		<j:ArrestSubjectAssociation>
			<nc:Activity s:ref="Arrest1"/>
			<j:Subject s:ref="Arrestee1"/>
		</j:ArrestSubjectAssociation>
		<!-- Offense Location Association -->
		<j:OffenseLocationAssociation>
			<j:Offense s:ref="Offense1"/>
			<nc:Location s:ref="Location1"/>
		</j:OffenseLocationAssociation>
		<!-- Element 24, Victim Connected to UCR Offense Code -->
		<j:OffenseVictimAssociation>
			<j:Offense s:ref="Offense1"/>
			<j:Victim s:ref="Victim1"/>
		</j:OffenseVictimAssociation>
		<!-- Element 24, Victim Connected to UCR Offense Code -->
		<j:OffenseVictimAssociation>
			<j:Offense s:ref="Offense1"/>
			<j:Victim s:ref="Victim2"/>
		</j:OffenseVictimAssociation>
		<!--Element 34, Offender Number(s) to be related -->
		<j:SubjectVictimAssociation s:id="SubjectVictimAssocSP1">
			<!-- Element 35, Relationship(s) of Victim To Offender -->
			<j:Subject s:ref="Subject1"/>
			<j:Victim s:ref="Victim1"/>
			<nibrs:VictimToSubjectRelationshipCode>Acquaintance</nibrs:VictimToSubjectRelationshipCode>
		</j:SubjectVictimAssociation>
		<!--Element 34, Offender Number(s) to be related -->
		<j:SubjectVictimAssociation s:id="SubjectVictimAssocSP2">
			<!-- Element 35, Relationship(s) of Victim To Offender -->
			<j:Subject s:ref="Subject1"/>
			<j:Victim s:ref="Victim2"/>
			<nibrs:VictimToSubjectRelationshipCode>Foster Child</nibrs:VictimToSubjectRelationshipCode>
		</j:SubjectVictimAssociation>
	</nibrs:Report>
</nibrs:Submission>
