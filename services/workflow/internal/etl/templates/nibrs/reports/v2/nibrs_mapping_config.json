{"format_name": "NIBRS_XML_2025_COMPACT_TEST", "description": "Compact NIBRS XML configuration for testing all transformation features with minimal output", "version": "5.0", "field_mappings": [{"name": "report_date", "description": "date_format: Convert ISO timestamp to NIBRS date format", "input_path": "reports[0].report.created_at", "transformation": "date_format", "output_path": "ReportDate", "config": {"date_format": "2006-01-02"}}, {"name": "report_title", "description": "direct: Pass-through transformation with no modification", "input_path": "reports[0].report.title", "transformation": "direct", "output_path": "ReportTitle", "config": {}}, {"name": "incident_number", "description": "format: Format report ID as incident number with string pattern", "input_path": "reports[0].report.id", "transformation": "format", "output_path": "IncidentNumber", "config": {"format_pattern": "INC-%s"}}, {"name": "org_id_as_string", "description": "format: Convert org_id to string for lookup compatibility", "input_path": "reports[0].report.org_id", "transformation": "format", "output_path": "OrgIdString", "config": {"format_pattern": "%.0f"}}, {"name": "agency_name_lookup", "description": "lookup: Map org_id to agency name using lookup table", "input_path": "OrgIdString", "transformation": "lookup", "output_path": "AgencyName", "config": {"lookup_table": "agency_names", "default_value": "Unknown Agency"}}, {"name": "person_entities_filter", "description": "filter: Extract person entities with 'equals' operator", "input_path": "entities", "transformation": "filter", "output_path": "PersonEntities", "config": {"field": "entity.entity_type", "operator": "equals", "value": "ENTITY_TYPE_PERSON"}}, {"name": "high_priority_filter", "description": "filter: Extract multiple entity types with 'in' operator", "input_path": "entities", "transformation": "filter", "output_path": "HighPriorityEntities", "config": {"field": "entity.entity_type", "operator": "in", "value": ["ENTITY_TYPE_PERSON", "ENTITY_TYPE_VEHICLE"]}}, {"name": "adult_persons_filter", "description": "filter: Extract adults 18+ with 'greater_than_or_equal' operator", "input_path": "PersonEntities", "transformation": "filter", "output_path": "<PERSON><PERSON><PERSON>s", "config": {"field": "entity.data.age", "operator": "greater_than_or_equal", "value": 18}}, {"name": "male_entities_filter", "description": "filter: Extract male entities with 'contains' operator", "input_path": "PersonEntities", "transformation": "filter", "output_path": "MaleEntities", "config": {"field": "entity.data.gender", "operator": "contains", "value": "Male"}}, {"name": "non_suspects_filter", "description": "filter: Extract entities that are NOT suspects using 'not_equals' operator", "input_path": "PersonEntities", "transformation": "filter", "output_path": "NonSuspects", "config": {"field": "extractionMetadata.context", "operator": "not_equals", "value": "arrestee"}}, {"name": "officer_entities_filter", "description": "filter: Extract entities whose first name starts with specific prefix using 'starts_with' operator", "input_path": "PersonEntities", "transformation": "filter", "output_path": "OfficerEntities", "config": {"field": "entity.data.firstName", "operator": "starts_with", "value": "Detective"}}, {"name": "email_entities_filter", "description": "filter: Extract entities with email addresses ending with specific domain using 'ends_with' operator", "input_path": "PersonEntities", "transformation": "filter", "output_path": "EmailEntities", "config": {"field": "entity.data.email", "operator": "ends_with", "value": "@email.com"}}, {"name": "excluded_entity_types_filter", "description": "filter: Extract entities NOT in specific types using 'not_in' operator", "input_path": "entities", "transformation": "filter", "output_path": "ExcludedEntityTypes", "config": {"field": "entity.entity_type", "operator": "not_in", "value": ["ENTITY_TYPE_UNKNOWN", "ENTITY_TYPE_OTHER"]}}, {"name": "younger_persons_filter", "description": "filter: Extract persons under 30 using 'less_than' operator", "input_path": "PersonEntities", "transformation": "filter", "output_path": "<PERSON><PERSON><PERSON><PERSON>", "config": {"field": "entity.data.age", "operator": "less_than", "value": 30}}, {"name": "senior_persons_filter", "description": "filter: Extract persons 65 and older using 'greater_than' operator", "input_path": "PersonEntities", "transformation": "filter", "output_path": "Senior<PERSON><PERSON>s", "config": {"field": "entity.data.age", "operator": "greater_than", "value": 64}}, {"name": "teenage_persons_filter", "description": "filter: Extract teenagers 18 and under using 'less_than_or_equal' operator", "input_path": "PersonEntities", "transformation": "filter", "output_path": "<PERSON><PERSON><PERSON><PERSON>", "config": {"field": "entity.data.age", "operator": "less_than_or_equal", "value": 18}}, {"name": "persons_with_sequence", "description": "add_sequence: Add NIBRS sequence numbers with custom format", "input_path": "PersonEntities", "transformation": "add_sequence", "output_path": "PersonsWithSequence", "config": {"sequence_field": "SequenceNumber", "start": 1, "format": "%02d"}}, {"name": "entities_sorted_by_age", "description": "sort: Sort entities by age in ascending order", "input_path": "PersonEntities", "transformation": "sort", "output_path": "EntitiesSortedByAge", "config": {"sort_field": "entity.data.age", "sort_order": "asc"}}, {"name": "entities_sorted_by_name_desc", "description": "sort: Sort entities by last name in descending order", "input_path": "PersonEntities", "transformation": "sort", "output_path": "EntitiesSortedByNameDesc", "config": {"sort_field": "entity.data.lastName", "sort_order": "desc"}}, {"name": "entities_grouped_by_type", "description": "group_by: Group entities by their type", "input_path": "entities", "transformation": "group_by", "output_path": "EntitiesGroupedByType", "config": {"group_field": "entity.entity_type"}}, {"name": "victim_genders_lookup_inplace", "description": "lookup: In-place mapping of person genders to NIBRS sex codes", "input_path": "PersonEntities[*].entity.data.gender", "transformation": "lookup", "output_path": "PersonEntities[*].entity.data.gender", "config": {"lookup_table": "sex_codes", "default_value": "U"}}, {"name": "offense_codes_lookup", "description": "lookup: Map report title to NIBRS offense codes", "input_path": "reports[0].report.title", "transformation": "lookup", "output_path": "OffenseUCRCode", "config": {"lookup_table": "offense_mapping", "default_value": "90Z"}}, {"name": "first_names_wildcard", "description": "direct: Extract all entity first names using wildcard", "input_path": "entities[*].entity.data.firstName", "transformation": "direct", "output_path": "AllEntityFirstNames", "config": {}}, {"name": "first_asset_name", "description": "direct: Extract first asset name using array index", "input_path": "assets[0].asset.name", "transformation": "direct", "output_path": "FirstAssetName", "config": {}}, {"name": "extraction_metadata", "description": "direct: Extract global extraction metadata", "input_path": "metadata.extraction_method", "transformation": "direct", "output_path": "ExtractionMethod", "config": {}}, {"name": "relationship_lookup", "description": "lookup: Map relationship types to standardized codes", "input_path": "relationships[*].relation.relation_type", "transformation": "lookup", "output_path": "StandardizedRelationTypes", "config": {"lookup_table": "relationship_codes", "default_value": "OTHER"}}, {"name": "person_race_codes_lookup_inplace", "description": "lookup: In-place mapping of race/ethnicity to NIBRS race codes", "input_path": "entities[?(@.entity.entity_type == 'ENTITY_TYPE_PERSON')].entity.data.race", "transformation": "lookup", "output_path": "entities[?(@.entity.entity_type == 'ENTITY_TYPE_PERSON')].entity.data.race", "config": {"lookup_table": "race_codes", "default_value": "U"}}, {"name": "victim_type_codes_lookup", "description": "lookup: Map entity types to NIBRS victim type codes", "input_path": "entities[*].entity.entity_type", "transformation": "lookup", "output_path": "VictimTypeCodes", "config": {"lookup_table": "victim_types", "default_value": "U"}}], "lookup_tables": {"agency_names": {"1": "Metropolitan Police Department", "2": "State Police Department", "3": "County Sheriff's Office", "4": "University Police Department", "5": "Federal Law Enforcement Agency"}, "sex_codes": {"Male": "M", "Female": "F", "GENDER_MALE": "M", "GENDER_FEMALE": "F", "GENDER_UNKNOWN": "U", "Unknown": "U", "Not Specified": "U", "null": "U", "": "U"}, "race_codes": {"Caucasian": "W", "White": "W", "Black": "B", "African American": "B", "Hispanic": "H", "Latino": "H", "Asian": "A", "American Indian": "I", "Native Hawaiian": "P", "Pacific Islander": "P", "RACE_WHITE": "W", "RACE_BLACK": "B", "RACE_HISPANIC": "H", "RACE_ASIAN": "A", "RACE_AMERICAN_INDIAN": "I", "RACE_PACIFIC_ISLANDER": "P", "RACE_UNKNOWN": "U", "Unknown": "U", "null": "U", "": "U"}, "victim_types": {"ENTITY_TYPE_PERSON": "I", "ENTITY_TYPE_BUSINESS": "B", "ENTITY_TYPE_ORGANIZATION": "G", "ENTITY_TYPE_GOVERNMENT": "G", "ENTITY_TYPE_FINANCIAL_INSTITUTION": "F", "ENTITY_TYPE_VEHICLE": "V", "ENTITY_TYPE_OTHER": "O", "ENTITY_TYPE_SOCIETY": "S", "ENTITY_TYPE_UNKNOWN": "U"}, "offense_mapping": {"Fully Hydrated Test Report": "120", "Burglary Report": "220", "Theft Report": "23A", "Assault Report": "13A", "Armed Robbery": "120", "Vehicle Theft": "240", "Domestic Violence": "13A", "Fraud Report": "26A"}, "relationship_codes": {"RELATION_TYPE_VICTIM_OFFENDER_ST": "VICTIM_OFFENDER", "worked_with": "PROFESSIONAL", "used_in_crime": "CRIME_TOOL", "employs": "EMPLOYMENT", "assigned_to": "ASSIGNMENT", "owns": "OWNERSHIP", "resides_at": "RESIDENCE", "operates": "OPERATION"}}, "validation_rules": {"required_fields": ["ReportDate", "ORI", "IncidentNumber", "IncidentDateTime"], "conditional_requirements": {"victim_segments": {"if_exists": "VictimsWithSequence", "required": ["SequenceNumber"]}}}, "metadata": {"created_by": "Hero ETL System - Compact Test Configuration", "created_date": "2025-07-11", "nibrs_version": "5.0", "data_structure": "Compact configuration for testing all transformation features", "purpose": "Produces minimal output while testing every transformation type and filter operator", "transformation_types_showcased": ["direct - Pass-through transformation", "lookup - Value mapping with lookup tables (including in-place transformations)", "filter - Array filtering with all operators", "format - String formatting with printf patterns", "date_format - Date/time format conversion", "add_sequence - Sequence numbering with custom formats", "group_by - Array grouping by field values", "sort - Array sorting ascending and descending"], "filter_operators_showcased": ["equals, not_equals, contains, starts_with, ends_with", "in, not_in, greater_than, greater_than_or_equal", "less_than, less_than_or_equal"], "output_fields_count": 28, "key_features": ["Tests all transformation types with minimal data", "Covers all filter operators comprehensively", "Includes wildcard paths and array indexing", "Validates all 5 lookup tables with in-place and new field transformations", "Compact output for easy verification"]}}