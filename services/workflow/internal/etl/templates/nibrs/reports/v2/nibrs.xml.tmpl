<?xml version="1.0" encoding="UTF-8"?>
{{- /* NIBRS XML Template - Using only core extraction data: assets, entities, relationships, reports, sections, situations */ -}}
<nibrs:Submission xmlns:nibrs="http://fbi.gov/cjis/nibrs/4.1">
  {{- /* Header with basic info and date formatting */ -}}
  <nibrs:ReportHeader>
    {{- /* Extract report date from first report */ -}}
    {{- if .reports}}
    {{- with index .reports 0}}
    <nibrs:ReportDate>{{.report.created_at | formatDate "2006-01-02" | default "2024-01-01"}}</nibrs:ReportDate>
    {{- end}}
    {{- else}}
    <nibrs:ReportDate>2024-01-01</nibrs:ReportDate>
    {{- end}}
    <nibrs:ORI>XX0000000</nibrs:ORI>
    {{- /* Extract agency name from org or use default */ -}}
    <nibrs:AgencyName>Metropolitan Police Department</nibrs:AgencyName>
  </nibrs:ReportHeader>
  
  <nibrs:Group-A-Incident-Report>
    {{- /* Administrative info from reports and situations */ -}}
    <nibrs:Administrative-Segment>
      <nibrs:ORI>XX0000000</nibrs:ORI>
      {{- if .reports}}
      {{- with index .reports 0}}
      <nibrs:Incident-Number>INC-{{.report.id}}</nibrs:Incident-Number>
      {{- end}}
      {{- else}}
      <nibrs:Incident-Number>0000-000</nibrs:Incident-Number>
      {{- end}}
      {{- /* Get incident time from first situation */ -}}
      {{- if .situations}}
      {{- with index .situations 0}}
      {{- if .situation}}
      {{- if .situation.incident_time}}
      <nibrs:Incident-Date-Hour>{{.situation.incident_time | formatDate "2006-01-02T15:04:05"}}</nibrs:Incident-Date-Hour>
      {{- end}}
      {{- end}}
      {{- end}}
      {{- end}}
    </nibrs:Administrative-Segment>
    
    {{- /* Process person entities from core entities array */ -}}
    {{- range $index, $item := .entities}}
    {{- if eq .entity.entity_type "ENTITY_TYPE_PERSON"}}
    <nibrs:Person-Segment>
      <nibrs:Person-Sequence-Number>{{printf "%02d" (add $index 1)}}</nibrs:Person-Sequence-Number>
      <nibrs:Person-Type>{{.entity.entity_type}}</nibrs:Person-Type>
      
      {{- /* Basic person data */ -}}
      {{- if .entity.data}}
      <nibrs:Name>{{.entity.data.firstName | upper}} {{.entity.data.lastName | upper}}</nibrs:Name>
      <nibrs:Age>{{.entity.data.age | default "00"}}</nibrs:Age>
      <nibrs:Sex>{{.entity.data.gender | default "U"}}</nibrs:Sex>
      <nibrs:Race>{{.entity.data.race | default "U"}}</nibrs:Race>
      
      {{- /* Showcase lower and trim functions */ -}}
      {{- if .entity.data.email}}
      <nibrs:Email>{{.entity.data.email | lower | trim}}</nibrs:Email>
      {{- end}}
      {{- end}}
      
      {{- /* Tags */ -}}
      {{- if .entity.tags}}
      <nibrs:Tags>{{range $i, $tag := .entity.tags}}{{if $i}}, {{end}}{{$tag | upper}}{{end}}</nibrs:Tags>
      {{- end}}
    </nibrs:Person-Segment>
    {{- end}}
    {{- end}}
    
    {{- /* Extract offense information from sections */ -}}
    {{- range .sections}}
    {{- if or (eq .section.section_type "SECTION_TYPE_OFFENSE") (eq .section.section_type "SECTION_TYPE_CHARGE")}}
    <nibrs:Offense-Segment>
      <nibrs:Offense-Code>{{.section.data.offense_code | default "99"}}</nibrs:Offense-Code>
      <nibrs:Status>{{.section.data.status | upper | default "UNKNOWN"}}</nibrs:Status>
    </nibrs:Offense-Segment>
    {{- end}}
    {{- end}}
    
    {{- /* Extract property information from assets */ -}}
    {{- if .assets}}
    {{- with index .assets 0}}
    <nibrs:Property-Segment>
      <nibrs:Loss-Type>{{.asset.data.loss_type | default "0"}}</nibrs:Loss-Type>
    </nibrs:Property-Segment>
    {{- end}}
    {{- end}}
    
    {{- /* Summary section */ -}}
    <nibrs:Summary>
      <nibrs:TotalPersons>{{len .entities}}</nibrs:TotalPersons>
      <nibrs:GeneratedAt>{{now | formatDate "2006-01-02T15:04:05Z"}}</nibrs:GeneratedAt>
      <nibrs:Status>
        {{- if .entities -}}
        CONTAINS_DATA
        {{- else -}}
        EMPTY
        {{- end -}}
      </nibrs:Status>
    </nibrs:Summary>
  </nibrs:Group-A-Incident-Report>
</nibrs:Submission>