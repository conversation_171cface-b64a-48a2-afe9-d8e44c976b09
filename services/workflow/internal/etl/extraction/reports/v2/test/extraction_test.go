package test

import (
	"context"
	"database/sql"
	"fmt"
	"net/http"
	"os"
	"strings"
	"testing"
	"time"

	assets "proto/hero/assets/v2"
	assetConnect "proto/hero/assets/v2/assetsconnect"
	entity "proto/hero/entity/v1"
	entityConnect "proto/hero/entity/v1/entityconnect"
	reports "proto/hero/reports/v2"
	reportsConnect "proto/hero/reports/v2/reportsconnect"
	situations "proto/hero/situations/v2"
	situationsConnect "proto/hero/situations/v2/situationsconnect"

	"connectrpc.com/connect"
	"google.golang.org/protobuf/types/known/structpb"

	assetsRepository "workflow/internal/assets/data"
	entityRepository "workflow/internal/entity/data"
	extractionV2 "workflow/internal/etl/extraction/reports/v2"
	reportsRepository "workflow/internal/reports/data"
	situationsRepository "workflow/internal/situations/data"

	_ "github.com/lib/pq"
)

const ServiceBaseURL = "http://localhost:9086"

type AuthTransport struct {
	base http.RoundTripper
}

func (t *AuthTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	token, err := getAccessToken()
	if err != nil {
		fmt.Printf("Warning: Could not get access token: %v\n", err)
	} else {
		req.Header.Set("Authorization", "Bearer "+token)
	}

	if t.base == nil {
		return http.DefaultTransport.RoundTrip(req)
	}

	return t.base.RoundTrip(req)
}

func getAccessToken() (string, error) {
	tokenBytes, err := os.ReadFile("token.txt")
	if err != nil {
		return "", err
	}

	token := strings.TrimSpace(string(tokenBytes))
	//nolint:gosec // This is a placeholder string for test configuration, not a real credential
	if token == "" || token == "PUT_YOUR_COGNITO_ACCESS_TOKEN_HERE" {
		return "", fmt.Errorf("token.txt is empty or contains placeholder")
	}

	return token, nil
}

func AddAuthHeader(client *http.Client) {
	client.Transport = &AuthTransport{base: client.Transport}
}

func TestETLExtraction_ActualExtraction(t *testing.T) {
	t.Log("\n=== Testing ReportDataExtractor.Extract() ===\n")

	// Setup HTTP client with auth
	httpClient := http.DefaultClient
	httpClient.Timeout = 30 * time.Second
	AddAuthHeader(httpClient)

	ctx := context.Background()

	// Create service clients
	reportsClient := reportsConnect.NewReportServiceClient(httpClient, ServiceBaseURL)
	assetsClient := assetConnect.NewAssetRegistryServiceClient(httpClient, ServiceBaseURL)
	situationsClient := situationsConnect.NewSituationServiceClient(httpClient, ServiceBaseURL)
	entityClient := entityConnect.NewEntityServiceClient(httpClient, ServiceBaseURL)

	// Track created resources for cleanup
	var createdAssetIDs []string
	var createdSituationIDs []string
	var createdEntityIDs []string
	var createdReportIDs []string

	// Cleanup function
	defer func() {
		t.Log("\n=== Cleaning up test data ===")

		// Delete reports
		for _, id := range createdReportIDs {
			delReq := &reports.DeleteReportRequest{Id: id}
			_, err := reportsClient.DeleteReport(ctx, connect.NewRequest(delReq))
			if err != nil {
				t.Logf("cleanup: failed to delete report %s: %v", id, err)
			}
		}

		// Delete entities (skip if delete method doesn't exist)
		for _, id := range createdEntityIDs {
			t.Logf("Would delete entity %s (delete method may not be implemented)", id)
			// Skip entity deletion as the API may not have a delete method
		}

		// Delete situations
		for _, id := range createdSituationIDs {
			delReq := &situations.DeleteSituationRequest{Id: id}
			_, err := situationsClient.DeleteSituation(ctx, connect.NewRequest(delReq))
			if err != nil {
				t.Logf("cleanup: failed to delete situation %s: %v", id, err)
			}
		}

		// Delete assets
		for _, id := range createdAssetIDs {
			delReq := &assets.DeleteAssetRequest{Id: id}
			_, err := assetsClient.DeleteAsset(ctx, connect.NewRequest(delReq))
			if err != nil {
				t.Logf("cleanup: failed to delete asset %s: %v", id, err)
			}
		}

		t.Log("=== Cleanup completed ===")
	}()

	// Step 1: Create test asset
	t.Log("Step 1: Creating test asset...")
	assetReq := &assets.CreateAssetRequest{
		Asset: &assets.Asset{
			Type:   assets.AssetType_ASSET_TYPE_TEST,
			Status: assets.AssetStatus_ASSET_STATUS_AVAILABLE,
			Name:   "ETL Test User",
			OrgId:  1,
		},
	}

	assetResp, err := assetsClient.CreateAsset(ctx, connect.NewRequest(assetReq))
	if err != nil {
		t.Fatalf("Failed to create test asset: %v", err)
	}
	assetID := assetResp.Msg.Asset.Id
	createdAssetIDs = append(createdAssetIDs, assetID)

	// Step 2: Create test situation
	t.Log("Step 2: Creating test situation...")
	situationReq := &situations.CreateSituationRequest{
		Situation: &situations.Situation{
			Title:        "ETL Test Incident",
			Description:  "Test incident for ETL extraction",
			Type:         situations.SituationType_SITUATION_TYPE_THEFT,
			Status:       situations.SituationStatus_SITUATION_STATUS_CREATED,
			Priority:     3,
			ReporterId:   assetID,
			IncidentTime: "2024-07-01T10:00:00Z",
		},
	}

	situationResp, err := situationsClient.CreateSituation(ctx, connect.NewRequest(situationReq))
	if err != nil {
		t.Fatalf("Failed to create test situation: %v", err)
	}
	situationID := situationResp.Msg.Situation.Id
	createdSituationIDs = append(createdSituationIDs, situationID)

	// Step 3: Create test entities
	t.Log("Step 3: Creating test entities...")

	personData, _ := structpb.NewStruct(map[string]interface{}{
		"first_name": "John",
		"last_name":  "Doe",
		"age":        30,
	})

	entityReq := &entity.CreateEntityRequest{
		Entity: &entity.Entity{
			SchemaId: "person",
			Data:     personData,
		},
	}

	entityResp, err := entityClient.CreateEntity(ctx, connect.NewRequest(entityReq))
	if err != nil {
		t.Fatalf("Failed to create test entity: %v", err)
	}
	entityID := entityResp.Msg.Entity.Id
	createdEntityIDs = append(createdEntityIDs, entityID)

	// Step 4: Create test report with sections
	t.Log("Step 4: Creating test report...")

	sections := []*reports.ReportSection{
		{
			Type: reports.SectionType_SECTION_TYPE_NARRATIVE,
			Content: &reports.ReportSection_Narrative{
				Narrative: &reports.NarrativeContent{
					RichText: "This is a test narrative for ETL extraction.",
				},
			},
		},
		{
			Type: reports.SectionType_SECTION_TYPE_INCIDENT_DETAILS,
			Content: &reports.ReportSection_IncidentDetails{
				IncidentDetails: &reports.IncidentDetailsContent{},
			},
		},
	}

	reportReq := &reports.CreateReportRequest{
		Report: &reports.Report{
			Title:            "ETL Extraction Test Report",
			CreatedByAssetId: assetID,
			SituationId:      situationID,
			Status:           reports.ReportStatus_REPORT_STATUS_IN_PROGRESS,
			Sections:         sections,
		},
	}

	reportResp, err := reportsClient.CreateReport(ctx, connect.NewRequest(reportReq))
	if err != nil {
		t.Fatalf("Failed to create test report: %v", err)
	}
	reportID := reportResp.Msg.Report.Id
	createdReportIDs = append(createdReportIDs, reportID)

	// Step 5: Set up database connection and repositories
	t.Log("Step 5: Setting up extractor...")

	databaseURL := os.Getenv("DATABASE_URL")
	if databaseURL == "" {
		databaseURL = "postgres://user:password@localhost/dbname?sslmode=disable"
	}

	db, err := sql.Open("postgres", databaseURL)
	if err != nil {
		t.Fatalf("Failed to connect to database: %v", err)
	}
	defer db.Close()

	// Initialize repositories
	reportsRepo, _, err := reportsRepository.NewReportRepository(db)
	if err != nil {
		t.Fatalf("Failed to initialize reports repository: %v", err)
	}

	entityRepo, _, err := entityRepository.NewEntityRepository(db)
	if err != nil {
		t.Fatalf("Failed to initialize entity repository: %v", err)
	}

	assetsRepo, _, err := assetsRepository.NewAssetRepository(db)
	if err != nil {
		t.Fatalf("Failed to initialize assets repository: %v", err)
	}

	situationsRepo, _, err := situationsRepository.NewSituationRepository(db)
	if err != nil {
		t.Fatalf("Failed to initialize situations repository: %v", err)
	}

	// Step 6: Initialize and test the extractor
	t.Log("Step 6: Testing ReportDataExtractor.Extract()...")

	extractor, err := extractionV2.NewReportDataExtractor(
		db, reportsRepo, situationsRepo, assetsRepo, entityRepo,
	)
	if err != nil {
		t.Fatalf("Failed to create extractor: %v", err)
	}

	// THE ACTUAL TEST - Extract data from the report using typed parallel extraction
	extractionResult, err := extractor.Extract(ctx, reportID)
	if err != nil {
		t.Fatalf("EXTRACTION FAILED: %v", err)
	}

	// Step 7: Verify extraction results
	t.Log("Step 7: Verifying extraction results...")

	if extractionResult == nil {
		t.Fatal("FAILED: Extraction result is nil")
	}

	if !extractionResult.Success {
		t.Fatal("FAILED: Extraction was not successful")
	}

	extractedData := extractionResult.Data
	if extractedData == nil {
		t.Fatal("FAILED: Extracted data is nil")
	}

	// Verify typed extraction results
	totalItems := extractedData.GetTotalItemCount()
	if totalItems == 0 {
		t.Fatal("FAILED: No data extracted")
	}

	t.Logf("✓ SUCCESS: Extracted %d total items", totalItems)

	// Verify specific typed data exists
	if len(extractedData.Reports) > 0 {
		t.Logf("✓ Reports extracted: %d items", len(extractedData.Reports))
		// Verify the report has hydration metadata
		if extractedData.Reports[0].HydrationMetadata != nil {
			t.Log("✓ Report has hydration metadata")
		}
	} else {
		t.Error("❌ FAILED: No reports extracted")
	}

	if len(extractedData.Assets) > 0 {
		t.Logf("✓ Assets extracted: %d items", len(extractedData.Assets))
		// Verify assets have role context
		for _, asset := range extractedData.Assets {
			if asset.Role != "" {
				t.Logf("✓ Asset has role context: %s", asset.Role)
				break
			}
		}
	} else {
		t.Log("⚠ No assets extracted (may be optional)")
	}

	if len(extractedData.Entities) > 0 {
		t.Logf("✓ Entities extracted: %d items", len(extractedData.Entities))
	} else {
		t.Log("⚠ No entities extracted (may be optional)")
	}

	if len(extractedData.Sections) > 0 {
		t.Logf("✓ Sections extracted: %d items", len(extractedData.Sections))
	} else {
		t.Log("⚠ No sections extracted (may be optional)")
	}

	if len(extractedData.Relationships) > 0 {
		t.Logf("✓ Relationships extracted: %d items", len(extractedData.Relationships))
	} else {
		t.Log("⚠ No relationships extracted (may be optional)")
	}

	if len(extractedData.Situations) > 0 {
		t.Logf("✓ Situations extracted: %d items", len(extractedData.Situations))
	} else {
		t.Log("⚠ No situations extracted (may be optional)")
	}

	// Verify extraction metrics
	if extractionResult.Metrics != nil {
		t.Logf("✓ Extraction metrics available - Duration: %v", extractionResult.Metrics.TotalDuration)
		t.Logf("✓ Items processed: %v", extractionResult.Metrics.ItemsProcessed)
	}

	// Verify warnings
	if len(extractionResult.Warnings) > 0 {
		t.Logf("⚠ Extraction warnings: %d", len(extractionResult.Warnings))
		for _, warning := range extractionResult.Warnings {
			t.Logf("  Warning: %s - %s", warning.Code, warning.Message)
		}
	}

	t.Logf("\n=== EXTRACTION TEST COMPLETED SUCCESSFULLY ===")
	t.Logf("✓ Created and extracted data from report: %s", reportID)
	t.Logf("✓ Extraction method: %s", extractedData.Metadata["extractionMethod"])
	t.Logf("✓ Total items extracted: %d", totalItems)
	t.Logf("✓ Extraction duration: %v", extractionResult.Metrics.TotalDuration)
}
