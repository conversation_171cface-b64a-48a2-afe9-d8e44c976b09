#!/bin/bash

# ETL Extraction Test Runner
# Usage: ./run_tests.sh [extraction|cleanup] [nocache]

# Default test type
TEST_TYPE="extraction"
NO_CACHE=""

# Parse command line arguments
if [ "$1" == "extraction" ]; then
  TEST_TYPE="extraction"
  echo "Running ETL extraction tests..."
elif [ "$1" == "cleanup" ]; then
  TEST_TYPE="cleanup"
  echo "Running cleanup to delete test data..."
elif [ "$1" == "help" ] || [ "$1" == "-h" ] || [ "$1" == "--help" ]; then
  echo "ETL Extraction Test Runner"
  echo "========================="
  echo ""
  echo "Usage: $0 [test-type] [options]"
  echo ""
  echo "Test Types:"
  echo "  extraction       Run ETL extraction tests (default)"
  echo "  cleanup         Remove all test data from database"
  echo ""
  echo "Options:"
  echo "  nocache         Bypass Go test cache (-count=1)"
  echo ""
  echo "Examples:"
  echo "  $0                    # Run extraction tests"
  echo "  $0 extraction         # Run extraction tests"
  echo "  $0 cleanup           # Clean up test data"
  echo "  $0 extraction nocache # Run tests bypassing cache"
  echo ""
  echo "Prerequisites:"
  echo "  - Token file: token.txt (contains COGNITO_ACCESS_TOKEN)"
  echo "  - Working directory: services/workflow/"
  echo "  - Services running on http://localhost:9086"
  echo ""
  exit 0
else
  echo "Unknown test type: $1"
  echo "Usage: $0 [extraction|cleanup] [nocache]"
  echo "Use '$0 help' for detailed information."
  exit 1
fi

# Check for nocache argument
if [ "$2" == "nocache" ]; then
  NO_CACHE="-count=1"
  echo "Bypassing test cache..."
fi

# Get directory of this script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
TOKEN_FILE="$SCRIPT_DIR/token.txt"

# Check if token file exists
if [ ! -f "$TOKEN_FILE" ]; then
  echo "Error: Token file '$TOKEN_FILE' not found."
  echo "Please create token.txt with your COGNITO_ACCESS_TOKEN"
  exit 1
fi

# Read the token from the file
TOKEN=$(cat "$TOKEN_FILE" | tr -d '\n\r ')

# Verify token is not empty
if [ -z "$TOKEN" ]; then
  echo "Error: No token found in '$TOKEN_FILE'."
  exit 1
fi

# Export the token as environment variable
export COGNITO_ACCESS_TOKEN="$TOKEN"

# Change to the workflow directory
cd "$SCRIPT_DIR/../../../../"

# Run the tests
case "$TEST_TYPE" in
  extraction)
    go test -v -run "^TestETLExtraction_" $NO_CACHE ./internal/etl/extraction/test -timeout=300s
    ;;
  cleanup)
    go test -v -run "^TestCleanupETLTestData$" $NO_CACHE ./internal/etl/extraction/test
    ;;
esac