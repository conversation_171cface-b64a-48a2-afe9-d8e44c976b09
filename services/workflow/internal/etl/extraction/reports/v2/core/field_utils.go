package core

import (
	"encoding/json"
	"strings"
	"workflow/internal/etl/extraction/types"
)

// Field Name Standardization Utilities
//
// This file provides comprehensive field name standardization functions for the ETL extraction pipeline.
// The primary goal is to ensure consistent camelCase naming across all extracted data, regardless of
// the original source format (snake_case, PascalCase, etc.).
//
// Key Functions:
//   - convertSnakeCaseToCamelCase: Transforms snake_case → camelCase
//   - convertPascalCaseToCamelCase: Transforms PascalCase → camelCase
//   - standardizeAllFieldNamesRecursively: Recursively standardizes nested structures
//   - StandardizeExtractedDataViaJSON: Complete standardization via JSO<PERSON> marshaling
//
// Usage in ETL Pipeline:
//   The StandardizeExtractedDataViaJSON function is typically called as the final step
//   in the extraction process to ensure all field names are consistently formatted
//   before being passed to the transformation layer.

// convertSnakeCaseToCamelCase transforms snake_case strings to camelCase format.
// This is the primary conversion function for standardizing field names.
//
// Examples:
//   - "first_name" → "firstName"
//   - "created_at" → "createdAt"
//   - "simple" → "simple" (no change needed)
//   - "" → "" (empty strings remain empty)
//
// Parameters:
//   - snakeCaseString: The snake_case string to convert
//
// Returns:
//   - string: The converted camelCase string
func convertSnakeCaseToCamelCase(snakeCaseString string) string {
	if snakeCaseString == "" {
		return snakeCaseString
	}

	// Split the string by underscores to get individual words
	wordParts := strings.Split(snakeCaseString, "_")
	if len(wordParts) == 1 {
		// No underscores found, return as-is
		return snakeCaseString
	}

	// First word stays lowercase (camelCase convention)
	convertedString := strings.ToLower(wordParts[0])

	// Capitalize the first letter of each subsequent word
	for wordIndex := 1; wordIndex < len(wordParts); wordIndex++ {
		currentWord := wordParts[wordIndex]
		if currentWord != "" {
			// Capitalize first letter, lowercase the rest
			convertedString += strings.ToUpper(currentWord[:1]) + strings.ToLower(currentWord[1:])
		}
	}

	return convertedString
}

// convertPascalCaseToCamelCase transforms PascalCase strings to camelCase format.
// This handles cases where field names start with capital letters.
//
// Examples:
//   - "FirstName" → "firstName"
//   - "ID" → "iD"
//   - "simple" → "simple" (already camelCase)
//   - "" → "" (empty strings remain empty)
//
// Parameters:
//   - pascalCaseString: The PascalCase string to convert
//
// Returns:
//   - string: The converted camelCase string
func convertPascalCaseToCamelCase(pascalCaseString string) string {
	if pascalCaseString == "" {
		return pascalCaseString
	}
	// Convert first character to lowercase, keep rest as-is
	return strings.ToLower(pascalCaseString[:1]) + pascalCaseString[1:]
}

// standardizeAllFieldNamesRecursively applies camelCase conversion to all field names
// in a nested data structure (maps, slices, and primitive values).
//
// This function recursively traverses the entire data structure and converts:
// - snake_case field names to camelCase
// - PascalCase field names to camelCase
// - Nested objects and arrays are processed recursively
//
// Parameters:
//   - dataStructure: The data structure to standardize (interface{} to handle any type)
//
// Returns:
//   - interface{}: The standardized data structure with camelCase field names
func standardizeAllFieldNamesRecursively(dataStructure interface{}) interface{} {
	switch typedData := dataStructure.(type) {
	case map[string]interface{}:
		// Handle object/map case - convert all field names
		standardizedMap := make(map[string]interface{})
		for originalFieldName, fieldValue := range typedData {
			// Apply PascalCase to camelCase conversion first
			convertedFieldName := convertPascalCaseToCamelCase(originalFieldName)
			// Then apply snake_case to camelCase conversion
			convertedFieldName = convertSnakeCaseToCamelCase(convertedFieldName)
			// Recursively standardize nested values
			standardizedMap[convertedFieldName] = standardizeAllFieldNamesRecursively(fieldValue)
		}
		return standardizedMap
	case []interface{}:
		// Handle array/slice case - process each element
		standardizedSlice := make([]interface{}, len(typedData))
		for elementIndex, elementValue := range typedData {
			standardizedSlice[elementIndex] = standardizeAllFieldNamesRecursively(elementValue)
		}
		return standardizedSlice
	default:
		// Handle primitive values (strings, numbers, booleans, etc.) - return as-is
		return dataStructure
	}
}

// standardizeExtractedDataViaJSON provides comprehensive field name standardization
// by converting the data structure to JSON and back, ensuring all nested fields
// are properly standardized to camelCase.
//
// This is the most thorough approach for standardization as it handles:
// - Complex nested structures
// - Protobuf-generated content that bypasses normal standardization
// - Any data type that can be JSON-marshaled
//
// Process:
// 1. Marshal the ExtractedData to JSON
// 2. Parse JSON to generic interface{} for manipulation
// 3. Apply recursive field name standardization
// 4. Marshal back to JSON with standardized field names
// 5. Unmarshal to ExtractedData structure
//
// Parameters:
//   - extractedData: The ExtractedData structure to standardize
//
// Returns:
//   - *types.ExtractedData: The standardized data structure
//   - If any step fails, returns the original data unchanged
func StandardizeExtractedDataViaJSON(extractedData *types.ExtractedData) *types.ExtractedData {
	// Step 1: Convert ExtractedData to JSON
	jsonBytes, marshalError := json.Marshal(extractedData)
	if marshalError != nil {
		// If marshaling fails, return original data
		return extractedData
	}

	// Step 2: Parse JSON to generic interface for field manipulation
	var genericDataStructure interface{}
	if unmarshalError := json.Unmarshal(jsonBytes, &genericDataStructure); unmarshalError != nil {
		// If unmarshaling fails, return original data
		return extractedData
	}

	// Step 3: Apply comprehensive field name standardization
	standardizedDataStructure := standardizeAllFieldNamesRecursively(genericDataStructure)

	// Step 4: Convert standardized data back to JSON
	standardizedJsonBytes, marshalError := json.Marshal(standardizedDataStructure)
	if marshalError != nil {
		// If marshaling fails, return original data
		return extractedData
	}

	// Step 5: Convert JSON back to ExtractedData structure
	var standardizedExtractedData types.ExtractedData
	if unmarshalError := json.Unmarshal(standardizedJsonBytes, &standardizedExtractedData); unmarshalError != nil {
		// If unmarshaling fails, return original data
		return extractedData
	}

	return &standardizedExtractedData
}
