// Package core provides essential constants, utilities, and data conversion functions
// for the ETL (Extract, Transform, Load) extraction pipeline.
//
// This package centralizes all string literals, field names, data types, and configuration
// values used throughout the extraction process to ensure consistency and prevent typos.
//
// Key Components:
//   - Data type constants: Define the different types of data being extracted (reports, assets, etc.)
//   - Field name constants: Standardized field names used across all extracted data structures
//   - Dataset key constants: Keys used to organize extracted data into logical groups
//   - Error message constants: Consistent error messages for common failure scenarios
//   - Configuration constants: Default values and limits for extraction operations
//
// Usage Example:
//
//	dataItem := map[string]interface{}{
//	    Type: core.DataTypeReport,
//	    Attributes: map[string]interface{}{
//	        core.FieldReportID: "12345",
//	        core.FieldReportTitle: "Incident Report",
//	    },
//	}
package core

// ETL Extraction Constants
//
// This file contains all constants used throughout the ETL extraction pipeline
// to ensure consistency, prevent magic strings, and improve code maintainability.
//
// Constants are organized by functional area:
// - Data Types: Categories of extracted data
// - Field Names: Standardized attribute names
// - Dataset Keys: Organization keys for extracted data
// - Error Messages: Consistent error handling
// - Configuration: Default values and limits

// Data type constants for different kinds of extracted data
const (
	// DataTypeReport represents the main report data type
	DataTypeReport = "report"

	// DataTypeAsset represents asset data type (people, organizations, etc.)
	DataTypeAsset = "asset"

	// DataTypeSituation represents situation data type
	DataTypeSituation = "situation"

	// DataTypeEntity represents entity data type (people, vehicles, properties, organizations)
	DataTypeEntity = "entity"

	// DataTypeSection represents report section data type
	DataTypeSection = "section"

	// DataTypeRelation represents relationship data type
	DataTypeRelation = "relation"

	// DataTypeSectionMetadata represents section metadata data type
	DataTypeSectionMetadata = "section_metadata"
)

// Field name constants for extracted data attributes
const (
	// Report-related field names
	FieldReportID               = "report_id"
	FieldReportTitle            = "title"
	FieldReportStatus           = "status"
	FieldReportType             = "type"
	FieldReportVersion          = "version"
	FieldReportCreatedAt        = "created_at"
	FieldReportUpdatedAt        = "updated_at"
	FieldReportCreatedByAssetID = "created_by_asset_id"
	FieldReportAuthorAssetID    = "author_asset_id"
	FieldReportSituationID      = "situation_id"

	// Asset-related field names
	FieldAssetID             = "asset_id"
	FieldAssetName           = "name"
	FieldAssetType           = "type"
	FieldAssetStatus         = "status"
	FieldAssetOrganizationID = "organization_id"
	FieldAssetCreatedAt      = "created_at"
	FieldAssetUpdatedAt      = "updated_at"

	// Situation-related field names
	FieldSituationID          = "situation_id"
	FieldSituationTitle       = "title"
	FieldSituationDescription = "description"
	FieldSituationStatus      = "status"
	FieldSituationPriority    = "priority"
	FieldSituationCreatedAt   = "created_at"
	FieldSituationUpdatedAt   = "updated_at"

	// Section-related field names
	FieldSectionID       = "section_id"
	FieldSectionType     = "section_type"
	FieldSectionTitle    = "title"
	FieldSectionContent  = "content"
	FieldSectionPosition = "position"

	// Entity-related field names
	FieldEntityID        = "entity_id"
	FieldEntityType      = "entity_type"
	FieldEntityName      = "name"
	FieldEntityStatus    = "status"
	FieldEntityCreatedAt = "created_at"
	FieldEntityUpdatedAt = "updated_at"

	// Relationship-related field names
	FieldRelationshipID        = "relationship_id"
	FieldRelationshipType      = "relationship_type"
	FieldRelationshipSource    = "source"
	FieldRelationshipTarget    = "target"
	FieldRelationshipCreatedAt = "created_at"

	// Metadata field names
	FieldMetadataExtractedAt    = "extracted_at"
	FieldMetadataDataType       = "data_type"
	FieldMetadataSourceTable    = "source_table"
	FieldMetadataProcessingTime = "processing_time"
)

// Data extraction configuration constants
const (
	// DefaultExtractionBatchSize is the default number of records to process in a single batch
	DefaultExtractionBatchSize = 100

	// MaxNestedLevelDepth is the maximum depth for recursive data extraction
	MaxNestedLevelDepth = 10

	// DefaultTimeoutSeconds is the default timeout for database operations
	DefaultTimeoutSeconds = 30
)

// Error message constants for consistent error handling
const (
	ErrorMsgDatabaseConnectionRequired = "database connection is required"
	ErrorMsgReportsRepositoryRequired  = "reports repository is required"
	ErrorMsgEntityRepositoryRequired   = "entity repository is required"
	ErrorMsgReportNotFound             = "report not found"
	ErrorMsgInvalidReportID            = "invalid report ID"
	ErrorMsgExtractionFailed           = "data extraction failed"
	ErrorMsgStandardizationFailed      = "field name standardization failed"
)

// Dataset key constants for organizing extracted data
const (
	// DatasetKeyReportMain contains the main report information
	DatasetKeyReportMain = "report_main"

	// DatasetKeyReportSections contains all report sections
	DatasetKeyReportSections = "report_sections"

	// DatasetKeyReportAssets contains associated assets
	DatasetKeyReportAssets = "report_assets"

	// DatasetKeyReportSituations contains linked situations
	DatasetKeyReportSituations = "report_situations"

	// DatasetKeyReportEntities contains referenced entities
	DatasetKeyReportEntities = "report_entities"

	// DatasetKeyReportRelationships contains relationships between objects
	DatasetKeyReportRelationships = "report_relationships"
)

// Section type constants for report sections
const (
	SectionTypeNarrative              = "narrative"
	SectionTypeIncidentDetails        = "incident_details"
	SectionTypeEntityListPeople       = "entity_list_people"
	SectionTypeEntityListVehicle      = "entity_list_vehicle"
	SectionTypeEntityListProperty     = "entity_list_property"
	SectionTypeEntityListOrganization = "entity_list_organization"
	SectionTypeOffense                = "offense"
	SectionTypeArrest                 = "arrest"
	SectionTypeMedia                  = "media"
)

// Role constants for different entity and asset roles
const (
	RoleCreator  = "creator"
	RoleAuthor   = "author"
	RoleWatcher  = "watcher"
	RoleVictim   = "victim"
	RoleWitness  = "witness"
	RoleArrester = "arrester"
	RoleSuspect  = "suspect"
	RoleOfficer  = "officer"
)

// Data field name constants for enrichment
const (
	FieldCreatedByAssetData = "created_by_asset_data"
	FieldAuthorAssetData    = "author_asset_data"
	FieldSituationData      = "situation_data"
	FieldEntityData         = "entity_data"
	FieldAssetData          = "asset_data"
	FieldRole               = "role"
)

// Extraction method constants
const (
	ExtractionMethodHydrated            = "hydrated_structure"
	ExtractionMethodComprehensive       = "comprehensive_with_hydration"
	ExtractionMethodSectionMetadata     = "section_metadata"
	ExtractionMethodSectionEntityRefs   = "section_entity_refs"
	ExtractionMethodReportSituationLink = "report_situation_link"
	ExtractionMethodParallel            = "parallel"
)

// Entity ID field patterns
const (
	FieldArresteeID = "arresteeId"
	FieldVictimID   = "victimId"
	FieldSuspectID  = "suspectId"
	FieldOfficerID  = "officerId"
)

// Entity data field patterns (for hydration)
const (
	FieldArresteeData = "arresteeData"
	FieldVictimData   = "victimData"
	FieldSuspectData  = "suspectData"
	FieldOfficerData  = "officerData"
)

// Content structure field names
const (
	FieldContent         = "Content"
	FieldEntityList      = "EntityList"
	FieldIncidentDetails = "IncidentDetails"
	FieldOffenseList     = "OffenseList"
	FieldArrestList      = "ArrestList"
	FieldEntityRefs      = "entity_refs"
	FieldResponders      = "responders"
	FieldReportingPerson = "reporting_person"
	FieldOffenses        = "offenses"
	FieldArrests         = "arrests"
	FieldData            = "data"
)

// Dataset key names for extraction results
const (
	DatasetKeyReport             = "report"
	DatasetKeySections           = "sections"
	DatasetKeyAssets             = "assets"
	DatasetKeyEntities           = "entities"
	DatasetKeyRelationships      = "relationships"
	DatasetKeyConnectedSituation = "connected_situation"
)

// Common field names that appear frequently
const (
	FieldReferencedInSectionID   = "referenced_in_section_id"
	FieldReferencedInSectionType = "referenced_in_section_type"
	FieldAssetIDField            = "asset_id"
	FieldSections                = "sections"
	FieldDescription             = "description"
	FieldFromObject              = "from_object"
	FieldToObject                = "to_object"
	FieldError                   = "error"
	FieldExtractedFrom           = "extracted_from"
	FieldExtractionError         = "extraction_error"
	FieldExtractionMethod        = "extraction_method"
	FieldHydrated                = "hydrated"
	FieldID                      = "id"
	FieldType                    = "type"
	FieldName                    = "name"
	FieldComments                = "comments"
	FieldCreatedAt               = "created_at"
	FieldUpdatedAt               = "updated_at"
	FieldValue                   = "value"
)
