package core

import (
	"fmt"
	"reflect"
	"strings"

	"google.golang.org/protobuf/types/known/structpb"
)

// ConvertToTemplateData transforms raw data structures into standardized DataItem format.
//
// This function is the primary entry point for converting various data types (structs, primitives,
// protobuf messages) into a consistent DataItem structure that can be used throughout the ETL pipeline.
//
// Key Features:
//   - Automatic struct-to-map conversion with field name standardization
//   - Protobuf message handling with proper type conversion
//   - ID extraction from common ID fields
//   - Metadata initialization for tracking extraction information
//
// Parameters:
//   - targetDataType: The semantic type of the data (e.g., "report", "asset", "entity")
//   - rawInputData: The source data to convert (struct, primitive, protobuf message, etc.)
//
// Returns:
//   - map[string]interface{}: Standardized data structure with ID, type, attributes, and metadata
//
// Example:
//
//	reportData := ConvertToTemplateData(DataTypeReport, reportStruct)
//	assetData := ConvertToTemplateData(DataTypeAsset, assetProtobuf)
func ConvertToTemplateData(targetDataType string, rawInputData interface{}) map[string]interface{} {
	// Use reflection to inspect the input data structure
	reflectionValue := reflect.ValueOf(rawInputData)

	// Dereference pointers to get the actual value
	if reflectionValue.Kind() == reflect.Ptr {
		reflectionValue = reflectionValue.Elem()
	}

	// Convert the data to a map of attributes based on its type
	var extractedAttributes map[string]interface{}
	if reflectionValue.Kind() == reflect.Struct {
		// For struct types, convert all fields to a flat map
		extractedAttributes = structToMap(reflectionValue)
	} else {
		// For primitive types, wrap in a simple value container
		extractedAttributes = map[string]interface{}{FieldValue: rawInputData}
	}

	// Attempt to extract an ID from common ID field patterns
	extractedEntityID := ""
	if identifierField, hasIDField := extractedAttributes[FieldID].(string); hasIDField {
		extractedEntityID = identifierField
	}

	// Create and return the standardized data structure
	return map[string]interface{}{
		FieldID:      extractedEntityID,
		FieldType:    targetDataType,
		"attributes": extractedAttributes,
		"metadata":   make(map[string]interface{}), // Initialize empty metadata for later enrichment
	}
}

// getFieldValue recursively extracts and converts values from Go reflection Value objects.
//
// This utility method handles the conversion of different Go types to interface{} values
// that can be safely used in templates and JSON serialization:
// - Primitives: strings, ints, floats, bools are extracted directly
// - Slices: recursively converted to []interface{}
// - Pointers: dereferenced and processed (nil pointers return nil)
// - Structs: converted to map[string]interface{} using structToMap
// - Enums: converted to strings using their String() method
// - Other types: returned as-is using Interface()
//
// Parameters:
//   - v: reflect.Value to extract data from
//
// Returns:
//   - interface{}: Converted value suitable for template processing
//
// Example:
//
//	field := structValue.Field(i)
//	value := extractor.getFieldValue(field)
//	// value now contains the field's data in a template-friendly format
//
// getFieldValue recursively extracts and converts values from Go reflection Value objects.
//
// This utility method handles the conversion of different Go types to interface{} values
// that can be safely used in templates and JSON serialization. It provides special handling
// for complex types like protobuf enums, structs, slices, and pointers.
//
// Type Conversion Strategy:
//   - Protobuf Enums: Converted to string representation using String() method
//   - Primitives: Direct extraction (strings, ints, floats, bools)
//   - Slices: Recursively converted to []interface{}
//   - Pointers: Dereferenced and processed (nil pointers return nil)
//   - Structs: Converted to map[string]interface{} using structToMap
//   - Protobuf Struct: Special handling for google.protobuf.Struct types
//
// Parameters:
//   - reflectionValue: reflect.Value containing the data to extract
//
// Returns:
//   - interface{}: Converted value suitable for template processing and JSON serialization
//
// Example:
//
//	structField := structValue.Field(fieldIndex)
//	convertedValue := getFieldValue(structField)
//	// convertedValue now contains the field's data in a template-friendly format
func getFieldValue(reflectionValue reflect.Value) interface{} {
	// Special handling for protobuf enums (before processing as basic integers)
	// Protobuf enums implement the String() method and should be converted to their string representation
	if reflectionValue.Kind() == reflect.Int32 || reflectionValue.Kind() == reflect.Int {
		// Check if this integer type has a String() method (indicates it's likely an enum)
		if stringerMethod := reflectionValue.MethodByName("String"); stringerMethod.IsValid() {
			enumConversionResult := stringerMethod.Call(nil)
			if len(enumConversionResult) > 0 {
				enumStringValue := enumConversionResult[0].String()
				// Verify this is actually an enum by checking if the string representation
				// differs from the numeric value (e.g., "ASSET_STATUS_ACTIVE" vs "1")
				numericStringRepresentation := fmt.Sprintf("%d", reflectionValue.Int())
				if enumStringValue != numericStringRepresentation {
					return enumStringValue
				}
			}
		}
	}

	// Process different reflection types based on their Kind
	switch reflectionValue.Kind() {
	case reflect.String:
		// Direct string extraction
		return reflectionValue.String()

	case reflect.Int, reflect.Int32, reflect.Int64:
		// Direct integer extraction
		return reflectionValue.Int()

	case reflect.Float32, reflect.Float64:
		// Direct floating-point extraction
		return reflectionValue.Float()

	case reflect.Bool:
		// Direct boolean extraction
		return reflectionValue.Bool()

	case reflect.Slice:
		// Recursively convert slice elements to interface{} array
		sliceLength := reflectionValue.Len()
		convertedSlice := make([]interface{}, sliceLength)
		for sliceIndex := 0; sliceIndex < sliceLength; sliceIndex++ {
			sliceElement := reflectionValue.Index(sliceIndex)
			convertedSlice[sliceIndex] = getFieldValue(sliceElement)
		}
		return convertedSlice
	case reflect.Ptr:
		// Handle pointer types by dereferencing or returning nil
		if reflectionValue.IsNil() {
			return nil
		}
		// Recursively process the dereferenced value
		dereferencedValue := reflectionValue.Elem()
		return getFieldValue(dereferencedValue)
	case reflect.Struct:
		// Special handling for google.protobuf.Struct types
		reflectionTypeString := reflectionValue.Type().String()
		if reflectionTypeString == "*structpb.Struct" || reflectionTypeString == "structpb.Struct" {
			// For protobuf Struct pointers, extract the Fields map and convert to regular map
			if protobufStruct, isValidProtobufStruct := reflectionValue.Interface().(*structpb.Struct); isValidProtobufStruct && protobufStruct != nil {
				convertedFieldsMap := make(map[string]interface{})
				for fieldKey, fieldValue := range protobufStruct.Fields {
					convertedFieldsMap[fieldKey] = convertProtobufValue(fieldValue)
				}
				return convertedFieldsMap
			}

			// Handle case where the value is structpb.Struct (not pointer)
			// Skip direct struct handling to avoid copying mutex - convert to pointer first
			if reflectionValue.Type() == reflect.TypeOf(structpb.Struct{}) {
				// Get the address of the struct to work with a pointer instead
				if reflectionValue.CanAddr() {
					structPointerValue := reflectionValue.Addr()
					if protobufStruct, isValidProtobufStruct := structPointerValue.Interface().(*structpb.Struct); isValidProtobufStruct && protobufStruct != nil {
						convertedFieldsMap := make(map[string]interface{})
						for fieldKey, fieldValue := range protobufStruct.Fields {
							convertedFieldsMap[fieldKey] = convertProtobufValue(fieldValue)
						}
						return convertedFieldsMap
					}
				}
				// Fallback: return empty map if we can't get the address
				return make(map[string]interface{})
			}
		}

		// Check if it's a pointer to structpb.Struct
		if reflectionValue.Type() == reflect.TypeOf((*structpb.Struct)(nil)) {
			if !reflectionValue.IsNil() {
				if protobufStruct, isValidProtobufStruct := reflectionValue.Interface().(*structpb.Struct); isValidProtobufStruct && protobufStruct != nil {
					convertedFieldsMap := make(map[string]interface{})
					for fieldKey, fieldValue := range protobufStruct.Fields {
						convertedFieldsMap[fieldKey] = convertProtobufValue(fieldValue)
					}
					return convertedFieldsMap
				}
			}
			return nil
		}

		// For regular structs, convert to map using field introspection
		return structToMap(reflectionValue)
	default:
		// For enums and other unknown types, attempt String() method conversion
		if stringerMethod := reflectionValue.MethodByName("String"); stringerMethod.IsValid() {
			stringConversionResult := stringerMethod.Call(nil)
			if len(stringConversionResult) > 0 {
				return stringConversionResult[0].String()
			}
		}
		// Fallback: return the raw interface{} value
		return reflectionValue.Interface()
	}
}

// structToMap converts a struct's fields to a map[string]interface{} for template use.
//
// This method performs comprehensive struct introspection to create a flattened map representation
// suitable for template processing and JSON serialization. It handles field naming conventions,
// JSON tag processing, and recursive value conversion.
//
// Key Features:
//   - Exports only public (capitalized) struct fields
//   - Respects JSON struct tags for field naming
//   - Converts snake_case field names to camelCase for consistency
//   - Recursively processes nested structs and complex types
//   - Skips fields marked with json:"-" tag
//
// Field Naming Priority:
//  1. JSON tag name (if present and not "-")
//  2. Original struct field name
//  3. Converted to camelCase if snake_case detected
//
// Parameters:
//   - structReflectionValue: reflect.Value representing a struct to introspect
//
// Returns:
//   - map[string]interface{}: Flattened map representation with normalized field names
//
// Example:
//
//	type User struct {
//	    FirstName string `json:"first_name"`
//	    LastName  string `json:"last_name"`
//	    Age       int
//	}
//	structValue := reflect.ValueOf(user)
//	mapData := structToMap(structValue)
//	// mapData["firstName"] contains the FirstName value
//	// mapData["lastName"] contains the LastName value
//	// mapData["age"] contains the Age value
func structToMap(structReflectionValue reflect.Value) map[string]interface{} {
	convertedFieldsMap := make(map[string]interface{})
	structType := structReflectionValue.Type()

	// Iterate through all fields in the struct
	numberOfFields := structReflectionValue.NumField()
	for fieldIndex := 0; fieldIndex < numberOfFields; fieldIndex++ {
		structField := structType.Field(fieldIndex)

		// Skip unexported (private) fields
		if !structField.IsExported() {
			continue
		}

		// Determine the output field name based on JSON tags and naming conventions
		outputFieldName := structField.Name

		// Check for JSON struct tag to override field name
		if jsonStructTag := structField.Tag.Get("json"); jsonStructTag != "" {
			// Extract the field name part before any options like ",omitempty"
			jsonTagParts := strings.Split(jsonStructTag, ",")
			jsonFieldName := jsonTagParts[0]

			// Use JSON tag name unless it's empty or marked to skip
			if jsonFieldName != "" && jsonFieldName != "-" {
				outputFieldName = jsonFieldName
			} else if jsonFieldName == "-" {
				// Skip fields explicitly marked to ignore
				continue
			}
		}

		// Normalize field name to camelCase for consistency
		normalizedFieldName := convertSnakeCaseToCamelCase(outputFieldName)

		// Extract and convert the field value recursively
		fieldReflectionValue := structReflectionValue.Field(fieldIndex)
		convertedFieldValue := getFieldValue(fieldReflectionValue)

		// Store the converted field in the output map
		convertedFieldsMap[normalizedFieldName] = convertedFieldValue
	}

	return convertedFieldsMap
}

// convertProtobufValue converts a protobuf Value to a Go interface{} value.
//
// This function handles the conversion of google.protobuf.Value types to standard Go types
// that can be used in templates and JSON serialization. It recursively processes nested
// structures and maintains type safety throughout the conversion process.
//
// Supported Protobuf Value Types:
//   - StringValue: Converted to Go string
//   - NumberValue: Converted to Go float64
//   - BoolValue: Converted to Go bool
//   - NullValue: Converted to Go nil
//   - ListValue: Recursively converted to []interface{}
//   - StructValue: Recursively converted to map[string]interface{}
//
// Parameters:
//   - protobufValue: Pointer to structpb.Value to convert (may be nil)
//
// Returns:
//   - interface{}: Converted Go value, or nil if input is nil
//
// Example:
//
//	protobufString := &structpb.Value{Kind: &structpb.Value_StringValue{StringValue: "hello"}}
//	goValue := convertProtobufValue(protobufString) // Returns "hello" as string
func convertProtobufValue(protobufValue *structpb.Value) interface{} {
	// Handle nil protobuf values gracefully
	if protobufValue == nil {
		return nil
	}

	// Process different protobuf value types
	switch protobufValue.Kind.(type) {
	case *structpb.Value_StringValue:
		// Extract string value from protobuf wrapper
		return protobufValue.GetStringValue()

	case *structpb.Value_NumberValue:
		// Extract numeric value from protobuf wrapper
		return protobufValue.GetNumberValue()

	case *structpb.Value_BoolValue:
		// Extract boolean value from protobuf wrapper
		return protobufValue.GetBoolValue()

	case *structpb.Value_NullValue:
		// Protobuf null values become Go nil
		return nil

	case *structpb.Value_ListValue:
		// Recursively convert protobuf list to Go slice
		protobufList := protobufValue.GetListValue()
		convertedSlice := make([]interface{}, len(protobufList.Values))
		for listIndex, protobufListItem := range protobufList.Values {
			convertedSlice[listIndex] = convertProtobufValue(protobufListItem)
		}
		return convertedSlice

	case *structpb.Value_StructValue:
		// Recursively convert protobuf struct to Go map
		protobufStruct := protobufValue.GetStructValue()
		convertedMap := make(map[string]interface{})
		for structKey, structValue := range protobufStruct.Fields {
			convertedMap[structKey] = convertProtobufValue(structValue)
		}
		return convertedMap

	default:
		// Fallback for unknown protobuf value types
		return protobufValue.AsInterface()
	}
}
