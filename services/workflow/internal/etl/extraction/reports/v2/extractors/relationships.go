// Package extractors provides specialized extraction functions for different types of report data.
//
// This package contains the core extraction logic for transforming report data into fully
// hydrated, type-safe structures. Each extractor handles a specific type of data (assets,
// entities, sections, relationships, etc.) and provides consistent hydration with proper
// context preservation.
//
// All extractors follow the same pattern:
//   - Accept a database transaction for consistency
//   - Take a report protobuf as the primary data source
//   - Use repository interfaces for data access
//   - Return fully hydrated structures with extraction metadata
//   - <PERSON>le missing repositories gracefully (return empty results, not errors)
//
// The extractors are designed to be:
//   - Thread-safe for parallel execution
//   - Resilient to missing or invalid data
//   - Consistent in their error handling approach
//   - Rich in contextual metadata for debugging and auditing
package extractors

import (
	"context"
	"database/sql"

	entityv1 "proto/hero/entity/v1"
	reports "proto/hero/reports/v2"
	assetsRepository "workflow/internal/assets/data"
	entityRepository "workflow/internal/entity/data"
	"workflow/internal/etl/extraction/types"

	"google.golang.org/protobuf/types/known/structpb"
)

// ExtractHydratedRelationships extracts relationships with resolved object names for a report.
//
// This function retrieves all relationship data from a report and provides comprehensive
// hydration by resolving object references to their actual names. It processes both
// sides of each relationship to provide complete context about how entities, assets,
// and other objects are connected within the report.
//
// Relationship Resolution Process:
//   - Extracts all relations from the report protobuf
//   - Resolves ObjectA and ObjectB references to human-readable names
//   - Maintains original relationship metadata (type, IDs, display names)
//   - Provides fallback resolution for missing or incomplete references
//
// The function handles different object types:
//   - Entities: Resolved through entity repository with smart name generation
//   - Assets: Resolved through asset repository using asset names
//   - Other objects: Falls back to display names or object type descriptions
//
// Resolution Strategy:
//   - First attempts to use existing display names from the relationship
//   - Falls back to repository resolution for more detailed names
//   - Provides meaningful fallbacks for unresolvable references
//   - Maintains type safety throughout the resolution process
//
// Each relationship includes rich context metadata:
//   - Source extraction information (report_relations)
//   - Report and relation IDs for traceability
//   - Resolved object names and types for both sides
//   - Relationship type and any additional metadata
//
// Parameters:
//   - ctx: Context for database operations and cancellation
//   - databaseTransaction: Database transaction for consistency across operations
//   - reportProtobuf: Source report protobuf containing relationship data
//   - entitiesRepo: Repository interface for entity name resolution (may be nil)
//   - assetsRepo: Repository interface for asset name resolution (may be nil)
//
// Returns:
//   - []*types.HydratedRelationship: Relationships with resolved object names and metadata
//   - error: Any errors encountered during relationship processing or resolution
//
// Thread Safety:
// This function is thread-safe and can be called concurrently from multiple goroutines
// as long as each call uses its own database transaction.
//
// Example Usage:
//
//	relationships, err := ExtractHydratedRelationships(ctx, tx, report, entitiesRepo, assetsRepo)
//	if err != nil {
//	    return fmt.Errorf("failed to extract relationships: %w", err)
//	}
//
//	// Filter by relationship type
//	parentChildRels := GetRelationshipsByType(relationships, "parent_child")
//
//	// Find relationships involving a specific object
//	entityRels := GetRelationshipsInvolvingObject(relationships, "entity-123")
func ExtractHydratedRelationships(
	ctx context.Context,
	databaseTransaction *sql.Tx,
	reportProtobuf *reports.Report,
	entitiesRepo entityRepository.EntityRepository,
	assetsRepo assetsRepository.AssetRepository,
) ([]*types.HydratedRelationship, error) {
	var hydratedRelationships []*types.HydratedRelationship

	// Extract relationships from the report
	for _, relationshipData := range reportProtobuf.Relations {
		hydratedRelationship := &types.HydratedRelationship{
			Relation: relationshipData,
			RelationshipContext: map[string]interface{}{
				"extractedFrom": "report_relations",
				"reportId":      reportProtobuf.Id,
				"relationId":    relationshipData.Id,
			},
		}

		// Resolve object A name with comprehensive fallback logic
		if relationshipData.ObjectA != nil {
			hydratedRelationship.FromObjectName = relationshipData.ObjectA.DisplayName
			hydratedRelationship.FromObjectType = relationshipData.ObjectA.ObjectType

			// Try to get more detailed name if display name is empty
			if hydratedRelationship.FromObjectName == "" {
				resolvedName := resolveObjectName(ctx, databaseTransaction, relationshipData.ObjectA, entitiesRepo, assetsRepo)
				if resolvedName != "" {
					hydratedRelationship.FromObjectName = resolvedName
				}
			}
		}

		// Resolve object B name with comprehensive fallback logic
		if relationshipData.ObjectB != nil {
			hydratedRelationship.ToObjectName = relationshipData.ObjectB.DisplayName
			hydratedRelationship.ToObjectType = relationshipData.ObjectB.ObjectType

			// Try to get more detailed name if display name is empty
			if hydratedRelationship.ToObjectName == "" {
				resolvedName := resolveObjectName(ctx, databaseTransaction, relationshipData.ObjectB, entitiesRepo, assetsRepo)
				if resolvedName != "" {
					hydratedRelationship.ToObjectName = resolvedName
				}
			}
		}

		hydratedRelationships = append(hydratedRelationships, hydratedRelationship)
	}

	return hydratedRelationships, nil
}

// resolveObjectName attempts to resolve an object reference to a human-readable name.
//
// This function provides comprehensive object name resolution by attempting to look up
// the actual object data through the appropriate repository. It handles different object
// types with specialized resolution logic and provides meaningful fallbacks when
// resolution fails.
//
// Resolution Strategy:
//   - Entities: Uses entity repository to get full entity data, then generates display names
//   - Assets: Uses asset repository to get asset names directly
//   - Other types: Falls back to display names or type descriptions
//
// Fallback Hierarchy:
//  1. Repository-resolved names (most accurate)
//  2. Existing display names from the object reference
//  3. Constructed names using object type and ID
//
// Parameters:
//   - ctx: Context for database operations
//   - databaseTransaction: Database transaction for consistency
//   - objectReference: Object reference to resolve
//   - entitiesRepo: Repository for entity resolution (may be nil)
//   - assetsRepo: Repository for asset resolution (may be nil)
//
// Returns:
//   - string: Resolved object name, or fallback description if resolution fails
func resolveObjectName(
	ctx context.Context,
	databaseTransaction *sql.Tx,
	objectReference *reports.ObjectReference,
	entitiesRepo entityRepository.EntityRepository,
	assetsRepo assetsRepository.AssetRepository,
) string {
	if objectReference == nil {
		return ""
	}

	// Use global_id for resolution if available
	if objectReference.GlobalId != "" {
		switch objectReference.ObjectType {
		case "entity":
			if entitiesRepo != nil {
				entityData, entityRetrievalError := entitiesRepo.GetLatestEntity(ctx, databaseTransaction, objectReference.GlobalId)
				if entityRetrievalError == nil {
					return generateEntityDisplayName(entityData)
				}
			}
		case "asset":
			if assetsRepo != nil {
				assetData, assetRetrievalError := assetsRepo.GetAsset(ctx, databaseTransaction, objectReference.GlobalId)
				if assetRetrievalError == nil {
					return assetData.Name
				}
			}
		}
	}

	// Fallback to existing display name or object type
	if objectReference.DisplayName != "" {
		return objectReference.DisplayName
	}

	return objectReference.ObjectType + " (" + objectReference.GlobalId + ")"
}

// generateEntityDisplayName creates a display name for an entity based on its data.
//
// This function analyzes the entity's dynamic data to construct the most meaningful
// display name possible. It uses entity type-specific logic to prioritize the most
// relevant fields and construct names that provide maximum context and readability.
//
// Entity Type Strategies:
//   - PERSON: Combines first/last names, falls back to generic "name" field
//   - VEHICLE: Constructs "year make model" with optional license plate
//   - PROPERTY: Uses name, description, or address with "Property at" prefix
//   - ORGANIZATION: Prioritizes "name" or "organizationName" fields
//   - Generic: Falls back to "name" field or "Entity" if no data available
//
// Name Construction Logic:
//   - Prioritizes complete, specific names over generic ones
//   - Combines multiple fields when appropriate (e.g., "John Smith")
//   - Adds contextual information when helpful (e.g., license plates)
//   - Provides meaningful fallbacks for incomplete data
//
// Parameters:
//   - entityData: Entity protobuf with dynamic data fields
//
// Returns:
//   - string: Human-readable display name, or generic fallback if data is insufficient
//
// Example Results:
//   - "John Smith" (person with first/last name)
//   - "2020 Toyota Camry (ABC-123)" (vehicle with year/make/model/plate)
//   - "Property at 123 Main St" (property with address)
//   - "Acme Corp" (organization with name)
func generateEntityDisplayName(entityData *entityv1.Entity) string {
	if entityData == nil || entityData.Data == nil || entityData.Data.Fields == nil {
		return "Unknown Entity"
	}

	protobufFields := entityData.Data.Fields

	// Try different name combinations based on entity type
	switch entityData.EntityType {
	case entityv1.EntityType_ENTITY_TYPE_PERSON:
		// Try to build person name from firstName, lastName, etc.
		firstName := getFieldStringValue(protobufFields, "firstName")
		lastName := getFieldStringValue(protobufFields, "lastName")
		genericName := getFieldStringValue(protobufFields, "name")

		if firstName != "" && lastName != "" {
			return firstName + " " + lastName
		}
		if genericName != "" {
			return genericName
		}
		return "Person"

	case entityv1.EntityType_ENTITY_TYPE_VEHICLE:
		// Try to build vehicle name from make, model, year
		vehicleMake := getFieldStringValue(protobufFields, "make")
		vehicleModel := getFieldStringValue(protobufFields, "model")
		vehicleYear := getFieldStringValue(protobufFields, "year")
		licensePlate := getFieldStringValue(protobufFields, "licensePlate")

		if vehicleYear != "" && vehicleMake != "" && vehicleModel != "" {
			vehicleDescription := vehicleYear + " " + vehicleMake + " " + vehicleModel
			if licensePlate != "" {
				vehicleDescription += " (" + licensePlate + ")"
			}
			return vehicleDescription
		}
		if vehicleMake != "" && vehicleModel != "" {
			return vehicleMake + " " + vehicleModel
		}
		if licensePlate != "" {
			return "Vehicle (" + licensePlate + ")"
		}
		return "Vehicle"

	case entityv1.EntityType_ENTITY_TYPE_PROPERTY:
		// Try to get property description or address
		propertyDescription := getFieldStringValue(protobufFields, "description")
		propertyAddress := getFieldStringValue(protobufFields, "address")
		propertyName := getFieldStringValue(protobufFields, "name")

		if propertyName != "" {
			return propertyName
		}
		if propertyDescription != "" {
			return propertyDescription
		}
		if propertyAddress != "" {
			return "Property at " + propertyAddress
		}
		return "Property"

	case entityv1.EntityType_ENTITY_TYPE_ORGANIZATION:
		// Try to get organization name
		organizationName := getFieldStringValue(protobufFields, "name")
		organizationDisplayName := getFieldStringValue(protobufFields, "organizationName")

		if organizationName != "" {
			return organizationName
		}
		if organizationDisplayName != "" {
			return organizationDisplayName
		}
		return "Organization"

	default:
		// Generic fallback
		genericName := getFieldStringValue(protobufFields, "name")
		if genericName != "" {
			return genericName
		}
		return "Entity"
	}
}

// getFieldStringValue safely extracts a string value from protobuf struct fields.
//
// This utility function provides safe access to string values stored in protobuf
// Struct fields, handling nil checks and type assertions gracefully. It's commonly
// used when extracting field values from dynamic entity data structures.
//
// Parameters:
//   - protobufFields: Map of protobuf field names to values
//   - fieldName: The field name to extract
//
// Returns:
//   - string: The extracted string value, or empty string if not found or not a string
func getFieldStringValue(protobufFields map[string]*structpb.Value, fieldName string) string {
	if fieldValue, fieldExists := protobufFields[fieldName]; fieldExists {
		return fieldValue.GetStringValue()
	}
	return ""
}

// GetRelationshipsByType filters hydrated relationships by relation type.
//
// This utility function provides a convenient way to filter relationships by their
// relation type, which is useful for processing specific categories of relationships
// separately (e.g., parent-child, ownership, association).
//
// Parameters:
//   - hydratedRelationships: Array of hydrated relationships to filter
//   - relationType: The relation type string to filter by
//
// Returns:
//   - []*types.HydratedRelationship: Relationships matching the specified type
//
// Example Usage:
//
//	relationships, _ := ExtractHydratedRelationships(ctx, tx, report, entitiesRepo, assetsRepo)
//	parentChildRels := GetRelationshipsByType(relationships, "parent_child")
//	ownershipRels := GetRelationshipsByType(relationships, "ownership")
func GetRelationshipsByType(hydratedRelationships []*types.HydratedRelationship, relationType string) []*types.HydratedRelationship {
	var filteredRelationships []*types.HydratedRelationship
	for _, hydratedRelationship := range hydratedRelationships {
		if hydratedRelationship.Relation.RelationType == relationType {
			filteredRelationships = append(filteredRelationships, hydratedRelationship)
		}
	}
	return filteredRelationships
}

// GetRelationshipsInvolvingObject filters relationships that involve a specific object.
//
// This utility function finds all relationships where the specified object appears
// as either ObjectA or ObjectB. This is useful for understanding all the connections
// a particular entity, asset, or other object has within the report.
//
// Parameters:
//   - hydratedRelationships: Array of hydrated relationships to filter
//   - objectGlobalId: The global ID of the object to search for
//
// Returns:
//   - []*types.HydratedRelationship: Relationships involving the specified object
//
// Example Usage:
//
//	relationships, _ := ExtractHydratedRelationships(ctx, tx, report, entitiesRepo, assetsRepo)
//	entityRels := GetRelationshipsInvolvingObject(relationships, "entity-123")
//	assetRels := GetRelationshipsInvolvingObject(relationships, "asset-456")
func GetRelationshipsInvolvingObject(hydratedRelationships []*types.HydratedRelationship, objectGlobalId string) []*types.HydratedRelationship {
	var filteredRelationships []*types.HydratedRelationship
	for _, hydratedRelationship := range hydratedRelationships {
		relationshipData := hydratedRelationship.Relation
		if relationshipData.ObjectA != nil && relationshipData.ObjectA.GlobalId == objectGlobalId {
			filteredRelationships = append(filteredRelationships, hydratedRelationship)
		} else if relationshipData.ObjectB != nil && relationshipData.ObjectB.GlobalId == objectGlobalId {
			filteredRelationships = append(filteredRelationships, hydratedRelationship)
		}
	}
	return filteredRelationships
}
