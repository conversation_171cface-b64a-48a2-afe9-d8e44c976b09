// Package extractors provides specialized extraction functions for different types of report data.
//
// This package contains the core extraction logic for transforming report data into fully
// hydrated, type-safe structures. Each extractor handles a specific type of data (assets,
// entities, sections, etc.) and provides consistent hydration with proper context preservation.
//
// All extractors follow the same pattern:
//   - Accept a database transaction for consistency
//   - Take a report protobuf as the primary data source
//   - Use repository interfaces for data access
//   - Return fully hydrated structures with extraction metadata
//   - <PERSON>le missing repositories gracefully (return empty results, not errors)
//
// The extractors are designed to be:
//   - Thread-safe for parallel execution
//   - Resilient to missing or invalid data
//   - Consistent in their error handling approach
//   - Rich in contextual metadata for debugging and auditing
package extractors

import (
	"context"
	"database/sql"

	assetsv2 "proto/hero/assets/v2"
	reports "proto/hero/reports/v2"
	assetsRepository "workflow/internal/assets/data"
	"workflow/internal/etl/extraction/reports/v2/core"
	"workflow/internal/etl/extraction/types"
)

// ExtractHydratedAssets extracts all assets associated with a report, enriched with role context.
//
// This function retrieves complete asset information for all assets connected to a report,
// including their specific roles (creator, author, watcher). It provides full hydration
// by replacing asset ID references with complete asset objects, maintaining the relationship
// context that indicates how each asset relates to the report.
//
// Asset Role Types:
//   - Creator: The asset that originally created the report
//   - Author: The asset that authored/wrote the report content
//   - Watcher: Assets that are monitoring or assigned to watch the report
//
// The function handles missing or invalid assets gracefully by skipping them rather than
// failing the entire extraction. This ensures that partial data doesn't prevent successful
// processing of available assets.
//
// Extraction Context:
// Each returned asset includes rich extraction metadata that records:
//   - Source of extraction (report_creator_link, report_author_link, report_watcher_link)
//   - Associated report ID for traceability
//   - Asset ID for reference
//   - Watcher index for maintaining order in watcher arrays
//
// Parameters:
//   - ctx: Context for database operations and cancellation
//   - databaseTransaction: Database transaction for consistency across operations
//   - reportProtobuf: Source report containing asset ID references
//   - assetsRepo: Repository interface for asset data access (may be nil)
//
// Returns:
//   - []*types.HydratedAsset: Complete asset data with role context and extraction metadata
//   - error: Database or repository errors (returns empty slice for missing repository)
//
// Thread Safety:
// This function is thread-safe and can be called concurrently from multiple goroutines
// as long as each call uses its own database transaction.
//
// Example Usage:
//
//	assets, err := ExtractHydratedAssets(ctx, transaction, report, assetsRepo)
//	if err != nil {
//	    return fmt.Errorf("failed to extract assets: %w", err)
//	}
//
//	// Find specific role assets
//	creator := FindAssetByRole(assets, core.RoleCreator)
//	watchers := FindAssetsByRole(assets, core.RoleWatcher)
func ExtractHydratedAssets(
	ctx context.Context,
	databaseTransaction *sql.Tx,
	reportProtobuf *reports.Report,
	assetsRepo assetsRepository.AssetRepository,
) ([]*types.HydratedAsset, error) {
	// Handle missing repository gracefully - return empty result, not error
	if assetsRepo == nil {
		return []*types.HydratedAsset{}, nil
	}

	var hydratedAssets []*types.HydratedAsset

	// Extract creator asset with role context
	if reportProtobuf.CreatedByAssetId != "" {
		creatorAsset, assetRetrievalError := assetsRepo.GetAsset(ctx, databaseTransaction, reportProtobuf.CreatedByAssetId)
		if assetRetrievalError == nil {
			hydratedAssets = append(hydratedAssets, &types.HydratedAsset{
				Asset: creatorAsset,
				Role:  core.RoleCreator,
				ExtractionContext: map[string]interface{}{
					core.FieldExtractedFrom: "report_creator_link",
					core.FieldReportID:      reportProtobuf.Id,
					"assetId":               creatorAsset.Id,
				},
			})
		}
		// Note: Failed asset retrieval is silently skipped to handle missing data gracefully
	}

	// Extract author asset with role context
	if reportProtobuf.AuthorAssetId != "" {
		authorAsset, assetRetrievalError := assetsRepo.GetAsset(ctx, databaseTransaction, reportProtobuf.AuthorAssetId)
		if assetRetrievalError == nil {
			hydratedAssets = append(hydratedAssets, &types.HydratedAsset{
				Asset: authorAsset,
				Role:  core.RoleAuthor,
				ExtractionContext: map[string]interface{}{
					core.FieldExtractedFrom: "report_author_link",
					core.FieldReportID:      reportProtobuf.Id,
					"assetId":               authorAsset.Id,
				},
			})
		}
		// Note: Failed asset retrieval is silently skipped to handle missing data gracefully
	}

	// Extract watcher assets with role context and preserve array order
	for watcherIndex, watcherAssetId := range reportProtobuf.WatcherAssetIds {
		watcherAsset, assetRetrievalError := assetsRepo.GetAsset(ctx, databaseTransaction, watcherAssetId)
		if assetRetrievalError == nil {
			hydratedAssets = append(hydratedAssets, &types.HydratedAsset{
				Asset: watcherAsset,
				Role:  core.RoleWatcher,
				ExtractionContext: map[string]interface{}{
					core.FieldExtractedFrom: "report_watcher_link",
					core.FieldReportID:      reportProtobuf.Id,
					"assetId":               watcherAsset.Id,
					"watcherIndex":          watcherIndex, // Preserve original order
				},
			})
		}
		// Note: Failed watcher asset retrieval is silently skipped to handle missing data gracefully
	}

	return hydratedAssets, nil
}

// FindAssetByRole finds the first asset with the specified role from hydrated assets.
//
// This utility function provides a convenient way to locate specific role-based assets
// from the extraction results. It returns the first asset found with the matching role,
// which is typically sufficient for single-role assets like creator and author.
//
// Common role values to search for:
//   - core.RoleCreator: The asset that created the report
//   - core.RoleAuthor: The asset that authored the report content
//   - core.RoleWatcher: A watcher asset (returns first one found)
//
// Parameters:
//   - hydratedAssets: Array of hydrated assets from ExtractHydratedAssets
//   - role: The role string to search for
//
// Returns:
//   - *assetsv2.Asset: The first asset with the matching role, or nil if not found
//
// Example Usage:
//
//	assets, _ := ExtractHydratedAssets(ctx, tx, report, assetsRepo)
//	creator := FindAssetByRole(assets, core.RoleCreator)
//	if creator != nil {
//	    fmt.Printf("Report created by: %s\n", creator.Name)
//	}
func FindAssetByRole(hydratedAssets []*types.HydratedAsset, role string) *assetsv2.Asset {
	for _, hydratedAsset := range hydratedAssets {
		if hydratedAsset.Role == role {
			return hydratedAsset.Asset
		}
	}
	return nil
}

// FindAssetsByRole finds all assets with the specified role from hydrated assets.
//
// This utility function is designed for roles that can have multiple assets, such as
// watchers. It returns all assets that match the specified role, preserving the order
// from the original extraction.
//
// This is particularly useful for:
//   - core.RoleWatcher: Getting all watcher assets
//   - Any custom roles that might have multiple assets
//
// Parameters:
//   - hydratedAssets: Array of hydrated assets from ExtractHydratedAssets
//   - role: The role string to search for
//
// Returns:
//   - []*assetsv2.Asset: All assets with the matching role (empty slice if none found)
//
// Example Usage:
//
//	assets, _ := ExtractHydratedAssets(ctx, tx, report, assetsRepo)
//	watchers := FindAssetsByRole(assets, core.RoleWatcher)
//	fmt.Printf("Found %d watchers\n", len(watchers))
//	for _, watcher := range watchers {
//	    fmt.Printf("Watcher: %s\n", watcher.Name)
//	}
func FindAssetsByRole(hydratedAssets []*types.HydratedAsset, role string) []*assetsv2.Asset {
	var matchingAssets []*assetsv2.Asset
	for _, hydratedAsset := range hydratedAssets {
		if hydratedAsset.Role == role {
			matchingAssets = append(matchingAssets, hydratedAsset.Asset)
		}
	}
	return matchingAssets
}

// GetAssetRoles returns all unique roles present in the hydrated assets.
//
// This utility function analyzes the extraction results to determine what roles
// are actually present, which is useful for debugging, validation, and understanding
// the completeness of the extraction.
//
// The returned roles are in the order they were first encountered in the assets array,
// which typically means: creator, author, then watchers.
//
// Parameters:
//   - hydratedAssets: Array of hydrated assets from ExtractHydratedAssets
//
// Returns:
//   - []string: Array of unique role strings found in the assets
//
// Example Usage:
//
//	assets, _ := ExtractHydratedAssets(ctx, tx, report, assetsRepo)
//	roles := GetAssetRoles(assets)
//	fmt.Printf("Extraction included roles: %v\n", roles)
//
//	// Check for specific roles
//	hasCreator := contains(roles, core.RoleCreator)
//	hasWatchers := contains(roles, core.RoleWatcher)
func GetAssetRoles(hydratedAssets []*types.HydratedAsset) []string {
	uniqueRoleMap := make(map[string]bool)
	var discoveredRoles []string

	for _, hydratedAsset := range hydratedAssets {
		if !uniqueRoleMap[hydratedAsset.Role] {
			uniqueRoleMap[hydratedAsset.Role] = true
			discoveredRoles = append(discoveredRoles, hydratedAsset.Role)
		}
	}

	return discoveredRoles
}
