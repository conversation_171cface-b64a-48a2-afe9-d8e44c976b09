package extractors

import (
	"context"
	"database/sql"

	entityv1 "proto/hero/entity/v1"
	reports "proto/hero/reports/v2"
	entityRepository "workflow/internal/entity/data"
	"workflow/internal/etl/extraction/types"

	"google.golang.org/protobuf/types/known/structpb"
)

// ExtractHydratedEntities extracts all entities referenced in a report with their section context.
//
// This function performs comprehensive entity extraction by scanning all report sections
// for entity references and retrieving the complete entity data. It maintains rich context
// about where each entity is referenced and how it relates to the report structure.
//
// Entity Reference Sources:
//   - Entity List Sections: People, vehicles, properties, organizations explicitly listed
//   - Arrest Sections: Arrestees referenced in arrest data
//   - Incident Details: Potential entity references in structured data
//   - Future: Additional section types can be added as needed
//
// The function provides complete entity hydration by:
//   - Replacing entity ID references with full entity protobuf objects
//   - Preserving section context for each entity reference
//   - Maintaining relationship metadata (type, display name, version)
//   - Handling duplicate entities across multiple sections intelligently
//
// Context Preservation:
// Each extracted entity includes detailed metadata about:
//   - Which section it was referenced in (ID and type)
//   - How it was referenced (entity list, arrest, etc.)
//   - Its role or relationship type within that context
//   - Additional section-specific metadata
//
// Parameters:
//   - ctx: Context for database operations and cancellation
//   - databaseTransaction: Database transaction for consistency across operations
//   - reportProtobuf: Source report containing entity references
//   - entitiesRepo: Repository interface for entity data access (may be nil)
//
// Returns:
//   - []*types.HydratedEntity: Complete entity data with section context and metadata
//   - error: Database or repository errors (returns empty slice for missing repository)
//
// Thread Safety:
// This function is thread-safe and can be called concurrently from multiple goroutines
// as long as each call uses its own database transaction.
//
// Example Usage:
//
//	entities, err := ExtractHydratedEntities(ctx, transaction, report, entitiesRepo)
//	if err != nil {
//	    return fmt.Errorf("failed to extract entities: %w", err)
//	}
//
//	// Filter by entity type
//	people := GetEntitiesByType(entities, entityv1.EntityType_ENTITY_TYPE_PERSON)
//	vehicles := GetEntitiesByType(entities, entityv1.EntityType_ENTITY_TYPE_VEHICLE)
//
//	// Filter by section
//	arrestEntities := GetEntitiesBySection(entities, "arrest-section-id")
func ExtractHydratedEntities(
	ctx context.Context,
	databaseTransaction *sql.Tx,
	reportProtobuf *reports.Report,
	entitiesRepo entityRepository.EntityRepository,
) ([]*types.HydratedEntity, error) {
	// Handle missing repository gracefully - return empty result, not error
	if entitiesRepo == nil {
		return []*types.HydratedEntity{}, nil
	}

	var hydratedEntities []*types.HydratedEntity

	// Iterate through all report sections to find entity references
	for _, section := range reportProtobuf.Sections {
		// Extract entities from entity list sections (people, vehicles, properties, organizations)
		if section.GetEntityList() != nil {
			entityList := section.GetEntityList()
			for _, entityRef := range entityList.EntityRefs {
				entity, entityRetrievalError := entitiesRepo.GetLatestEntity(ctx, databaseTransaction, entityRef.Id)
				if entityRetrievalError == nil {
					hydratedEntities = append(hydratedEntities, &types.HydratedEntity{
						Entity:                  entity,
						ReferencedInSectionId:   section.Id,
						ReferencedInSectionType: section.Type.String(),
						ExtractionMetadata: map[string]interface{}{
							"extractedFrom": "section_entity_refs",
							"sectionId":     section.Id,
							"sectionType":   section.Type.String(),
							"relationType":  entityRef.RelationType,
							"entityRefId":   entityRef.Id,
							"displayName":   entityRef.DisplayName,
							"version":       entityRef.Version,
						},
					})
				}
				// Note: Failed entity retrieval is silently skipped to handle missing data gracefully
			}
		}

		// Extract entities from incident details sections
		if section.GetIncidentDetails() != nil {
			incidentDetails := section.GetIncidentDetails()

			// Process reporting person if linked to an entity
			if incidentDetails.ReportingPerson != nil && incidentDetails.ReportingPerson.AssetId != "" {
				// Note: This is an asset reference, not an entity reference
				// Future enhancement: could cross-reference asset-to-entity mappings
			}

			// Process responders if they reference entities
			for _, responder := range incidentDetails.Responders {
				if responder.AssetId != "" {
					// Note: This is an asset reference, not an entity reference
					// Future enhancement: could cross-reference asset-to-entity mappings
				}
			}
		}

		// Extract entities from arrest sections (arrestees)
		if section.GetArrestList() != nil {
			arrestList := section.GetArrestList()
			for _, arrest := range arrestList.Arrests {
				if arrest.Data != nil {
					// Check if arrestee is referenced as an entity
					if arresteeId := getStringFromProtobufStruct(arrest.Data, "arresteeId"); arresteeId != "" {
						entity, entityRetrievalError := entitiesRepo.GetLatestEntity(ctx, databaseTransaction, arresteeId)
						if entityRetrievalError == nil {
							hydratedEntities = append(hydratedEntities, &types.HydratedEntity{
								Entity:                  entity,
								ReferencedInSectionId:   section.Id,
								ReferencedInSectionType: section.Type.String(),
								ExtractionMetadata: map[string]interface{}{
									"extractedFrom": "arrest_arrestee_ref",
									"sectionId":     section.Id,
									"sectionType":   section.Type.String(),
									"arrestId":      arrest.Id,
									"context":       "arrestee",
								},
							})
						}
						// Note: Failed entity retrieval is silently skipped to handle missing data gracefully
					}
				}
			}
		}
	}

	// Remove duplicate entities while preserving all section reference metadata
	return deduplicateEntities(hydratedEntities), nil
}

// deduplicateEntities removes duplicate entities while preserving all section reference metadata.
//
// When the same entity is referenced in multiple sections of a report, this function
// consolidates them into a single HydratedEntity instance while preserving all the
// section reference information. This prevents duplicate entities in the results while
// maintaining complete context about where each entity was referenced.
//
// The deduplication strategy:
//   - Uses entity ID as the unique key for deduplication
//   - Keeps the first occurrence of each entity as the primary instance
//   - Aggregates all section references into a "sectionReferences" metadata array
//   - Preserves all other extraction metadata from the primary occurrence
//
// Parameters:
//   - entities: Array of hydrated entities that may contain duplicates
//
// Returns:
//   - []*types.HydratedEntity: Deduplicated entities with combined section references
//
// Example:
//
//	If the same person entity appears in both a "people list" section and an "arrest" section,
//	the result will be a single HydratedEntity with sectionReferences containing both sections.
func deduplicateEntities(entities []*types.HydratedEntity) []*types.HydratedEntity {
	entityDedupMap := make(map[string]*types.HydratedEntity)

	for _, hydratedEntity := range entities {
		entityId := hydratedEntity.Entity.Id

		if existingEntity, entityExists := entityDedupMap[entityId]; entityExists {
			// Entity already exists, combine section reference metadata
			if existingEntity.ExtractionMetadata == nil {
				existingEntity.ExtractionMetadata = make(map[string]interface{})
			}

			// Add this entity's section reference to the existing aggregated list
			existingSectionRefs := existingEntity.ExtractionMetadata["sectionReferences"]
			if existingSectionRefs == nil {
				existingSectionRefs = []map[string]interface{}{}
			}

			sectionReferencesList := existingSectionRefs.([]map[string]interface{})
			sectionReferencesList = append(sectionReferencesList, map[string]interface{}{
				"sectionId":   hydratedEntity.ReferencedInSectionId,
				"sectionType": hydratedEntity.ReferencedInSectionType,
			})

			existingEntity.ExtractionMetadata["sectionReferences"] = sectionReferencesList
		} else {
			// First time seeing this entity, initialize section references
			if hydratedEntity.ExtractionMetadata == nil {
				hydratedEntity.ExtractionMetadata = make(map[string]interface{})
			}

			hydratedEntity.ExtractionMetadata["sectionReferences"] = []map[string]interface{}{
				{
					"sectionId":   hydratedEntity.ReferencedInSectionId,
					"sectionType": hydratedEntity.ReferencedInSectionType,
				},
			}

			entityDedupMap[entityId] = hydratedEntity
		}
	}

	// Convert deduplication map back to slice
	var deduplicatedEntities []*types.HydratedEntity
	for _, entity := range entityDedupMap {
		deduplicatedEntities = append(deduplicatedEntities, entity)
	}

	return deduplicatedEntities
}

// getStringFromProtobufStruct safely extracts a string value from protobuf Struct data.
//
// This utility function provides safe access to string values stored in protobuf
// Struct fields, handling nil checks and type assertions gracefully. It's commonly
// used when extracting entity IDs from dynamic protobuf data structures.
//
// Parameters:
//   - data: The protobuf Struct containing the field
//   - key: The field name to extract
//
// Returns:
//   - string: The extracted string value, or empty string if not found or not a string
//
// Example:
//
//	arresteeId := getStringFromProtobufStruct(arrest.Data, "arresteeId")
//	if arresteeId != "" {
//	    // Process the arrestee entity
//	}
func getStringFromProtobufStruct(data *structpb.Struct, key string) string {
	if data != nil && data.Fields != nil {
		if field, fieldExists := data.Fields[key]; fieldExists {
			return field.GetStringValue()
		}
	}
	return ""
}

// GetEntitiesByType filters hydrated entities by entity type.
//
// This utility function provides a convenient way to filter entities by their
// protobuf EntityType, which is useful for processing specific categories of
// entities separately (e.g., people vs vehicles vs organizations).
//
// Common entity types:
//   - entityv1.EntityType_ENTITY_TYPE_PERSON: People (victims, suspects, witnesses)
//   - entityv1.EntityType_ENTITY_TYPE_VEHICLE: Vehicles involved in incidents
//   - entityv1.EntityType_ENTITY_TYPE_ORGANIZATION: Organizations, businesses, agencies
//   - entityv1.EntityType_ENTITY_TYPE_PROPERTY: Physical property or evidence
//
// Parameters:
//   - hydratedEntities: Array of hydrated entities to filter
//   - entityType: The protobuf EntityType to filter by
//
// Returns:
//   - []*types.HydratedEntity: Entities matching the specified type
//
// Example Usage:
//
//	entities, _ := ExtractHydratedEntities(ctx, tx, report, entitiesRepo)
//	people := GetEntitiesByType(entities, entityv1.EntityType_ENTITY_TYPE_PERSON)
//	vehicles := GetEntitiesByType(entities, entityv1.EntityType_ENTITY_TYPE_VEHICLE)
//
//	fmt.Printf("Found %d people and %d vehicles\n", len(people), len(vehicles))
func GetEntitiesByType(hydratedEntities []*types.HydratedEntity, entityType entityv1.EntityType) []*types.HydratedEntity {
	var filteredEntities []*types.HydratedEntity
	for _, hydratedEntity := range hydratedEntities {
		if hydratedEntity.Entity.EntityType == entityType {
			filteredEntities = append(filteredEntities, hydratedEntity)
		}
	}
	return filteredEntities
}

// GetEntitiesBySection filters hydrated entities by section ID.
//
// This utility function provides a way to find all entities that were referenced
// in a specific report section. This is useful for understanding which entities
// are associated with particular parts of a report.
//
// Note: If an entity appears in multiple sections, it will only be returned if
// one of those sections matches the specified sectionId. For entities that appear
// in multiple sections, check the "sectionReferences" metadata for complete context.
//
// Parameters:
//   - hydratedEntities: Array of hydrated entities to filter
//   - sectionId: The section ID to filter by
//
// Returns:
//   - []*types.HydratedEntity: Entities referenced in the specified section
//
// Example Usage:
//
//	entities, _ := ExtractHydratedEntities(ctx, tx, report, entitiesRepo)
//	arrestSectionEntities := GetEntitiesBySection(entities, "arrest-section-123")
//
//	for _, entity := range arrestSectionEntities {
//	    fmt.Printf("Entity %s appears in arrest section\n", entity.Entity.Id)
//	}
func GetEntitiesBySection(hydratedEntities []*types.HydratedEntity, sectionId string) []*types.HydratedEntity {
	var filteredEntities []*types.HydratedEntity
	for _, hydratedEntity := range hydratedEntities {
		if hydratedEntity.ReferencedInSectionId == sectionId {
			filteredEntities = append(filteredEntities, hydratedEntity)
		}
	}
	return filteredEntities
}
