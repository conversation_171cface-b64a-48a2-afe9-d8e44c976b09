package extractors

import (
	"context"
	"database/sql"

	reports "proto/hero/reports/v2"
	situationsv2 "proto/hero/situations/v2"
	situationsRepository "workflow/internal/situations/data"
)

// ExtractConnectedSituations extracts all situations connected to a report.
//
// This function retrieves complete situation data for all situations linked to a report,
// providing full context about the underlying incidents or events. Reports can be
// connected to situations through two primary mechanisms:
//
// Connection Types:
//  1. Direct Connection: The report's SituationId field links to a primary situation
//  2. Relationship Connection: Report relations can reference situations as related objects
//
// The function provides comprehensive situation hydration by:
//   - Replacing situation ID references with complete situation protobuf objects
//   - Automatically deduplicating situations found through multiple connection paths
//   - Maintaining all situation data including status, type, and metadata
//   - Handling missing or invalid situation references gracefully
//
// Situation Context:
// Situations represent the broader context or incident that a report is documenting.
// They provide essential information about:
//   - The type of incident (emergency, investigation, routine, etc.)
//   - Current status of the situation (active, resolved, pending, etc.)
//   - Timeline and location information
//   - Associated resources and responding assets
//
// Parameters:
//   - ctx: Context for database operations and cancellation
//   - databaseTransaction: Database transaction for consistency across operations
//   - reportProtobuf: Source report containing situation references
//   - situationsRepo: Repository interface for situation data access (may be nil)
//
// Returns:
//   - []*situationsv2.Situation: Complete situation data for all connected situations
//   - error: Database or repository errors (returns empty slice for missing repository)
//
// Thread Safety:
// This function is thread-safe and can be called concurrently from multiple goroutines
// as long as each call uses its own database transaction.
//
// Example Usage:
//
//	situations, err := ExtractConnectedSituations(ctx, transaction, report, situationsRepo)
//	if err != nil {
//	    return fmt.Errorf("failed to extract situations: %w", err)
//	}
//
//	// Get the primary situation
//	primary := GetPrimarySituation(situations, report)
//
//	// Filter by status
//	active := GetSituationsByStatus(situations, situationsv2.SituationStatus_SITUATION_STATUS_ACTIVE)
//	resolved := GetSituationsByStatus(situations, situationsv2.SituationStatus_SITUATION_STATUS_RESOLVED)
func ExtractConnectedSituations(
	ctx context.Context,
	databaseTransaction *sql.Tx,
	reportProtobuf *reports.Report,
	situationsRepo situationsRepository.SituationRepository,
) ([]*situationsv2.Situation, error) {
	// Handle missing repository gracefully - return empty result, not error
	if situationsRepo == nil {
		return []*situationsv2.Situation{}, nil
	}

	var connectedSituations []*situationsv2.Situation

	// Extract primary situation directly linked to the report
	if reportProtobuf.SituationId != "" {
		primarySituation, situationRetrievalError := situationsRepo.GetSituation(ctx, databaseTransaction, reportProtobuf.SituationId)
		if situationRetrievalError == nil {
			connectedSituations = append(connectedSituations, primarySituation)
		}
		// Note: Failed situation retrieval is silently skipped to handle missing data gracefully
	}

	// Extract any related situations mentioned in report relationships
	for _, relation := range reportProtobuf.Relations {
		// Check if ObjectA references a situation
		if relation.ObjectA != nil && relation.ObjectA.ObjectType == "situation" && relation.ObjectA.GlobalId != "" {
			// Avoid duplicates - check if we already have this situation
			if !containsSituation(connectedSituations, relation.ObjectA.GlobalId) {
				relatedSituation, situationRetrievalError := situationsRepo.GetSituation(ctx, databaseTransaction, relation.ObjectA.GlobalId)
				if situationRetrievalError == nil {
					connectedSituations = append(connectedSituations, relatedSituation)
				}
				// Note: Failed situation retrieval is silently skipped to handle missing data gracefully
			}
		}

		// Check if ObjectB references a situation
		if relation.ObjectB != nil && relation.ObjectB.ObjectType == "situation" && relation.ObjectB.GlobalId != "" {
			// Avoid duplicates - check if we already have this situation
			if !containsSituation(connectedSituations, relation.ObjectB.GlobalId) {
				relatedSituation, situationRetrievalError := situationsRepo.GetSituation(ctx, databaseTransaction, relation.ObjectB.GlobalId)
				if situationRetrievalError == nil {
					connectedSituations = append(connectedSituations, relatedSituation)
				}
				// Note: Failed situation retrieval is silently skipped to handle missing data gracefully
			}
		}
	}

	return connectedSituations, nil
}

// containsSituation checks if a situation with the given ID is already in the slice.
//
// This internal utility function prevents duplicate situations from being added to the
// extraction results when the same situation is referenced through multiple connection
// paths (e.g., both direct SituationId and relationship references).
//
// Parameters:
//   - situations: Array of situations to search through
//   - situationId: The situation ID to check for
//
// Returns:
//   - bool: True if the situation is already present, false otherwise
func containsSituation(situations []*situationsv2.Situation, situationId string) bool {
	for _, situation := range situations {
		if situation.Id == situationId {
			return true
		}
	}
	return false
}

// GetPrimarySituation returns the primary situation linked directly to the report.
//
// This utility function identifies the primary situation associated with a report,
// which is the situation referenced by the report's SituationId field. This represents
// the main incident or event that the report is documenting.
//
// The primary situation is distinguished from related situations that may be referenced
// through relationship connections. It typically represents the core incident being
// reported on, while related situations might be supporting or connected incidents.
//
// Parameters:
//   - situations: Array of connected situations from ExtractConnectedSituations
//   - reportProtobuf: The report containing the primary situation reference
//
// Returns:
//   - *situationsv2.Situation: The primary situation, or nil if not found or not set
//
// Example Usage:
//
//	situations, _ := ExtractConnectedSituations(ctx, tx, report, situationsRepo)
//	primary := GetPrimarySituation(situations, report)
//	if primary != nil {
//	    fmt.Printf("Primary incident: %s (Status: %s)\n", primary.Title, primary.Status)
//	}
func GetPrimarySituation(situations []*situationsv2.Situation, reportProtobuf *reports.Report) *situationsv2.Situation {
	if reportProtobuf.SituationId == "" {
		return nil
	}

	for _, situation := range situations {
		if situation.Id == reportProtobuf.SituationId {
			return situation
		}
	}

	return nil
}

// GetSituationsByStatus filters situations by their current status.
//
// This utility function provides a way to filter situations based on their current
// operational status, which is useful for understanding the state of connected incidents
// and determining appropriate actions or prioritization.
//
// Common situation statuses:
//   - SITUATION_STATUS_ACTIVE: Currently active/ongoing incidents
//   - SITUATION_STATUS_RESOLVED: Completed or resolved incidents
//   - SITUATION_STATUS_PENDING: Incidents awaiting action or classification
//   - SITUATION_STATUS_CANCELLED: Cancelled or invalidated incidents
//
// Parameters:
//   - situations: Array of situations to filter
//   - status: The protobuf SituationStatus to filter by
//
// Returns:
//   - []*situationsv2.Situation: Situations matching the specified status
//
// Example Usage:
//
//	situations, _ := ExtractConnectedSituations(ctx, tx, report, situationsRepo)
//	active := GetSituationsByStatus(situations, situationsv2.SituationStatus_SITUATION_STATUS_ACTIVE)
//	resolved := GetSituationsByStatus(situations, situationsv2.SituationStatus_SITUATION_STATUS_RESOLVED)
//
//	fmt.Printf("Active incidents: %d, Resolved incidents: %d\n", len(active), len(resolved))
func GetSituationsByStatus(situations []*situationsv2.Situation, status situationsv2.SituationStatus) []*situationsv2.Situation {
	var filteredSituations []*situationsv2.Situation
	for _, situation := range situations {
		if situation.Status == status {
			filteredSituations = append(filteredSituations, situation)
		}
	}
	return filteredSituations
}

// GetSituationsByType filters situations by their type.
//
// This utility function provides a way to filter situations based on their type,
// which categorizes the nature of the incident. This is useful for understanding
// what kinds of incidents are connected to a report and for processing different
// types of situations with appropriate workflows.
//
// Common situation types:
//   - SITUATION_TYPE_EMERGENCY: Emergency response situations
//   - SITUATION_TYPE_INVESTIGATION: Investigation or case situations
//   - SITUATION_TYPE_ROUTINE: Routine operational situations
//   - SITUATION_TYPE_ADMINISTRATIVE: Administrative or support situations
//
// Parameters:
//   - situations: Array of situations to filter
//   - situationType: The protobuf SituationType to filter by
//
// Returns:
//   - []*situationsv2.Situation: Situations matching the specified type
//
// Example Usage:
//
//	situations, _ := ExtractConnectedSituations(ctx, tx, report, situationsRepo)
//	emergencies := GetSituationsByType(situations, situationsv2.SituationType_SITUATION_TYPE_EMERGENCY)
//	investigations := GetSituationsByType(situations, situationsv2.SituationType_SITUATION_TYPE_INVESTIGATION)
//
//	if len(emergencies) > 0 {
//	    fmt.Printf("Report involves %d emergency situations\n", len(emergencies))
//	}
func GetSituationsByType(situations []*situationsv2.Situation, situationType situationsv2.SituationType) []*situationsv2.Situation {
	var filteredSituations []*situationsv2.Situation
	for _, situation := range situations {
		if situation.Type == situationType {
			filteredSituations = append(filteredSituations, situation)
		}
	}
	return filteredSituations
}
