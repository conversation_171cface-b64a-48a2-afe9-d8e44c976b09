package parallel

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"time"

	"workflow/internal/etl/extraction/reports/v2/core"
	"workflow/internal/etl/extraction/reports/v2/extractors"
	"workflow/internal/etl/extraction/types"

	reports "proto/hero/reports/v2"
	situationsv2 "proto/hero/situations/v2"
	assetsRepository "workflow/internal/assets/data"
	entityRepository "workflow/internal/entity/data"
	reportsRepository "workflow/internal/reports/data"
	situationsRepository "workflow/internal/situations/data"
)

// TypedParallelReportDataExtractor provides high-performance parallel extraction with typed data.
//
// This extractor implements the same 7-phase parallel extraction pipeline as the original
// but returns fully typed protobuf data instead of generic maps, providing complete
// visibility into the data structure while maintaining maximum performance.
//
// Parallel Execution Architecture:
//
//	Phase 1: Base Report Data (foundation - must complete first)
//	Phases 2-6: Execute concurrently after Phase 1:
//	  - Phase 2: Sections with hydrated entities/assets
//	  - Phase 3: Assets with role context
//	  - Phase 4: Relationships with resolved object names
//	  - Phase 5: Entities with section reference context
//	  - Phase 6: Connected situations
//	Phase 7: Final enrichment and hydration (after dependencies)
//
// Performance Benefits:
//   - 5 phases run concurrently (phases 2-6)
//   - Dependency-aware execution minimizes waiting time
//   - Batch operations eliminate N+1 query problems
//   - Read-only transactions optimize database performance
type ParallelReportDataExtractor struct {
	// Core database connection for transaction management
	databaseConnection *sql.DB

	// Repository interfaces for accessing different data sources
	reportsRepository    reportsRepository.ReportRepository
	situationsRepository situationsRepository.SituationRepository
	assetsRepository     assetsRepository.AssetRepository
	entityRepository     entityRepository.EntityRepository
}

// NewTypedParallelReportDataExtractor creates a new typed parallel report data extractor.
//
// This constructor validates repository dependencies and creates a ready-to-use
// parallel extractor with typed data capabilities.
//
// Parameters:
//   - databaseConnection: SQL database connection for transactions
//   - reportsRepository: Repository interface for accessing report data
//   - situationsRepository: Repository interface for accessing situation data (can be nil)
//   - assetsRepository: Repository interface for accessing asset data (can be nil)
//   - entityRepository: Repository interface for accessing entity data (can be nil)
//
// Returns:
//   - *TypedParallelReportDataExtractor: Ready-to-use typed parallel extractor
//   - error: Validation errors
func NewParallelReportDataExtractor(
	databaseConnection *sql.DB,
	reportsRepository reportsRepository.ReportRepository,
	situationsRepository situationsRepository.SituationRepository,
	assetsRepository assetsRepository.AssetRepository,
	entityRepository entityRepository.EntityRepository,
) (*ParallelReportDataExtractor, error) {
	// Validate required dependencies
	if databaseConnection == nil {
		return nil, fmt.Errorf("database connection is required")
	}
	if reportsRepository == nil {
		return nil, fmt.Errorf("reports repository is required")
	}

	// Note: other repositories are optional and will be handled gracefully

	return &ParallelReportDataExtractor{
		databaseConnection:   databaseConnection,
		reportsRepository:    reportsRepository,
		situationsRepository: situationsRepository,
		assetsRepository:     assetsRepository,
		entityRepository:     entityRepository,
	}, nil
}

// Extract performs high-performance parallel extraction with typed data.
//
// This method implements the 7-phase parallel extraction pipeline with dependency-aware
// execution. Phases 2-6 run concurrently for maximum performance while maintaining
// data consistency and type safety.
//
// Execution Flow:
//  1. Phase 1: Extract base report data (foundation)
//  2. Phases 2-6: Execute concurrently:
//     - Sections with hydrated references
//     - Assets with role context
//     - Relationships with resolved names
//     - Entities with section context
//     - Connected situations
//  3. Phase 7: Final aggregation and enrichment
//  4. Return fully typed extraction result
//
// Parameters:
//   - ctx: Context for cancellation and timeouts
//   - reportID: Unique identifier of the report to extract
//
// Returns:
//   - *types.ExtractionResult: Complete typed extraction result with metrics
//   - error: Database, repository, or extraction errors
func (extractor *ParallelReportDataExtractor) Extract(
	ctx context.Context,
	reportID string,
) (*types.ExtractionResult, error) {
	startTime := time.Now()

	// Create parallel extraction orchestrator
	orchestrator := NewParallelExtractionOrchestrator()

	// Register all extraction phases
	extractor.registerExtractionPhases(orchestrator)

	// PHASE 1: Extract base report data (foundation for all other phases)
	// Create a separate transaction for the base report data
	tx, err := extractor.databaseConnection.BeginTx(ctx, &sql.TxOptions{
		ReadOnly:  true,
		Isolation: sql.LevelReadCommitted,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to begin database transaction: %w", err)
	}
	defer func() {
		if rollbackErr := tx.Rollback(); rollbackErr != nil {
			log.Printf("failed to rollback transaction: %v", rollbackErr)
		}
	}()

	reportProtobuf, err := extractor.reportsRepository.GetReport(ctx, tx, reportID)
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve report %s: %w", reportID, err)
	}

	// Create initial extraction data with base report
	initialData := &types.ExtractedData{
		Reports:       []*types.HydratedReport{},
		Assets:        []*types.HydratedAsset{},
		Entities:      []*types.HydratedEntity{},
		Sections:      []*types.HydratedSection{},
		Relationships: []*types.HydratedRelationship{},
		Situations:    []*situationsv2.Situation{},
		Metadata: map[string]interface{}{
			core.FieldReportID:         reportID,
			"baseReportData":           reportProtobuf,
			core.FieldExtractionMethod: core.ExtractionMethodParallel,
		},
		ExtractedAt: startTime,
	}

	// Execute all phases in parallel with dependency management (each phase gets its own transaction)
	extractedData, err := orchestrator.ExecuteAllPhasesInParallel(ctx, extractor.databaseConnection, initialData)
	if err != nil {
		return nil, fmt.Errorf("parallel extraction failed: %w", err)
	}

	// Create final hydrated report with all connected data
	hydratedReport := extractor.createHydratedReport(reportProtobuf, extractedData, startTime)
	extractedData.Reports = []*types.HydratedReport{hydratedReport}

	// Remove baseReportData from metadata to avoid circular references in JSON serialization
	delete(extractedData.Metadata, "baseReportData")

	// Create extraction metrics
	metrics := &types.ExtractionMetrics{
		TotalDuration: time.Since(startTime),
		QueryCount:    0, // Would need to track this across phases
		QueryTime:     0, // Would need to track this across phases
		ItemsProcessed: map[string]int{
			"reports":       len(extractedData.Reports),
			"assets":        len(extractedData.Assets),
			"entities":      len(extractedData.Entities),
			"sections":      len(extractedData.Sections),
			"relationships": len(extractedData.Relationships),
			"situations":    len(extractedData.Situations),
		},
	}

	// Create warnings from any phase errors (collect from orchestrator if needed)
	var warnings []types.ExtractionWarning

	return &types.ExtractionResult{
		Data:     extractedData,
		Warnings: warnings,
		Metrics:  metrics,
		Success:  true,
	}, nil
}

// registerExtractionPhases registers all extraction phases with their dependencies.
func (extractor *ParallelReportDataExtractor) registerExtractionPhases(
	orchestrator *ParallelExtractionOrchestrator,
) {
	// Phase 2: Sections (depends on base report data)
	orchestrator.RegisterExtractionPhase(&ExtractionPhase{
		Name:         "sections",
		Dependencies: []string{}, // Base report data is provided in initial data
		Execute:      extractor.extractSectionsPhase,
	})

	// Phase 3: Assets (depends on base report data)
	orchestrator.RegisterExtractionPhase(&ExtractionPhase{
		Name:         "assets",
		Dependencies: []string{}, // Base report data is provided in initial data
		Execute:      extractor.extractAssetsPhase,
	})

	// Phase 4: Relationships (depends on base report data)
	orchestrator.RegisterExtractionPhase(&ExtractionPhase{
		Name:         "relationships",
		Dependencies: []string{}, // Base report data is provided in initial data
		Execute:      extractor.extractRelationshipsPhase,
	})

	// Phase 5: Entities (depends on base report data)
	orchestrator.RegisterExtractionPhase(&ExtractionPhase{
		Name:         "entities",
		Dependencies: []string{}, // Base report data is provided in initial data
		Execute:      extractor.extractEntitiesPhase,
	})

	// Phase 6: Situations (depends on base report data)
	orchestrator.RegisterExtractionPhase(&ExtractionPhase{
		Name:         "situations",
		Dependencies: []string{}, // Base report data is provided in initial data
		Execute:      extractor.extractSituationsPhase,
	})
}

// extractSectionsPhase extracts sections with hydrated entities and assets.
func (extractor *ParallelReportDataExtractor) extractSectionsPhase(
	ctx context.Context,
	tx *sql.Tx,
	inputData *types.ExtractedData,
) (*PhaseResult, error) {
	// Get base report from metadata
	reportProtobuf, ok := inputData.Metadata["baseReportData"].(*reports.Report)
	if !ok {
		return nil, fmt.Errorf("base report data not found in input")
	}

	hydratedSections, err := extractors.ExtractHydratedSections(
		ctx, tx, reportProtobuf, extractor.reportsRepository,
		extractor.assetsRepository, extractor.entityRepository)
	if err != nil {
		return &PhaseResult{
			PhaseName:      "sections",
			ExecutionError: err,
		}, nil
	}

	return &PhaseResult{
		PhaseName: "sections",
		Sections:  hydratedSections,
		Metadata: map[string]interface{}{
			"sectionsCount": len(hydratedSections),
		},
	}, nil
}

// extractAssetsPhase extracts assets with role context.
func (extractor *ParallelReportDataExtractor) extractAssetsPhase(
	ctx context.Context,
	tx *sql.Tx,
	inputData *types.ExtractedData,
) (*PhaseResult, error) {
	reportProtobuf, ok := inputData.Metadata["baseReportData"].(*reports.Report)
	if !ok {
		return nil, fmt.Errorf("base report data not found in input")
	}

	hydratedAssets, err := extractors.ExtractHydratedAssets(
		ctx, tx, reportProtobuf, extractor.assetsRepository)
	if err != nil {
		return &PhaseResult{
			PhaseName:      "assets",
			ExecutionError: err,
		}, nil
	}

	return &PhaseResult{
		PhaseName: "assets",
		Assets:    hydratedAssets,
		Metadata: map[string]interface{}{
			"assetsCount": len(hydratedAssets),
		},
	}, nil
}

// extractRelationshipsPhase extracts relationships with resolved object names.
func (extractor *ParallelReportDataExtractor) extractRelationshipsPhase(
	ctx context.Context,
	tx *sql.Tx,
	inputData *types.ExtractedData,
) (*PhaseResult, error) {
	reportProtobuf, ok := inputData.Metadata["baseReportData"].(*reports.Report)
	if !ok {
		return nil, fmt.Errorf("base report data not found in input")
	}

	hydratedRelationships, err := extractors.ExtractHydratedRelationships(
		ctx, tx, reportProtobuf, extractor.entityRepository, extractor.assetsRepository)
	if err != nil {
		return &PhaseResult{
			PhaseName:      "relationships",
			ExecutionError: err,
		}, nil
	}

	return &PhaseResult{
		PhaseName:     "relationships",
		Relationships: hydratedRelationships,
		Metadata: map[string]interface{}{
			"relationshipsCount": len(hydratedRelationships),
		},
	}, nil
}

// extractEntitiesPhase extracts entities with section reference context.
func (extractor *ParallelReportDataExtractor) extractEntitiesPhase(
	ctx context.Context,
	tx *sql.Tx,
	inputData *types.ExtractedData,
) (*PhaseResult, error) {
	reportProtobuf, ok := inputData.Metadata["baseReportData"].(*reports.Report)
	if !ok {
		return nil, fmt.Errorf("base report data not found in input")
	}

	hydratedEntities, err := extractors.ExtractHydratedEntities(
		ctx, tx, reportProtobuf, extractor.entityRepository)
	if err != nil {
		return &PhaseResult{
			PhaseName:      "entities",
			ExecutionError: err,
		}, nil
	}

	return &PhaseResult{
		PhaseName: "entities",
		Entities:  hydratedEntities,
		Metadata: map[string]interface{}{
			"entitiesCount": len(hydratedEntities),
		},
	}, nil
}

// extractSituationsPhase extracts connected situations.
func (extractor *ParallelReportDataExtractor) extractSituationsPhase(
	ctx context.Context,
	tx *sql.Tx,
	inputData *types.ExtractedData,
) (*PhaseResult, error) {
	reportProtobuf, ok := inputData.Metadata["baseReportData"].(*reports.Report)
	if !ok {
		return nil, fmt.Errorf("base report data not found in input")
	}

	connectedSituations, err := extractors.ExtractConnectedSituations(
		ctx, tx, reportProtobuf, extractor.situationsRepository)
	if err != nil {
		return &PhaseResult{
			PhaseName:      "situations",
			ExecutionError: err,
		}, nil
	}

	return &PhaseResult{
		PhaseName:  "situations",
		Situations: connectedSituations,
		Metadata: map[string]interface{}{
			"situationsCount": len(connectedSituations),
		},
	}, nil
}

// createHydratedReport creates a hydrated report with all connected data.
func (extractor *ParallelReportDataExtractor) createHydratedReport(
	reportProtobuf *reports.Report,
	extractedData *types.ExtractedData,
	extractionTime time.Time,
) *types.HydratedReport {
	hydratedReport := &types.HydratedReport{
		Report: reportProtobuf,
		HydrationMetadata: map[string]interface{}{
			"extractedAt":              extractionTime,
			core.FieldExtractionMethod: core.ExtractionMethodParallel,
			core.FieldReportID:         reportProtobuf.Id,
		},
	}

	// Link hydrated assets to report based on role
	for _, asset := range extractedData.Assets {
		switch asset.Role {
		case core.RoleCreator:
			hydratedReport.CreatorAsset = asset.Asset
		case core.RoleAuthor:
			hydratedReport.AuthorAsset = asset.Asset
		case core.RoleWatcher:
			hydratedReport.WatcherAssets = append(hydratedReport.WatcherAssets, asset.Asset)
		}
	}

	// Link connected situation to report (use first one if multiple)
	if len(extractedData.Situations) > 0 {
		hydratedReport.ConnectedSituation = extractedData.Situations[0]
	}

	return hydratedReport
}
