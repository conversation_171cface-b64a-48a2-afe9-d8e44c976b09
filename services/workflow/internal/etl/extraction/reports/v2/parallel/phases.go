package parallel

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"sync"
	"time"

	situationsv2 "proto/hero/situations/v2"
	"workflow/internal/etl/extraction/reports/v2/core"
	"workflow/internal/etl/extraction/types"
)

// TypedExtractionPhase represents a single extraction phase with typed data and dependencies.
//
// Each phase defines a unit of work that can be executed in parallel with other phases,
// provided its dependencies are satisfied. The Execute function receives consolidated
// inputs from all completed dependency phases and returns typed extraction data.
//
// This provides type safety over the generic map[string]interface{} approach
// while maintaining the same parallel execution capabilities.
type ExtractionPhase struct {
	// Name is the unique identifier for this extraction phase
	Name string

	// Dependencies is a list of phase names that must complete before this phase can execute
	Dependencies []string

	// Execute is the function that performs the actual extraction work for this phase
	// Returns typed data specific to this phase's responsibility
	Execute func(extractionContext context.Context, databaseTransaction *sql.Tx, phaseInputData *types.ExtractedData) (*PhaseResult, error)
}

// TypedPhaseResult contains the result of a typed extraction phase execution.
//
// This struct provides typed data for each extraction component while maintaining
// performance metrics and error information from the parallel execution framework.
type PhaseResult struct {
	// PhaseName identifies which phase produced this result
	PhaseName string

	// Reports contains hydrated reports from this phase (usually Phase 1)
	Reports []*types.HydratedReport

	// Assets contains hydrated assets from this phase (usually Phase 3)
	Assets []*types.HydratedAsset

	// Entities contains hydrated entities from this phase (usually Phase 5)
	Entities []*types.HydratedEntity

	// Sections contains hydrated sections from this phase (usually Phase 2)
	Sections []*types.HydratedSection

	// Relationships contains hydrated relationships from this phase (usually Phase 4)
	Relationships []*types.HydratedRelationship

	// Situations contains connected situations from this phase (usually Phase 6)
	Situations []*situationsv2.Situation

	// Metadata contains phase-specific metadata
	Metadata map[string]interface{}

	// ExecutionStartTime records when the phase started executing
	ExecutionStartTime time.Time

	// ExecutionEndTime records when the phase completed
	ExecutionEndTime time.Time

	// ExecutionError contains any error that occurred during execution (nil if successful)
	ExecutionError error
}

// TypedParallelExtractionOrchestrator orchestrates parallel execution of typed extraction phases.
//
// This orchestrator maintains the same parallel execution capabilities as the original
// but works with typed extraction data instead of generic maps, providing better
// type safety and data structure visibility.
//
// Key Responsibilities:
//   - Manages typed phase registration and dependency validation
//   - Coordinates parallel execution using goroutines and channels
//   - Handles thread-safe result collection and aggregation
//   - Provides timeout and cancellation support
//   - Tracks performance metrics for each phase
//   - Returns fully typed extraction results
type ParallelExtractionOrchestrator struct {
	// registeredPhases contains all phases that have been registered for execution
	registeredPhases map[string]*ExtractionPhase

	// phaseExecutionResults contains the results of executed phases
	phaseExecutionResults map[string]*PhaseResult

	// concurrencyLock protects access to shared state during concurrent operations
	concurrencyLock sync.RWMutex
}

// NewTypedParallelExtractionOrchestrator creates a new typed parallel extraction orchestrator.
//
// This function creates a new orchestrator instance with empty phase and result maps.
// The orchestrator is ready to accept typed phase registrations and execute them in parallel.
//
// Returns:
//
//	*TypedParallelExtractionOrchestrator: A new orchestrator instance ready for phase registration
func NewParallelExtractionOrchestrator() *ParallelExtractionOrchestrator {
	return &ParallelExtractionOrchestrator{
		registeredPhases:      make(map[string]*ExtractionPhase),
		phaseExecutionResults: make(map[string]*PhaseResult),
	}
}

// RegisterExtractionPhase registers a typed extraction phase for parallel execution.
//
// This method adds a phase to the orchestrator's execution queue. Phases with no dependencies
// will start immediately when execution begins. Phases with dependencies will wait for
// their dependencies to complete before starting.
//
// Parameters:
//
//	phase: The typed extraction phase to register
//
// Thread Safety:
//
//	This method is thread-safe and can be called from multiple goroutines.
func (orchestrator *ParallelExtractionOrchestrator) RegisterExtractionPhase(phase *ExtractionPhase) {
	orchestrator.concurrencyLock.Lock()
	defer orchestrator.concurrencyLock.Unlock()

	orchestrator.registeredPhases[phase.Name] = phase
}

// ExecuteAllPhasesInParallel executes all registered phases with dependency-aware parallelization.
//
// This method coordinates the parallel execution of all registered phases while respecting
// their dependencies. Phases with no dependencies start immediately, and as phases complete,
// dependent phases are automatically queued for execution.
//
// Execution Flow:
//  1. Start all phases with no dependencies
//  2. As phases complete, check which dependent phases can now start
//  3. Continue until all phases complete or timeout/cancellation occurs
//  4. Aggregate all results into a single typed extraction result
//
// Parameters:
//
//	ctx: Context for cancellation and timeout handling
//	db: Database connection (each phase will create its own transaction)
//	initialData: Initial data to pass to phases (usually contains base report data)
//
// Returns:
//
//	*types.ExtractedData: Aggregated results from all phases
//	error: Any errors that occurred during execution
func (orchestrator *ParallelExtractionOrchestrator) ExecuteAllPhasesInParallel(
	ctx context.Context,
	db *sql.DB,
	initialData *types.ExtractedData,
) (*types.ExtractedData, error) {
	orchestrator.concurrencyLock.Lock()
	totalPhases := len(orchestrator.registeredPhases)
	orchestrator.concurrencyLock.Unlock()

	if totalPhases == 0 {
		return initialData, nil
	}

	// Channel for phase completion notifications
	phaseCompletionChannel := make(chan *PhaseResult, totalPhases)

	// Track which phases have completed
	completedPhases := make(map[string]bool)

	// Track which phases have been started to avoid double-starting
	startedPhases := make(map[string]bool)

	// Start phases with no dependencies
	orchestrator.startIndependentPhases(ctx, db, initialData, phaseCompletionChannel, startedPhases)

	// Process phase completions and start dependent phases
	for len(completedPhases) < totalPhases {
		select {
		case result := <-phaseCompletionChannel:
			orchestrator.concurrencyLock.Lock()
			orchestrator.phaseExecutionResults[result.PhaseName] = result
			completedPhases[result.PhaseName] = true
			orchestrator.concurrencyLock.Unlock()

			if result.ExecutionError != nil {
				// Log the error for debugging
				fmt.Printf("Phase %s failed with error: %v\n", result.PhaseName, result.ExecutionError)
				// Continue processing other phases even if one fails
				continue
			}

			// Check if any dependent phases can now start
			orchestrator.startReadyDependentPhases(ctx, db, completedPhases, phaseCompletionChannel, startedPhases)

		case <-ctx.Done():
			return nil, ctx.Err()
		}
	}

	// Aggregate results from all phases
	return orchestrator.aggregateTypedResults(initialData), nil
}

// startIndependentPhases starts all phases that have no dependencies.
func (orchestrator *ParallelExtractionOrchestrator) startIndependentPhases(
	ctx context.Context,
	db *sql.DB,
	initialData *types.ExtractedData,
	resultChannel chan<- *PhaseResult,
	startedPhases map[string]bool,
) {
	orchestrator.concurrencyLock.RLock()
	defer orchestrator.concurrencyLock.RUnlock()

	for _, phase := range orchestrator.registeredPhases {
		if len(phase.Dependencies) == 0 {
			startedPhases[phase.Name] = true
			go orchestrator.executePhase(ctx, db, phase, initialData, resultChannel)
		}
	}
}

// startReadyDependentPhases starts phases whose dependencies have been satisfied.
func (orchestrator *ParallelExtractionOrchestrator) startReadyDependentPhases(
	ctx context.Context,
	db *sql.DB,
	completedPhases map[string]bool,
	resultChannel chan<- *PhaseResult,
	startedPhases map[string]bool,
) {
	orchestrator.concurrencyLock.RLock()
	defer orchestrator.concurrencyLock.RUnlock()

	for _, phase := range orchestrator.registeredPhases {
		// Skip if already completed or already started
		if completedPhases[phase.Name] || startedPhases[phase.Name] {
			continue
		}

		// Check if all dependencies are satisfied
		allDependenciesSatisfied := true
		for _, dependency := range phase.Dependencies {
			if !completedPhases[dependency] {
				allDependenciesSatisfied = false
				break
			}
		}

		if allDependenciesSatisfied {
			// Prepare input data from completed dependencies
			inputData := orchestrator.prepareInputDataFromDependencies(phase)
			startedPhases[phase.Name] = true
			go orchestrator.executePhase(ctx, db, phase, inputData, resultChannel)
		}
	}
}

// executePhase executes a single phase and sends the result to the completion channel.
func (orchestrator *ParallelExtractionOrchestrator) executePhase(
	ctx context.Context,
	db *sql.DB,
	phase *ExtractionPhase,
	inputData *types.ExtractedData,
	resultChannel chan<- *PhaseResult,
) {
	startTime := time.Now()

	result := &PhaseResult{
		PhaseName:          phase.Name,
		ExecutionStartTime: startTime,
		Metadata:           make(map[string]interface{}),
	}

	// Create a separate read-only transaction for this phase to avoid concurrency issues
	tx, err := db.BeginTx(ctx, &sql.TxOptions{
		ReadOnly:  true,
		Isolation: sql.LevelReadCommitted,
	})
	if err != nil {
		result.ExecutionError = fmt.Errorf("failed to begin transaction for phase %s: %w", phase.Name, err)
		result.ExecutionEndTime = time.Now()
		resultChannel <- result
		return
	}
	defer func() {
		if rollbackErr := tx.Rollback(); rollbackErr != nil {
			log.Printf("failed to rollback transaction: %v", rollbackErr)
		}
	}()

	// Execute the phase with timeout
	fmt.Printf("Executing phase: %s\n", phase.Name)
	phaseResult, err := phase.Execute(ctx, tx, inputData)
	fmt.Printf("Phase %s completed with error: %v\n", phase.Name, err)

	result.ExecutionEndTime = time.Now()
	result.ExecutionError = err

	if phaseResult != nil {
		result.Reports = phaseResult.Reports
		result.Assets = phaseResult.Assets
		result.Entities = phaseResult.Entities
		result.Sections = phaseResult.Sections
		result.Relationships = phaseResult.Relationships
		result.Situations = phaseResult.Situations
		result.Metadata = phaseResult.Metadata
	}

	resultChannel <- result
}

// prepareInputDataFromDependencies creates input data by aggregating results from dependency phases.
func (orchestrator *ParallelExtractionOrchestrator) prepareInputDataFromDependencies(
	phase *ExtractionPhase,
) *types.ExtractedData {
	inputData := &types.ExtractedData{
		Reports:       []*types.HydratedReport{},
		Assets:        []*types.HydratedAsset{},
		Entities:      []*types.HydratedEntity{},
		Sections:      []*types.HydratedSection{},
		Relationships: []*types.HydratedRelationship{},
		Situations:    []*situationsv2.Situation{},
		Metadata:      make(map[string]interface{}),
		ExtractedAt:   time.Now(),
	}

	// Aggregate data from all dependency phases
	for _, dependencyName := range phase.Dependencies {
		if dependencyResult, exists := orchestrator.phaseExecutionResults[dependencyName]; exists {
			inputData.Reports = append(inputData.Reports, dependencyResult.Reports...)
			inputData.Assets = append(inputData.Assets, dependencyResult.Assets...)
			inputData.Entities = append(inputData.Entities, dependencyResult.Entities...)
			inputData.Sections = append(inputData.Sections, dependencyResult.Sections...)
			inputData.Relationships = append(inputData.Relationships, dependencyResult.Relationships...)
			inputData.Situations = append(inputData.Situations, dependencyResult.Situations...)
		}
	}

	return inputData
}

// aggregateTypedResults combines results from all phases into a single ExtractedData structure.
func (orchestrator *ParallelExtractionOrchestrator) aggregateTypedResults(
	initialData *types.ExtractedData,
) *types.ExtractedData {
	aggregatedData := &types.ExtractedData{
		Reports:       []*types.HydratedReport{},
		Assets:        []*types.HydratedAsset{},
		Entities:      []*types.HydratedEntity{},
		Sections:      []*types.HydratedSection{},
		Relationships: []*types.HydratedRelationship{},
		Situations:    []*situationsv2.Situation{},
		Metadata:      make(map[string]interface{}),
		ExtractedAt:   initialData.ExtractedAt,
	}

	// Start with initial data
	aggregatedData.Reports = append(aggregatedData.Reports, initialData.Reports...)
	aggregatedData.Assets = append(aggregatedData.Assets, initialData.Assets...)
	aggregatedData.Entities = append(aggregatedData.Entities, initialData.Entities...)
	aggregatedData.Sections = append(aggregatedData.Sections, initialData.Sections...)
	aggregatedData.Relationships = append(aggregatedData.Relationships, initialData.Relationships...)
	aggregatedData.Situations = append(aggregatedData.Situations, initialData.Situations...)

	// Aggregate results from all phases
	orchestrator.concurrencyLock.RLock()
	defer orchestrator.concurrencyLock.RUnlock()

	for _, result := range orchestrator.phaseExecutionResults {
		if result.ExecutionError == nil {
			aggregatedData.Reports = append(aggregatedData.Reports, result.Reports...)
			aggregatedData.Assets = append(aggregatedData.Assets, result.Assets...)
			aggregatedData.Entities = append(aggregatedData.Entities, result.Entities...)
			aggregatedData.Sections = append(aggregatedData.Sections, result.Sections...)
			aggregatedData.Relationships = append(aggregatedData.Relationships, result.Relationships...)
			aggregatedData.Situations = append(aggregatedData.Situations, result.Situations...)
		}
	}

	// Add aggregation metadata
	aggregatedData.Metadata["aggregatedPhaseCount"] = len(orchestrator.phaseExecutionResults)
	aggregatedData.Metadata[core.FieldExtractionMethod] = core.ExtractionMethodParallel

	return aggregatedData
}
