# Hero ETL Extraction Layer

A unified system for extracting and hydrating data from the Hero platform. This layer provides high-performance, type-safe extraction of any kind of data with complete visibility into the data structure.

## 📖 What You'll Learn

- [What This Does](#what-this-does)
- [Available Extractors](#available-extractors)
- [Report Extraction](#report-extraction)
- [Understanding the Architecture](#understanding-the-architecture)
- [Adding New Extractors](#adding-new-extractors)
- [Examples](#examples)
- [Performance](#performance)
- [Testing](#testing)
- [Troubleshooting](#troubleshooting)

## What This Does

### 🎯 The Core Problem

When working with Hero data, you often need to gather related information from multiple sources:
- **Reports** need their connected assets, entities, situations, and relationships
- **Assets** might need their associated reports, teams, and permissions
- **Incidents** could need all related reports, responding assets, and outcomes
- **Investigations** might need case files, evidence, witness statements, etc.

**Before this system:**
```go
// Manual data gathering - slow and error-prone!
report := getReport("report-123")
creator := getAsset(report.CreatedByAssetId)           // Extra database call
author := getAsset(report.AuthorAssetId)              // Another database call
for _, watcherId := range report.WatcherAssetIds {
    watcher := getAsset(watcherId)                    // More database calls!
}
// ... repeat for entities, situations, relationships, etc.
```

**With the extraction layer:**
```go
// One call gets everything, fully organized!
result, err := reportExtractor.Extract(ctx, "report-123")
data := result.Data

// Everything is already loaded and structured:
fmt.Printf("Creator: %s\n", data.Reports[0].CreatorAsset.Name)
fmt.Printf("Found %d people involved\n", len(data.Entities))
fmt.Printf("Connected to %d situations\n", len(data.Situations))
```

### 🏗️ Core Benefits

1. **Unified Interface**: Same pattern for extracting any type of data
2. **Performance**: Automatic parallelization and batching
3. **Type Safety**: Strongly-typed protobuf structures, no generic maps
4. **Resilience**: Graceful handling of missing or invalid data
5. **Extensibility**: Easy to add new extractors for new data types

## Available Extractors

Currently implemented extractors:

### 📋 Report Extractor (`reports/v2/`)
Extracts complete report data with all connected information:
- **Assets**: People involved (creator, author, watchers) with role context
- **Entities**: Things involved (people, vehicles, properties) with section context  
- **Situations**: Connected incidents and events
- **Relationships**: How things connect with resolved object names
- **Sections**: Report sections with embedded references

**Status**: ✅ Production ready
**Performance**: Parallel execution (5 concurrent phases)
**Use Cases**: Report viewing, case management, incident analysis

### 🚀 Future Extractors (Planned)
- **Asset Extractor**: Extract asset data with permissions, teams, reports
- **Incident Extractor**: Extract incident data with all related reports and responses  
- **Investigation Extractor**: Extract case data with evidence, statements, outcomes
- **Organization Extractor**: Extract org data with members, roles, reports

## Report Extraction

The report extractor is the first and most comprehensive extractor in the system. It demonstrates the full extraction pattern that other extractors will follow.

### 🔄 Report Extraction Flow

```mermaid
graph TD
    A[Client Request: reportID] --> B[ReportDataExtractor.Extract]
    B --> C{Choose Execution Mode}
    
    %% Parallel Path
    C -->|Default: Parallel| D[Create Orchestrator]
    D --> E[Phase 1: Get Base Report]
    E --> F[Start 5 Parallel Phases]
    
    F --> G[Phase 2: Sections]
    F --> H[Phase 3: Assets] 
    F --> I[Phase 4: Relationships]
    F --> J[Phase 5: Entities]
    F --> K[Phase 6: Situations]
    
    G --> L[Wait for All Phases]
    H --> L
    I --> L  
    J --> L
    K --> L
    
    L --> M[Aggregate Results]
    M --> N[Create Hydrated Report]
    N --> O[Return ExtractionResult]
    
    %% Sequential Path  
    C -->|Debug: Sequential| P[Sequential Execution]
    P --> Q[Extract Assets]
    Q --> R[Extract Entities] 
    R --> S[Extract Sections]
    S --> T[Extract Relationships]
    T --> U[Extract Situations]
    U --> M
    
    %% Database Layer
    V[(Database)] --> E
    V --> G
    V --> H
    V --> I
    V --> J
    V --> K
    V --> Q
    V --> R
    V --> S
    V --> T
    V --> U
    
    %% Repositories
    W[Reports Repo] --> E
    X[Assets Repo] --> H
    Y[Entities Repo] --> J
    Z[Situations Repo] --> K
    AA[Reports Repo] --> G
    
    style F fill:#e1f5fe
    style L fill:#e8f5e8
    style O fill:#fff3e0
```

### 🏗️ How to Use Report Extraction

#### Step 1: Set Up Your Extractor

Think of the extractor like a smart assistant that knows how to find data. You give it access to your databases:

```go
// Import what you need
import (
    "context"
    "database/sql"
    "workflow/internal/etl/extraction/reports/v2"
)

// Create the extractor - it needs to know where to find data
extractor, err := v2.NewReportDataExtractor(
    db,                  // Your database connection
    reportsRepo,         // How to get reports (required)
    situationsRepo,      // How to get situations (optional)
    assetsRepo,          // How to get assets (optional)
    entitiesRepo,        // How to get entities (optional)
)
if err != nil {
    // Something went wrong with setup
    return fmt.Errorf("couldn't create extractor: %w", err)
}
```

**Why these parameters?**
- `db`: The database connection - this is where all the data lives
- `reportsRepo`: Required because we need to get the main report
- Other repos: Optional because some reports might not have assets, entities, etc.

### Step 2: Extract the Data

Now just ask for what you want:

```go
// Get everything related to this report
result, err := extractor.Extract(ctx, "report-123")
if err != nil {
    return fmt.Errorf("couldn't get report data: %w", err)
}

// The result contains everything organized and ready to use
data := result.Data
```

**What happens here?**
1. The extractor finds the report with ID "report-123"
2. It automatically looks up all connected data
3. It organizes everything into easy-to-use structures
4. You get back a complete picture

### Step 3: Use the Data

Now you have everything organized:

```go
// Look at the main report
mainReport := data.Reports[0]
fmt.Printf("Report title: %s\n", mainReport.Report.Title)

// See who's involved
fmt.Printf("Created by: %s\n", mainReport.CreatorAsset.Name)
fmt.Printf("Watching: %d people\n", len(mainReport.WatcherAssets))

// Check what was found
fmt.Printf("People involved: %d\n", len(data.Entities))
fmt.Printf("Situations: %d\n", len(data.Situations))
fmt.Printf("Relationships: %d\n", len(data.Relationships))
```

## Understanding the Architecture

### 🗂️ Overall Structure

```
extraction/
├── types/                    # Common data structures for all extractors
│   └── extracted_data.go     # Result types, hydrated structures
├── reports/v2/              # Report extractor (first implementation)
│   ├── extractor.go         # 👈 Main entry point for reports
│   ├── extractors/          # Specialized report data extractors
│   │   ├── assets.go        # People involved (officers, suspects, etc.)
│   │   ├── entities.go      # Things involved (vehicles, properties, etc.)
│   │   ├── situations.go    # Connected incidents and events
│   │   ├── relationships.go # How things connect
│   │   └── sections.go      # Report sections and content
│   ├── parallel/            # High-performance parallel execution
│   │   ├── phases.go        # Orchestration and dependency management
│   │   └── parallel_extractor.go # Parallel execution implementation
│   ├── core/               # Report-specific utilities
│   │   ├── constants.go    # Role definitions, field names
│   │   ├── converter.go    # Data conversion utilities
│   │   └── field_utils.go  # Field manipulation helpers
│   └── test/               # Comprehensive test suite
├── assets/v1/              # Future: Asset extractor
├── incidents/v1/           # Future: Incident extractor  
└── investigations/v1/      # Future: Investigation extractor
```

### 🎯 Extraction Pattern

All extractors follow the same pattern:

1. **Main Extractor** (`extractor.go`): Entry point with convenience methods
2. **Specialized Extractors** (`extractors/`): Functions that extract specific data types
3. **Parallel Execution** (`parallel/`): Optional high-performance orchestration
4. **Core Utilities** (`core/`): Shared helpers specific to this extractor type
5. **Types** (`../types/`): Shared data structures across all extractors

**For reports, start with `reports/v2/extractor.go`**

### 🧠 What "Hydration" Means

**The Problem:** Reports contain IDs, not the actual data

```go
// A report just has IDs - not very useful!
type Report struct {
    CreatedByAssetId string   // Just "asset-123" 
    WatcherAssetIds []string  // Just ["asset-456", "asset-789"]
}
```

**The Solution:** "Hydration" replaces IDs with real data

```go
// After hydration - much more useful!
type HydratedReport struct {
    CreatorAsset *Asset {     // Full data: Name: "Officer Smith", Badge: "1234"
        Id: "asset-123",
        Name: "Officer Smith",
        Badge: "1234",
    }
    WatcherAssets []*Asset {  // Full data for all watchers
        {Id: "asset-456", Name: "Supervisor Jones"},
        {Id: "asset-789", Name: "Captain Wilson"},
    }
}
```

**Why this matters:**
- Before: You'd have to look up each ID manually (slow, error-prone)
- After: You get complete data ready to use (fast, reliable)

### 🏷️ Understanding "Roles"

People (assets) can have different roles in a report:

```go
// Instead of just "this person is involved"
// We know exactly HOW they're involved:

type HydratedAsset struct {
    Asset *Asset           // The person's data
    Role  string          // WHY they're in this report
}

// Examples:
creator := HydratedAsset{
    Asset: &Asset{Name: "Officer Smith"},
    Role: "creator",      // This person wrote the report
}

watcher := HydratedAsset{
    Asset: &Asset{Name: "Supervisor Jones"}, 
    Role: "watcher",      // This person is monitoring the case
}
```

**Why roles matter:**
- You can find the report author: `FindAssetByRole(assets, "creator")`
- You can list all supervisors: `FindAssetsByRole(assets, "watcher")`
- You know who has what responsibility

### 🔗 How Things Connect

The system tracks not just WHAT is connected, but HOW it's connected:

```go
// Before: Just know two things are related
relation := "person-123 relates to vehicle-456"

// After: Know the full story
hydratedRelation := HydratedRelationship{
    FromObjectName: "John Smith",        // Person's actual name
    ToObjectName: "2020 Toyota Camry",   // Vehicle's actual description  
    RelationType: "owns",                // HOW they're connected
}
```

## Adding New Extractors

This section shows how to add completely new extractors (like Asset Extractor, Incident Extractor, etc.) and how to extend existing extractors with new data types.

### 🆕 Creating a New Extractor (e.g., Asset Extractor)

Let's say you want to create an extractor for assets that gets all reports, teams, and permissions for an asset:

#### Step 1: Create the Directory Structure

```bash
mkdir -p assets/v1/{extractors,core,parallel,test}
```

#### Step 2: Create the Main Extractor

Create `assets/v1/extractor.go`:

```go
package v1

import (
    "context"
    "database/sql" 
    "workflow/internal/etl/extraction/types"
    // Asset-specific repositories
    assetsRepo "workflow/internal/assets/data"
    reportsRepo "workflow/internal/reports/data"
    teamsRepo "workflow/internal/teams/data"
)

// AssetDataExtractor extracts complete asset data with all connected information
type AssetDataExtractor struct {
    database     *sql.DB
    assetsRepo   assetsRepo.AssetRepository
    reportsRepo  reportsRepo.ReportRepository  
    teamsRepo    teamsRepo.TeamRepository
}

// NewAssetDataExtractor creates a new asset data extractor
func NewAssetDataExtractor(
    db *sql.DB,
    assetsRepo assetsRepo.AssetRepository,     // Required
    reportsRepo reportsRepo.ReportRepository, // Optional
    teamsRepo teamsRepo.TeamRepository,       // Optional
) (*AssetDataExtractor, error) {
    if db == nil || assetsRepo == nil {
        return nil, fmt.Errorf("database and assets repository are required")
    }
    
    return &AssetDataExtractor{
        database:    db,
        assetsRepo:  assetsRepo,
        reportsRepo: reportsRepo,
        teamsRepo:   teamsRepo,
    }, nil
}

// Extract gets complete asset data with all connected information
func (extractor *AssetDataExtractor) Extract(
    ctx context.Context,
    assetID string,
) (*types.ExtractionResult, error) {
    // Implementation follows the same pattern as report extractor
    // See reports/v2/extractor.go for the complete pattern
}
```

#### Step 3: Create Specialized Extractors

Create `assets/v1/extractors/reports.go`:

```go
package extractors

// ExtractAssetReports gets all reports connected to an asset
func ExtractAssetReports(
    ctx context.Context,
    tx *sql.Tx,
    asset *assetsv1.Asset,
    reportsRepo reportsRepo.ReportRepository,
) ([]*types.HydratedReport, error) {
    // RULE 1: Handle missing repository gracefully
    if reportsRepo == nil {
        return []*types.HydratedReport{}, nil
    }
    
    // RULE 2: Find reports where this asset is involved
    // Look for reports where asset is creator, author, or watcher
    var hydratedReports []*types.HydratedReport
    
    // Find reports created by this asset
    createdReports, err := reportsRepo.GetReportsByCreator(ctx, tx, asset.Id)
    if err == nil {
        for _, report := range createdReports {
            hydratedReports = append(hydratedReports, &types.HydratedReport{
                Report: report,
                CreatorAsset: asset, // We already have the asset
                HydrationMetadata: map[string]interface{}{
                    "extractedFrom": "asset_created_reports",
                    "assetId":       asset.Id,
                    "reportId":      report.Id,
                    "role":         "creator",
                },
            })
        }
    }
    
    // Find reports authored by this asset  
    // Find reports watched by this asset
    // ... similar pattern
    
    return hydratedReports, nil
}
```

### 🔧 Adding New Data Types to Existing Extractors

Let's say you want to add "Attachments" to the report extractor:

#### Step 1: Create the Extractor Function

First, create a new file `reports/v2/extractors/attachments.go`:

```go
package extractors

import (
    "context"
    "database/sql"
    "workflow/internal/etl/extraction/types"
    // Your attachment repository
    attachmentsRepo "workflow/internal/attachments/data"
)

// ExtractHydratedAttachments gets all files attached to a report
func ExtractHydratedAttachments(
    ctx context.Context,
    tx *sql.Tx,                           // Database transaction
    report *reports.Report,               // The main report
    attachmentsRepo attachmentsRepo.Repository, // How to get attachments
) ([]*types.HydratedAttachment, error) {
    
    // RULE 1: Always handle missing repositories gracefully
    // If there's no way to get attachments, return empty list (not an error)
    if attachmentsRepo == nil {
        return []*types.HydratedAttachment{}, nil
    }

    var result []*types.HydratedAttachment
    
    // RULE 2: Look for attachment references in the report
    // Let's say reports have an AttachmentIds field
    for _, attachmentId := range report.AttachmentIds {
        
        // RULE 3: Handle individual failures gracefully
        // If one attachment fails, keep going with the others
        attachment, err := attachmentsRepo.GetAttachment(ctx, tx, attachmentId)
        if err != nil {
            // Log the error but don't fail the whole extraction
            continue
        }
        
        // RULE 4: Add context about where this came from
        hydratedAttachment := &types.HydratedAttachment{
            Attachment: attachment,  // The actual attachment data
            ExtractionContext: map[string]interface{}{
                "extractedFrom": "report_attachment_refs",
                "reportId":      report.Id,
                "attachmentId":  attachment.Id,
            },
        }
        
        result = append(result, hydratedAttachment)
    }
    
    return result, nil
}
```

**Why these rules?**
- **Rule 1**: Some reports might not have attachments, so no repository is OK
- **Rule 2**: Look in the report structure for references to your data
- **Rule 3**: If one attachment is missing, still get the others
- **Rule 4**: Always track where data came from for debugging

### Step 2: Define the Data Structure

Add to `types/extracted_data.go`:

```go
// HydratedAttachment represents a file attached to a report
type HydratedAttachment struct {
    Attachment *attachmentv1.Attachment        // The actual attachment data
    ExtractionContext map[string]interface{}   // Where it came from
}

// Add to the main ExtractedData struct
type ExtractedData struct {
    Reports       []*HydratedReport       `json:"reports"`
    Assets        []*HydratedAsset        `json:"assets"`
    Entities      []*HydratedEntity       `json:"entities"`
    Sections      []*HydratedSection      `json:"sections"`
    Relationships []*HydratedRelationship `json:"relationships"`
    Situations    []*situationsv2.Situation `json:"situations"`
    
    // Add your new data type here
    Attachments   []*HydratedAttachment   `json:"attachments"`
    
    Metadata    map[string]interface{} `json:"metadata"`
    ExtractedAt time.Time              `json:"extractedAt"`
}
```

### Step 3: Add a Convenience Method

Add to `extractor.go`:

```go
// ExtractAttachments gets just the attachments for a report
func (extractor *ReportDataExtractor) ExtractAttachments(
    ctx context.Context,
    reportID string,
) ([]*types.HydratedAttachment, error) {
    
    // Step 1: Get the main report
    tx, err := extractor.database.BeginTx(ctx, &sql.TxOptions{ReadOnly: true})
    if err != nil {
        return nil, fmt.Errorf("couldn't start database transaction: %w", err)
    }
    defer tx.Rollback()
    
    report, err := extractor.reportsRepo.GetReport(ctx, tx, reportID)
    if err != nil {
        return nil, fmt.Errorf("couldn't find report %s: %w", reportID, err)
    }
    
    // Step 2: Extract attachments
    attachments, err := extractors.ExtractHydratedAttachments(
        ctx, tx, report, extractor.attachmentsRepo)
    if err != nil {
        return nil, fmt.Errorf("couldn't extract attachments: %w", err)
    }
    
    return attachments, nil
}
```

### Step 4: Add to Full Extraction

Modify the main `Extract` method to include your new data:

```go
// In extractor.go, update the Extract method
func (extractor *ReportDataExtractor) Extract(ctx context.Context, reportID string) (*types.ExtractionResult, error) {
    // ... existing code to get report and other data ...
    
    // Add extraction of your new data type
    attachments, err := extractors.ExtractHydratedAttachments(
        ctx, tx, reportProtobuf, extractor.attachmentsRepo)
    if err != nil {
        return nil, fmt.Errorf("failed to extract attachments: %w", err)
    }
    
    // Include in the final result
    extractedData := &types.ExtractedData{
        Reports:       []*types.HydratedReport{hydratedReport},
        Assets:        assets,
        Entities:      entities,
        Sections:      sections,
        Relationships: relationships,
        Situations:    situations,
        Attachments:   attachments,  // Add your new data
        // ... rest of the fields
    }
    
    // ... rest of the method
}
```

### 🎯 Simple Rules to Follow

**Do:**
- ✅ Return empty lists when repositories are missing
- ✅ Continue processing when individual items fail  
- ✅ Add context about where data came from
- ✅ Use the provided database transaction

**Don't:**
- ❌ Return errors for missing repositories
- ❌ Stop everything when one item fails
- ❌ Create new database transactions
- ❌ Assume repositories exist

### 🧪 Test Your New Feature

Create a simple test:

```go
func TestExtractAttachments(t *testing.T) {
    // Setup test data
    mockRepo := &MockAttachmentsRepository{
        attachments: map[string]*Attachment{
            "att-1": {Id: "att-1", Filename: "photo.jpg"},
        },
    }
    
    // Test the extraction
    result := ExtractHydratedAttachments(ctx, tx, testReport, mockRepo)
    
    // Check the results
    assert.Len(t, result, 1)
    assert.Equal(t, "photo.jpg", result[0].Attachment.Filename)
}
```

## Examples

### 🔍 Find Specific People and Things

Once you have extracted data, you can easily find what you need:

```go
// Get all the data
result, err := extractor.Extract(ctx, "report-123")
data := result.Data

// Find who created the report
creator := extractors.FindAssetByRole(data.Assets, "creator")
if creator != nil {
    fmt.Printf("Report created by: %s\n", creator.Name)
}

// Find all supervisors watching this case
supervisors := extractors.FindAssetsByRole(data.Assets, "watcher")
fmt.Printf("Supervisors watching: %d\n", len(supervisors))

// Find all people involved
people := extractors.GetEntitiesByType(data.Entities, entityv1.EntityType_ENTITY_TYPE_PERSON)
fmt.Printf("People involved: %d\n", len(people))

// Find all vehicles involved  
vehicles := extractors.GetEntitiesByType(data.Entities, entityv1.EntityType_ENTITY_TYPE_VEHICLE)
fmt.Printf("Vehicles involved: %d\n", len(vehicles))

// Find active incidents
activeSituations := extractors.GetSituationsByStatus(data.Situations, 
    situationsv2.SituationStatus_SITUATION_STATUS_ACTIVE)
fmt.Printf("Active incidents: %d\n", len(activeSituations))
```

### 🏗️ Build a Summary Report

Here's how to combine different types of data:

```go
// Create a simple incident summary
func CreateIncidentSummary(ctx context.Context, reportID string) (*IncidentSummary, error) {
    // Get all the data
    result, err := extractor.Extract(ctx, reportID)
    if err != nil {
        return nil, fmt.Errorf("couldn't get report data: %w", err)
    }
    
    data := result.Data
    
    // Build a summary from the extracted data
    summary := &IncidentSummary{
        ReportID: reportID,
        
        // Who's the main officer?
        PrimaryOfficer: extractors.FindAssetByRole(data.Assets, "creator"),
        
        // How many supervisors are watching?
        SupervisorCount: len(extractors.FindAssetsByRole(data.Assets, "watcher")),
        
        // What people are involved?
        PeopleInvolved: extractors.GetEntitiesByType(data.Entities, 
            entityv1.EntityType_ENTITY_TYPE_PERSON),
        
        // What vehicles are involved?
        VehiclesInvolved: extractors.GetEntitiesByType(data.Entities, 
            entityv1.EntityType_ENTITY_TYPE_VEHICLE),
        
        // Are there active situations?
        HasActiveSituations: len(extractors.GetSituationsByStatus(data.Situations, 
            situationsv2.SituationStatus_SITUATION_STATUS_ACTIVE)) > 0,
    }
    
    return summary, nil
}
```

## Performance

### 🚀 Parallel Execution (Report Extractor Example)

The report extractor demonstrates the performance benefits of the extraction layer. By default, it runs multiple extraction tasks at the same time:

```
Step 1: Get the main report first
        ↓
Step 2: Run these 5 tasks at the same time:
        ┌─ Get Assets ─┐
        ┌─ Get Entities ─┐  
        ┌─ Get Sections ─┐
        ┌─ Get Relationships ─┐
        ┌─ Get Situations ─┘
        ↓
Step 3: Combine everything together
```

**Why this is faster:**
- Instead of waiting for each task to finish, they all run together
- Database is used more efficiently
- You get results much quicker

### 📊 Performance Monitoring

All extractors provide performance metrics:

```go
// For any extractor
result, err := extractor.Extract(ctx, resourceID)

// Check how long it took
fmt.Printf("Extraction took: %v\n", result.Metrics.TotalDuration)

// See what was found
fmt.Printf("Items processed: %+v\n", result.Metrics.ItemsProcessed)

// Check for any problems
for _, warning := range result.Warnings {
    fmt.Printf("Warning: %s\n", warning.Message)
}
```

### ⚡ Performance Best Practices

1. **Use parallel execution when available** - Much faster than sequential
2. **Use sequential mode only for debugging** - Easier to debug but slower
3. **Optimize your repositories** - Implement efficient batch loading
4. **Database optimization** - Ensure proper indexes and connection pooling
5. **Monitor metrics** - Track performance over time

## Testing

### 🧪 Run the Tests

```bash
# Go to the test directory
cd services/workflow/internal/etl/extraction/reports/v2/test

# Run all tests
./run_tests.sh

# Or run a specific test
go test -v -run TestParallelExtraction
```

### 🎯 Write Your Own Tests

Here's a simple pattern to test your extractors:

```go
func TestMyExtractor(t *testing.T) {
    // Step 1: Create a fake repository for testing
    mockRepo := &MockMyDataRepository{
        data: map[string]*MyData{
            "item-1": {Id: "item-1", Name: "Test Item"},
        },
    }
    
    // Step 2: Create the extractor
    extractor, err := v2.NewReportDataExtractor(
        testDB,      // Use a test database
        reportsRepo, // Real reports repo
        nil, nil,    // Don't need other repos for this test
        mockRepo,    // Use our fake repo
    )
    require.NoError(t, err)
    
    // Step 3: Test extraction
    result, err := extractor.Extract(context.Background(), "test-report")
    require.NoError(t, err)
    
    // Step 4: Check the results
    assert.True(t, result.Success)
    assert.Len(t, result.Data.MyData, 1)
    assert.Equal(t, "Test Item", result.Data.MyData[0].MyData.Name)
}
```

## When Things Go Wrong

### 🐛 Common Problems

**Problem: "No data extracted"**
```
What you see: Empty results even though data should exist
Why it happens:
- Repository passed as nil to the extractor
- Database connection issues
- Test data missing

How to fix:
- Check that you passed the repository to NewReportDataExtractor()
- Verify database connection is working
- Make sure test data exists in the database
```

**Problem: "JSON circular reference error"**  
```
What you see: Error about circular references when converting to JSON
Why it happens: Some internal data references itself

How to fix: This is usually handled automatically, but if you see it:
- Check if you're storing report data inside itself
- Look for metadata that points back to the same object
```

**Problem: "Database transaction errors"**
```
What you see: Errors about transactions or connections in parallel mode
Why it happens: Multiple tasks trying to use the same database transaction

How to fix:
- Use the provided transaction, don't create your own
- Each extractor should use the tx parameter, not create new ones
```

### 🔍 Debug Your Code

If something isn't working, try these steps:

```go
// Step 1: Use sequential mode (easier to debug)
result, err := extractor.ExtractSequential(ctx, reportID)

// Step 2: Add temporary debug prints
fmt.Printf("DEBUG: Found %d items\n", len(items))

// Step 3: Check each piece separately
assets, err := extractor.ExtractAssets(ctx, reportID)
fmt.Printf("Assets: %d, Error: %v\n", len(assets), err)

entities, err := extractor.ExtractEntities(ctx, reportID)  
fmt.Printf("Entities: %d, Error: %v\n", len(entities), err)
```

### 📞 Getting Help

1. **Look at existing code** - Check how `assets.go`, `entities.go` work
2. **Run the tests** - See `extraction_test.go` for working examples
3. **Check the data types** - Look at `types/extracted_data.go` to understand structures
4. **Start simple** - Try extracting one thing at a time

### 🚨 Performance Problems

If extraction is slow:

```go
// Time your extraction
start := time.Now()
result, err := extractor.Extract(ctx, reportID)
duration := time.Since(start)

if duration > 5*time.Second {
    fmt.Printf("Slow! Took %v for report %s\n", duration, reportID)
    // Check:
    // - Is your database slow?
    // - Do you have proper indexes?
    // - Are you making too many database calls?
}
```

---

## 🎉 Summary

The Hero ETL Extraction Layer provides a unified, high-performance system for extracting any type of data:

### 🌟 Key Benefits
- **Unified Interface**: Same pattern for extracting reports, assets, incidents, etc.
- **High Performance**: Automatic parallelization and intelligent batching
- **Type Safety**: Strongly-typed protobuf structures, no generic maps
- **Resilient Design**: Graceful handling of missing data and failures
- **Easy Extension**: Simple patterns for adding new extractors and data types

### 🚀 Currently Available
- **Report Extractor** (`reports/v2/`): Production-ready with parallel execution
  - Extracts assets, entities, situations, relationships, and sections
  - 5-phase parallel execution for optimal performance

### 🛣️ Future Roadmap
- **Asset Extractor**: Extract asset data with reports, teams, permissions
- **Incident Extractor**: Extract incident data with responses and outcomes
- **Investigation Extractor**: Extract case data with evidence and statements

### 📖 Getting Started

**Using the Report Extractor:**
```go
extractor, err := v2.NewReportDataExtractor(db, reportsRepo, situationsRepo, assetsRepo, entitiesRepo)
result, err := extractor.Extract(ctx, "report-123")
// Access result.Data for all connected information
```

**Adding a New Extractor:**
1. Create directory structure: `yourtype/v1/{extractors,core,parallel,test}`
2. Implement main extractor following the established pattern
3. Create specialized extractors for each data type
4. Add data structures to `types/extracted_data.go`
5. Implement parallel execution for performance

**Adding Data Types to Existing Extractors:**
1. Create extractor function in `extractors/`
2. Add data type to `types/extracted_data.go`
3. Update main extractor to include new data
4. Add tests and documentation

### 🎯 Design Principles
- **Extraction ≠ Transformation**: Pure data retrieval, no business logic
- **Fail-Safe**: Empty results instead of errors for missing data
- **Context-Rich**: Every extracted item includes source metadata
- **Performance by Default**: Parallel execution where beneficial

Follow the patterns demonstrated in the report extractor, and you'll be able to efficiently extract any data you need from the Hero platform!