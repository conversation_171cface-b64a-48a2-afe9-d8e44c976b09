package etl

import (
	"database/sql"
	"log"
	"net/http"

	etlconnect "proto/hero/etl/v1/etlconnect"

	assetsrepository "workflow/internal/assets/data"
	"workflow/internal/common/middleware"
	entityrepository "workflow/internal/entity/data"
	etlconnectapi "workflow/internal/etl/api/connect"
	etlrepository "workflow/internal/etl/data"
	"workflow/internal/etl/usecase"
	reportsrepository "workflow/internal/reports/data"
	situationsrepository "workflow/internal/situations/data"
)

// RegisterRoutes wires all HTTP handlers for the ETL service onto the given mux.
// It expects a fully-initialised *sql.DB and repository implementations that
// persist data for the ETL and reports domains.
func RegisterRoutes(
	mux *http.ServeMux,
	etlDB *sql.DB,
	etlRepo etlrepository.ETLRepository,
	reportsRepo reportsrepository.ReportRepository,
	entityRepo entityrepository.EntityRepository,
	situationsRepo situationsrepository.SituationRepository,
	assetsRepo assetsrepository.AssetRepository,
) {
	// Construct the domain use-case layer
	etlUseCase, err := usecase.NewETLUseCase(etlDB, etlRepo, reportsRepo, entityRepo, situationsRepo, assetsRepo)
	if err != nil {
		log.Fatalf("Failed to initialize ETL Use Case: %v", err)
	}

	// Construct the Connect RPC server
	etlServer := etlconnectapi.NewETLServer(etlUseCase, nil)

	// Generate HTTP handler from Connect
	servicePath, serviceHandler := etlconnect.NewETLServiceHandler(etlServer)

	// Add handler with middleware
	mux.Handle(servicePath, middleware.LoggingMiddleware(serviceHandler))

	log.Printf("ETL service registered at path: %s", servicePath)
}
