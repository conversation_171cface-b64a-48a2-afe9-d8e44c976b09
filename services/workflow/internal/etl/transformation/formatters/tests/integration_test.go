package tests

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"testing"
	"time"

	etl "proto/hero/etl/v1"
	"workflow/internal/etl/transformation/formatters"
)

// TestEndToEnd_NIBRSFormatting tests the complete NIBRS formatting pipeline.
func TestEndToEnd_NIBRSFormatting(t *testing.T) {
	// Create test template directory
	tempDir := t.TempDir()
	nibrsDir := filepath.Join(tempDir, "nibrs", "reports", "v2")
	err := os.MkdirAll(nibrsDir, 0755)
	if err != nil {
		t.Fatalf("Failed to create nibrs directory: %v", err)
	}

	// Copy the actual NIBRS template we created
	nibrsTemplate := `<?xml version="1.0" encoding="UTF-8"?>
{{- /* NIBRS XML Template - Demonstrates all formatter features with minimal output */ -}}
<nibrs:Submission xmlns:nibrs="http://fbi.gov/cjis/nibrs/4.1">
  {{- /* Header with basic info and date formatting */ -}}
  <nibrs:ReportHeader>
    <nibrs:ReportDate>{{.ReportDate | formatDate "2006-01-02" | default "2024-01-01"}}</nibrs:ReportDate>
    <nibrs:ORI>{{.ORI | upper | default "XX0000000"}}</nibrs:ORI>
    <nibrs:AgencyName>{{.AgencyName | trim | default "Unknown Agency"}}</nibrs:AgencyName>
  </nibrs:ReportHeader>
  
  <nibrs:Group-A-Incident-Report>
    {{- /* Administrative info with conditional rendering */ -}}
    <nibrs:Administrative-Segment>
      <nibrs:ORI>{{.ORI | upper | default "XX0000000"}}</nibrs:ORI>
      <nibrs:Incident-Number>{{.IncidentNumber | default "0000-000"}}</nibrs:Incident-Number>
      {{- if .IncidentDateTime}}
      <nibrs:Incident-Date-Hour>{{.IncidentDateTime | formatDate "2006-01-02T15:04:05"}}</nibrs:Incident-Date-Hour>
      {{- end}}
    </nibrs:Administrative-Segment>
    
    {{- /* Demonstrate looping with adult persons data */ -}}
    {{- if .AdultPersons}}
    {{- range .AdultPersons}}
    <nibrs:Person-Segment>
      <nibrs:Person-Sequence-Number>{{.SequenceNumber}}</nibrs:Person-Sequence-Number>
      {{- /* Nested data access with default values */ -}}
      <nibrs:Person-Type>{{.referencedInSectionType | default "UNKNOWN"}}</nibrs:Person-Type>
      
      {{- /* Accessing nested entity data with string manipulation */ -}}
      {{- if .entity}}
      {{- with .entity.data}}
      <nibrs:Name>{{.firstName | upper}} {{.middleName | upper}} {{.lastName | upper}}</nibrs:Name>
      <nibrs:Age>{{.age | default "00"}}</nibrs:Age>
      <nibrs:Sex>{{.gender | upper | default "U"}}</nibrs:Sex>
      <nibrs:Race>{{.race | default "U"}}</nibrs:Race>
      
      {{- /* Conditional rendering with nested data */ -}}
      {{- if .dateOfBirth}}
      <nibrs:DOB>{{.dateOfBirth | formatDate "01/02/2006"}}</nibrs:DOB>
      {{- end}}
      
      {{- /* Working with arrays */ -}}
      {{- if .distinguishingMarks}}
      <nibrs:Marks>
        {{- range .distinguishingMarks}}
        <nibrs:Mark>{{. | trim}}</nibrs:Mark>
        {{- end}}
      </nibrs:Marks>
      {{- end}}
      
      {{- /* Complex nested object access */ -}}
      {{- if .emergencyContact}}
      <nibrs:EmergencyContact>
        <nibrs:Name>{{.emergencyContact.name | default "Not Provided"}}</nibrs:Name>
        <nibrs:Relationship>{{.emergencyContact.relationship | upper}}</nibrs:Relationship>
      </nibrs:EmergencyContact>
      {{- end}}
      {{- end}}
      {{- end}}
      
      {{- /* Tags as comma-separated list */ -}}
      {{- if .entity.tags}}
      <nibrs:Tags>{{range $i, $tag := .entity.tags}}{{if $i}}, {{end}}{{$tag | upper}}{{end}}</nibrs:Tags>
      {{- end}}
    </nibrs:Person-Segment>
    {{- end}}
    {{- end}}
    
    {{- /* Simple offense example with minimal data */ -}}
    {{- range .Offenses}}
    <nibrs:Offense-Segment>
      <nibrs:Offense-Code>{{.Code | default "99"}}</nibrs:Offense-Code>
      <nibrs:Status>{{.Status | upper | default "UNKNOWN"}}</nibrs:Status>
    </nibrs:Offense-Segment>
    {{- end}}
    
    {{- /* Demonstrate empty section handling */ -}}
    {{- if .Property}}
    <nibrs:Property-Segment>
      <nibrs:Loss-Type>{{.Property.TypePropertyLoss | default "0"}}</nibrs:Loss-Type>
    </nibrs:Property-Segment>
    {{- end}}
    
    {{- /* Summary section with function chaining */ -}}
    <nibrs:Summary>
      {{- /* Count of persons (if available) */ -}}
      {{- if .AdultPersons}}
      <nibrs:TotalPersons>{{len .AdultPersons}}</nibrs:TotalPersons>
      {{- end}}
      
      {{- /* Generated timestamp - Note: now function returns time.Time */ -}}
      <nibrs:GeneratedAt>{{.GeneratedAt | default "2024-01-01T00:00:00Z"}}</nibrs:GeneratedAt>
      
      {{- /* Example of complex conditional */ -}}
      <nibrs:Status>
        {{- if and .AdultPersons (gt (len .AdultPersons) 0) -}}
        CONTAINS_DATA
        {{- else -}}
        EMPTY
        {{- end -}}
      </nibrs:Status>
    </nibrs:Summary>
  </nibrs:Group-A-Incident-Report>
</nibrs:Submission>`

	err = os.WriteFile(filepath.Join(nibrsDir, "nibrs.xml.tmpl"), []byte(nibrsTemplate), 0600)
	if err != nil {
		t.Fatalf("Failed to create NIBRS template: %v", err)
	}

	// Create factory and formatter
	factory := formatters.NewFormatterFactory(tempDir)
	formatter, err := factory.CreateFormatter(etl.OutputFormat_OUTPUT_FORMAT_NIBRS_XML)
	if err != nil {
		t.Fatalf("Failed to create formatter: %v", err)
	}

	// Create comprehensive test data (matching new template structure)
	data := map[string]interface{}{
		"ReportDate":       "2024-01-15",
		"ORI":              "tx0000000",                  // lowercase to test upper function
		"AgencyName":       "  Test Police Department  ", // with spaces to test trim
		"IncidentNumber":   "2024-001",
		"IncidentDateTime": "2024-01-15T10:30:00Z",
		"GeneratedAt":      "2024-01-15T12:00:00Z",
		"AdultPersons": []interface{}{
			map[string]interface{}{
				"SequenceNumber":          "01",
				"referencedInSectionType": "SECTION_TYPE_ARREST",
				"entity": map[string]interface{}{
					"data": map[string]interface{}{
						"firstName":   "john",
						"middleName":  "michael",
						"lastName":    "suspect",
						"age":         30,
						"gender":      "m",
						"race":        "W",
						"dateOfBirth": "1994-12-15",
						"distinguishingMarks": []interface{}{
							"  Scar on left cheek  ",
							"Tattoo on right arm",
						},
						"emergencyContact": map[string]interface{}{
							"name":         "Mary Suspect",
							"relationship": "mother",
						},
					},
					"tags": []interface{}{
						"suspect",
						"armed_robbery",
						"high_priority",
					},
				},
			},
			map[string]interface{}{
				"SequenceNumber":          "02",
				"referencedInSectionType": nil, // Test default
				"entity": map[string]interface{}{
					"data": map[string]interface{}{
						"firstName":  "jane",
						"middleName": nil,
						"lastName":   "victim",
						"age":        nil, // Test default
						"gender":     "f",
						"race":       nil, // Test default
						// No dateOfBirth to test conditional
						// No distinguishingMarks to test conditional
						// No emergencyContact to test conditional
					},
					"tags": []interface{}{
						"victim",
						"witness",
					},
				},
			},
		},
		"Offenses": []interface{}{
			map[string]interface{}{
				"Code":   "13A",
				"Status": "completed",
			},
			map[string]interface{}{
				"Code":   nil, // Test default
				"Status": "attempted",
			},
		},
		"Property": map[string]interface{}{
			"TypePropertyLoss": "7",
		},
	}

	// Format data
	result, err := formatter.Format(data)
	if err != nil {
		t.Fatalf("Failed to format data: %v", err)
	}

	// Validate result
	resultStr := string(result)

	// Check XML structure
	xmlErrors := validateXMLStructure(resultStr)
	if len(xmlErrors) > 0 {
		t.Errorf("XML structure validation errors: %v", xmlErrors)
	}

	// Check for all required elements
	requiredElements := []string{
		// Header - testing formatter functions
		`<nibrs:ReportDate>2024-01-15</nibrs:ReportDate>`,
		`<nibrs:ORI>TX0000000</nibrs:ORI>`,                            // Should be uppercase
		`<nibrs:AgencyName>Test Police Department</nibrs:AgencyName>`, // Should be trimmed

		// Administrative
		`<nibrs:Incident-Number>2024-001</nibrs:Incident-Number>`,
		`<nibrs:Incident-Date-Hour>2024-01-15T10:30:00</nibrs:Incident-Date-Hour>`,

		// Person data - testing nested access and string functions
		`<nibrs:Person-Sequence-Number>01</nibrs:Person-Sequence-Number>`,
		`<nibrs:Person-Type>SECTION_TYPE_ARREST</nibrs:Person-Type>`,
		`<nibrs:Name>JOHN MICHAEL SUSPECT</nibrs:Name>`, // Should be uppercase
		`<nibrs:Age>30</nibrs:Age>`,
		`<nibrs:Sex>M</nibrs:Sex>`, // Should be uppercase
		`<nibrs:Race>W</nibrs:Race>`,
		`<nibrs:DOB>12/15/1994</nibrs:DOB>`, // Date formatting

		// Marks - testing array iteration
		`<nibrs:Mark>Scar on left cheek</nibrs:Mark>`, // Should be trimmed
		`<nibrs:Mark>Tattoo on right arm</nibrs:Mark>`,

		// Emergency contact - testing nested object
		`<nibrs:Name>Mary Suspect</nibrs:Name>`,
		`<nibrs:Relationship>MOTHER</nibrs:Relationship>`, // Should be uppercase

		// Tags - testing comma-separated list
		`<nibrs:Tags>SUSPECT, ARMED_ROBBERY, HIGH_PRIORITY</nibrs:Tags>`,

		// Second person with defaults
		`<nibrs:Person-Sequence-Number>02</nibrs:Person-Sequence-Number>`,
		`<nibrs:Person-Type>UNKNOWN</nibrs:Person-Type>`, // Default value
		`<nibrs:Name>JANE  VICTIM</nibrs:Name>`,          // Middle name nil
		`<nibrs:Age>00</nibrs:Age>`,                      // Default for nil age
		`<nibrs:Sex>F</nibrs:Sex>`,
		`<nibrs:Race>U</nibrs:Race>`, // Default for nil race

		// Offenses
		`<nibrs:Offense-Code>13A</nibrs:Offense-Code>`,
		`<nibrs:Status>COMPLETED</nibrs:Status>`,
		`<nibrs:Offense-Code>99</nibrs:Offense-Code>`, // Default for nil code
		`<nibrs:Status>ATTEMPTED</nibrs:Status>`,

		// Property
		`<nibrs:Loss-Type>7</nibrs:Loss-Type>`,

		// Summary
		`<nibrs:TotalPersons>2</nibrs:TotalPersons>`,
		`<nibrs:GeneratedAt>2024-01-15T12:00:00Z</nibrs:GeneratedAt>`,
		`<nibrs:Status>CONTAINS_DATA</nibrs:Status>`,
	}

	for _, expected := range requiredElements {
		if !strings.Contains(resultStr, expected) {
			t.Errorf("Missing required element: %s", expected)
		}
	}

	// Log formatted output for manual inspection
	t.Logf("Formatted NIBRS XML:\n%s", resultStr)
}

// TestEndToEnd_ErrorScenarios tests error handling in the complete pipeline.
func TestEndToEnd_ErrorScenarios(t *testing.T) {
	tests := []struct {
		name        string
		setupError  string // "template", "data"
		template    string
		data        interface{}
		wantErr     bool
		errContains string
	}{
		{
			name:        "Invalid template file",
			setupError:  "template",
			wantErr:     true,
			errContains: "failed to read template file",
		},
		{
			name:        "Template with invalid function call",
			template:    `{{.Field | unknownFunction}}`,
			data:        map[string]interface{}{"Field": "value"},
			wantErr:     true,
			errContains: "failed to parse template",
		},
		{
			name:     "Data causing template execution error",
			template: `{{range .Items}}{{.Invalid.Deep.Access}}{{end}}`,
			data: map[string]interface{}{
				"Items": []interface{}{"string", "not", "maps"},
			},
			wantErr:     true,
			errContains: "template execution failed",
		},
		{
			name:     "Nil data with required fields",
			template: `<Required>{{.Field}}</Required>`,
			data:     nil,
			wantErr:  false, // Templates handle nil gracefully
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tempDir := t.TempDir()
			nibrsDir := filepath.Join(tempDir, "nibrs", "reports", "v2")

			if tt.setupError != "template" {
				// Create directory and template
				err := os.MkdirAll(nibrsDir, 0755)
				if err != nil {
					t.Fatalf("Failed to create directory: %v", err)
				}

				if tt.template != "" {
					err = os.WriteFile(
						filepath.Join(nibrsDir, "nibrs.xml.tmpl"),
						[]byte(tt.template),
						0600,
					)
					if err != nil {
						t.Fatalf("Failed to create template: %v", err)
					}
				}
			}

			// Create factory
			factory := formatters.NewFormatterFactory(tempDir)

			// Try to create formatter
			formatter, err := factory.CreateFormatter(etl.OutputFormat_OUTPUT_FORMAT_NIBRS_XML)

			if tt.setupError == "template" {
				if err == nil {
					t.Error("Expected error creating formatter with missing template")
				}
				if !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("Error should contain '%s', got: %v", tt.errContains, err)
				}
				return
			}

			if err != nil {
				if !tt.wantErr {
					t.Fatalf("Unexpected error creating formatter: %v", err)
				}
				if !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("Error should contain '%s', got: %v", tt.errContains, err)
				}
				return
			}

			// Try to format data
			_, err = formatter.Format(tt.data)

			if tt.wantErr {
				if err == nil {
					t.Error("Expected error during formatting")
				} else if !strings.Contains(err.Error(), tt.errContains) {
					t.Errorf("Error should contain '%s', got: %v", tt.errContains, err)
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error: %v", err)
				}
			}
		})
	}
}

// TestEndToEnd_LargeDataset tests formatting with large datasets.
func TestEndToEnd_LargeDataset(t *testing.T) {
	// Create template for large dataset
	tempDir := t.TempDir()
	nibrsDir := filepath.Join(tempDir, "nibrs", "reports", "v2")
	err := os.MkdirAll(nibrsDir, 0755)
	if err != nil {
		t.Fatalf("Failed to create directory: %v", err)
	}

	template := `<?xml version="1.0" encoding="UTF-8"?>
<Report>
  <Summary>
    <TotalVictims>{{len .Victims}}</TotalVictims>
    <TotalOffenders>{{len .Offenders}}</TotalOffenders>
  </Summary>
  <Victims>
    {{- range .Victims}}
    <Victim>
      <ID>{{.ID}}</ID>
      <Type>{{.Type | upper}}</Type>
      <Details>{{.Description | trim}}</Details>
    </Victim>
    {{- end}}
  </Victims>
  <Offenders>
    {{- range .Offenders}}
    <Offender>
      <ID>{{.ID}}</ID>
      <Status>{{.Status | default "UNKNOWN"}}</Status>
    </Offender>
    {{- end}}
  </Offenders>
</Report>`

	err = os.WriteFile(filepath.Join(nibrsDir, "nibrs.xml.tmpl"), []byte(template), 0600)
	if err != nil {
		t.Fatalf("Failed to create template: %v", err)
	}

	// Create factory and formatter
	factory := formatters.NewFormatterFactory(tempDir)
	formatter, err := factory.CreateFormatter(etl.OutputFormat_OUTPUT_FORMAT_NIBRS_XML)
	if err != nil {
		t.Fatalf("Failed to create formatter: %v", err)
	}

	// Create large dataset
	const victimCount = 1000
	const offenderCount = 500

	victims := make([]interface{}, victimCount)
	for i := 0; i < victimCount; i++ {
		victims[i] = map[string]interface{}{
			"ID":          fmt.Sprintf("V%06d", i),
			"Type":        []string{"individual", "business", "society"}[i%3],
			"Description": fmt.Sprintf("  Victim %d description with extra spaces  ", i),
		}
	}

	offenders := make([]interface{}, offenderCount)
	for i := 0; i < offenderCount; i++ {
		offenders[i] = map[string]interface{}{
			"ID": fmt.Sprintf("O%06d", i),
		}
		if i%3 != 0 {
			offenders[i].(map[string]interface{})["Status"] = []string{"arrested", "at_large"}[i%2]
		}
	}

	data := map[string]interface{}{
		"Victims":   victims,
		"Offenders": offenders,
	}

	// Measure formatting time
	start := time.Now()
	result, err := formatter.Format(data)
	duration := time.Since(start)

	if err != nil {
		t.Fatalf("Failed to format large dataset: %v", err)
	}

	// Validate result
	resultStr := string(result)

	// Check summary counts
	if !strings.Contains(resultStr, fmt.Sprintf("<TotalVictims>%d</TotalVictims>", victimCount)) {
		t.Error("Incorrect victim count in summary")
	}
	if !strings.Contains(resultStr, fmt.Sprintf("<TotalOffenders>%d</TotalOffenders>", offenderCount)) {
		t.Error("Incorrect offender count in summary")
	}

	// Spot check some elements
	if !strings.Contains(resultStr, "<ID>V000000</ID>") {
		t.Error("Missing first victim")
	}
	if !strings.Contains(resultStr, "<Type>INDIVIDUAL</Type>") {
		t.Error("Upper case function not working")
	}
	if !strings.Contains(resultStr, "<Details>Victim 0 description with extra spaces</Details>") {
		t.Error("Trim function not working")
	}
	if !strings.Contains(resultStr, "<Status>UNKNOWN</Status>") {
		t.Error("Default function not working")
	}

	// Check performance
	if duration > 5*time.Second {
		t.Errorf("Formatting took too long: %v", duration)
	}

	t.Logf("Formatted %d victims and %d offenders in %v", victimCount, offenderCount, duration)
	t.Logf("Output size: %d bytes", len(result))
}
