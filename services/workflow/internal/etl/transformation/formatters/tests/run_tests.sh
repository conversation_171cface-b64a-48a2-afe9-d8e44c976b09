#!/bin/bash

# Colorful test runner for formatter tests
# Usage: ./run_tests.sh [test_name]

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                          🎨 FORMATTER TEST SUITE 🎨                                    ║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

get_test_description() {
    local test_name=$1
    case "$test_name" in
        # Template Formatter Tests
        "TestTemplateFormatter_Creation")
            echo "🏗️  Template Formatter - Creation and initialization"
            ;;
        "TestTemplateFormatter_Format")
            echo "📝 Template Formatter - Data formatting with templates"
            ;;
        "TestTemplateFormatter_TemplateFunctions")
            echo "🔧 Template Formatter - Custom template functions"
            ;;
        "TestTemplateFormatter_ErrorHandling")
            echo "🚨 Template Formatter - Error handling scenarios"
            ;;
        "TestTemplateFormatter_ContentType")
            echo "📋 Template Formatter - Content type handling"
            ;;
        "TestTemplateFormatter_ThreadSafety")
            echo "🔀 Template Formatter - Concurrent formatting safety"
            ;;
            
        # Formatter Factory Tests
        "TestFormatterFactory_Creation")
            echo "🏭 Formatter Factory - Factory creation"
            ;;
        "TestFormatterFactory_CreateFormatter")
            echo "🎯 Formatter Factory - Formatter creation for different formats"
            ;;
        "TestFormatterFactory_GetTemplatePath")
            echo "📍 Formatter Factory - Template path resolution"
            ;;
        "TestFormatterFactory_MissingTemplate")
            echo "❌ Formatter Factory - Missing template handling"
            ;;
        "TestFormatterFactory_Integration")
            echo "🔗 Formatter Factory - Integration with template formatting"
            ;;
        "TestFormatterFactory_PathHandling")
            echo "📂 Formatter Factory - Path handling scenarios"
            ;;
            
        # Integration Tests
        "TestEndToEnd_NIBRSFormatting")
            echo "🎯 End-to-End - Complete NIBRS formatting pipeline"
            ;;
        "TestEndToEnd_ErrorScenarios")
            echo "🚨 End-to-End - Error handling in complete pipeline"
            ;;
        "TestEndToEnd_LargeDataset")
            echo "📊 End-to-End - Large dataset formatting"
            ;;
            
        # Performance Tests
        "TestPerformance_TemplateExecution")
            echo "⚡ Performance - Template execution speed"
            ;;
        "TestPerformance_ConcurrentFormatting")
            echo "⚡ Performance - Concurrent formatting throughput"
            ;;
            
        # Benchmark Tests
        "BenchmarkTemplateFormatter_Format")
            echo "⚡ Benchmark - Template formatting speed"
            ;;
        "BenchmarkFormatterFactory_CreateFormatter")
            echo "⚡ Benchmark - Formatter creation overhead"
            ;;
        "BenchmarkTemplateFormatter_ConcurrentFormat")
            echo "⚡ Benchmark - Concurrent formatting performance"
            ;;
            
        *)
            echo ""
            ;;
    esac
}

print_test_category() {
    local test_name=$1
    local description=$(get_test_description "$test_name")
    
    if [[ -n $description ]]; then
        echo -e "${CYAN}┌─ $description${NC}"
    fi
}

print_test_result() {
    local line=$1
    
    if [[ $line =~ ^[[:space:]]*---[[:space:]]*PASS:[[:space:]]*([^[:space:]]+) ]]; then
        local test_name="${BASH_REMATCH[1]}"
        local short_name=$(echo "$test_name" | sed 's/.*\///')
        echo -e "${GREEN}    ✓ $short_name${NC}"
    elif [[ $line =~ ^[[:space:]]*---[[:space:]]*FAIL:[[:space:]]*([^[:space:]]+) ]]; then
        local test_name="${BASH_REMATCH[1]}"
        local short_name=$(echo "$test_name" | sed 's/.*\///')
        echo -e "${RED}    ✗ $short_name${NC}"
    elif [[ $line =~ ^===[[:space:]]*RUN[[:space:]]*([^[:space:]]+) ]]; then
        local test_name="${BASH_REMATCH[1]}"
        # Only print category for top-level tests (no '/' in name after removing package prefix)
        if [[ ! $test_name =~ / ]]; then
            print_test_category "$test_name"
        fi
    fi
}

print_summary() {
    local exit_code=$1
    echo ""
    if [[ $exit_code -eq 0 ]]; then
        echo -e "${GREEN}╔══════════════════════════════════════════════════════════════════════════════════════════╗${NC}"
        echo -e "${GREEN}║                              🎉 ALL TESTS PASSED! 🎉                                   ║${NC}"
        echo -e "${GREEN}╚══════════════════════════════════════════════════════════════════════════════════════════╝${NC}"
    else
        echo -e "${RED}╔══════════════════════════════════════════════════════════════════════════════════════════╗${NC}"
        echo -e "${RED}║                               ❌ TESTS FAILED ❌                                         ║${NC}"
        echo -e "${RED}╚══════════════════════════════════════════════════════════════════════════════════════════╝${NC}"
    fi
}

# Get directory of this script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Change to the workflow directory (go up 4 levels from tests/)
cd "$SCRIPT_DIR/../../../../.."

# Print header
print_header

# Determine test filter
TEST_FILTER=""
if [[ $# -gt 0 ]]; then
    TEST_FILTER="-run $1"
    echo -e "${YELLOW}🔍 Running filtered tests: $1${NC}"
    echo ""
fi

# Run tests and process output
echo -e "${PURPLE}🧪 Running formatter tests...${NC}"
echo ""

# Capture both stdout and stderr, and the exit code
output=$(go test -v ./internal/etl/transformation/formatters/tests/ $TEST_FILTER 2>&1)
exit_code=$?

# Process each line of output
while IFS= read -r line; do
    print_test_result "$line"
done <<< "$output"

# Print summary
print_summary $exit_code

# If tests failed, show the raw output for debugging
if [[ $exit_code -ne 0 ]]; then
    echo ""
    echo -e "${RED}🔍 Raw test output for debugging:${NC}"
    echo -e "${WHITE}$output${NC}"
fi

exit $exit_code