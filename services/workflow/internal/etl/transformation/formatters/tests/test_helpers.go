package tests

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"testing"
)

// TestHelpers provides common utility functions for formatter tests.
//
// This file contains shared helper functions to reduce code duplication
// across test files and provide consistent testing utilities.

// createTestTemplate creates a temporary template file for testing.
//
// This function creates a template file with the given content and returns
// the path to the file. The file is cleaned up after the test.
//
// Parameters:
//   - t: Testing instance for cleanup registration
//   - name: Name of the template file
//   - content: Template content
//
// Returns:
//   - string: Path to the created template file
func createTestTemplate(t *testing.T, content string) string {
	t.Helper()

	// Create temp directory
	tempDir := t.TempDir()
	templatePath := filepath.Join(tempDir, "test.tmpl")

	// Write template content
	err := os.WriteFile(templatePath, []byte(content), 0600)
	if err != nil {
		t.Fatalf("Failed to create test template: %v", err)
	}

	return templatePath
}

// validateXMLStructure performs basic XML structure validation.
//
// This function checks if the output has valid XML structure without
// requiring a full XML parser.
//
// Parameters:
//   - content: XML content to validate
//
// Returns:
//   - []string: List of validation errors (empty if valid)
func validateXMLStructure(content string) []string {
	errors := []string{}

	// Check XML declaration
	if !strings.HasPrefix(strings.TrimSpace(content), "<?xml") {
		errors = append(errors, "Missing XML declaration")
	}

	// Basic tag balance check
	openTags := 0
	closeTags := 0

	for i := 0; i < len(content); i++ {
		if content[i] == '<' && i+1 < len(content) {
			if content[i+1] == '/' {
				closeTags++
			} else if content[i+1] != '?' && content[i+1] != '!' {
				openTags++
			}
		}
	}

	// Very basic check - not perfect but catches obvious issues
	if openTags != closeTags {
		errors = append(errors, fmt.Sprintf("Unbalanced tags: %d open, %d close", openTags, closeTags))
	}

	return errors
}

// compareOutput compares actual output with expected output.
//
// This function provides detailed comparison for test assertions.
//
// Parameters:
//   - actual: Actual output
//   - expected: Expected output
//
// Returns:
//   - string: Difference description (empty if equal)
func compareOutput(actual, expected string) string {
	actual = strings.TrimSpace(actual)
	expected = strings.TrimSpace(expected)

	if actual == expected {
		return ""
	}

	// Find first difference
	minLen := len(actual)
	if len(expected) < minLen {
		minLen = len(expected)
	}

	for i := 0; i < minLen; i++ {
		if actual[i] != expected[i] {
			start := i - 20
			if start < 0 {
				start = 0
			}
			end := i + 20
			if end > minLen {
				end = minLen
			}

			return fmt.Sprintf(
				"First difference at position %d:\nActual:   ...%s...\nExpected: ...%s...",
				i,
				actual[start:end],
				expected[start:end],
			)
		}
	}

	if len(actual) != len(expected) {
		return fmt.Sprintf("Length mismatch: actual=%d, expected=%d", len(actual), len(expected))
	}

	return "Contents appear equal but comparison failed"
}
