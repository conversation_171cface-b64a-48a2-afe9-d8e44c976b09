package formatters

import (
	"bytes"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"text/template"
	"time"
)

// TemplateFormatter is a dumb template executor that generates formatted output.
//
// This formatter is intentionally minimal - it only loads and executes templates
// without any business logic. All formatting decisions, conditional rendering,
// data manipulation, and output structure are controlled entirely by the template.
//
// Template Features Available:
// Templates have access to Go's standard template features:
//   - Conditionals: {{if .Field}}...{{end}}
//   - Loops: {{range .Items}}...{{end}}
//   - Variables: {{$var := .Field}}
//   - Pipelines: {{.Field | function}}
//   - Comments: {{/* comment */}}
//   - Whitespace control: {{- and -}}
//
// The formatter provides minimal helper functions that are generic utilities,
// not business logic. Templates use these to control formatting.
//
// Thread Safety:
// TemplateFormatter is thread-safe after initialization. Multiple goroutines
// can call Format concurrently.
type TemplateFormatter struct {
	templatePath string             // Path to template file
	contentType  string             // MIME type of output
	template     *template.Template // Parsed template (cached)
}

// NewTemplateFormatter creates a new template-based formatter.
//
// The formatter loads and parses the template file once during initialization
// for better performance. The template is cached and reused for all Format calls.
//
// Parameters:
//   - templatePath: Filesystem path to the template file
//   - contentType: MIME type of the output format
//
// Returns:
//   - *TemplateFormatter: Initialized formatter ready for use
//   - error: If template file doesn't exist or fails to parse
//
// Example:
//
//	formatter, err := NewTemplateFormatter(
//	    "templates/nibrs/nibrs.xml.tmpl",
//	    "application/xml",
//	)
func NewTemplateFormatter(templatePath, contentType string) (*TemplateFormatter, error) {
	// Create base template with minimal helper functions
	tmpl := template.New(filepath.Base(templatePath)).Funcs(templateFunctions())

	// Read template file
	templateContent, err := os.ReadFile(templatePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read template file %s: %w", templatePath, err)
	}

	// Parse template
	tmpl, err = tmpl.Parse(string(templateContent))
	if err != nil {
		return nil, fmt.Errorf("failed to parse template %s: %w", templatePath, err)
	}

	return &TemplateFormatter{
		templatePath: templatePath,
		contentType:  contentType,
		template:     tmpl,
	}, nil
}

// NewTemplateFormatterFromString creates a new template-based formatter from template content.
//
// This variant creates a formatter from template content directly instead of reading
// from a file. Useful for testing and API-based template overrides.
//
// Parameters:
//   - templateContent: Template content as a string
//   - contentType: MIME type of the output format
//
// Returns:
//   - *TemplateFormatter: Initialized formatter ready for use
//   - error: If template fails to parse
//
// Example:
//
//	formatter, err := NewTemplateFormatterFromString(
//	    "<?xml version=\"1.0\"?><Report>{{.title}}</Report>",
//	    "application/xml",
//	)
func NewTemplateFormatterFromString(templateContent, contentType string) (*TemplateFormatter, error) {
	// Create base template with minimal helper functions
	tmpl := template.New("custom").Funcs(templateFunctions())

	// Parse template
	tmpl, err := tmpl.Parse(templateContent)
	if err != nil {
		return nil, fmt.Errorf("failed to parse template: %w", err)
	}

	return &TemplateFormatter{
		templatePath: "custom-template",
		contentType:  contentType,
		template:     tmpl,
	}, nil
}

// Format executes the template with the provided data.
//
// This method is a pure template executor - it simply runs the template
// with the data and returns the output. All formatting logic, conditionals,
// loops, and data manipulation are handled by the template itself.
//
// Parameters:
//   - data: Data to pass to the template (any type the template expects)
//
// Returns:
//   - []byte: Raw template output
//   - error: Template execution errors only
//
// The template has complete control over:
//   - Output structure and format
//   - Handling missing/nil fields
//   - Conditional rendering
//   - Loops and iterations
//   - Field formatting
//   - Default values
func (f *TemplateFormatter) Format(data interface{}) ([]byte, error) {
	var buf bytes.Buffer

	// Execute template - let template handle everything
	if err := f.template.Execute(&buf, data); err != nil {
		return nil, fmt.Errorf("template execution failed: %w", err)
	}

	return buf.Bytes(), nil
}

// ContentType returns the MIME type of the formatted output.
func (f *TemplateFormatter) ContentType() string {
	return f.contentType
}

// TemplatePath returns the filesystem path of the template being used.
func (f *TemplateFormatter) TemplatePath() string {
	return f.templatePath
}

// templateFunctions returns minimal helper functions for templates.
//
// These are generic utilities only - no business logic. Templates use these
// functions to control formatting, but all decisions about when and how to
// use them are made in the template itself.
//
// Available Functions:
//   - default: Return default value if input is nil or empty
//   - upper: Convert string to uppercase
//   - lower: Convert string to lowercase
//   - trim: Trim whitespace from string
//   - formatDate: Format date/time values
//
// Templates control how these are used:
//
//	{{.Field | default "N/A"}}
//	{{.Name | upper}}
//	{{.Date | formatDate "2006-01-02"}}
func templateFunctions() template.FuncMap {
	return template.FuncMap{
		// default returns the default value if the input is nil or empty
		"default": func(defaultValue interface{}, value interface{}) interface{} {
			if value == nil {
				return defaultValue
			}
			// Check for empty string
			if str, ok := value.(string); ok && str == "" {
				return defaultValue
			}
			return value
		},

		// upper converts string to uppercase
		"upper": func(value interface{}) string {
			if value == nil {
				return ""
			}
			if str, ok := value.(string); ok {
				return strings.ToUpper(str)
			}
			return fmt.Sprintf("%v", value)
		},

		// lower converts string to lowercase
		"lower": func(value interface{}) string {
			if value == nil {
				return ""
			}
			if str, ok := value.(string); ok {
				return strings.ToLower(str)
			}
			return fmt.Sprintf("%v", value)
		},

		// trim removes leading and trailing whitespace
		"trim": func(value interface{}) string {
			if value == nil {
				return ""
			}
			if str, ok := value.(string); ok {
				return strings.TrimSpace(str)
			}
			return strings.TrimSpace(fmt.Sprintf("%v", value))
		},

		// formatDate formats a date/time value according to the given layout
		"formatDate": func(layout string, value interface{}) (string, error) {
			if value == nil {
				return "", nil
			}

			switch v := value.(type) {
			case time.Time:
				return v.Format(layout), nil
			case string:
				// Try to parse as RFC3339
				t, err := time.Parse(time.RFC3339, v)
				if err != nil {
					// Try other common formats
					for _, format := range []string{
						"2006-01-02T15:04:05Z",
						"2006-01-02 15:04:05",
						"2006-01-02",
					} {
						if t, err = time.Parse(format, v); err == nil {
							return t.Format(layout), nil
						}
					}
					return v, nil // Return original string if parsing fails
				}
				return t.Format(layout), nil
			default:
				return fmt.Sprintf("%v", v), nil
			}
		},

		// now returns the current time
		"now": time.Now,

		// add performs addition (useful for sequence numbers)
		"add": func(a, b int) int {
			return a + b
		},
	}
}
