// Package formatters provides a dumb template execution system for generating
// various output formats from transformed data. All business logic resides in
// templates, not in Go code.
//
// The formatters package follows the same philosophy as the core transformation
// engine: zero business logic in Go code. All formatting rules, conditional
// logic, loops, and data manipulation are controlled entirely by template files.
// The formatter's only responsibility is to load and execute templates.
//
// Template-Driven Architecture:
// The formatter acts as a simple template executor that:
//   - Loads template files from the filesystem
//   - Executes templates with provided data
//   - Returns the raw template output without modification
//
// All intelligence resides in the templates themselves:
//   - Conditional rendering using {{if}} blocks
//   - Loops and iterations using {{range}}
//   - Field formatting using template functions
//   - Default values and null handling
//   - Complete output structure definition
//
// This approach ensures that:
//   - New formats can be added by creating templates only
//   - Format changes require no Go code modifications
//   - Business logic remains transparent and auditable
//   - Templates are self-contained and testable
package formatters

// Formatter defines the minimal interface for format generation.
//
// Implementations of this interface should remain as dumb as possible,
// delegating all formatting logic to template files. The formatter's
// only job is to execute templates - it should not contain any business
// logic, validation, or post-processing.
//
// Template Control:
// Templates have complete control over:
//   - Output structure and format
//   - Conditional logic for optional fields
//   - Loops for repeated elements
//   - Field formatting and transformations
//   - Default values for missing data
//   - Error handling within the template
//
// Example Template Logic:
//
//	{{- if .ReportDate}}
//	<ReportDate>{{.ReportDate | formatDate "2006-01-02"}}</ReportDate>
//	{{- end}}
//
// Thread Safety:
// Formatter implementations must be thread-safe as they may be used
// concurrently by multiple goroutines.
type Formatter interface {
	// Format executes the template with the provided data.
	//
	// This method should be a pure template executor with no business logic.
	// All formatting decisions, conditional rendering, and data manipulation
	// must be handled by the template itself.
	//
	// Parameters:
	//   - data: The transformed data to format (typically map[string]interface{})
	//
	// Returns:
	//   - []byte: Raw template output without any post-processing
	//   - error: Template execution errors only (not business logic errors)
	//
	// Template Responsibilities:
	//   - Define complete output structure
	//   - Handle missing or nil fields gracefully
	//   - Apply all formatting rules
	//   - Implement all conditional logic
	//   - Generate valid output for the target format
	Format(data interface{}) ([]byte, error)

	// ContentType returns the MIME type of the formatted output.
	//
	// This should return a standard MIME type that matches the output format:
	//   - "application/xml" for XML formats
	//   - "application/json" for JSON formats
	//   - "text/csv" for CSV formats
	//   - "text/plain" for plain text formats
	//
	// Returns:
	//   - string: MIME type of the formatted output
	ContentType() string

	// TemplatePath returns the filesystem path of the template being used.
	//
	// This is useful for debugging and tracking which template was used
	// for a particular transformation.
	//
	// Returns:
	//   - string: Absolute or relative path to the template file
	TemplatePath() string
}
