# XML Template Formatter Guide

This guide will help you create XML templates to transform your data. No programming knowledge required!

## Table of Contents
- [What is an XML Template?](#what-is-an-xml-template)
- [Basic XML Template](#basic-xml-template)
- [Available Functions](#available-functions)
- [Conditionals (Optional Elements)](#conditionals-optional-elements)
- [Loops (Repeating Elements)](#loops-repeating-elements)
- [Attributes](#attributes)
- [Handling Special Characters](#handling-special-characters)
- [Complete NIBRS Example](#complete-nibrs-example)

## What is an XML Template?

An XML template is a blueprint for creating XML documents. You write the XML structure and use `{{...}}` tags to insert data dynamically.

### Simple Example

**Input Data:**
```json
{
  "report": {
    "id": "RPT-001",
    "date": "2024-07-14",
    "status": "Active"
  }
}
```

**Template:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Report>
  <ReportID>{{.report.id}}</ReportID>
  <Date>{{.report.date}}</Date>
  <Status>{{.report.status}}</Status>
</Report>
```

**Output:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Report>
  <ReportID>RPT-001</ReportID>
  <Date>2024-07-14</Date>
  <Status>Active</Status>
</Report>
```

## Basic XML Template

### Nested Data

**Input Data:**
```json
{
  "incident": {
    "number": "INC-2024-001",
    "location": {
      "address": "123 Main St",
      "city": "Springfield",
      "state": "IL",
      "zip": "62701"
    }
  }
}
```

**Template:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Incident>
  <IncidentNumber>{{.incident.number}}</IncidentNumber>
  <Location>
    <Address>{{.incident.location.address}}</Address>
    <City>{{.incident.location.city}}</City>
    <State>{{.incident.location.state}}</State>
    <ZipCode>{{.incident.location.zip}}</ZipCode>
  </Location>
</Incident>
```

**Output:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Incident>
  <IncidentNumber>INC-2024-001</IncidentNumber>
  <Location>
    <Address>123 Main St</Address>
    <City>Springfield</City>
    <State>IL</State>
    <ZipCode>62701</ZipCode>
  </Location>
</Incident>
```

## Available Functions

### 1. `default` - Provide Missing Values

**Input Data:**
```json
{
  "person": {
    "name": "John Doe",
    "age": "",
    "gender": null
  }
}
```

**Template:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Person>
  <Name>{{.person.name}}</Name>
  <Age>{{.person.age | default "Unknown"}}</Age>
  <Gender>{{.person.gender | default "U"}}</Gender>
</Person>
```

**Output:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Person>
  <Name>John Doe</Name>
  <Age>Unknown</Age>
  <Gender>U</Gender>
</Person>
```

### 2. `upper` - Convert to Uppercase

**Input Data:**
```json
{
  "officer": {
    "badge": "det-789",
    "lastName": "smith"
  }
}
```

**Template:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Officer>
  <BadgeNumber>{{.officer.badge | upper}}</BadgeNumber>
  <LastName>{{.officer.lastName | upper}}</LastName>
</Officer>
```

**Output:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Officer>
  <BadgeNumber>DET-789</BadgeNumber>
  <LastName>SMITH</LastName>
</Officer>
```

### 3. `lower` - Convert to Lowercase

**Input Data:**
```json
{
  "person": {
    "email": "<EMAIL>",
    "status": "ACTIVE"
  }
}
```

**Template:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Person>
  <Email>{{.person.email | lower}}</Email>
  <Status>{{.person.status | lower}}</Status>
</Person>
```

**Output:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Person>
  <Email><EMAIL></Email>
  <Status>active</Status>
</Person>
```

### 4. `trim` - Remove Extra Spaces

**Input Data:**
```json
{
  "report": {
    "title": "  Incident Report  ",
    "description": "   Suspicious activity observed   "
  }
}
```

**Template:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Report>
  <Title>{{.report.title | trim}}</Title>
  <Description>{{.report.description | trim}}</Description>
</Report>
```

**Output:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Report>
  <Title>Incident Report</Title>
  <Description>Suspicious activity observed</Description>
</Report>
```

### 5. `formatDate` - Format Dates

**Input Data:**
```json
{
  "incident": {
    "occurredAt": "2024-07-14T15:30:00Z",
    "reportedAt": "2024-07-14T16:45:00Z"
  }
}
```

**Template:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Incident>
  <OccurredDate>{{.incident.occurredAt | formatDate "2006-01-02"}}</OccurredDate>
  <OccurredTime>{{.incident.occurredAt | formatDate "15:04:05"}}</OccurredTime>
  <ReportedDateTime>{{.incident.reportedAt | formatDate "01/02/2006 3:04 PM"}}</ReportedDateTime>
</Incident>
```

**Output:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Incident>
  <OccurredDate>2024-07-14</OccurredDate>
  <OccurredTime>15:30:00</OccurredTime>
  <ReportedDateTime>07/14/2024 4:45 PM</ReportedDateTime>
</Incident>
```

### 6. `now` - Current Date/Time

**Template:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Report>
  <GeneratedDate>{{now | formatDate "2006-01-02"}}</GeneratedDate>
  <GeneratedTime>{{now | formatDate "15:04:05"}}</GeneratedTime>
  <GeneratedDateTime>{{now | formatDate "2006-01-02T15:04:05Z"}}</GeneratedDateTime>
</Report>
```

**Output:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Report>
  <GeneratedDate>2024-07-14</GeneratedDate>
  <GeneratedTime>10:30:45</GeneratedTime>
  <GeneratedDateTime>2024-07-14T10:30:45Z</GeneratedDateTime>
</Report>
```

### 7. `add` - Addition

**Input Data:**
```json
{
  "items": ["first", "second", "third"],
  "page": 5,
  "baseNumber": 100
}
```

**Template:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Document>
  <CurrentPage>{{.page}}</CurrentPage>
  <NextPage>{{add .page 1}}</NextPage>
  <PreviousPage>{{add .page -1}}</PreviousPage>
  {{- range $index, $item := .items}}
  <Item sequence="{{add $index 1}}" id="{{add $.baseNumber $index}}">{{$item}}</Item>
  {{- end}}
</Document>
```

**Output:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Document>
  <CurrentPage>5</CurrentPage>
  <NextPage>6</NextPage>
  <PreviousPage>4</PreviousPage>
  <Item sequence="1" id="100">first</Item>
  <Item sequence="2" id="101">second</Item>
  <Item sequence="3" id="102">third</Item>
</Document>
```

### 8. Combining Functions

**Input Data:**
```json
{
  "suspect": {
    "firstName": "  john  ",
    "lastName": "  doe  ",
    "alias": ""
  }
}
```

**Template:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Suspect>
  <FullName>{{.suspect.firstName | trim | upper}} {{.suspect.lastName | trim | upper}}</FullName>
  <Alias>{{.suspect.alias | default "None" | upper}}</Alias>
</Suspect>
```

**Output:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Suspect>
  <FullName>JOHN DOE</FullName>
  <Alias>NONE</Alias>
</Suspect>
```

## Conditionals (Optional Elements)

### Basic If Statement - Include Elements Only When Data Exists

**Input Data:**
```json
{
  "victim": {
    "name": "Jane Smith",
    "phone": "555-1234",
    "email": ""
  }
}
```

**Template:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Victim>
  <Name>{{.victim.name}}</Name>
  {{- if .victim.phone}}
  <Phone>{{.victim.phone}}</Phone>
  {{- end}}
  {{- if .victim.email}}
  <Email>{{.victim.email}}</Email>
  {{- end}}
</Victim>
```

**Output:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Victim>
  <Name>Jane Smith</Name>
  <Phone>555-1234</Phone>
</Victim>
```

### If-Else for Different Values

**Input Data:**
```json
{
  "incident": {
    "severity": "high",
    "resolved": false
  }
}
```

**Template:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Incident>
  <Priority>
    {{- if eq .incident.severity "high"}}
    <Code>1</Code>
    <Description>URGENT</Description>
    {{- else if eq .incident.severity "medium"}}
    <Code>2</Code>
    <Description>NORMAL</Description>
    {{- else}}
    <Code>3</Code>
    <Description>LOW</Description>
    {{- end}}
  </Priority>
  <Status>{{if .incident.resolved}}CLOSED{{else}}OPEN{{end}}</Status>
</Incident>
```

**Output:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Incident>
  <Priority>
    <Code>1</Code>
    <Description>URGENT</Description>
  </Priority>
  <Status>OPEN</Status>
</Incident>
```

## Loops (Repeating Elements)

### Basic List

**Input Data:**
```json
{
  "witnesses": [
    {"name": "Alice Johnson", "phone": "555-0001"},
    {"name": "Bob Williams", "phone": "555-0002"},
    {"name": "Carol Davis", "phone": "555-0003"}
  ]
}
```

**Template:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<WitnessList>
  {{- range .witnesses}}
  <Witness>
    <Name>{{.name}}</Name>
    <Phone>{{.phone}}</Phone>
  </Witness>
  {{- end}}
</WitnessList>
```

**Output:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<WitnessList>
  <Witness>
    <Name>Alice Johnson</Name>
    <Phone>555-0001</Phone>
  </Witness>
  <Witness>
    <Name>Bob Williams</Name>
    <Phone>555-0002</Phone>
  </Witness>
  <Witness>
    <Name>Carol Davis</Name>
    <Phone>555-0003</Phone>
  </Witness>
</WitnessList>
```

### Loop with Sequence Numbers

**Input Data:**
```json
{
  "offenses": [
    {"code": "240", "description": "Assault"},
    {"code": "459", "description": "Burglary"},
    {"code": "487", "description": "Grand Theft"}
  ]
}
```

**Template:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<OffenseList>
  {{- range $index, $offense := .offenses}}
  <Offense sequence="{{add $index 1}}">
    <Code>{{$offense.code}}</Code>
    <Description>{{$offense.description | upper}}</Description>
  </Offense>
  {{- end}}
</OffenseList>
```

**Output:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<OffenseList>
  <Offense sequence="1">
    <Code>240</Code>
    <Description>ASSAULT</Description>
  </Offense>
  <Offense sequence="2">
    <Code>459</Code>
    <Description>BURGLARY</Description>
  </Offense>
  <Offense sequence="3">
    <Code>487</Code>
    <Description>GRAND THEFT</Description>
  </Offense>
</OffenseList>
```

## Attributes

### Adding Attributes to Elements

**Input Data:**
```json
{
  "report": {
    "id": "RPT-2024-001",
    "type": "incident",
    "priority": "high",
    "officer": {
      "id": "OFC-123",
      "name": "Det. Smith"
    }
  }
}
```

**Template:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Report id="{{.report.id}}" type="{{.report.type}}" priority="{{.report.priority}}">
  <Officer id="{{.report.officer.id}}">
    <Name>{{.report.officer.name}}</Name>
  </Officer>
</Report>
```

**Output:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Report id="RPT-2024-001" type="incident" priority="high">
  <Officer id="OFC-123">
    <Name>Det. Smith</Name>
  </Officer>
</Report>
```

## Handling Special Characters

XML requires certain characters to be escaped. The template system handles this automatically:

**Input Data:**
```json
{
  "evidence": {
    "description": "Found <5 items & noted \"suspicious\" behavior",
    "location": "Smith & Son's Store"
  }
}
```

**Template:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Evidence>
  <Description>{{.evidence.description}}</Description>
  <Location>{{.evidence.location}}</Location>
</Evidence>
```

**Output:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Evidence>
  <Description>Found &lt;5 items &amp; noted &quot;suspicious&quot; behavior</Description>
  <Location>Smith &amp; Son's Store</Location>
</Evidence>
```

## Complete NIBRS Example

Here's a complete example showing a NIBRS-style incident report:

**Input Data:**
```json
{
  "agency": {
    "ori": "*********",
    "name": "Austin Police Department"
  },
  "incident": {
    "number": "2024-001234",
    "date": "2024-07-14T10:30:00Z"
  },
  "offenses": [
    {
      "code": "13A",
      "description": "Aggravated Assault",
      "location": "20",
      "completed": true
    }
  ],
  "victims": [
    {
      "sequence": 1,
      "type": "I",
      "name": "John Doe",
      "age": 35,
      "sex": "M",
      "race": "W",
      "injuries": ["Broken Nose", "Bruises"]
    }
  ],
  "offenders": [
    {
      "sequence": 1,
      "name": "Unknown",
      "age": null,
      "sex": "M",
      "race": "U"
    }
  ]
}
```

**Template:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<nibrs:Submission xmlns:nibrs="http://fbi.gov/cjis/nibrs/4.1">
  <nibrs:ReportHeader>
    <nibrs:ReportDate>{{now | formatDate "2006-01-02"}}</nibrs:ReportDate>
    <nibrs:ORI>{{.agency.ori}}</nibrs:ORI>
    <nibrs:AgencyName>{{.agency.name | upper}}</nibrs:AgencyName>
  </nibrs:ReportHeader>
  
  <nibrs:Incident>
    <nibrs:IncidentNumber>{{.incident.number}}</nibrs:IncidentNumber>
    <nibrs:IncidentDate>{{.incident.date | formatDate "2006-01-02"}}</nibrs:IncidentDate>
    <nibrs:IncidentTime>{{.incident.date | formatDate "15:04"}}</nibrs:IncidentTime>
    
    {{- range $index, $offense := .offenses}}
    <nibrs:Offense sequence="{{printf "%02d" (add $index 1)}}">
      <nibrs:UCRCode>{{$offense.code}}</nibrs:UCRCode>
      <nibrs:Description>{{$offense.description | upper}}</nibrs:Description>
      <nibrs:LocationType>{{$offense.location}}</nibrs:LocationType>
      <nibrs:AttemptedCompleted>{{if $offense.completed}}C{{else}}A{{end}}</nibrs:AttemptedCompleted>
    </nibrs:Offense>
    {{- end}}
    
    {{- range .victims}}
    <nibrs:Victim sequence="{{printf "%02d" .sequence}}">
      <nibrs:VictimType>{{.type}}</nibrs:VictimType>
      <nibrs:Name>{{.name | upper}}</nibrs:Name>
      <nibrs:Age>{{.age | default "00"}}</nibrs:Age>
      <nibrs:Sex>{{.sex}}</nibrs:Sex>
      <nibrs:Race>{{.race}}</nibrs:Race>
      {{- if .injuries}}
      <nibrs:Injuries>
        {{- range .injuries}}
        <nibrs:Injury>{{. | upper}}</nibrs:Injury>
        {{- end}}
      </nibrs:Injuries>
      {{- end}}
    </nibrs:Victim>
    {{- end}}
    
    {{- range .offenders}}
    <nibrs:Offender sequence="{{printf "%02d" .sequence}}">
      <nibrs:Name>{{.name | default "UNKNOWN" | upper}}</nibrs:Name>
      <nibrs:Age>{{.age | default "00"}}</nibrs:Age>
      <nibrs:Sex>{{.sex | default "U"}}</nibrs:Sex>
      <nibrs:Race>{{.race | default "U"}}</nibrs:Race>
    </nibrs:Offender>
    {{- end}}
  </nibrs:Incident>
</nibrs:Submission>
```

**Output:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<nibrs:Submission xmlns:nibrs="http://fbi.gov/cjis/nibrs/4.1">
  <nibrs:ReportHeader>
    <nibrs:ReportDate>2024-07-14</nibrs:ReportDate>
    <nibrs:ORI>*********</nibrs:ORI>
    <nibrs:AgencyName>AUSTIN POLICE DEPARTMENT</nibrs:AgencyName>
  </nibrs:ReportHeader>
  
  <nibrs:Incident>
    <nibrs:IncidentNumber>2024-001234</nibrs:IncidentNumber>
    <nibrs:IncidentDate>2024-07-14</nibrs:IncidentDate>
    <nibrs:IncidentTime>10:30</nibrs:IncidentTime>
    
    <nibrs:Offense sequence="01">
      <nibrs:UCRCode>13A</nibrs:UCRCode>
      <nibrs:Description>AGGRAVATED ASSAULT</nibrs:Description>
      <nibrs:LocationType>20</nibrs:LocationType>
      <nibrs:AttemptedCompleted>C</nibrs:AttemptedCompleted>
    </nibrs:Offense>
    
    <nibrs:Victim sequence="01">
      <nibrs:VictimType>I</nibrs:VictimType>
      <nibrs:Name>JOHN DOE</nibrs:Name>
      <nibrs:Age>35</nibrs:Age>
      <nibrs:Sex>M</nibrs:Sex>
      <nibrs:Race>W</nibrs:Race>
      <nibrs:Injuries>
        <nibrs:Injury>BROKEN NOSE</nibrs:Injury>
        <nibrs:Injury>BRUISES</nibrs:Injury>
      </nibrs:Injuries>
    </nibrs:Victim>
    
    <nibrs:Offender sequence="01">
      <nibrs:Name>UNKNOWN</nibrs:Name>
      <nibrs:Age>00</nibrs:Age>
      <nibrs:Sex>M</nibrs:Sex>
      <nibrs:Race>U</nibrs:Race>
    </nibrs:Offender>
  </nibrs:Incident>
</nibrs:Submission>
```

## Whitespace Control

Template tags can generate unwanted whitespace in your XML. Use `{{-` and `-}}` to trim whitespace:

**Without whitespace control:**
```xml
<Persons>
  {{range .persons}}
  <Person>
    {{.name}}
  </Person>
  {{end}}
</Persons>
```

Output has extra blank lines:
```xml
<Persons>
  
  <Person>
    John
  </Person>
  
  <Person>
    Jane
  </Person>
  
</Persons>
```

**With whitespace control:**
```xml
<Persons>
  {{- range .persons}}
  <Person>
    {{- .name -}}
  </Person>
  {{- end}}
</Persons>
```

Output is clean:
```xml
<Persons>
  <Person>John</Person>
  <Person>Jane</Person>
</Persons>
```

**Rules:**
- `{{-` removes whitespace before the tag
- `-}}` removes whitespace after the tag
- Use both for maximum control: `{{- .value -}}`

## Tips for XML Templates

1. **Always include the XML declaration**: `<?xml version="1.0" encoding="UTF-8"?>`
2. **Use whitespace control** (`{{-` and `-}}`) to keep XML clean
3. **Handle missing data** with `default` function
4. **Test with sample data** to ensure valid XML output
5. **Use `printf`** for formatted numbers: `{{printf "%02d" .number}}` gives "01", "02", etc.
6. **Escape is automatic**: Special characters like `<`, `>`, `&` are automatically escaped
7. **Use comments** for complex logic: `{{/* This is a comment */}}`

## Common Patterns

### Optional Parent Element
Only include a parent element if it has children:
```xml
{{- if .person.addresses}}
<Addresses>
  {{- range .person.addresses}}
  <Address>{{.}}</Address>
  {{- end}}
</Addresses>
{{- end}}
```

### Conditional Attributes
```xml
<Person{{if .person.id}} id="{{.person.id}}"{{end}}>
  <Name>{{.person.name}}</Name>
</Person>
```

### Formatted Sequence Numbers
```xml
{{range $index, $item := .items}}
<Item number="{{printf "%03d" (add $index 1)}}">
  {{/* This creates: 001, 002, 003, etc. */}}
</Item>
{{end}}
```

## Advanced Template Features

### The `with` Statement - Safe Navigation

Use `with` to safely access nested data and change context:

**Input Data:**
```json
{
  "reports": [
    {"report": {"id": "123", "title": "Incident"}},
    null
  ],
  "emptyReports": []
}
```

**Template:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Document>
  {{- with index .reports 0}}
  <Report>
    <ID>{{.report.id}}</ID>
    <Title>{{.report.title}}</Title>
  </Report>
  {{- else}}
  <NoReportFound/>
  {{- end}}
  
  {{- with index .emptyReports 0}}
  <FirstEmpty>{{.}}</FirstEmpty>
  {{- else}}
  <EmptyArray/>
  {{- end}}
</Document>
```

**Output:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Document>
  <Report>
    <ID>123</ID>
    <Title>Incident</Title>
  </Report>
  <EmptyArray/>
</Document>
```

### The `index` Function - Array Access

Access specific array elements safely:

**Input Data:**
```json
{
  "officers": ["Smith", "Johnson", "Williams"],
  "badges": [101, 102, 103]
}
```

**Template:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Assignment>
  <LeadOfficer>{{index .officers 0}}</LeadOfficer>
  <SecondOfficer>{{index .officers 1}}</SecondOfficer>
  <LeadBadge>{{index .badges 0}}</LeadBadge>
</Assignment>
```

**Output:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Assignment>
  <LeadOfficer>Smith</LeadOfficer>
  <SecondOfficer>Johnson</SecondOfficer>
  <LeadBadge>101</LeadBadge>
</Assignment>
```

### The `len` Function - Count Elements

Count items in arrays or strings:

**Input Data:**
```json
{
  "witnesses": ["Alice", "Bob", "Charlie"],
  "evidence": [],
  "caseNumber": "2024-ABC"
}
```

**Template:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Summary>
  <WitnessCount>{{len .witnesses}}</WitnessCount>
  <EvidenceCount>{{len .evidence}}</EvidenceCount>
  <CaseNumberLength>{{len .caseNumber}}</CaseNumberLength>
  {{- if gt (len .witnesses) 2}}
  <MultipleWitnesses>true</MultipleWitnesses>
  {{- end}}
</Summary>
```

**Output:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Summary>
  <WitnessCount>3</WitnessCount>
  <EvidenceCount>0</EvidenceCount>
  <CaseNumberLength>8</CaseNumberLength>
  <MultipleWitnesses>true</MultipleWitnesses>
</Summary>
```

### The `printf` Function - Format Output

Format strings, numbers, and other values:

**Input Data:**
```json
{
  "caseNumber": 2024,
  "department": "Robbery",
  "percentage": 95.5
}
```

**Template:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Case>
  <CaseID>{{printf "CASE-%04d" .caseNumber}}</CaseID>
  <DeptCode>{{printf "%.3s" .department}}</DeptCode>
  <ClearanceRate>{{printf "%.1f%%" .percentage}}</ClearanceRate>
  <Reference>{{printf "%s-%d" .department .caseNumber}}</Reference>
</Case>
```

**Output:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Case>
  <CaseID>CASE-2024</CaseID>
  <DeptCode>Rob</DeptCode>
  <ClearanceRate>95.5%</ClearanceRate>
  <Reference>Robbery-2024</Reference>
</Case>
```

**Printf Format Codes:**
- `%s` - String
- `%d` - Integer
- `%04d` - Integer padded with zeros (0024)
- `%f` - Float
- `%.2f` - Float with 2 decimals
- `%%` - Literal % sign

### Comparison Functions

**Input Data:**
```json
{
  "status": "active",
  "priority": 1,
  "age": 25
}
```

**Template:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Record>
  {{- if eq .status "active"}}
  <Status>ACTIVE</Status>
  {{- else if eq .status "pending"}}
  <Status>PENDING</Status>
  {{- else}}
  <Status>INACTIVE</Status>
  {{- end}}
  
  {{- if ne .priority 0}}
  <HasPriority>true</HasPriority>
  {{- end}}
  
  {{- if gt .age 21}}
  <AgeGroup>Adult</AgeGroup>
  {{- else if ge .age 18}}
  <AgeGroup>YoungAdult</AgeGroup>
  {{- else}}
  <AgeGroup>Minor</AgeGroup>
  {{- end}}
</Record>
```

**Output:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Record>
  <Status>ACTIVE</Status>
  <HasPriority>true</HasPriority>
  <AgeGroup>Adult</AgeGroup>
</Record>
```

**Comparison Functions:**
- `eq` - Equal (eq .x .y)
- `ne` - Not equal
- `gt` - Greater than
- `ge` - Greater than or equal
- `lt` - Less than
- `le` - Less than or equal

### Logical Operators

**Input Data:**
```json
{
  "type": "OFFENSE",
  "subtype": "CHARGE",
  "severity": "high",
  "resolved": false
}
```

**Template:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Case>
  {{- if or (eq .type "OFFENSE") (eq .type "VIOLATION")}}
  <Category>Criminal</Category>
  {{- end}}
  
  {{- if and (eq .severity "high") (not .resolved)}}
  <Priority>URGENT</Priority>
  {{- end}}
  
  {{- if not .resolved}}
  <Status>Open</Status>
  {{- else}}
  <Status>Closed</Status>
  {{- end}}
</Case>
```

**Output:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Case>
  <Category>Criminal</Category>
  <Priority>URGENT</Priority>
  <Status>Open</Status>
</Case>
```

**Logical Operators:**
- `or` - Logical OR
- `and` - Logical AND
- `not` - Logical NOT

### Advanced Range with Variables

**Input Data:**
```json
{
  "officers": [
    {"name": "Smith", "badge": 101, "rank": "Sergeant"},
    {"name": "Johnson", "badge": 102, "rank": "Officer"},
    {"name": "Williams", "badge": 103, "rank": "Detective"}
  ]
}
```

**Template:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Roster>
  {{- range $index, $officer := .officers}}
  <Officer sequence="{{add $index 1}}">
    <Badge>{{$officer.badge}}</Badge>
    <Name>{{$officer.name | upper}}</Name>
    <Rank>{{$officer.rank}}</Rank>
    {{- if eq $index 0}}
    <Lead>true</Lead>
    {{- end}}
  </Officer>
  {{- end}}
</Roster>
```

**Output:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Roster>
  <Officer sequence="1">
    <Badge>101</Badge>
    <Name>SMITH</Name>
    <Rank>Sergeant</Rank>
    <Lead>true</Lead>
  </Officer>
  <Officer sequence="2">
    <Badge>102</Badge>
    <Name>JOHNSON</Name>
    <Rank>Officer</Rank>
  </Officer>
  <Officer sequence="3">
    <Badge>103</Badge>
    <Name>WILLIAMS</Name>
    <Rank>Detective</Rank>
  </Officer>
</Roster>
```

### Creating Clean Lists

Generate comma-separated or other delimited lists:

**Input Data:**
```json
{
  "tags": ["urgent", "robbery", "armed", "suspects-at-large"],
  "officers": ["Smith", "Johnson", "Williams"]
}
```

**Template:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Report>
  <Tags>{{range $i, $tag := .tags}}{{if $i}}, {{end}}{{$tag | upper}}{{end}}</Tags>
  <AssignedTo>{{range $i, $name := .officers}}{{if $i}} &amp; {{end}}{{$name}}{{end}}</AssignedTo>
</Report>
```

**Output:**
```xml
<?xml version="1.0" encoding="UTF-8"?>
<Report>
  <Tags>URGENT, ROBBERY, ARMED, SUSPECTS-AT-LARGE</Tags>
  <AssignedTo>Smith &amp; Johnson &amp; Williams</AssignedTo>
</Report>
```

## Complete Reference

### All Available Functions

| Function | Description | Example |
|----------|-------------|---------|
| `default` | Provide fallback value | `{{.field \| default "N/A"}}` |
| `upper` | Convert to uppercase | `{{.name \| upper}}` |
| `lower` | Convert to lowercase | `{{.email \| lower}}` |
| `trim` | Remove whitespace | `{{.text \| trim}}` |
| `formatDate` | Format date/time | `{{.date \| formatDate "2006-01-02"}}` |
| `now` | Current time | `{{now \| formatDate "15:04:05"}}` |
| `add` | Add numbers | `{{add .x 1}}` |
| `len` | Length/count | `{{len .array}}` |
| `index` | Array element | `{{index .array 0}}` |
| `printf` | Format string | `{{printf "%04d" .number}}` |

### All Comparison Functions

| Function | Description | Example |
|----------|-------------|---------|
| `eq` | Equal | `{{if eq .x .y}}` |
| `ne` | Not equal | `{{if ne .x .y}}` |
| `gt` | Greater than | `{{if gt .x .y}}` |
| `ge` | Greater or equal | `{{if ge .x .y}}` |
| `lt` | Less than | `{{if lt .x .y}}` |
| `le` | Less or equal | `{{if le .x .y}}` |

### All Logical Operators

| Operator | Description | Example |
|----------|-------------|---------|
| `or` | Logical OR | `{{if or .x .y}}` |
| `and` | Logical AND | `{{if and .x .y}}` |
| `not` | Logical NOT | `{{if not .x}}` |