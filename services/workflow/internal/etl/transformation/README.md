# ETL Transformation System - Complete Developer Guide

## Overview

The ETL Transformation System is a sophisticated, configuration-driven data transformation engine designed for law enforcement data processing. It converts data between formats (NIBRS, UCR, custom agency formats) without requiring code changes, embodying a **zero business logic in code** philosophy.

## Quick Navigation
- **📘 [Creating Mapping Configurations](./core/README.md)** - User guide for transformation rules
- **📗 [Creating Output Templates](./formatters/README.md)** - User guide for formatting templates

## Table of Contents
- [Architecture & Philosophy](#architecture--philosophy)
- [System Design](#system-design)
- [File Structure & Component Guide](#file-structure--component-guide)
- [Core Concepts](#core-concepts)
- [Data Flow Architecture](#data-flow-architecture)
- [Development Guide](#development-guide)
- [Performance Architecture](#performance-architecture)
- [Testing Philosophy](#testing-philosophy)
- [Debugging & Troubleshooting](#debugging--troubleshooting)
- [Security Considerations](#security-considerations)
- [Contributing Guidelines](#contributing-guidelines)

## Architecture & Philosophy

### Core Design Principles

#### 1. Zero Business Logic in Code
```mermaid
graph TD
    A[Business Requirements] --> B[Configuration Files]
    B --> C[Generic Engine]
    C --> D[Output]
    
    E[Business Changes] -.-> B
    E -.->|No Code Changes| F[Immediate Updates]
    
    style B fill:#f9f,stroke:#333,stroke-width:4px
    style F fill:#9f9,stroke:#333,stroke-width:2px
```

**Philosophy**: The transformation engine is a "dumb" executor. All intelligence resides in configuration files.

**Benefits**:
- **Agility**: Business rule changes without deployment
- **Transparency**: All logic visible in JSON/templates
- **Auditability**: Version control tracks all rule changes
- **Accessibility**: Non-developers can modify rules

**Implementation**:
```go
// ❌ NEVER: Business logic in code
if entityType == "PERSON" && age >= 18 {
    return "ADULT"
}

// ✅ ALWAYS: Business logic in configuration
{
  "transformation": "filter",
  "config": {
    "field": "age",
    "operator": "greater_than_or_equal",
    "value": 18
  }
}
```

#### 2. Configuration-Driven Architecture

All transformation logic lives in three types of configurations:

1. **Mapping Configurations** - Define data transformations
2. **Lookup Tables** - Map values between systems
3. **Templates** - Control output structure

#### 3. Immutable Data Pipeline
```mermaid
graph LR
    A[Input Data] --> B[Transform 1]
    B --> C[Input + Result 1]
    C --> D[Transform 2]
    D --> E[Input + Result 1 + Result 2]
    
    style A fill:#fff,stroke:#333
    style C fill:#f9f,stroke:#333
    style E fill:#9f9,stroke:#333
```

**Complete Dataset Approach**: Each transformation adds to the dataset rather than replacing it, enabling:
- Transformation chaining
- Debugging visibility
- Complex dependencies
- Rollback capabilities

### Architectural Patterns

#### 1. Pipeline Pattern
```go
type Pipeline struct {
    stages []TransformationStage
}

type TransformationStage interface {
    Process(data interface{}) (interface{}, error)
}
```

#### 2. Strategy Pattern
Each transformation type implements a common interface:
```go
type Transformation interface {
    Apply(input interface{}, config map[string]interface{}) (interface{}, error)
    Validate(config map[string]interface{}) error
}
```

#### 3. Factory Pattern
Creates appropriate formatters based on output requirements:
```go
type FormatterFactory interface {
    CreateFormatter(format OutputFormat) (Formatter, error)
}
```

## System Design

### High-Level Architecture
```mermaid
graph TB
    subgraph "External Systems"
        A1[Data Extraction Service]
        A2[Storage Service]
        A3[Reporting Systems]
    end
    
    subgraph "Transformation System"
        B[API Gateway]
        C[Transformation Controller]
        D[Mapping Engine]
        E[Template Engine]
        F[Output Generator]
    end
    
    subgraph "Configuration Management"
        G[Config Store]
        H[Template Store]
        I[Validation Service]
    end
    
    A1 --> B
    B --> C
    C --> D
    C --> E
    D --> F
    E --> F
    F --> A2
    F --> A3
    
    G --> D
    H --> E
    I --> G
    I --> H
```

### Component Architecture
```mermaid
classDiagram
    class MappingEngine {
        -configLoader ConfigLoader
        -pathResolver PathResolver
        -transformEngine TransformationEngine
        +TransformData(input, config) map
        +TransformDataInPlace(input, config) interface
    }
    
    class PathResolver {
        -pathCache sync.Map
        +GetValue(data, path) interface
        +SetValue(data, path, value) error
        -parsePath(path) []PathSegment
    }
    
    class TransformationEngine {
        -transformations map[string]Transformation
        +ApplyTransformation(data, type, config) interface
        +RegisterTransformation(name, transform) error
    }
    
    class ConfigLoader {
        +LoadFromFile(path) MappingConfig
        +ValidateConfig(config) error
        -validateFieldMapping(mapping) error
    }
    
    class TemplateFormatter {
        -template *template.Template
        -functions template.FuncMap
        +Format(data) []byte
        +ContentType() string
    }
    
    MappingEngine --> PathResolver
    MappingEngine --> TransformationEngine
    MappingEngine --> ConfigLoader
    TransformationEngine --> PathResolver
```

## File Structure & Component Guide

```
transformation/
├── README.md                           # This comprehensive developer guide
│
├── core/                              # Core transformation engine
│   ├── README.md                      # User guide for configurations
│   ├── config_loader.go               # Configuration parsing and validation
│   │   ├── ConfigLoader struct        # Main configuration loader
│   │   ├── LoadFromFile()            # Load JSON config from file
│   │   ├── LoadFromString()          # Parse JSON string config
│   │   └── ValidateConfig()          # Comprehensive validation
│   │
│   ├── config_types.go               # All configuration structures
│   │   ├── MappingConfig             # Root configuration
│   │   ├── FieldMapping              # Individual transformation rule
│   │   ├── ValidationRules           # Output validation rules
│   │   └── Constants                 # Transformation type constants
│   │
│   ├── mapping_engine.go             # Main orchestration engine
│   │   ├── MappingEngine struct      # Central coordinator
│   │   ├── TransformData()           # Copy mode transformation
│   │   ├── TransformDataInPlace()    # Memory-efficient mode
│   │   └── processFieldMappings()    # Core processing loop
│   │
│   ├── path_resolver.go              # JSONPath-like navigation
│   │   ├── PathResolver struct       # Path navigation engine
│   │   ├── GetValue()               # Extract data from paths
│   │   ├── SetValue()               # Set data at paths
│   │   ├── parsePath()              # Parse path expressions
│   │   └── navigateWildcard()       # Handle array wildcards
│   │
│   ├── transformations.go            # Transformation implementations
│   │   ├── TransformationEngine      # Transformation executor
│   │   ├── ApplyTransformation()     # Main dispatch method
│   │   ├── ApplyLookup()            # Value mapping
│   │   ├── ApplyFilter()            # Array filtering
│   │   ├── ApplyFormat()            # String formatting
│   │   ├── ApplyDateFormat()        # Date conversion
│   │   ├── ApplyAddSequence()       # Sequential numbering
│   │   ├── ApplyGroupBy()           # Group by field
│   │   └── ApplySort()              # Sort arrays
│   │
│   ├── sample/                       # Example data
│   │   ├── ExtractReportData.json   # Sample input structure
│   │   └── TestReportMapping.json   # Sample output structure
│   │
│   └── tests/                        # Comprehensive test suite
│       ├── complete_dataset_test.go  # End-to-end tests
│       ├── config_loader_test.go     # Configuration tests
│       ├── edge_cases_test.go        # Boundary conditions
│       ├── integration_test.go       # Component integration
│       ├── jsonpath_filter_test.go   # Path filter tests
│       ├── mapping_engine_test.go    # Engine tests
│       ├── path_resolver_test.go     # Path navigation tests
│       └── transformations_test.go   # Transform tests
│
└── formatters/                       # Output formatting system
    ├── README.md                     # User guide for templates
    ├── formatter.go                  # Formatter interface
    │   └── Formatter interface       # Format(), ContentType()
    │
    ├── formatter_factory.go          # Creates formatters
    │   ├── FormatterFactory          # Factory pattern impl
    │   └── CreateFormatter()         # Format-specific creation
    │
    ├── template_formatter.go         # Go template engine
    │   ├── TemplateFormatter         # Template executor
    │   ├── Format()                  # Execute template
    │   └── templateFunctions()       # Helper functions
    │
    └── tests/                        # Formatter tests
        ├── formatter_factory_test.go
        ├── template_formatter_test.go
        └── integration_test.go
```

### Component Responsibilities Matrix

| Component | Purpose | Inputs | Outputs | Dependencies |
|-----------|---------|--------|---------|--------------|
| ConfigLoader | Parse & validate configs | JSON files/strings | MappingConfig | None |
| MappingEngine | Orchestrate transformations | Data + Config | Transformed data | All others |
| PathResolver | Navigate data structures | Data + Path | Values | None |
| TransformationEngine | Execute transformations | Data + Type + Config | Transformed values | PathResolver |
| TemplateFormatter | Generate formatted output | Data + Template | Formatted bytes | None |

## Core Concepts

### 1. Path Resolution System

The path resolver implements a JSONPath-inspired syntax for navigating complex data structures:

```
Syntax Elements:
- Field access:     parent.child
- Array index:      items[0]
- Array wildcard:   items[*]
- Filters:          items[?(@.age > 18)]
- Combinations:     data.users[*].profile.address[0].city
```

**Path Parsing State Machine**:
```mermaid
stateDiagram-v2
    [*] --> Field
    Field --> Dot: .
    Field --> Bracket: [
    Dot --> Field
    Bracket --> Index: digit
    Bracket --> Wildcard: *
    Bracket --> Filter: ?
    Index --> CloseBracket: ]
    Wildcard --> CloseBracket: ]
    Filter --> CloseBracket: ]
    CloseBracket --> Field: .
    CloseBracket --> Bracket: [
    CloseBracket --> [*]
```

### 2. Transformation Pipeline

```mermaid
graph TD
    A[Input Data] --> B{For Each Mapping}
    B --> C[Extract Value<br/>Path Resolution]
    C --> D[Apply Transformation<br/>Based on Type]
    D --> E[Set Output Value<br/>At Target Path]
    E --> B
    B --> F[Complete Dataset]
    
    G[Configuration] --> B
    H[Lookup Tables] --> D
```

### 3. Memory Models

#### Copy Mode (Default)
```go
// Creates new structures, preserves original
Input:  {"name": "John", "age": 25}
Config: Transform age to string
Output: {
  "name": "John",      // Original preserved
  "age": 25,           // Original preserved  
  "ageStr": "25"       // New field added
}
```

#### In-Place Mode
```go
// Modifies existing structures
Input:  {"name": "John", "age": 25}
Config: Transform age to string at same path
Output: {
  "name": "John",
  "age": "25"          // Original modified
}
```

## Data Flow Architecture

### Request Lifecycle
```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Engine
    participant PathResolver
    participant Transform
    participant Formatter
    participant Storage
    
    Client->>API: Transform Request
    API->>Engine: Load Config & Data
    
    loop For Each Field Mapping
        Engine->>PathResolver: Extract Value
        PathResolver-->>Engine: Value
        Engine->>Transform: Apply Transformation
        Transform-->>Engine: Result
        Engine->>PathResolver: Set Output Value
    end
    
    Engine->>Formatter: Format Output
    Formatter-->>Engine: Formatted Data
    Engine->>Storage: Save Result
    Engine-->>Client: Response
```

### Error Propagation
```mermaid
graph TD
    A[Transformation Error] --> B{Error Type}
    B -->|Path Error| C[Path Resolution Failed]
    B -->|Transform Error| D[Transformation Failed]
    B -->|Config Error| E[Invalid Configuration]
    
    C --> F[Wrap with Context]
    D --> F
    E --> F
    
    F --> G[Return to Caller]
    G --> H[Log with Details]
```

## Development Guide

### Adding New Transformation Types

#### Step 1: Define the Transformation
```go
// transformations.go
type MyNewTransform struct{}

func (t *MyNewTransform) Apply(
    input interface{},
    config map[string]interface{},
) (interface{}, error) {
    // Validate input type
    data, ok := input.([]interface{})
    if !ok {
        return nil, fmt.Errorf("expected array input, got %T", input)
    }
    
    // Extract configuration
    param := config["parameter"].(string)
    
    // Apply transformation logic
    result := processData(data, param)
    
    return result, nil
}

func (t *MyNewTransform) Validate(config map[string]interface{}) error {
    if _, ok := config["parameter"]; !ok {
        return fmt.Errorf("parameter is required")
    }
    return nil
}
```

#### Step 2: Register the Transformation
```go
// transformations.go - in init() or ApplyTransformation()
func (te *TransformationEngine) init() {
    te.transformations = map[string]Transformation{
        "my_new_transform": &MyNewTransform{},
        // ... other transformations
    }
}
```

#### Step 3: Add Configuration Support
```go
// config_loader.go
func (cl *ConfigLoader) validateTransformConfig(
    mapping FieldMapping,
) error {
    switch mapping.Transformation {
    case "my_new_transform":
        return validateMyNewTransformConfig(mapping.Config)
    // ... other cases
    }
}
```

#### Step 4: Write Tests
```go
// transformations_test.go
func TestMyNewTransform(t *testing.T) {
    transform := &MyNewTransform{}
    
    tests := []struct {
        name     string
        input    interface{}
        config   map[string]interface{}
        expected interface{}
        wantErr  bool
    }{
        {
            name:  "successful transformation",
            input: []interface{}{"a", "b", "c"},
            config: map[string]interface{}{
                "parameter": "test",
            },
            expected: []interface{}{"a-test", "b-test", "c-test"},
        },
        {
            name:    "missing parameter",
            input:   []interface{}{"a"},
            config:  map[string]interface{}{},
            wantErr: true,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            result, err := transform.Apply(tt.input, tt.config)
            if tt.wantErr {
                assert.Error(t, err)
                return
            }
            assert.NoError(t, err)
            assert.Equal(t, tt.expected, result)
        })
    }
}
```

### Working with Path Resolution

#### Understanding Path Segments
```go
// Path: "reports[0].entities[*].demographics.age"
// Parsed into:
type PathSegment struct {
    Type      SegmentType  // Field, Index, Wildcard, Filter
    FieldName string       // "reports", "entities", "demographics", "age"
    Index     int          // 0 for array index
    Filter    *FilterExpr  // For JSONPath filters
}

// Results in segments:
// 1. {Type: Field, FieldName: "reports"}
// 2. {Type: Index, Index: 0}
// 3. {Type: Field, FieldName: "entities"}
// 4. {Type: Wildcard}
// 5. {Type: Field, FieldName: "demographics"}
// 6. {Type: Field, FieldName: "age"}
```

#### Implementing Path Navigation
```go
func (pr *PathResolver) navigateSegment(
    data interface{},
    segment PathSegment,
) (interface{}, error) {
    switch segment.Type {
    case SegmentTypeField:
        return pr.navigateField(data, segment.FieldName)
    case SegmentTypeIndex:
        return pr.navigateIndex(data, segment.Index)
    case SegmentTypeWildcard:
        return pr.navigateWildcard(data)
    case SegmentTypeFilter:
        return pr.navigateFilter(data, segment.Filter)
    default:
        return nil, fmt.Errorf("unknown segment type: %v", segment.Type)
    }
}
```

### Thread Safety Considerations

The transformation engine is designed for concurrent use:

```go
// Thread-safe components
type ThreadSafeEngine struct {
    configCache sync.Map      // Cache parsed configs
    pathCache   sync.Map      // Cache parsed paths
    mu          sync.RWMutex  // Protect shared state
}

// Concurrent transformation
func (e *ThreadSafeEngine) TransformConcurrent(
    datasets []interface{},
    config *MappingConfig,
) ([]interface{}, error) {
    results := make([]interface{}, len(datasets))
    errors := make([]error, len(datasets))
    
    var wg sync.WaitGroup
    for i, data := range datasets {
        wg.Add(1)
        go func(idx int, d interface{}) {
            defer wg.Done()
            result, err := e.TransformData(d, config)
            results[idx] = result
            errors[idx] = err
        }(i, data)
    }
    
    wg.Wait()
    
    // Check for errors
    for _, err := range errors {
        if err != nil {
            return nil, err
        }
    }
    
    return results, nil
}
```

## Performance Architecture

### Optimization Strategies

#### 1. Path Caching
```go
type CachedPathResolver struct {
    PathResolver
    cache sync.Map // path -> []PathSegment
}

func (r *CachedPathResolver) parsePath(path string) []PathSegment {
    if cached, ok := r.cache.Load(path); ok {
        return cached.([]PathSegment)
    }
    
    segments := r.PathResolver.parsePath(path)
    r.cache.Store(path, segments)
    return segments
}
```

#### 2. Batch Processing
```go
// Process multiple items in single operation
func (te *TransformationEngine) ApplyBatchTransformation(
    items []interface{},
    transformation string,
    config map[string]interface{},
) ([]interface{}, error) {
    // Optimize based on transformation type
    switch transformation {
    case "lookup":
        // Build lookup map once
        lookupMap := buildLookupMap(config)
        return te.batchLookup(items, lookupMap)
    default:
        // Fall back to individual processing
        return te.processIndividually(items, transformation, config)
    }
}
```

#### 3. Memory Pool
```go
// Reuse allocations for large datasets
type BufferPool struct {
    pool sync.Pool
}

func NewBufferPool() *BufferPool {
    return &BufferPool{
        pool: sync.Pool{
            New: func() interface{} {
                return bytes.NewBuffer(make([]byte, 0, 4096))
            },
        },
    }
}

func (p *BufferPool) Get() *bytes.Buffer {
    return p.pool.Get().(*bytes.Buffer)
}

func (p *BufferPool) Put(buf *bytes.Buffer) {
    buf.Reset()
    p.pool.Put(buf)
}
```

### Performance Metrics

```go
type PerformanceMetrics struct {
    TransformationDuration time.Duration
    MemoryUsed            uint64
    ItemsProcessed        int
    ErrorCount            int
}

type MetricsCollector struct {
    metrics sync.Map // mapping_name -> *PerformanceMetrics
}

func (mc *MetricsCollector) Record(
    mappingName string,
    duration time.Duration,
    memUsed uint64,
    items int,
) {
    metric := &PerformanceMetrics{
        TransformationDuration: duration,
        MemoryUsed:            memUsed,
        ItemsProcessed:        items,
    }
    mc.metrics.Store(mappingName, metric)
}
```

## Testing Philosophy

### Test Pyramid
```mermaid
graph TD
    A[Unit Tests - 70%] --> B[Integration Tests - 20%]
    B --> C[E2E Tests - 10%]
    
    D[Fast] -.-> A
    E[Focused] -.-> A
    F[Isolated] -.-> A
    
    G[Component Integration] -.-> B
    H[Error Scenarios] -.-> B
    
    I[Full Pipeline] -.-> C
    J[Performance] -.-> C
```

### Testing Patterns

#### 1. Table-Driven Tests
```go
func TestTransformation(t *testing.T) {
    tests := []struct {
        name     string
        input    interface{}
        config   map[string]interface{}
        expected interface{}
        wantErr  bool
        errMsg   string
    }{
        {
            name:     "valid transformation",
            input:    testInput,
            config:   validConfig,
            expected: expectedOutput,
            wantErr:  false,
        },
        {
            name:    "invalid input type",
            input:   "not-an-array",
            config:  validConfig,
            wantErr: true,
            errMsg:  "expected array input",
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            result, err := ApplyTransformation(tt.input, tt.config)
            
            if tt.wantErr {
                require.Error(t, err)
                assert.Contains(t, err.Error(), tt.errMsg)
                return
            }
            
            require.NoError(t, err)
            assert.Equal(t, tt.expected, result)
        })
    }
}
```

#### 2. Property-Based Testing
```go
func TestPathResolverProperties(t *testing.T) {
    // Property: GetValue followed by SetValue preserves data
    quick.Check(func(path string, value interface{}) bool {
        data := make(map[string]interface{})
        resolver := NewPathResolver()
        
        err := resolver.SetValue(data, path, value)
        if err != nil {
            return true // Skip invalid paths
        }
        
        retrieved, err := resolver.GetValue(data, path)
        if err != nil {
            return false
        }
        
        return reflect.DeepEqual(value, retrieved)
    }, nil)
}
```

#### 3. Benchmark Tests
```go
func BenchmarkTransformationPipeline(b *testing.B) {
    config := loadBenchmarkConfig()
    data := generateLargeDataset(10000)
    engine := NewMappingEngine()
    
    b.ResetTimer()
    b.ReportAllocs()
    
    for i := 0; i < b.N; i++ {
        _, err := engine.TransformData(data, config)
        if err != nil {
            b.Fatal(err)
        }
    }
    
    b.ReportMetric(float64(b.N*10000), "items/op")
}
```

## Debugging & Troubleshooting

### Debug Tools

#### 1. Transformation Tracer
```go
type TransformationTracer struct {
    steps []TraceStep
    mu    sync.Mutex
}

type TraceStep struct {
    Timestamp   time.Time
    MappingName string
    InputPath   string
    OutputPath  string
    InputValue  interface{}
    OutputValue interface{}
    Duration    time.Duration
    Error       error
}

func (t *TransformationTracer) Trace(step TraceStep) {
    t.mu.Lock()
    defer t.mu.Unlock()
    t.steps = append(t.steps, step)
}

func (t *TransformationTracer) GenerateReport() string {
    // Generate detailed trace report
}
```

#### 2. Configuration Validator
```go
type ConfigValidator struct {
    warnings []ValidationWarning
    errors   []ValidationError
}

type ValidationLevel int

const (
    LevelError ValidationLevel = iota
    LevelWarning
    LevelInfo
)

func (v *ConfigValidator) ValidateComprehensive(
    config *MappingConfig,
) ValidationReport {
    v.checkRequiredFields(config)
    v.checkLookupTableReferences(config)
    v.checkPathSyntax(config)
    v.checkTransformationChains(config)
    v.checkPerformanceRisks(config)
    
    return ValidationReport{
        Errors:   v.errors,
        Warnings: v.warnings,
        Valid:    len(v.errors) == 0,
    }
}
```

#### 3. Interactive Debugger
```go
type InteractiveDebugger struct {
    engine *MappingEngine
    data   interface{}
    config *MappingConfig
}

func (d *InteractiveDebugger) Start() {
    reader := bufio.NewReader(os.Stdin)
    
    for {
        fmt.Print("debug> ")
        cmd, _ := reader.ReadString('\n')
        cmd = strings.TrimSpace(cmd)
        
        parts := strings.Fields(cmd)
        if len(parts) == 0 {
            continue
        }
        
        switch parts[0] {
        case "path":
            d.testPath(parts[1])
        case "transform":
            d.testTransformation(parts[1], parts[2])
        case "config":
            d.showConfig()
        case "data":
            d.showData()
        case "step":
            d.stepThrough()
        case "help":
            d.showHelp()
        case "exit":
            return
        }
    }
}
```

### Common Issues & Solutions

#### 1. Path Resolution Failures
```go
// Debug helper
func debugPath(data interface{}, path string) {
    fmt.Printf("Debugging path: %s\n", path)
    fmt.Printf("Data type: %T\n", data)
    
    resolver := NewPathResolver()
    segments := resolver.parsePath(path)
    
    current := data
    for i, seg := range segments {
        fmt.Printf("\nStep %d: %+v\n", i, seg)
        fmt.Printf("Current: %+v\n", current)
        
        next, err := resolver.navigateSegment(current, seg)
        if err != nil {
            fmt.Printf("ERROR: %v\n", err)
            fmt.Printf("Suggestion: %s\n", suggestFix(err, current, seg))
            break
        }
        current = next
    }
}
```

#### 2. Memory Profiling
```go
func profileMemoryUsage(engine *MappingEngine, data interface{}, config *MappingConfig) {
    var m runtime.MemStats
    
    runtime.GC()
    runtime.ReadMemStats(&m)
    before := m.Alloc
    
    result, err := engine.TransformData(data, config)
    
    runtime.GC()
    runtime.ReadMemStats(&m)
    after := m.Alloc
    
    fmt.Printf("Memory used: %d KB\n", (after-before)/1024)
    fmt.Printf("Result size: %d bytes\n", estimateSize(result))
    fmt.Printf("Efficiency: %.2f\n", float64(estimateSize(result))/float64(after-before))
}
```

## Security Considerations

### Input Validation
```go
type SecurityValidator struct {
    maxPathDepth   int
    maxArraySize   int
    maxStringLength int
}

func (v *SecurityValidator) ValidateInput(data interface{}) error {
    return v.validateRecursive(data, 0)
}

func (v *SecurityValidator) validateRecursive(data interface{}, depth int) error {
    if depth > v.maxPathDepth {
        return fmt.Errorf("max depth exceeded")
    }
    
    switch val := data.(type) {
    case string:
        if len(val) > v.maxStringLength {
            return fmt.Errorf("string too long")
        }
    case []interface{}:
        if len(val) > v.maxArraySize {
            return fmt.Errorf("array too large")
        }
        for _, item := range val {
            if err := v.validateRecursive(item, depth+1); err != nil {
                return err
            }
        }
    case map[string]interface{}:
        for _, value := range val {
            if err := v.validateRecursive(value, depth+1); err != nil {
                return err
            }
        }
    }
    
    return nil
}
```

### Configuration Security
- Validate all paths to prevent traversal attacks
- Sanitize lookup table values
- Limit template complexity
- Validate output size limits
