// Package core provides the foundational transformation infrastructure for converting
// extracted report data into various output formats (NIBRS XML, UCR, custom formats).
//
// This package implements a completely configuration-driven transformation system where
// ALL business logic resides in JSON configuration files, not in Go code. This design
// ensures that new output formats can be added, modified, or debugged without any
// code changes, deployments, or developer intervention.
//
// Core Architecture Principles:
//   - Zero Business Logic: Go code only provides generic transformation primitives
//   - Configuration-Driven: All mapping rules, lookup tables, and field transformations in JSON
//   - Composable Design: Small, focused components that work together
//   - Type Safety: Strong typing for configuration structures with comprehensive validation
//   - Extensive Documentation: Every component documents its purpose and usage patterns
//
// Transformation Pipeline:
//  1. Configuration Loading: JSON configs loaded and validated for correctness
//  2. Path Resolution: Generic navigation through complex nested data structures
//  3. Value Transformation: Apply one of 8 transformation types (lookup, filter, format, etc.)
//  4. Output Generation: Place transformed values in structured output using path expressions
//
// The system supports these transformation types:
//   - direct: Pass values through unchanged (field copying)
//   - lookup: Map values using lookup tables (enum conversion, code mapping)
//   - filter: Extract subsets of arrays based on criteria (victims only, active cases)
//   - format: Apply printf-style formatting (padding, case conversion)
//   - date_format: Convert date strings between formats (ISO to MM/DD/YYYY)
//   - add_sequence: Add sequence numbers to array items (victim numbering)
//   - group_by: Group array items by field values (group by type, status)
//   - sort: Sort arrays by field values (alphabetical, numerical, custom)
//
// Example Configuration:
//
//	{
//	  "format_name": "NIBRS_XML_2023",
//	  "field_mappings": [
//	    {
//	      "name": "victim_sex_code",
//	      "input_path": "entities[0].entity.demographics.sex",
//	      "transformation": "lookup",
//	      "output_path": "victim.sex_code",
//	      "config": {"lookup_table": "sex_codes"}
//	    }
//	  ],
//	  "lookup_tables": {
//	    "sex_codes": {"SEX_MALE": "M", "SEX_FEMALE": "F"}
//	  }
//	}
//
// This transforms extraction data like:
//
//	{"entities": [{"entity": {"demographics": {"sex": "SEX_MALE"}}}]}
//
// Into output data like:
//
//	{"victim": {"sex_code": "M"}}
package core

// MappingConfig represents the complete configuration for a data transformation format.
//
// This is the root configuration structure that defines how extracted report data
// should be transformed into a specific output format (e.g., NIBRS XML, UCR reporting).
// It contains NO business logic - it's purely a declarative container that describes
// the desired transformations in a data-driven way.
//
// Configuration Philosophy:
// The MappingConfig embodies the principle that transformation behavior should be
// completely configurable without code changes. Law enforcement agencies, compliance
// teams, or analysts can modify these JSON files to:
//   - Add new field mappings for additional data elements
//   - Update lookup tables when codes change (new offense types, jurisdiction codes)
//   - Modify output formats for different reporting requirements
//   - Debug transformation issues by examining configuration
//
// Structure Organization:
//   - FormatName: Unique identifier for this specific transformation configuration
//   - FieldMappings: Ordered list of individual field transformation rules
//   - LookupTables: Reusable value mapping tables referenced by lookup transformations
//
// Usage Pattern:
//
//	config := &MappingConfig{
//	    FormatName: "NIBRS_XML_2023",
//	    FieldMappings: []FieldMapping{...},
//	    LookupTables: map[string]map[string]string{...},
//	}
//	result, err := mappingEngine.TransformData(extractedData, config)
type MappingConfig struct {
	// FormatName is the unique identifier for this transformation format.
	//
	// This serves as both a human-readable label and a system identifier for the
	// configuration. Common examples include:
	//   - "NIBRS_XML_2023": National Incident-Based Reporting System XML format
	//   - "UCR_SUMMARY_2024": Uniform Crime Reporting summary format
	//   - "CUSTOM_AGENCY_FORMAT": Agency-specific custom format
	//
	// The format name is used for:
	//   - Configuration file organization and selection
	//   - Logging and debugging output identification
	//   - Validation and version management
	//   - System audit trails
	FormatName string `json:"format_name"`

	// FieldMappings defines the complete list of field transformations to apply.
	//
	// This is an ordered array of transformation rules that are executed sequentially.
	// Each mapping describes how to extract a value from the input data, transform it
	// using one of the available transformation types, and place the result in the
	// output structure.
	//
	// Order Significance:
	// The order of field mappings can be important when:
	//   - Later mappings depend on intermediate results from earlier mappings
	//   - Filter transformations create intermediate arrays used by subsequent mappings
	//   - Sequence numbering depends on the order of array processing
	//
	// Common Patterns:
	//   1. Extract and filter data first (entities -> victims)
	//   2. Add sequence numbers to arrays (victim numbering)
	//   3. Transform individual fields (demographics, codes)
	//   4. Format final output values (padding, case conversion)
	FieldMappings []FieldMapping `json:"field_mappings"`

	// LookupTables contains all value mapping tables used by lookup transformations.
	//
	// These tables enable mapping from internal system values to external format
	// requirements. They are essential for compliance with reporting standards that
	// require specific codes, abbreviations, or formats.
	//
	// Common Lookup Table Categories:
	//   - Demographics: Sex codes (M/F/U), race codes (W/B/A/H), ethnicity codes
	//   - Geographic: State codes, county codes, jurisdiction identifiers
	//   - Legal: Offense codes (NIBRS/UCR), charge types, disposition codes
	//   - Administrative: Agency codes (ORI), report types, status codes
	//
	// Table Structure:
	// Each table maps string keys to string values:
	//   "sex_codes": {"SEX_MALE": "M", "SEX_FEMALE": "F", "SEX_UNKNOWN": "U"}
	//   "state_codes": {"CALIFORNIA": "CA", "NEW_YORK": "NY", "TEXAS": "TX"}
	//
	// Maintenance Considerations:
	// Lookup tables should be maintained by domain experts who understand the
	// mapping requirements for each output format. Changes to these tables
	// immediately affect all transformations that reference them.
	LookupTables map[string]map[string]string `json:"lookup_tables"`

	// TransformationMode specifies whether to use in-place or copy-based transformation.
	//
	// This configuration option determines the transformation strategy for this mapping:
	//   - "copy": Create new output structures without modifying input data (default)
	//   - "in_place": Modify the input data structure directly for memory efficiency
	//
	// Mode Selection Guidelines:
	//   - Use "copy" when original data needs to be preserved for other purposes
	//   - Use "copy" for debugging scenarios where original data comparison is needed
	//   - Use "in_place" for large datasets where memory efficiency is critical
	//   - Use "in_place" when input data will not be used after transformation
	//   - Use "in_place" for high-throughput scenarios where performance is critical
	//
	// Safety Considerations:
	//   - "copy" mode is safer as it preserves original data integrity
	//   - "in_place" mode requires careful consideration of data ownership
	//   - "in_place" mode may not be suitable for concurrent processing scenarios
	//   - Choose based on memory constraints and data preservation requirements
	//
	// Business Context:
	// The transformation mode choice impacts:
	//   - Memory usage patterns in ETL pipelines
	//   - Data debugging and validation capabilities
	//   - Performance characteristics of bulk transformations
	//   - System resource utilization in high-volume scenarios
	//
	// Default Behavior:
	// If not specified or empty, defaults to "copy" for backward compatibility
	// and data safety. Explicit configuration is recommended for production use.
	//
	// Examples:
	//   - Memory-constrained environments: "in_place"
	//   - Development and testing: "copy"
	//   - High-volume production ETL: "in_place"
	//   - Data validation scenarios: "copy"
	TransformationMode string `json:"transformation_mode,omitempty"`
}

// FieldMapping represents a single field transformation rule within a format configuration.
//
// Each FieldMapping defines one step in the transformation pipeline: extract a value
// from the input data, apply a transformation to it, and place the result in the output.
// This is the fundamental building block of the configuration-driven transformation system.
//
// Business Context:
// Field mappings encode domain knowledge about how extracted law enforcement data
// should be formatted for specific reporting standards. For example, mapping internal
// sex enum values (SEX_MALE, SEX_FEMALE) to NIBRS-compliant codes (M, F) ensures
// that reports meet federal compliance requirements.
//
// Transformation Flow:
//  1. Input Extraction: Use InputPath to navigate to source data location
//  2. Value Transformation: Apply the specified transformation type with configuration
//  3. Output Placement: Store result at OutputPath in the growing output structure
//
// Path Expression Examples:
//   - Simple field: "report.id" -> accesses report.id field
//   - Array index: "entities[0].name" -> accesses first entity's name
//   - Array wildcard: "entities[*].type" -> accesses type field from all entities
//   - Nested paths: "sections[0].data.location.address" -> deep navigation
//
// Common Transformation Patterns:
//   - Code Mapping: Internal enums -> external codes (sex, race, offense types)
//   - Data Filtering: Large arrays -> specific subsets (all entities -> victims only)
//   - Format Conversion: Values -> formatted strings (123 -> "INC-00123")
//   - Date Transformation: ISO timestamps -> formatted dates (RFC3339 -> MM/DD/YYYY)
//   - Sequence Generation: Arrays -> numbered items (victims[0], victims[1])
//
// Error Handling Philosophy:
// Field mappings are designed to fail gracefully. If an input path doesn't exist
// or a transformation fails, the mapping is skipped rather than failing the entire
// transformation. This ensures that partial data doesn't prevent report generation.
//
// Example Usage:
//
//	mapping := FieldMapping{
//	    Name: "victim_sex_mapping",
//	    InputPath: "entities[0].entity.demographics.sex",
//	    Transformation: "lookup",
//	    OutputPath: "victim.sex_code",
//	    Config: map[string]interface{}{
//	        "lookup_table": "nibrs_sex_codes",
//	        "default_value": "U",
//	    },
//	    Description: "Maps internal sex enum to NIBRS-compliant sex codes",
//	}
type FieldMapping struct {
	// Name is a human-readable identifier for this specific mapping rule.
	//
	// This field serves multiple important purposes:
	//   - Debugging: Identify which mapping failed during transformation
	//   - Testing: Reference specific mappings in unit tests
	//   - Documentation: Explain the purpose of complex transformations
	//   - Maintenance: Track changes and updates to mapping configurations
	//
	// Naming Conventions:
	//   - Use descriptive names that explain the business purpose
	//   - Include source and target context: "victim_demographics_sex_to_nibrs_code"
	//   - Avoid technical jargon, focus on business meaning
	//   - Use consistent patterns across related mappings
	//
	// Examples:
	//   - "incident_number_extraction": Extract main incident identifier
	//   - "victim_sex_code_mapping": Map sex enum to reporting code
	//   - "offense_classification_lookup": Classify offense using lookup table
	Name string `json:"name"`

	// InputPath defines the path expression for extracting the source value.
	//
	// This uses a JSON-like path syntax to navigate through the complex nested
	// structure of extracted report data. The path resolver supports sophisticated
	// navigation including array operations and wildcard expansion.
	//
	// Path Syntax Reference:
	//   - Field access: "report.title" (access title field of report object)
	//   - Array indexing: "entities[0]" (access first element of entities array)
	//   - Array wildcards: "entities[*]" (access all elements of entities array)
	//   - Nested navigation: "entities[0].entity.demographics.sex" (deep object access)
	//   - Complex paths: "sections[*].arrests[0].data.charges" (mixed array/object access)
	//
	// Data Source Context:
	// Input paths reference the standardized extraction output structure that contains:
	//   - reports[]: Array of report objects with metadata
	//   - entities[]: Array of people, vehicles, organizations involved
	//   - assets[]: Array of resources, documents, evidence
	//   - sections[]: Array of report sections with typed content
	//   - relationships[]: Array of connections between entities
	//   - situations[]: Array of incident contexts and circumstances
	//
	// Path Design Considerations:
	//   - Paths should be resilient to data structure changes
	//   - Use wildcards when processing arrays of similar items
	//   - Consider optional fields and provide fallback paths when needed
	//   - Document the expected data type at each path location
	InputPath string `json:"input_path"`

	// Transformation specifies which transformation function to apply to the extracted value.
	//
	// The transformation system provides 8 core transformation types that cover
	// the vast majority of data formatting requirements for law enforcement reporting.
	// Each transformation type is designed to be generic and configuration-driven.
	//
	// Available Transformation Types:
	//
	// "direct": Pass the value through unchanged
	//   - Use for: Simple field copying, data passthrough
	//   - Config: None required
	//   - Example: Copy report ID directly to output
	//
	// "lookup": Map values using a lookup table
	//   - Use for: Code conversion, enum mapping, standardization
	//   - Config: lookup_table (required), default_value (optional)
	//   - Example: Map "SEX_MALE" to "M" for NIBRS compliance
	//
	// "filter": Extract subset of array based on criteria
	//   - Use for: Filtering entities by role, status, type
	//   - Config: field, operator, value (all required)
	//   - Example: Extract only entities where role = "VICTIM"
	//
	// "format": Apply printf-style string formatting
	//   - Use for: Number padding, string templates, case conversion
	//   - Config: format_pattern (required)
	//   - Example: Format incident number as "INC-%05d"
	//
	// "date_format": Convert date strings between formats
	//   - Use for: Timestamp formatting, date standardization
	//   - Config: date_format (required Go time format)
	//   - Example: Convert RFC3339 to "01/02/2006" format
	//
	// "add_sequence": Add sequence numbers to array items
	//   - Use for: Victim numbering, item indexing
	//   - Config: sequence_field, start, format
	//   - Example: Add victim sequence numbers 01, 02, 03...
	//
	// "group_by": Group array items by field value
	//   - Use for: Organizing data by categories
	//   - Config: group_field (required)
	//   - Example: Group entities by type (PERSON, VEHICLE, etc.)
	//
	// "sort": Sort array items by field value
	//   - Use for: Ordering data alphabetically or numerically
	//   - Config: sort_field (required), sort_order (optional: asc/desc)
	//   - Example: Sort victims by age ascending
	Transformation string `json:"transformation"`

	// OutputPath defines where to place the transformed value in the output structure.
	//
	// This path expression determines the location in the final output where the
	// transformed value will be stored. The path resolver automatically creates
	// nested objects and arrays as needed to accommodate the specified path.
	//
	// Output Structure Design:
	// Output paths should reflect the target format's expected structure:
	//   - NIBRS XML: "incident.number", "victims[0].sex", "offenses[*].code"
	//   - UCR Summary: "summary.total_offenses", "clearances.adult"
	//   - Custom formats: Agency-specific field names and structures
	//
	// Path Creation Behavior:
	//   - Missing intermediate objects are created automatically
	//   - Arrays are created when using array notation in paths
	//   - Existing values at the same path are overwritten
	//   - Conflicting path types (object vs array) result in errors
	//
	// Naming Considerations:
	//   - Use target format's preferred field names
	//   - Maintain consistency with external system expectations
	//   - Consider XML/JSON schema requirements
	//   - Document any special formatting requirements
	//
	// Examples:
	//   - "incident.ori_number": Store in incident object, ori_number field
	//   - "victims[0].demographics.sex": First victim's sex in demographics object
	//   - "property.stolen_items[*].value": Value field for all stolen items
	OutputPath string `json:"output_path"`

	// Config contains transformation-specific configuration parameters.
	//
	// The configuration map provides the parameters that customize the behavior
	// of each transformation type. The required and optional parameters vary
	// based on the transformation type specified.
	//
	// Configuration by Transformation Type:
	//
	// "lookup" transformation config:
	//   - lookup_table (string, required): Name of lookup table in LookupTables
	//   - default_value (string, optional): Value to use when no lookup match found
	//   - Example: {"lookup_table": "sex_codes", "default_value": "U"}
	//
	// "filter" transformation config:
	//   - field (string, required): Field path within each array item to evaluate
	//   - operator (string, required): Comparison operator (equals, contains, in, etc.)
	//   - value (any, required): Value to compare against
	//   - Example: {"field": "role", "operator": "equals", "value": "VICTIM"}
	//
	// "format" transformation config:
	//   - format_pattern (string, required): Printf-style format string
	//   - Example: {"format_pattern": "INC-%05d"} to format 123 as "INC-00123"
	//
	// "date_format" transformation config:
	//   - date_format (string, required): Go time format string for output
	//   - Example: {"date_format": "01/02/2006"} for MM/DD/YYYY format
	//
	// "add_sequence" transformation config:
	//   - sequence_field (string, required): Field name to add to each item
	//   - start (int, optional): Starting number (default: 1)
	//   - format (string, optional): Printf format for numbers (default: "%d")
	//   - Example: {"sequence_field": "victim_sequence", "start": 1, "format": "%02d"}
	//
	// "group_by" transformation config:
	//   - group_field (string, required): Field path to group by
	//   - Example: {"group_field": "entity.type"}
	//
	// "sort" transformation config:
	//   - sort_field (string, required): Field path to sort by
	//   - sort_order (string, optional): "asc" or "desc" (default: "asc")
	//   - Example: {"sort_field": "entity.demographics.age", "sort_order": "asc"}
	//
	// "direct" transformation config:
	//   - No configuration required (can be nil or empty)
	Config map[string]interface{} `json:"config,omitempty"`

	// Description provides human-readable documentation for this mapping.
	//
	// While optional, descriptions are highly recommended for complex mappings
	// to help future maintainers understand the business logic and requirements.
	// This is especially important for compliance-driven transformations where
	// the mapping rules may be mandated by external standards.
	//
	// Good descriptions should include:
	//   - Business purpose: Why this mapping exists
	//   - Data source context: What the input represents
	//   - Output requirements: How the output will be used
	//   - Special cases: Edge conditions or exceptional handling
	//   - Compliance notes: Regulatory requirements this mapping satisfies
	//
	// Examples:
	//   - "Maps internal entity sex enum values to NIBRS-compliant single-character codes as required by FBI UCR guidelines"
	//   - "Extracts victim entities from the complete entity list for separate processing in victim-specific sections"
	//   - "Formats incident numbers with zero-padding to meet agency reporting system requirements"
	Description string `json:"description,omitempty"`

	// OverwriteExisting indicates whether to overwrite existing data at the output path.
	//
	// This field provides explicit permission for transformations that would overwrite
	// existing data in the source structure. This safety mechanism prevents accidental
	// data loss when output paths conflict with existing fields.
	//
	// Behavior:
	//   - When output_path == input_path: Automatic in-place transformation (this field ignored)
	//   - When output_path != input_path AND output_path exists in data:
	//     - false (default): Transformation fails with error about existing data
	//     - true: Explicitly permits overwriting existing data
	//   - When output_path doesn't exist: Creates new field (this field ignored)
	//
	// Use Cases:
	//   - Normalizing data: Replace raw values with standardized versions
	//   - Correcting data: Fix formatting or encoding issues in place
	//   - Merging fields: Combine multiple source fields into existing target field
	//
	// Safety Considerations:
	//   - Default false prevents accidental data corruption
	//   - Explicit true makes overwriting intentional and documented
	//   - Consider data backup strategies when using overwrite operations
	//
	// Examples:
	//   - Replace inconsistent date formats with standardized format
	//   - Overwrite temporary fields with computed final values
	//   - Update status fields based on business logic
	OverwriteExisting bool `json:"overwrite_existing,omitempty"`
}

// FilterConfig represents the configuration for a filter transformation.
//
// Filter transformations are essential for processing law enforcement data where large
// arrays of entities need to be filtered into specific categories. This is particularly
// important for compliance reporting where different entity roles (victims, offenders,
// witnesses) must be processed separately according to reporting standards.
//
// Business Context:
// In law enforcement reports, entities often have multiple roles and classifications.
// For example, an incident might have 10 entities, but only 3 are victims, 2 are
// offenders, and the rest are witnesses. Filter transformations enable precise
// extraction of the relevant entities for each section of the output report.
//
// Common Filtering Scenarios:
//   - Entity Role Filtering: Extract victims, offenders, witnesses from entity list
//   - Status Filtering: Filter active vs inactive records
//   - Type Filtering: Separate people, vehicles, organizations
//   - Temporal Filtering: Records within specific date ranges
//   - Geographic Filtering: Records within specific jurisdictions
//
// Example Usage:
//
//	filterConfig := FilterConfig{
//	    Field: "extractionMetadata.role",
//	    Operator: "equals",
//	    Value: "VICTIM",
//	}
//	// This extracts only entities where role equals "VICTIM"
type FilterConfig struct {
	// Field specifies the path within each array item to evaluate for filtering.
	//
	// This field path is evaluated relative to each individual item in the input array.
	// The path resolver navigates to this field location within each array element
	// and extracts the value for comparison against the filter criteria.
	//
	// Common Field Paths:
	//   - "role": Simple field in the item root
	//   - "entity.type": Nested field access
	//   - "extractionMetadata.role": Metadata field access
	//   - "demographics.age": Demographic information access
	//   - "status": Status or state field access
	//
	// Field Selection Strategy:
	// Choose fields that contain discrete, comparable values that cleanly separate
	// the desired items from the rest. Avoid fields with complex or nested values
	// that might not compare reliably.
	//
	// Examples:
	//   - Filter entities by role: Field = "extractionMetadata.role"
	//   - Filter by entity type: Field = "entity.entity_type"
	//   - Filter by age range: Field = "entity.demographics.age"
	Field string `json:"field"`

	// Operator defines the comparison operation to apply between field value and filter value.
	//
	// The filter system supports a comprehensive set of comparison operators that
	// handle different data types and comparison scenarios commonly needed in
	// law enforcement data processing.
	//
	// Supported Operators:
	//
	// "equals": Exact value match (most common)
	//   - Use for: Role matching, status checking, type filtering
	//   - Data types: strings, numbers, booleans
	//   - Example: role equals "VICTIM"
	//
	// "not_equals": Inverse of equals
	//   - Use for: Exclusion filtering, negative conditions
	//   - Data types: strings, numbers, booleans
	//   - Example: status not_equals "INACTIVE"
	//
	// "contains": String contains substring
	//   - Use for: Partial text matching, keyword searching
	//   - Data types: strings only
	//   - Example: name contains "SMITH"
	//
	// "starts_with": String starts with prefix
	//   - Use for: Prefix matching, code classification
	//   - Data types: strings only
	//   - Example: offense_code starts_with "13" (assault codes)
	//
	// "ends_with": String ends with suffix
	//   - Use for: Suffix matching, file extensions
	//   - Data types: strings only
	//   - Example: case_number ends_with "-A" (adult cases)
	//
	// "in": Value is in provided array
	//   - Use for: Multiple value matching, category filtering
	//   - Data types: any type, compared against array of values
	//   - Example: role in ["VICTIM", "WITNESS"]
	//
	// "not_in": Value is not in provided array
	//   - Use for: Exclusion from multiple categories
	//   - Data types: any type, compared against array of values
	//   - Example: status not_in ["CLOSED", "ARCHIVED"]
	//
	// Operator Selection Guidelines:
	//   - Use "equals" for exact categorical matching (most common)
	//   - Use "in" when filtering for multiple acceptable values
	//   - Use string operators ("contains", "starts_with") for flexible text matching
	//   - Use negation operators ("not_equals", "not_in") for exclusion filtering
	Operator string `json:"operator"`

	// Value specifies the comparison value or values for the filter operation.
	//
	// The value type and structure must match the operator being used:
	//   - Simple operators (equals, not_equals, contains, etc.): single value
	//   - Array operators (in, not_in): array of values
	//
	// Value Type Considerations:
	//   - String values: Most common for categorical data (roles, types, statuses)
	//   - Numeric values: For age ranges, counts, quantities
	//   - Boolean values: For binary flags and switches
	//   - Arrays: For "in" and "not_in" operators
	//
	// Common Value Examples:
	//   - Role filtering: "VICTIM", "OFFENDER", "WITNESS"
	//   - Type filtering: "PERSON", "VEHICLE", "ORGANIZATION"
	//   - Status filtering: "ACTIVE", "INACTIVE", "PENDING"
	//   - Multi-value filtering: ["VICTIM", "WITNESS"] with "in" operator
	//   - Age filtering: 18 (for age comparisons)
	//
	// Special Considerations:
	//   - String comparisons are case-sensitive
	//   - Numeric comparisons work with int and float types
	//   - Array values for "in"/"not_in" should contain homogeneous types
	//   - Null/empty values should be handled gracefully
	Value interface{} `json:"value"`
}

// LookupConfig represents the configuration for a lookup transformation.
//
// Lookup transformations are fundamental to law enforcement data compliance, enabling
// the conversion of internal system values to standardized codes required by external
// reporting systems like NIBRS, UCR, and state reporting databases.
//
// Business Context:
// Law enforcement agencies use internal coding systems that may differ significantly
// from federal or state reporting requirements. Lookup transformations bridge this gap
// by mapping agency-specific values to compliance-mandated codes while maintaining
// data integrity and audit trails.
//
// Common Lookup Scenarios:
//   - Demographics: Internal sex enums (SEX_MALE) -> NIBRS codes (M)
//   - Geographic: Agency names -> ORI numbers for federal reporting
//   - Legal: Internal offense categories -> UCR offense codes
//   - Administrative: Internal status codes -> standardized disposition codes
//
// Example Usage:
//
//	lookupConfig := LookupConfig{
//	    LookupTable: "nibrs_sex_codes",
//	    DefaultValue: "U", // Unknown when no match found
//	}
//	// Maps "SEX_MALE" -> "M", "SEX_FEMALE" -> "F", unknown values -> "U"
type LookupConfig struct {
	// LookupTable specifies the name of the lookup table to use for value mapping.
	//
	// This table name must exist as a key in the MappingConfig.LookupTables map.
	// The transformation engine validates this reference during configuration loading
	// to ensure all required lookup tables are properly defined.
	//
	// Table Naming Conventions:
	//   - Use descriptive names that indicate purpose: "nibrs_sex_codes", "ucr_offense_types"
	//   - Include target format in name when multiple formats supported
	//   - Use consistent patterns across related tables
	//   - Avoid abbreviations that might be unclear to maintainers
	//
	// Common Lookup Table Names:
	//   - "sex_codes": Demographic sex mapping
	//   - "race_codes": Racial category mapping
	//   - "ethnicity_codes": Ethnicity classification mapping
	//   - "offense_codes": Criminal offense type mapping
	//   - "jurisdiction_codes": Geographic jurisdiction mapping
	//   - "agency_ori_codes": Agency identifier mapping
	//   - "disposition_codes": Case outcome mapping
	//
	// Table Management:
	// Lookup tables should be maintained by domain experts who understand both
	// the source data format and the target reporting requirements. Changes to
	// lookup tables can have immediate compliance implications.
	LookupTable string `json:"lookup_table"`

	// DefaultValue specifies the value to return when no match is found in the lookup table.
	//
	// This is crucial for maintaining data integrity when source data contains values
	// that aren't explicitly mapped in the lookup table. Rather than failing the
	// transformation, a default value ensures that reports can still be generated
	// with appropriate "unknown" or "other" classifications.
	//
	// Default Value Strategy:
	//   - Use standard "unknown" codes when available (e.g., "U" for unknown sex)
	//   - Follow target format conventions for missing data representation
	//   - Document the meaning of default values for compliance officers
	//   - Consider whether defaults should trigger data quality alerts
	//
	// Common Default Values:
	//   - Demographics: "U" (Unknown), "O" (Other), "N" (Not specified)
	//   - Geographic: "99" (Unknown jurisdiction), "XX" (Unknown state)
	//   - Legal: "90Z" (All other offenses), "99" (Unknown classification)
	//   - Status: "UNK" (Unknown status), "OTH" (Other)
	//
	// Compliance Considerations:
	// Some reporting standards have specific requirements for handling unknown values.
	// Ensure default values comply with target format specifications and don't
	// violate reporting standard requirements.
	//
	// Example:
	//   DefaultValue: "U" // NIBRS unknown sex code
	//   DefaultValue: "99" // Unknown jurisdiction code
	//   DefaultValue: "OTHER" // Generic other category
	DefaultValue string `json:"default_value,omitempty"`
}

// FormatConfig represents the configuration for a format transformation.
//
// Format transformations provide standardized string formatting for values that need
// specific presentation formats in compliance reports. This is essential for meeting
// exact field formatting requirements mandated by reporting standards.
//
// Business Context:
// Many compliance reporting systems require precise formatting of numeric and string
// values. For example, incident numbers might need zero-padding, case numbers need
// specific prefixes, or numeric codes need fixed-width formatting for system compatibility.
//
// Common Formatting Scenarios:
//   - Incident Numbers: 123 -> "INC-00123" (with prefix and zero-padding)
//   - Sequence Numbers: 5 -> "05" (zero-padded for ordering)
//   - Currency Values: 1234.5 -> "$1,234.50" (currency formatting)
//   - Percentage Values: 0.15 -> "15.0%" (percentage representation)
//   - Case Numbers: "ABC123" -> "CASE-ABC123" (standardized prefixes)
//
// Example Usage:
//
//	formatConfig := FormatConfig{
//	    FormatPattern: "INC-%05d", // Formats 123 as "INC-00123"
//	}
type FormatConfig struct {
	// FormatPattern specifies the printf-style format string to apply to input values.
	//
	// This uses Go's standard fmt package formatting syntax, which is based on
	// printf-style format strings. The pattern determines how the input value
	// will be formatted into the final string output.
	//
	// Common Format Patterns:
	//
	// Integer Formatting:
	//   - "%d": Basic integer (123 -> "123")
	//   - "%05d": Zero-padded 5-digit integer (123 -> "00123")
	//   - "%+d": Integer with sign (123 -> "+123", -123 -> "-123")
	//   - "INC-%06d": Prefixed zero-padded (123 -> "INC-000123")
	//
	// Float Formatting:
	//   - "%.2f": Two decimal places (3.14159 -> "3.14")
	//   - "%8.2f": Width 8, two decimals (3.14 -> "    3.14")
	//   - "$%.2f": Currency prefix (123.4 -> "$123.40")
	//
	// String Formatting:
	//   - "%s": Basic string passthrough
	//   - "%-10s": Left-aligned 10-character width
	//   - "%10s": Right-aligned 10-character width
	//   - "CASE-%s": String with prefix ("ABC123" -> "CASE-ABC123")
	//
	// Special Formatting:
	//   - "%x": Hexadecimal representation
	//   - "%X": Uppercase hexadecimal
	//   - "%e": Scientific notation
	//   - "%t": Boolean values (true/false)
	//
	// Compliance Considerations:
	// Format patterns should match the exact requirements of the target reporting
	// system. Some systems are very strict about field width, padding, and formatting.
	// Test formatting thoroughly with edge cases (very large numbers, negative values, etc.)
	//
	// Examples:
	//   - Incident numbers: "INC-%05d" (creates "INC-00123" from 123)
	//   - Victim sequence: "%02d" (creates "01", "02", "03" for numbering)
	//   - Currency amounts: "$%.2f" (creates "$1,234.56" from 1234.56)
	//   - Report codes: "RPT-%s-%d" (creates "RPT-BURGLARY-2024" from two inputs)
	FormatPattern string `json:"format_pattern"`
}

// DateFormatConfig represents the configuration for a date_format transformation.
//
// Date format transformations handle the conversion of timestamp data between different
// date/time representations required by various reporting systems. This is critical for
// compliance reporting where different systems expect dates in specific formats.
//
// Business Context:
// Law enforcement systems often store timestamps in ISO/RFC3339 format for precision
// and timezone handling, but reporting systems may require dates in specific local
// formats, abbreviated formats, or legacy system formats for compatibility.
//
// Common Date Formatting Scenarios:
//   - NIBRS Reporting: ISO timestamps -> YYYY-MM-DD format
//   - Legacy Systems: Modern timestamps -> MM/DD/YYYY format
//   - Display Formatting: Database timestamps -> "Jan 15, 2024" format
//   - Time Extraction: Full timestamps -> HH:MM format for time-only fields
//   - Timezone Conversion: UTC timestamps -> local time representation
//
// Example Usage:
//
//	dateConfig := DateFormatConfig{
//	    DateFormat: "01/02/2006", // Converts to MM/DD/YYYY format
//	}
//	// Transforms "2024-01-15T10:30:00Z" -> "01/15/2024"
type DateFormatConfig struct {
	// DateFormat specifies the Go time format string for the desired output format.
	//
	// Go uses a specific reference time for format strings: "Mon Jan 2 15:04:05 MST 2006"
	// This corresponds to Unix time 1136239445. Format patterns are based on how this
	// reference time would be displayed in the desired format.
	//
	// Common Date Format Patterns:
	//
	// Standard Date Formats:
	//   - "2006-01-02": ISO date format (YYYY-MM-DD)
	//   - "01/02/2006": US date format (MM/DD/YYYY)
	//   - "02/01/2006": European date format (DD/MM/YYYY)
	//   - "2006-01-02T15:04:05": ISO datetime without timezone
	//   - "2006-01-02T15:04:05Z07:00": ISO datetime with timezone
	//
	// Display-Friendly Formats:
	//   - "January 2, 2006": Full month name
	//   - "Jan 2, 2006": Abbreviated month name
	//   - "Monday, January 2, 2006": Full weekday and month
	//   - "Mon, Jan 2, 2006": Abbreviated weekday and month
	//
	// Time-Only Formats:
	//   - "15:04": 24-hour time (HH:MM)
	//   - "3:04 PM": 12-hour time with AM/PM
	//   - "15:04:05": 24-hour time with seconds
	//
	// Custom Agency Formats:
	//   - "060102": YYMMDD compact format
	//   - "20060102": YYYYMMDD compact format
	//   - "2006.01.02": Dot-separated format
	//
	// Input Format Handling:
	// The transformation expects input dates to be in RFC3339 format (ISO 8601)
	// which is the standard for Go time parsing: "2006-01-02T15:04:05Z07:00"
	// This ensures consistent parsing regardless of the source data format.
	//
	// Timezone Considerations:
	// Date formatting can handle timezone conversion, but the output format
	// should specify whether timezone information should be preserved or
	// converted to a standard timezone (usually local or UTC).
	//
	// Compliance Requirements:
	// Different reporting systems have strict date format requirements:
	//   - NIBRS: Requires YYYY-MM-DD format for date fields
	//   - UCR: May require MM/DD/YYYY format for legacy compatibility
	//   - State systems: Often have their own specific format requirements
	//
	// Examples:
	//   - NIBRS compliance: "2006-01-02" (converts to YYYY-MM-DD)
	//   - US display format: "01/02/2006" (converts to MM/DD/YYYY)
	//   - Report headers: "January 2, 2006" (converts to readable format)
	//   - Time extraction: "15:04" (extracts just the time portion)
	DateFormat string `json:"date_format"`
}

// SequenceConfig represents the configuration for an add_sequence transformation.
//
// Sequence transformations are essential for compliance reporting where arrays of items
// (victims, offenders, arrestees) need sequential numbering for identification and
// ordering in the target format. This is particularly important for NIBRS reporting
// where each victim and offender must have a unique sequence number.
//
// Business Context:
// Many law enforcement reporting standards require sequential identification of entities
// within a report. For example, NIBRS requires victim sequence numbers (01, 02, 03)
// and offender sequence numbers to maintain referential integrity across related records.
//
// Common Sequencing Scenarios:
//   - Victim Numbering: Assign sequence 01, 02, 03 to victims for NIBRS compliance
//   - Offender Numbering: Sequential numbering of offenders in incident reports
//   - Arrestee Numbering: Sequential identification of arrestees in arrest records
//   - Property Numbering: Sequential numbering of stolen/recovered property items
//   - Charge Numbering: Sequential numbering of charges within an arrest
//
// Example Usage:
//
//	sequenceConfig := SequenceConfig{
//	    SequenceField: "victim_sequence",
//	    Start: 1,
//	    Format: "%02d", // Creates "01", "02", "03", etc.
//	}
type SequenceConfig struct {
	// SequenceField specifies the name of the field to add to each array item.
	//
	// This field will be added to each object in the input array with the
	// calculated sequence number. The field name should match the requirements
	// of the target reporting format.
	//
	// Field Naming Conventions:
	//   - Use descriptive names that indicate purpose: "victim_sequence", "offender_number"
	//   - Follow target format field naming conventions
	//   - Use consistent patterns across related sequence fields
	//   - Consider both human readability and system requirements
	//
	// Common Sequence Field Names:
	//   - "victim_sequence": For NIBRS victim numbering
	//   - "offender_sequence": For NIBRS offender numbering
	//   - "arrestee_number": For arrestee identification
	//   - "item_sequence": For property or evidence numbering
	//   - "charge_number": For charge ordering within arrests
	//
	// Target Format Considerations:
	// Different reporting systems may require different field names:
	//   - NIBRS: "SequenceNumber" for victims and offenders
	//   - UCR: May use different numbering field conventions
	//   - State systems: Often have agency-specific field naming requirements
	SequenceField string `json:"sequence_field"`

	// Start specifies the starting number for the sequence.
	//
	// Most compliance reporting starts numbering from 1, but some systems
	// or specific use cases may require different starting points. This allows
	// flexibility for various reporting requirements.
	//
	// Common Starting Values:
	//   - 1: Standard for most compliance reporting (NIBRS, UCR)
	//   - 0: Some technical systems prefer zero-based indexing
	//   - 10: When reserving lower numbers for special categories
	//   - Custom values: For specific agency or system requirements
	//
	// Business Considerations:
	//   - NIBRS specifically requires victim and offender numbering to start from 1
	//   - Some legacy systems may expect different starting points
	//   - Consider whether the starting point has business meaning (reserved ranges)
	//   - Ensure starting point doesn't conflict with existing numbering schemes
	//
	// Example:
	//   Start: 1 // Standard for NIBRS victim/offender sequences
	//   Start: 0 // For zero-based indexing systems
	Start int `json:"start"`

	// Format specifies the printf-style format pattern for the sequence numbers.
	//
	// This determines how the sequence numbers will be formatted as strings.
	// The format should match the target system's expectations for field width,
	// padding, and number representation.
	//
	// Common Format Patterns:
	//
	// Zero-Padded Integers:
	//   - "%02d": Two-digit zero-padded (1 -> "01", 10 -> "10")
	//   - "%03d": Three-digit zero-padded (1 -> "001", 100 -> "100")
	//   - "%04d": Four-digit zero-padded for large sequences
	//
	// Simple Integers:
	//   - "%d": Basic integer format (1 -> "1", 10 -> "10")
	//   - "%+d": Integer with sign (rare for sequences)
	//
	// Prefixed Formats:
	//   - "V%02d": Victim prefix (1 -> "V01", 2 -> "V02")
	//   - "OFF%02d": Offender prefix (1 -> "OFF01")
	//   - "SEQ-%03d": General sequence prefix
	//
	// Compliance Requirements:
	// Different reporting systems have specific formatting requirements:
	//   - NIBRS: Typically requires zero-padded two-digit numbers ("01", "02")
	//   - Some systems require specific field widths for data alignment
	//   - Legacy systems may have strict formatting requirements for compatibility
	//
	// Default Behavior:
	// If not specified, defaults to "%d" (simple integer formatting)
	//
	// Examples:
	//   - NIBRS sequences: "%02d" (produces "01", "02", "03")
	//   - Simple numbering: "%d" (produces "1", "2", "3")
	//   - Prefixed sequences: "ITEM-%03d" (produces "ITEM-001", "ITEM-002")
	Format string `json:"format"`
}

// GroupByConfig represents the configuration for a group_by transformation.
//
// Group-by transformations organize array data into categories based on field values,
// which is essential for creating structured reports that separate different types
// of entities or organize data by classification criteria.
//
// Business Context:
// Law enforcement reports often need to organize heterogeneous data into homogeneous
// groups for separate processing. For example, separating people, vehicles, and
// organizations from a mixed entity list, or grouping offenses by type or severity.
//
// Common Grouping Scenarios:
//   - Entity Type Grouping: Separate people, vehicles, organizations from entity lists
//   - Role-Based Grouping: Group entities by their roles (victim, offender, witness)
//   - Status Grouping: Group records by status (active, closed, pending)
//   - Geographic Grouping: Group incidents by jurisdiction or geographic area
//   - Temporal Grouping: Group events by time period (monthly, quarterly)
//
// Example Usage:
//
//	groupConfig := GroupByConfig{
//	    GroupField: "entity.entity_type",
//	}
//	// Groups entities by type: PERSON, VEHICLE, ORGANIZATION
type GroupByConfig struct {
	// GroupField specifies the field path within each array item to use for grouping.
	//
	// This field path is evaluated for each item in the input array, and items
	// with the same field value are placed into the same group. The field should
	// contain discrete, comparable values that define meaningful categories.
	//
	// Field Selection Guidelines:
	//   - Choose fields with stable, discrete values that define clear categories
	//   - Avoid fields with highly variable or unique values (like IDs or timestamps)
	//   - Prefer enum-like fields that have a limited set of possible values
	//   - Consider the business meaning of the grouping for report organization
	//
	// Common Grouping Fields:
	//   - "entity.entity_type": Groups by entity type (PERSON, VEHICLE, etc.)
	//   - "extractionMetadata.role": Groups by role (VICTIM, OFFENDER, WITNESS)
	//   - "status": Groups by record status (ACTIVE, INACTIVE, PENDING)
	//   - "incident.jurisdiction": Groups by geographic jurisdiction
	//   - "offense.category": Groups by offense category or type
	//   - "case.priority": Groups by case priority level
	//
	// Output Structure:
	// The group_by transformation produces an array of group objects, each containing:
	//   - "group_key": The value that defines this group
	//   - "items": Array of all items that belong to this group
	//
	// Example Output:
	//   [
	//     {
	//       "group_key": "PERSON",
	//       "items": [/* all person entities */]
	//     },
	//     {
	//       "group_key": "VEHICLE",
	//       "items": [/* all vehicle entities */]
	//     }
	//   ]
	//
	// Downstream Processing:
	// Grouped data is often used as input for subsequent transformations that
	// process each group separately or apply different transformation rules
	// to different groups based on their characteristics.
	//
	// Performance Considerations:
	// Grouping is performed in memory, so very large arrays or arrays with
	// many unique group values may impact performance. Consider the expected
	// data volumes when designing grouping strategies.
	GroupField string `json:"group_field"`
}

// SortConfig represents the configuration for a sort transformation.
//
// Sort transformations provide ordered data presentation for compliance reports
// where specific ordering is required or beneficial for data analysis and review.
// Proper sorting is often mandated by reporting standards to ensure consistent
// data presentation across agencies and time periods.
//
// Business Context:
// Law enforcement reports often require specific ordering for regulatory compliance,
// data analysis, or operational efficiency. For example, victims might need to be
// ordered by age for statistical analysis, or offenses ordered by severity for
// investigative prioritization.
//
// Common Sorting Scenarios:
//   - Demographic Ordering: Sort people by age, name alphabetically
//   - Chronological Ordering: Sort events by occurrence time or report date
//   - Severity Ordering: Sort offenses by severity level or classification
//   - Priority Ordering: Sort cases by investigation priority or status
//   - Geographic Ordering: Sort by jurisdiction, zone, or beat assignment
//
// Example Usage:
//
//	sortConfig := SortConfig{
//	    SortField: "entity.demographics.age",
//	    SortOrder: "asc", // Youngest to oldest
//	}
type SortConfig struct {
	// SortField specifies the field path within each array item to use for sorting.
	//
	// This field path is evaluated for each item in the input array to extract
	// the comparison value. The field should contain values that can be meaningfully
	// compared and ordered (strings, numbers, dates).
	//
	// Field Selection Guidelines:
	//   - Choose fields with comparable values (numbers, dates, strings)
	//   - Ensure the field exists in most/all items to avoid inconsistent ordering
	//   - Consider the business meaning of the sort order for report utility
	//   - Prefer fields with stable values that won't change during processing
	//
	// Common Sort Fields:
	//   - "entity.demographics.age": Sort people by age
	//   - "entity.demographics.last_name": Alphabetical sorting by surname
	//   - "incident.occurred_date": Chronological incident ordering
	//   - "offense.severity_level": Sort by offense severity
	//   - "case.priority": Sort by investigation priority
	//   - "report.created_at": Sort by report creation time
	//   - "amount": Sort financial records by amount
	//
	// Data Type Handling:
	//   - Numeric fields: Natural numeric ordering (1, 2, 10, 20)
	//   - String fields: Lexicographic ordering (A, B, C, a, b, c)
	//   - Date fields: Chronological ordering (earliest to latest)
	//   - Mixed types: Converted to strings for comparison
	//
	// Missing Value Handling:
	// Items where the sort field is missing or null are typically placed
	// at the end of the sorted array regardless of sort order.
	SortField string `json:"sort_field"`

	// SortOrder specifies the direction of the sort operation.
	//
	// The sort direction determines whether the array is ordered from smallest
	// to largest values (ascending) or largest to smallest values (descending).
	// The interpretation of "smallest" and "largest" depends on the data type.
	//
	// Supported Sort Orders:
	//
	// "asc" (Ascending - Default):
	//   - Numbers: 1, 2, 3, 10, 20 (smallest to largest)
	//   - Strings: "A", "B", "C", "a", "b" (alphabetical)
	//   - Dates: Earlier dates to later dates (chronological)
	//   - Common use: Age sorting (youngest first), alphabetical lists
	//
	// "desc" (Descending):
	//   - Numbers: 20, 10, 3, 2, 1 (largest to smallest)
	//   - Strings: "z", "b", "a", "C", "B" (reverse alphabetical)
	//   - Dates: Later dates to earlier dates (reverse chronological)
	//   - Common use: Priority sorting (highest first), recent-first lists
	//
	// Business Considerations:
	//   - Victim lists: Often sorted by age ascending for statistical clarity
	//   - Incident lists: Often sorted by date descending (most recent first)
	//   - Offense lists: May be sorted by severity descending (most serious first)
	//   - Name lists: Typically sorted alphabetically ascending
	//
	// Default Behavior:
	// If not specified, defaults to "asc" (ascending order)
	//
	// Examples:
	//   - Age sorting: "asc" (youngest to oldest)
	//   - Priority sorting: "desc" (highest priority first)
	//   - Name sorting: "asc" (A to Z alphabetical)
	//   - Date sorting: "desc" (most recent first)
	SortOrder string `json:"sort_order"`
}

// PathSegment represents a single component in a complex field path expression.
//
// Path segments are the building blocks of the path resolution system, allowing
// navigation through complex nested data structures with arrays and objects.
// Each segment represents one step in the navigation process.
//
// Business Context:
// Law enforcement data is often deeply nested with multiple levels of objects
// and arrays. Path segments enable precise navigation to specific data elements
// regardless of the complexity of the data structure.
//
// Segment Types:
//   - Field Segments: Navigate to object properties ("entity", "demographics")
//   - Index Segments: Navigate to specific array elements ("[0]", "[5]")
//   - Wildcard Segments: Navigate to all array elements ("[*]")
//
// Example Path Breakdown:
//
//	"entities[0].entity.demographics.sex"
//	Segments: [field:"entities", index:"0", field:"entity", field:"demographics", field:"sex"]
type PathSegment struct {
	// Type specifies the navigation operation for this path segment.
	//
	// The type determines how the path resolver interprets and processes
	// this segment during data navigation.
	//
	// Segment Types:
	//   - "field": Object property access (navigate to named field)
	//   - "index": Array element access (navigate to specific array index)
	//   - "wildcard": Array wildcard access (navigate to all array elements)
	//   - "filter": JSONPath filter access (navigate to filtered array elements)
	//
	// Navigation Behavior:
	//   - "field": Looks up the named property in an object/map
	//   - "index": Accesses the specified index in an array/slice
	//   - "wildcard": Processes all elements in an array/slice
	//   - "filter": Filters array elements based on JSONPath expression
	Type string

	// Value contains the specific identifier or index for this segment.
	//
	// The interpretation of Value depends on the Type:
	//   - For "field" type: Contains the field/property name
	//   - For "index" type: Contains the array index as a string
	//   - For "wildcard" type: Contains "*" to indicate wildcard operation
	//   - For "filter" type: Contains the original filter expression for reference
	//
	// Examples:
	//   - Field segment: Type="field", Value="demographics"
	//   - Index segment: Type="index", Value="0"
	//   - Wildcard segment: Type="wildcard", Value="*"
	//   - Filter segment: Type="filter", Value="?(@.entity.entity_type == 'ENTITY_TYPE_PERSON')"
	Value string

	// IsWildcard indicates whether this segment represents a wildcard operation.
	//
	// Wildcard segments are special because they operate on all elements of an
	// array rather than a single element. This flag enables optimized processing
	// of wildcard operations during path resolution.
	//
	// Wildcard Processing:
	//   - true: Process all array elements (returns array of results)
	//   - false: Process single element (returns single result)
	//
	// This flag is redundant with checking Type=="wildcard" but provides
	// convenient access for performance-critical path resolution operations.
	IsWildcard bool

	// FilterExpression contains parsed JSONPath filter details for filter-type segments.
	//
	// This field is only populated when Type="filter" and contains the structured
	// representation of the JSONPath filter expression for efficient evaluation.
	//
	// Filter expressions follow JSONPath syntax: ?(@.field operator value)
	// Where @ represents the current array element being evaluated.
	//
	// Examples:
	//   - ?(@.entity.entity_type == 'ENTITY_TYPE_PERSON') - Filter for person entities
	//   - ?(@.data.age > 18) - Filter for entities with age greater than 18
	//   - ?(@.role == 'VICTIM') - Filter for victim roles
	FilterExpression *JSONPathFilter
}

// JSONPathFilter represents a parsed JSONPath filter expression for array filtering.
//
// JSONPath filters enable sophisticated array filtering using expressions that
// evaluate conditions against each array element. This provides a powerful way
// to select specific items from arrays based on their properties.
//
// Filter Syntax:
//
//	JSONPath filters follow the pattern: ?(@.field operator value)
//	- @ represents the current array element being evaluated
//	- field is a dot-separated path to the property to test
//	- operator specifies the comparison operation
//	- value is the comparison value (string, number, boolean)
//
// Supported Operators:
//   - "==" or "=" : Equal to
//   - "!=" : Not equal to
//   - ">" : Greater than
//   - ">=" : Greater than or equal to
//   - "<" : Less than
//   - "<=" : Less than or equal to
//   - "contains" : String contains substring
//   - "starts_with" : String starts with prefix
//   - "ends_with" : String ends with suffix
//
// Examples:
//   - ?(@.entity_type == 'ENTITY_TYPE_PERSON') - Select person entities
//   - ?(@.data.age >= 18) - Select entities with age 18 or older
//   - ?(@.role != 'SUSPECT') - Select non-suspect entities
//   - ?(@.name contains 'John') - Select entities whose name contains 'John'
//
// Business Context:
// JSONPath filters are essential for law enforcement data processing where
// reports contain mixed entity types (persons, vehicles, organizations) that
// need to be separated for different reporting requirements.
type JSONPathFilter struct {
	// FieldPath specifies the dot-separated path to the field being tested.
	//
	// The path is relative to the current array element (@) and uses dot notation
	// to navigate through nested objects. This field identifies which property
	// of each array element should be evaluated in the filter condition.
	//
	// Examples:
	//   - "entity_type" - Test the entity_type field directly
	//   - "entity.entity_type" - Navigate to entity object, then entity_type field
	//   - "data.demographics.age" - Navigate through nested objects to age
	//   - "extractionMetadata.context" - Access extraction context information
	FieldPath string

	// Operator specifies the comparison operation to perform.
	//
	// The operator determines how the field value should be compared against
	// the filter value. Different operators support different data types and
	// comparison semantics.
	//
	// Supported operators:
	//   - Equality: "==", "=" (any type)
	//   - Inequality: "!=" (any type)
	//   - Numeric: ">", ">=", "<", "<=" (numbers only)
	//   - String: "contains", "starts_with", "ends_with" (strings only)
	Operator string

	// Value contains the comparison value for the filter condition.
	//
	// The value is compared against the field value using the specified operator.
	// The type of this value should be compatible with the field being tested
	// and the operator being used.
	//
	// Type compatibility:
	//   - String values: Use for string fields and string operators
	//   - Numeric values: Use for numeric fields and numeric operators
	//   - Boolean values: Use for boolean fields with equality operators
	//
	// Examples:
	//   - "ENTITY_TYPE_PERSON" (string for entity type filtering)
	//   - 18 (number for age filtering)
	//   - true (boolean for flag filtering)
	Value interface{}
}

// ValidationError represents a specific configuration validation failure.
//
// Validation errors provide detailed information about configuration problems
// to help developers and configuration maintainers quickly identify and fix
// issues in transformation configurations.
//
// Error Context:
// These errors are collected during configuration validation and presented
// as a comprehensive list of all problems found, allowing for efficient
// debugging of complex configuration files.
//
// Common Validation Scenarios:
//   - Missing required fields in field mappings
//   - References to undefined lookup tables
//   - Invalid transformation type names
//   - Malformed configuration parameters
//   - Circular dependencies in field mappings
//
// Example Error:
//
//	ValidationError{
//	    Field: "field_mappings[0].lookup_table",
//	    Message: "lookup table 'sex_codes' is used but not defined",
//	    Value: "sex_codes",
//	}
type ValidationError struct {
	// Field identifies the specific configuration location that failed validation.
	//
	// This uses a JSONPath-like syntax to precisely identify the problematic
	// configuration element, making it easy for developers to locate and fix
	// the issue in complex configuration files.
	//
	// Field Path Examples:
	//   - "format_name": Root-level field error
	//   - "field_mappings[0].name": Error in first field mapping's name
	//   - "field_mappings[2].config.lookup_table": Error in lookup table reference
	//   - "lookup_tables.sex_codes": Error in lookup table definition
	//
	// Path Construction:
	// The field path is constructed during validation to provide precise
	// error location information, including array indices and nested object paths.
	Field string `json:"field"`

	// Message provides a human-readable description of the validation failure.
	//
	// Error messages should be:
	//   - Clear and specific about what is wrong
	//   - Actionable (indicate what needs to be fixed)
	//   - Professional (suitable for configuration maintainers)
	//   - Consistent in tone and format across all validation errors
	//
	// Good Message Examples:
	//   - "field_mappings is required and cannot be empty"
	//   - "lookup table 'sex_codes' is used but not defined"
	//   - "transformation type 'unknown_type' is not supported"
	//   - "filter transformation requires field, operator, and value config"
	//
	// Message Guidelines:
	//   - Start with the problem description
	//   - Include specific values when helpful
	//   - Suggest the required action when obvious
	//   - Avoid technical jargon when possible
	Message string `json:"message"`

	// Value contains the problematic value that caused the validation error.
	//
	// This field is optional but provides additional context for debugging
	// by showing the actual value that failed validation. This is particularly
	// helpful for identifying typos, incorrect references, or malformed data.
	//
	// Value Examples:
	//   - "unknown_transformation": For unsupported transformation types
	//   - "missing_table": For undefined lookup table references
	//   - "": For empty required fields
	//   - 123: For invalid numeric values where strings expected
	//
	// Privacy Considerations:
	// Be cautious about including sensitive values in error messages.
	// While configuration data is typically not sensitive, avoid exposing
	// any potentially confidential information in error reporting.
	//
	// When to Include Value:
	//   - Include for reference errors (missing table names, invalid types)
	//   - Include for format errors (malformed patterns, invalid syntax)
	//   - Omit for obvious cases (empty required fields)
	//   - Omit for potentially sensitive data
	Value interface{} `json:"value,omitempty"`
}
