// Package core provides the foundational transformation infrastructure for converting
// extracted report data into various output formats (NIBRS XML, UCR, custom formats).
//
// The mapping engine serves as the orchestration layer that coordinates all transformation
// operations, combining path resolution, transformation application, and output generation
// to convert complex extraction data into format-specific output structures according to
// configuration rules.
//
// Mapping Engine Philosophy:
// The mapping engine embodies the core principle of configuration-driven transformations
// where all business logic resides in JSON configuration files rather than Go code.
// This approach provides several critical benefits:
//   - New output formats can be supported by creating configuration files only
//   - Field mappings can be modified without code changes or deployments
//   - Compliance teams can adjust transformation rules without developer intervention
//   - Transformation logic is transparent and auditable by non-technical stakeholders
//   - Complex data transformations can be tested and debugged through configuration analysis
//
// Transformation Orchestration Workflow:
// The engine coordinates a sophisticated multi-step transformation process:
// 1. Configuration Loading: Parse and validate transformation configurations
// 2. Input Data Extraction: Use path resolution to extract values from complex data structures
// 3. Transformation Application: Apply configured transformations (lookup, filter, format, etc.)
// 4. Output Generation: Place transformed values at specified locations in output structures
// 5. Validation and Error Handling: Ensure data integrity throughout the process
//
// Architecture Components:
// The mapping engine integrates three core components to achieve comprehensive transformation:
//   - PathResolver: Provides sophisticated navigation through complex nested data structures
//   - TransformationEngine: Applies 8 different transformation types based on configuration
//   - ConfigLoader: Handles loading, parsing, and validation of transformation configurations
//
// Business Context:
// The mapping engine is the heart of the ETL system's Transform phase, converting typed
// extraction data into format-specific output structures that comply with various reporting
// standards (NIBRS, UCR, custom agency formats). It ensures that Hero's internal data
// representation is accurately translated into external format requirements.
//
// Thread Safety:
// MappingEngine instances are thread-safe and can process multiple transformation operations
// concurrently. Each transformation operation is independent and stateless, making the
// engine suitable for high-throughput parallel processing scenarios.
//
// Error Handling Strategy:
// The engine implements comprehensive error handling that provides detailed context:
//   - Configuration validation errors with specific field feedback
//   - Path resolution errors with navigation context
//   - Transformation errors with input data and configuration details
//   - Output generation errors with target path information
//
// Performance Characteristics:
// The engine is optimized for transformation workloads with:
//   - Efficient configuration parsing and caching
//   - Fast path resolution with minimal reflection overhead
//   - Optimized transformation algorithms for common use cases
//   - Minimal memory allocation during transformation operations
package core

import (
	"fmt"
	"slices"
	"strings"
)

// MappingEngine orchestrates the complete transformation process for converting input data to output formats.
//
// This component serves as the central coordinator for all transformation operations, integrating
// path resolution, transformation application, and output generation capabilities. It implements
// the configuration-driven transformation approach where all business logic is defined in JSON
// configuration files rather than embedded in Go code.
//
// Core Responsibilities:
//   - Coordinate multi-step transformation workflows using configuration-driven rules
//   - Integrate PathResolver for sophisticated data navigation through complex structures
//   - Apply TransformationEngine operations for data manipulation and formatting
//   - Generate structured output data according to format-specific requirements
//   - Provide comprehensive validation and error handling throughout the transformation process
//   - Maintain high performance and thread-safety for concurrent transformation operations
//
// Business Context:
// The mapping engine is essential for the ETL Transform phase, enabling Hero to convert its
// internal extraction data into various external formats required by law enforcement agencies.
// It supports compliance with reporting standards like NIBRS while allowing customization
// for agency-specific requirements through configuration changes rather than code modifications.
//
// Transformation Process Flow:
// 1. Configuration Validation: Ensure transformation rules are complete and consistent
// 2. Field Mapping Iteration: Process each configured field mapping in sequence
// 3. Input Value Extraction: Use path resolution to navigate complex input data structures
// 4. Transformation Application: Apply configured transformation with type-specific parameters
// 5. Output Value Placement: Store transformed results at specified output paths
// 6. Result Compilation: Return complete transformed output ready for format generation
//
// Integration Pattern:
// The engine integrates three specialized components to achieve comprehensive transformation:
//   - PathResolver: Handles complex data navigation with support for nested objects and arrays
//   - TransformationEngine: Provides 8 transformation types (lookup, filter, format, etc.)
//   - ConfigLoader: Manages configuration loading, parsing, and validation operations
//
// Thread Safety:
// MappingEngine instances are completely thread-safe and stateless. Multiple goroutines
// can use the same engine instance concurrently to process different transformation
// operations without any synchronization requirements.
//
// Error Handling Approach:
// The engine provides detailed error context for all failure scenarios:
//   - Field mapping errors include mapping name and configuration details
//   - Path resolution errors include input/output path information
//   - Transformation errors include transformation type and configuration
//   - Configuration errors include specific field paths and validation messages
type MappingEngine struct {
	pathResolver         *PathResolver         // Sophisticated navigation for complex data structures
	transformationEngine *TransformationEngine // Engine for applying 8 transformation types
	configLoader         *ConfigLoader         // Loader for configuration parsing and validation
}

// NewMappingEngine creates a new mapping engine instance with all required transformation components.
//
// This function initializes a complete mapping engine ready for transformation operations
// by creating and integrating all necessary components: path resolution, transformation
// application, and configuration loading. The returned engine is thread-safe and can
// be used concurrently across multiple transformation operations.
//
// Component Initialization:
// The function creates and integrates three core components:
//   - PathResolver: For sophisticated navigation through complex nested data structures
//   - TransformationEngine: For applying 8 different transformation types based on configuration
//   - ConfigLoader: For loading, parsing, and validating transformation configurations
//
// Returns:
//   - *MappingEngine: A fully configured mapping engine ready for immediate use
//
// Thread Safety:
// The returned mapping engine is completely thread-safe and stateless. Multiple
// goroutines can use the same engine instance concurrently without synchronization.
//
// Example Usage:
//
//	engine := NewMappingEngine()
//	result, err := engine.TransformData(extractionData, mappingConfig)
//	if err != nil {
//	    return fmt.Errorf("transformation failed: %w", err)
//	}
//	// Use transformed result for output generation
func NewMappingEngine() *MappingEngine {
	return &MappingEngine{
		pathResolver:         NewPathResolver(),
		transformationEngine: NewTransformationEngine(),
		configLoader:         NewConfigLoader(),
	}
}

// TransformData transforms input data according to the provided configuration using comprehensive field mappings.
//
// This method serves as the primary interface for complete data transformation operations,
// converting complex input data structures (typically extraction results) into format-specific
// output structures according to configuration-driven rules. It orchestrates the entire
// transformation workflow while maintaining data integrity and providing detailed error context.
//
// Transformation Orchestration Process:
// The method coordinates a sophisticated multi-step transformation workflow:
//  1. Configuration Validation: Comprehensive validation of all mapping rules and dependencies
//  2. Working Data Creation: Create a working copy of the original input data that will be modified
//  3. Field Mapping Processing: Sequential processing of each configured field mapping:
//     a. Input Data Extraction: Navigate complex input structures using path resolution
//     b. Transformation Application: Apply configured transformation with type-specific logic
//     c. Output Data Placement: Store transformed values at specified locations in working data
//  4. Result Compilation: Return the complete dataset containing both original and transformed data
//
// Configuration Processing:
// The method processes comprehensive mapping configurations that include:
//   - Field Mappings: Define input-to-output data transformations with specific rules
//   - Lookup Tables: Provide value mapping for enum conversion and code standardization
//   - Transformation Types: Support 8 different transformation operations (lookup, filter, etc.)
//   - Validation Rules: Ensure data integrity and compliance requirements
//
// Business Context:
// This method is essential for ETL Transform operations, enabling Hero to convert its
// internal extraction data into various external formats required by law enforcement
// agencies. It supports compliance with standards like NIBRS while allowing customization
// for agency-specific requirements through configuration rather than code changes.
//
// Error Handling Strategy:
// Comprehensive error handling provides detailed context for all failure scenarios:
//   - Configuration validation errors with specific field paths and requirements
//   - Path resolution errors with navigation context and data structure information
//   - Transformation errors with input data details and configuration parameters
//   - Output generation errors with target path information and value details
//
// Parameters:
//   - inputData: Complex data structure to transform (typically HydratedReportData from extraction)
//   - config: Complete mapping configuration with field mappings, lookup tables, and validation rules
//
// Returns:
//   - map[string]interface{}: Complete dataset containing both original data and all transformations
//   - error: Detailed error information if any transformation step fails
//
// Common Use Cases:
//   - Transform extraction data to NIBRS XML format for compliance reporting
//   - Convert Hero data to UCR format for statistical analysis
//   - Generate custom agency reports using agency-specific field mappings
//   - Transform data for integration with external law enforcement systems
//
// Example Usage:
//
//	engine := NewMappingEngine()
//
//	// Transform extraction data to NIBRS format
//	nibrsData, err := engine.TransformData(extractionData, nibrsConfig)
//	if err != nil {
//	    return fmt.Errorf("NIBRS transformation failed: %w", err)
//	}
//
//	// The result contains both original data and transformations:
//	// - Original fields: nibrsData["entities"], nibrsData["reports"], etc.
//	// - Transformed fields: nibrsData["Victims"], nibrsData["IncidentNumber"], etc.
//	xmlOutput := generateNIBRSXML(nibrsData)
//
// Performance Considerations:
// The method is optimized for transformation workloads with:
//   - Efficient path resolution with minimal reflection overhead
//   - Fast transformation algorithms for common operations
//   - Minimal memory allocation during field mapping processing
//   - Parallel-friendly stateless design for concurrent operations
func (engine *MappingEngine) TransformData(inputData interface{}, config *MappingConfig) (map[string]interface{}, error) {
	// Validate input data
	if inputData == nil {
		return nil, fmt.Errorf("input data cannot be nil")
	}

	// Validate configuration first
	if err := engine.configLoader.ValidateConfig(config); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	// Create working copy of the original input data that we'll modify throughout
	// This will be our final result containing both original data + transformations
	// This approach ensures that the output contains the complete dataset,
	// not just the transformed fields, making it easier to reference both
	// original and transformed data in subsequent operations
	workingData := make(map[string]interface{})

	// Deep copy original input data to preserve all original fields
	if inputMap, ok := inputData.(map[string]interface{}); ok {
		for key, value := range inputMap {
			workingData[key] = value
		}
	}

	// Process each field mapping one by one on the working data
	for _, mapping := range config.FieldMappings {
		// Determine if this mapping should be in-place or add new field
		useInPlace := mapping.InputPath == mapping.OutputPath

		// Safety check: if output path exists and is different from input path
		if !useInPlace {
			pathExists := engine.pathResolver.pathExistsInData(workingData, mapping.OutputPath)

			if pathExists && !mapping.OverwriteExisting {
				return nil, fmt.Errorf("output path '%s' already exists in data. Set overwrite_existing:true to confirm overwriting, or use a different output path", mapping.OutputPath)
			}
		}

		// Apply the transformation directly to the working data
		if err := engine.applyTransformationToWorkingData(workingData, mapping, config); err != nil {
			return nil, fmt.Errorf("failed to apply transformation '%s': %w", mapping.Name, err)
		}
	}

	// Return the complete working data (original + all transformations)
	return workingData, nil
}

// applyTransformationToWorkingData applies a single transformation to the working data
func (engine *MappingEngine) applyTransformationToWorkingData(workingData map[string]interface{}, mapping FieldMapping, config *MappingConfig) error {
	// Determine if this is in-place or new field transformation
	useInPlace := mapping.InputPath == mapping.OutputPath

	// Check if this is a wildcard transformation that should be processed element by element
	if engine.containsWildcard(mapping.InputPath) && useInPlace {
		// Process wildcard transformation element by element
		return engine.processWildcardMappingElementByElementOnWorkingData(workingData, mapping, config)
	} else {
		// Standard transformation processing
		return engine.processStandardMappingOnWorkingData(workingData, mapping, config, useInPlace)
	}
}

// processWildcardMappingElementByElementOnWorkingData processes wildcard transformations element by element on working data
func (engine *MappingEngine) processWildcardMappingElementByElementOnWorkingData(workingData map[string]interface{}, mapping FieldMapping, config *MappingConfig) error {
	// Parse the wildcard path to understand the structure
	wildcardIndex := strings.Index(mapping.InputPath, "[*]")
	if wildcardIndex == -1 {
		return fmt.Errorf("wildcard not found in path: %s", mapping.InputPath)
	}

	// Split path into array part and field part
	arrayPath := mapping.InputPath[:wildcardIndex]
	fieldPath := ""
	if wildcardIndex+3 < len(mapping.InputPath) {
		fieldPath = mapping.InputPath[wildcardIndex+4:] // Skip "[*]."
	}

	// Get the array from working data
	arrayValue, err := engine.pathResolver.GetValue(workingData, arrayPath)
	if err != nil {
		return fmt.Errorf("failed to get array at path '%s': %w", arrayPath, err)
	}

	// Convert to slice
	arraySlice, ok := arrayValue.([]any)
	if !ok {
		return fmt.Errorf("expected array at path '%s', got %T", arrayPath, arrayValue)
	}

	// Process each array element individually
	for elementIndex, element := range arraySlice {
		// Extract the specific field value from this element
		var fieldValue any
		if fieldPath == "" {
			// Wildcard refers to the array element itself
			fieldValue = element
		} else {
			// Extract nested field from the element
			fieldValue, err = engine.pathResolver.GetValue(element, fieldPath)
			if err != nil {
				// Skip elements that don't have the field (graceful handling)
				continue
			}
		}

		// Prepare transformation configuration
		transformConfig := make(map[string]any)
		for key, value := range mapping.Config {
			transformConfig[key] = value
		}

		// For lookup transformations, inject the actual lookup table data
		if mapping.Transformation == transformationLookup {
			if lookupTableName, ok := transformConfig["lookup_table"].(string); ok {
				if lookupTableData, exists := config.LookupTables[lookupTableName]; exists {
					transformConfig["lookup_table_data"] = lookupTableData
				}
			}
		}

		// Apply transformation to this individual element
		transformedValue, err := engine.transformationEngine.ApplyTransformation(
			fieldValue,
			mapping.Transformation,
			transformConfig,
		)
		if err != nil {
			return fmt.Errorf("failed to apply transformation '%s' to element %d: %w", mapping.Transformation, elementIndex, err)
		}

		// Set the transformed value back in place in the working data
		// Create specific path for this array element: "PersonEntities[0].entity.data.gender"
		specificPath := arrayPath + "[" + fmt.Sprintf("%d", elementIndex) + "]"
		if fieldPath != "" {
			specificPath += "." + fieldPath
		}

		// Set value in place in the working data
		if err := engine.pathResolver.SetValueInPlace(workingData, specificPath, transformedValue); err != nil {
			return fmt.Errorf("failed to set transformed value at path '%s': %w", specificPath, err)
		}
	}

	return nil
}

// processStandardMappingOnWorkingData processes non-wildcard transformations on working data
func (engine *MappingEngine) processStandardMappingOnWorkingData(workingData map[string]interface{}, mapping FieldMapping, config *MappingConfig, useInPlace bool) error {
	// Extract input value from working data
	inputValue, err := engine.pathResolver.GetValue(workingData, mapping.InputPath)
	if err != nil {
		return fmt.Errorf("failed to extract value from input path '%s': %w", mapping.InputPath, err)
	}

	// Prepare transformation configuration
	transformConfig := make(map[string]any)
	for key, value := range mapping.Config {
		transformConfig[key] = value
	}

	// For lookup transformations, inject the actual lookup table data
	if mapping.Transformation == transformationLookup {
		if lookupTableName, ok := transformConfig["lookup_table"].(string); ok {
			if lookupTableData, exists := config.LookupTables[lookupTableName]; exists {
				transformConfig["lookup_table_data"] = lookupTableData
			}
		}
	}

	// Apply transformation
	transformedValue, err := engine.transformationEngine.ApplyTransformation(
		inputValue,
		mapping.Transformation,
		transformConfig,
	)
	if err != nil {
		return fmt.Errorf("failed to apply transformation '%s': %w", mapping.Transformation, err)
	}

	if useInPlace {
		// In-place transformation: modify the working data directly
		if err := engine.pathResolver.SetValueInPlace(workingData, mapping.OutputPath, transformedValue); err != nil {
			return fmt.Errorf("failed to set value in-place at path '%s': %w", mapping.OutputPath, err)
		}
	} else {
		// New field transformation: add new field to working data
		if err := engine.pathResolver.SetValue(workingData, mapping.OutputPath, transformedValue); err != nil {
			return fmt.Errorf("failed to set value at output path '%s': %w", mapping.OutputPath, err)
		}
	}

	return nil
}

// containsWildcard checks if a path contains wildcard notation
func (engine *MappingEngine) containsWildcard(path string) bool {
	return strings.Contains(path, "[*]")
}

// TransformationResult holds the result of a transformation operation with warnings
type TransformationResult struct {
	Data     map[string]interface{} // Transformed data
	Warnings []string               // Warnings about missing fields, errors handled gracefully
	Success  bool                   // Whether transformation completed successfully
}

// TransformDataWithWarnings processes input data and captures warnings about gracefully handled issues.
//
// This method provides the same transformation capabilities as TransformData but also
// collects warnings about missing fields, out-of-bounds access, and other issues that
// are handled gracefully (returning nil) instead of failing with errors.
//
// This is useful for debugging and monitoring transformation quality while maintaining
// robust, graceful behavior in production.
//
// Parameters:
//   - inputData: The extracted data to transform (any supported type)
//   - config: Complete mapping configuration with field mappings and lookup tables
//
// Returns:
//   - *TransformationResult: Contains transformed data, warnings, and success status
//   - error: Critical errors that prevent transformation (configuration issues, etc.)
func (engine *MappingEngine) TransformDataWithWarnings(inputData interface{}, config *MappingConfig) (*TransformationResult, error) {
	result := &TransformationResult{
		Data:     make(map[string]interface{}),
		Warnings: []string{},
		Success:  false,
	}

	// Validate input data
	if inputData == nil {
		result.Warnings = append(result.Warnings, "input data is nil")
		result.Success = true // Still considered successful as it's handled gracefully
		return result, nil
	}

	// Validate configuration first
	if err := engine.configLoader.ValidateConfig(config); err != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", err)
	}

	// Initialize result.Data with original input data (complete dataset approach)
	if inputMap, ok := inputData.(map[string]interface{}); ok {
		for key, value := range inputMap {
			result.Data[key] = value
		}
	}

	// Process each field mapping one by one on the result data (complete dataset)
	for _, mapping := range config.FieldMappings {
		// Apply transformation with warning collection to the complete dataset
		if err := engine.applyTransformationToWorkingDataWithWarnings(result.Data, mapping, config, &result.Warnings); err != nil {
			result.Warnings = append(result.Warnings, fmt.Sprintf("field mapping '%s': %v", mapping.Name, err))
			// Continue with other mappings even if one fails
		}
	}

	result.Success = true
	return result, nil
}

// applyTransformationToWorkingDataWithWarnings applies a single transformation to working data with warning collection
func (engine *MappingEngine) applyTransformationToWorkingDataWithWarnings(workingData map[string]interface{}, mapping FieldMapping, config *MappingConfig, warnings *[]string) error {
	// Determine if this is in-place or new field transformation
	useInPlace := mapping.InputPath == mapping.OutputPath

	// Check if this is a wildcard transformation that should be processed element by element
	if engine.containsWildcard(mapping.InputPath) && useInPlace {
		// Process wildcard transformation element by element with warnings
		return engine.processWildcardMappingElementByElementOnWorkingDataWithWarnings(workingData, mapping, config, warnings)
	} else {
		// Standard transformation processing with warnings
		return engine.processStandardMappingOnWorkingDataWithWarnings(workingData, mapping, config, useInPlace, warnings)
	}
}

// processStandardMappingOnWorkingDataWithWarnings processes standard transformations on working data with warning collection
func (engine *MappingEngine) processStandardMappingOnWorkingDataWithWarnings(workingData map[string]interface{}, mapping FieldMapping, config *MappingConfig, useInPlace bool, warnings *[]string) error {
	// Extract input value with warning collection instead of error
	inputValue, err := engine.pathResolver.GetValue(workingData, mapping.InputPath)
	if err != nil {
		*warnings = append(*warnings, fmt.Sprintf("field mapping '%s': failed to extract from path '%s': %v", mapping.Name, mapping.InputPath, err))
		return nil // Don't return error, just warn and continue
	}

	if inputValue == nil {
		*warnings = append(*warnings, fmt.Sprintf("field mapping '%s': no data found at path '%s'", mapping.Name, mapping.InputPath))
		// Continue processing - nil values can be valid in transformations
	}

	// Prepare transformation configuration with lookup table data
	transformConfig := make(map[string]interface{})
	if mapping.Config != nil {
		for key, value := range mapping.Config {
			transformConfig[key] = value
		}
	}

	// Add lookup table data if this is a lookup transformation
	if mapping.Transformation == transformationLookup {
		if lookupTableName, exists := mapping.Config["lookup_table"].(string); exists {
			if lookupTable, tableExists := config.LookupTables[lookupTableName]; tableExists {
				transformConfig["lookup_table_data"] = lookupTable
			} else {
				*warnings = append(*warnings, fmt.Sprintf("field mapping '%s': lookup table '%s' not found", mapping.Name, lookupTableName))
				return nil // Don't fail, just warn and continue
			}
		}
	}

	// Apply transformation with warning collection
	transformedValue, err := engine.transformationEngine.ApplyTransformation(inputValue, mapping.Transformation, transformConfig)
	if err != nil {
		*warnings = append(*warnings, fmt.Sprintf("field mapping '%s': transformation failed: %v", mapping.Name, err))
		return nil // Don't fail, just warn and continue
	}

	// Apply the transformation result
	if useInPlace {
		// In-place transformation: modify existing data
		if err := engine.pathResolver.SetValueInPlace(workingData, mapping.OutputPath, transformedValue); err != nil {
			*warnings = append(*warnings, fmt.Sprintf("field mapping '%s': failed to set value in-place at path '%s': %v", mapping.Name, mapping.OutputPath, err))
			return nil // Don't fail, just warn and continue
		}
	} else {
		// Safety check for output path conflicts (non-in-place)
		pathExists := engine.pathResolver.pathExistsInData(workingData, mapping.OutputPath)
		if pathExists && !mapping.OverwriteExisting {
			*warnings = append(*warnings, fmt.Sprintf("field mapping '%s': output path '%s' already exists and overwrite_existing is false", mapping.Name, mapping.OutputPath))
			return nil
		}

		// Copy transformation: create new field in working data
		if err := engine.pathResolver.SetValue(workingData, mapping.OutputPath, transformedValue); err != nil {
			*warnings = append(*warnings, fmt.Sprintf("field mapping '%s': failed to set value at output path '%s': %v", mapping.Name, mapping.OutputPath, err))
			return nil // Don't fail, just warn and continue
		}
	}

	return nil
}

// processWildcardMappingElementByElementOnWorkingDataWithWarnings processes wildcard transformations element by element with warnings
func (engine *MappingEngine) processWildcardMappingElementByElementOnWorkingDataWithWarnings(workingData map[string]interface{}, mapping FieldMapping, config *MappingConfig, warnings *[]string) error {
	// For now, fall back to the non-warning version and convert any errors to warnings
	if err := engine.processWildcardMappingElementByElementOnWorkingData(workingData, mapping, config); err != nil {
		*warnings = append(*warnings, fmt.Sprintf("field mapping '%s': wildcard transformation failed: %v", mapping.Name, err))
		return nil // Don't fail, just warn and continue
	}
	return nil
}

// TransformWithConfigAndWarnings transforms input data and returns warnings about gracefully handled issues.
func (engine *MappingEngine) TransformWithConfigAndWarnings(inputData interface{}, configJSON string) (*TransformationResult, error) {
	// Load configuration from JSON
	config, err := engine.configLoader.LoadFromString(configJSON)
	if err != nil {
		return nil, fmt.Errorf("failed to load configuration: %w", err)
	}

	// Transform data using loaded configuration with warning collection
	return engine.TransformDataWithWarnings(inputData, config)
}

// TransformWithConfigFileAndWarnings transforms input data using configuration from a file and returns warnings.
func (engine *MappingEngine) TransformWithConfigFileAndWarnings(inputData interface{}, configFilePath string) (*TransformationResult, error) {
	// Load configuration from file
	config, err := engine.configLoader.LoadFromFile(configFilePath)
	if err != nil {
		return nil, fmt.Errorf("failed to load configuration from file: %w", err)
	}

	// Transform data using loaded configuration with warning collection
	return engine.TransformDataWithWarnings(inputData, config)
}

// TransformWithConfig transforms input data using configuration from a JSON string.
//
// This convenience method provides a streamlined interface for transformation operations
// when configuration data is available as a JSON string rather than a pre-loaded
// configuration object. It combines configuration loading, validation, and transformation
// into a single operation, making it ideal for scenarios where configurations are
// received dynamically or stored as string data.
//
// Processing Workflow:
// The method implements a two-step process that ensures configuration integrity:
// 1. Configuration Loading: Parse JSON string into validated MappingConfig structure
// 2. Transformation Application: Apply the loaded configuration to transform input data
//
// Transformation Mode Support:
// This method automatically detects and applies the transformation mode specified in
// the configuration:
//   - "copy" mode: Creates new output structures (default, safe)
//   - "in_place" mode: Modifies input data directly (memory efficient)
//
// Configuration Sources:
// This method is particularly useful when configuration data comes from:
//   - Environment variables containing JSON configuration strings
//   - API responses with transformation configuration data
//   - Command-line arguments providing configuration as string literals
//   - Configuration management systems that return string-based configurations
//   - Database records storing transformation configurations as JSON strings
//
// Business Context:
// The method supports dynamic transformation scenarios where configurations are not
// stored as static files but are managed through configuration systems, APIs, or
// databases. This enables runtime configuration changes without file system access.
//
// Parameters:
//   - inputData: Complex data structure to transform (typically extraction results)
//   - configJSON: Valid JSON string containing complete transformation configuration
//
// Returns:
//   - map[string]interface{}: Fully transformed output data ready for format generation
//   - error: Detailed error information if configuration loading or transformation fails
//
// Error Conditions:
//   - JSON parsing errors if configJSON contains invalid JSON syntax
//   - Configuration validation errors if loaded configuration is incomplete or invalid
//   - Transformation errors if field mappings cannot be applied to input data
//
// Example Usage:
//
//	configJSON := `{
//	  "format_name": "NIBRS_XML_2024",
//	  "transformation_mode": "in_place",
//	  "field_mappings": [
//	    {
//	      "name": "victim_sex_mapping",
//	      "input_path": "entities[*].demographics.sex",
//	      "transformation": transformationLookup,
//	      "output_path": "victims[*].sex_code",
//	      "config": {"lookup_table": "sex_codes"}
//	    }
//	  ],
//	  "lookup_tables": {
//	    "sex_codes": {"SEX_MALE": "M", "SEX_FEMALE": "F"}
//	  }
//	}`
//
//	result, err := engine.TransformWithConfig(extractionData, configJSON)
//	if err != nil {
//	    return fmt.Errorf("transformation failed: %w", err)
//	}
func (engine *MappingEngine) TransformWithConfig(inputData interface{}, configJSON string) (map[string]interface{}, error) {
	// Load configuration from JSON
	config, err := engine.configLoader.LoadFromString(configJSON)
	if err != nil {
		return nil, fmt.Errorf("failed to load configuration: %w", err)
	}

	// Transform data using loaded configuration
	return engine.TransformData(inputData, config)
}

// TransformWithConfigFile transforms input data using configuration loaded from a JSON file.
//
// This convenience method provides a streamlined interface for transformation operations
// when configuration data is stored in the filesystem as JSON files. It combines file
// loading, configuration parsing, validation, and transformation into a single operation,
// making it ideal for scenarios with static configuration files or file-based configuration
// management approaches.
//
// Processing Workflow:
// The method implements a comprehensive three-step process:
// 1. File Loading: Read JSON configuration data from the specified file path
// 2. Configuration Validation: Parse and validate the configuration structure and content
// 3. Transformation Application: Apply the validated configuration to transform input data
//
// Transformation Mode Support:
// This method automatically detects and applies the transformation mode specified in
// the configuration file:
//   - "copy" mode: Creates new output structures (default, safe)
//   - "in_place" mode: Modifies input data directly (memory efficient)
//
// File-Based Configuration Management:
// This method is particularly useful for scenarios involving:
//   - Static transformation configurations stored as configuration files
//   - Version-controlled transformation rules managed through Git or similar systems
//   - Environment-specific configurations organized in directory structures
//   - Configuration templates and examples stored in the filesystem
//   - Production configuration files deployed with application releases
//
// Business Context:
// The method supports traditional file-based configuration management where transformation
// rules are defined, tested, and deployed as JSON files. This approach enables version
// control, code review, and structured configuration management for transformation logic.
//
// Configuration File Organization:
// Common file organization patterns include:
//   - Format-specific directories: "configs/nibrs/", "configs/ucr/", "configs/custom/"
//   - Environment-specific files: "nibrs_production.json", "nibrs_testing.json"
//   - Version-specific configurations: "nibrs_v2024.json", "nibrs_v2025.json"
//
// Parameters:
//   - inputData: Complex data structure to transform (typically extraction results)
//   - configFilePath: Absolute or relative path to JSON configuration file
//
// Returns:
//   - map[string]interface{}: Fully transformed output data ready for format generation
//   - error: Detailed error information if file loading, configuration validation, or transformation fails
//
// Error Conditions:
//   - File system errors if configuration file cannot be accessed or read
//   - JSON parsing errors if configuration file contains invalid JSON syntax
//   - Configuration validation errors if loaded configuration is incomplete or invalid
//   - Transformation errors if field mappings cannot be applied to input data
//
// Example Usage:
//
//	// Load NIBRS configuration from file with in-place transformation
//	nibrsResult, err := engine.TransformWithConfigFile(
//	    extractionData,
//	    "configs/nibrs/nibrs_v2024_mapping.json",
//	)
//	if err != nil {
//	    return fmt.Errorf("NIBRS transformation failed: %w", err)
//	}
//
//	// Load custom agency configuration with copy-based transformation
//	customResult, err := engine.TransformWithConfigFile(
//	    extractionData,
//	    "configs/agencies/chicago_pd_mapping.json",
//	)
//	if err != nil {
//	    return fmt.Errorf("custom transformation failed: %w", err)
//	}
//
// File Path Considerations:
//   - Relative paths are resolved relative to the current working directory
//   - Absolute paths provide explicit file location specification
//   - File path validation includes existence checks and read permission verification
func (engine *MappingEngine) TransformWithConfigFile(inputData interface{}, configFilePath string) (map[string]interface{}, error) {
	// Load configuration from file
	config, err := engine.configLoader.LoadFromFile(configFilePath)
	if err != nil {
		return nil, fmt.Errorf("failed to load configuration from file: %w", err)
	}

	// Transform data using loaded configuration
	return engine.TransformData(inputData, config)
}

// GetSupportedTransformations returns a list of all supported transformation types.
//
// This is useful for configuration validation and documentation purposes.
//
// Returns:
//   - []string: List of supported transformation type names
func (engine *MappingEngine) GetSupportedTransformations() []string {
	return []string{
		"direct",
		transformationLookup,
		transformationFilter,
		"format",
		"date_format",
		"add_sequence",
		"group_by",
		"sort",
	}
}

// ValidateFieldMapping validates a single field mapping configuration.
//
// This method checks:
// 1. Required fields are present
// 2. Transformation type is supported
// 3. Transformation-specific configuration is valid
// 4. Referenced lookup tables exist
//
// Parameters:
//   - mapping: Field mapping to validate
//   - config: Full configuration (for lookup table references)
//
// Returns:
//   - error: Any validation error found
func (engine *MappingEngine) ValidateFieldMapping(mapping FieldMapping, config *MappingConfig) error {
	// Check required fields
	if mapping.Name == "" {
		return fmt.Errorf("field mapping name is required")
	}
	if mapping.InputPath == "" {
		return fmt.Errorf("field mapping input_path is required")
	}
	if mapping.Transformation == "" {
		return fmt.Errorf("field mapping transformation is required")
	}
	if mapping.OutputPath == "" {
		return fmt.Errorf("field mapping output_path is required")
	}

	// Check transformation type is supported
	supportedTypes := engine.GetSupportedTransformations()
	if !slices.Contains(supportedTypes, mapping.Transformation) {
		return fmt.Errorf("unsupported transformation type: %s", mapping.Transformation)
	}

	// Validate transformation-specific configuration
	if err := engine.validateTransformationConfig(mapping, config); err != nil {
		return fmt.Errorf("invalid transformation configuration: %w", err)
	}

	return nil
}

// validateTransformationConfig validates transformation-specific configuration.
//
// Each transformation type has specific configuration requirements:
// - lookup: requires lookup_table reference
// - filter: requires field, operator, and value
// - format: requires format_pattern
// - date_format: requires date_format
// - add_sequence: requires sequence_field
// - group_by: requires group_field
// - sort: requires sort_field
//
// Parameters:
//   - mapping: Field mapping with transformation configuration
//   - config: Full configuration (for lookup table validation)
//
// Returns:
//   - error: Any configuration validation error
func (engine *MappingEngine) validateTransformationConfig(mapping FieldMapping, config *MappingConfig) error {
	switch mapping.Transformation {
	case transformationLookup:
		if mapping.Config == nil {
			return fmt.Errorf("lookup transformation requires config")
		}
		lookupTable, ok := mapping.Config["lookup_table"].(string)
		if !ok {
			return fmt.Errorf("lookup transformation requires lookup_table config")
		}
		if _, exists := config.LookupTables[lookupTable]; !exists {
			return fmt.Errorf("lookup table '%s' is used but not defined", lookupTable)
		}

	case transformationFilter:
		if mapping.Config == nil {
			return fmt.Errorf("filter transformation requires config")
		}
		if _, ok := mapping.Config["field"].(string); !ok {
			return fmt.Errorf("filter transformation requires field config")
		}
		if _, ok := mapping.Config["operator"].(string); !ok {
			return fmt.Errorf("filter transformation requires operator config")
		}
		if mapping.Config["value"] == nil {
			return fmt.Errorf("filter transformation requires value config")
		}

	case "format":
		if mapping.Config == nil {
			return fmt.Errorf("format transformation requires config")
		}
		if _, ok := mapping.Config["format_pattern"].(string); !ok {
			return fmt.Errorf("format transformation requires format_pattern config")
		}

	case "date_format":
		if mapping.Config == nil {
			return fmt.Errorf("date_format transformation requires config")
		}
		if _, ok := mapping.Config["date_format"].(string); !ok {
			return fmt.Errorf("date_format transformation requires date_format config")
		}

	case "add_sequence":
		if mapping.Config == nil {
			return fmt.Errorf("add_sequence transformation requires config")
		}
		if _, ok := mapping.Config["sequence_field"].(string); !ok {
			return fmt.Errorf("add_sequence transformation requires sequence_field config")
		}

	case "group_by":
		if mapping.Config == nil {
			return fmt.Errorf("group_by transformation requires config")
		}
		if _, ok := mapping.Config["group_field"].(string); !ok {
			return fmt.Errorf("group_by transformation requires group_field config")
		}

	case "sort":
		if mapping.Config == nil {
			return fmt.Errorf("sort transformation requires config")
		}
		if _, ok := mapping.Config["sort_field"].(string); !ok {
			return fmt.Errorf("sort transformation requires sort_field config")
		}

	case "direct":
		// Direct transformation requires no configuration

	default:
		return fmt.Errorf("unknown transformation type: %s", mapping.Transformation)
	}

	return nil
}

// TransformDataInPlace transforms input data in-place according to the provided configuration.
//
// NOTE: Despite the name, this method actually delegates to TransformData which creates
// a working copy of the input data and returns the complete dataset (original + transformations).
// The input data is NOT modified. This method exists for API compatibility but does not
// provide true in-place modification.
//
// In-Place Transformation Benefits:
//   - Memory Efficiency: Eliminates the need to create duplicate data structures
//   - Performance: Reduces data copying overhead for large datasets
//   - Type Preservation: Maintains original typed structures when possible
//   - Reference Integrity: Preserves object relationships and references
//
// Transformation Process:
// The method coordinates an in-place transformation workflow:
//  1. Configuration Validation: Comprehensive validation of all mapping rules and dependencies
//  2. Field Mapping Processing: Sequential processing of each configured field mapping
//     a. Input Data Extraction: Navigate to source data using path resolution
//     b. Transformation Application: Apply configured transformation with type-specific logic
//     c. In-Place Value Assignment: Modify the original data structure directly
//  3. Result Return: Return reference to the modified input structure
//
// Safety Considerations:
// The method includes comprehensive safety measures for in-place operations:
//   - Type compatibility validation between transformed values and target fields
//   - Field accessibility checks to ensure target fields are settable
//   - Automatic type conversion for compatible types
//   - Detailed error reporting for debugging transformation issues
//
// Business Context:
// In-place transformation is ideal for scenarios where:
//   - Memory usage is a concern for large datasets
//   - Performance is critical for high-throughput operations
//   - Type preservation is important for downstream processing
//   - The original data structure will not be needed after transformation
//
// Parameters:
//   - inputData: Data structure to transform in-place (can be maps, structs, or pointers)
//   - config: Complete mapping configuration with field mappings, lookup tables, and validation rules
//
// Returns:
//   - interface{}: Reference to the modified input data structure
//   - error: Detailed error information if any transformation step fails
//
// Common Use Cases:
//   - Transform large extraction datasets for memory efficiency
//   - Modify protobuf structures while preserving type information
//   - Update nested data structures without creating new objects
//   - Apply transformations to reduce memory footprint in ETL pipelines
//
// Example Usage:
//
//	engine := NewMappingEngine()
//
//	// Transform typed extraction data in-place
//	result, err := engine.TransformDataInPlace(extractionData, mappingConfig)
//	if err != nil {
//	    return fmt.Errorf("in-place transformation failed: %w", err)
//	}
//
//	// The original extractionData has been modified
//	// result points to the same modified structure
//
// Important Notes:
//   - Despite the method name, input data is NOT modified directly
//   - Returns a new data structure containing both original and transformed data
//   - The method delegates to TransformData internally
//   - For true in-place modification, custom implementation would be needed
func (engine *MappingEngine) TransformDataInPlace(inputData interface{}, config *MappingConfig) (interface{}, error) {
	// Validate input data
	if inputData == nil {
		return nil, fmt.Errorf("input data cannot be nil")
	}

	// Validate configuration first
	if configValidationError := engine.configLoader.ValidateConfig(config); configValidationError != nil {
		return nil, fmt.Errorf("configuration validation failed: %w", configValidationError)
	}

	// Process each field mapping in-place
	for _, mapping := range config.FieldMappings {
		if mappingError := engine.processFieldMappingInPlace(inputData, mapping, config); mappingError != nil {
			return nil, fmt.Errorf("failed to process field mapping '%s' in-place: %w", mapping.Name, mappingError)
		}
	}

	return inputData, nil
}

// TransformWithConfigInPlace transforms input data in-place using configuration from a JSON string.
//
// This convenience method provides a streamlined interface for in-place transformation operations
// when configuration data is available as a JSON string. It combines configuration loading,
// validation, and in-place transformation into a single operation.
//
// Processing Workflow:
// The method implements a two-step process for configuration-driven in-place transformation:
// 1. Configuration Loading: Parse JSON string into validated MappingConfig structure
// 2. In-Place Transformation: Apply the loaded configuration to modify input data directly
//
// Configuration Sources:
// This method is particularly useful when configuration data comes from:
//   - Environment variables containing JSON configuration strings
//   - API responses with transformation configuration data
//   - Command-line arguments providing configuration as string literals
//   - Configuration management systems that return string-based configurations
//   - Database records storing transformation configurations as JSON strings
//
// Parameters:
//   - inputData: Data structure to transform in-place (can be maps, structs, or pointers)
//   - configJSON: Valid JSON string containing complete transformation configuration
//
// Returns:
//   - interface{}: Reference to the modified input data structure
//   - error: Detailed error information if configuration loading or transformation fails
//
// Example Usage:
//
//	configJSON := `{
//	  "format_name": "IN_PLACE_TRANSFORMATION",
//	  "field_mappings": [
//	    {
//	      "name": "update_report_id",
//	      "input_path": "report.id",
//	      "transformation": "format",
//	      "output_path": "report.id",
//	      "config": {"format_pattern": "RPT-%s"}
//	    }
//	  ]
//	}`
//
//	result, err := engine.TransformWithConfigInPlace(extractionData, configJSON)
//	if err != nil {
//	    return fmt.Errorf("in-place transformation failed: %w", err)
//	}
func (engine *MappingEngine) TransformWithConfigInPlace(inputData interface{}, configJSON string) (interface{}, error) {
	// Load configuration from JSON
	config, configLoadError := engine.configLoader.LoadFromString(configJSON)
	if configLoadError != nil {
		return nil, fmt.Errorf("failed to load configuration: %w", configLoadError)
	}

	// Transform data in-place using loaded configuration
	return engine.TransformDataInPlace(inputData, config)
}

// TransformWithConfigFileInPlace transforms input data in-place using configuration loaded from a JSON file.
//
// This convenience method provides a streamlined interface for in-place transformation operations
// when configuration data is stored in the filesystem as JSON files. It combines file loading,
// configuration parsing, validation, and in-place transformation into a single operation.
//
// Processing Workflow:
// The method implements a comprehensive three-step process:
// 1. File Loading: Read JSON configuration data from the specified file path
// 2. Configuration Validation: Parse and validate the configuration structure and content
// 3. In-Place Transformation: Apply the validated configuration to modify input data directly
//
// File-Based Configuration Management:
// This method is particularly useful for scenarios involving:
//   - Static transformation configurations stored as configuration files
//   - Version-controlled transformation rules managed through Git or similar systems
//   - Environment-specific configurations organized in directory structures
//   - Production configuration files deployed with application releases
//
// Parameters:
//   - inputData: Data structure to transform in-place (can be maps, structs, or pointers)
//   - configFilePath: Absolute or relative path to JSON configuration file
//
// Returns:
//   - interface{}: Reference to the modified input data structure
//   - error: Detailed error information if file loading, configuration validation, or transformation fails
//
// Example Usage:
//
//	// Transform extraction data in-place using NIBRS configuration
//	result, err := engine.TransformWithConfigFileInPlace(
//	    extractionData,
//	    "configs/nibrs/nibrs_in_place_mapping.json",
//	)
//	if err != nil {
//	    return fmt.Errorf("NIBRS in-place transformation failed: %w", err)
//	}
//
//	// The original extractionData has been modified according to NIBRS requirements
func (engine *MappingEngine) TransformWithConfigFileInPlace(inputData interface{}, configFilePath string) (interface{}, error) {
	// Load configuration from file
	config, configLoadError := engine.configLoader.LoadFromFile(configFilePath)
	if configLoadError != nil {
		return nil, fmt.Errorf("failed to load configuration from file: %w", configLoadError)
	}

	// Transform data in-place using loaded configuration
	return engine.TransformDataInPlace(inputData, config)
}

// processFieldMappingInPlace processes a single field mapping in-place from input to output within the same data structure.
//
// This method provides in-place field mapping processing that modifies the input data structure
// directly rather than creating separate output structures. It's designed for memory-efficient
// transformation scenarios where the original data structure can be safely modified.
//
// Processing Strategy:
// The method implements a specialized in-place transformation workflow:
// 1. Input Value Extraction: Navigate to source data using path resolution
// 2. Transformation Application: Apply configured transformation with type-specific logic
// 3. In-Place Value Assignment: Use enhanced PathResolver to modify original structure directly
//
// Safety and Validation:
// The method includes comprehensive safety measures for in-place operations:
//   - Validates that the input and output paths can coexist safely
//   - Ensures type compatibility between transformed values and target fields
//   - Provides detailed error context for debugging transformation issues
//   - Maintains data integrity throughout the modification process
//
// Parameters:
//   - inputData: Source and target data structure for in-place modification
//   - mapping: Field mapping configuration specifying transformation rules
//   - config: Complete configuration context for lookup tables and validation
//
// Returns:
//   - error: Detailed error information if any transformation step fails
//
// Important Considerations:
//   - The input data structure is modified directly
//   - Input and output paths may reference the same or different locations
//   - Type safety is maintained through reflection-based validation
//   - Original field values are lost after transformation
func (engine *MappingEngine) processFieldMappingInPlace(
	inputData interface{},
	mapping FieldMapping,
	config *MappingConfig,
) error {
	// Step 1: Extract input value using path resolver
	inputValue, extractionError := engine.pathResolver.GetValue(inputData, mapping.InputPath)
	if extractionError != nil {
		return fmt.Errorf("failed to extract value from input path '%s': %w", mapping.InputPath, extractionError)
	}

	// Step 2: Prepare transformation configuration
	transformConfig := mapping.Config
	if transformConfig == nil {
		transformConfig = make(map[string]interface{})
	}

	// For lookup transformations, inject the actual lookup table data
	if mapping.Transformation == transformationLookup {
		if lookupTableName, tableNameExists := transformConfig["lookup_table"].(string); tableNameExists {
			if lookupTableData, tableDataExists := config.LookupTables[lookupTableName]; tableDataExists {
				transformConfig["lookup_table_data"] = lookupTableData
			}
		}
	}

	// Step 3: Apply transformation
	transformedValue, transformationError := engine.transformationEngine.ApplyTransformation(
		inputValue,
		mapping.Transformation,
		transformConfig,
	)
	if transformationError != nil {
		return fmt.Errorf("failed to apply transformation '%s': %w", mapping.Transformation, transformationError)
	}

	// Step 4: Store result in-place using enhanced path resolver
	if setValueError := engine.pathResolver.SetValueInPlace(inputData, mapping.OutputPath, transformedValue); setValueError != nil {
		return fmt.Errorf("failed to set value in-place at output path '%s': %w", mapping.OutputPath, setValueError)
	}

	return nil
}
