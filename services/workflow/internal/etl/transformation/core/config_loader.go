// Package core provides the foundational transformation infrastructure for converting
// extracted report data into various output formats (NIBRS XML, UCR, custom formats).
//
// The configuration loading system is the entry point for all transformation operations,
// responsible for parsing, validating, and preparing transformation configurations that
// define how Hero extraction data should be converted to specific output formats.
//
// Configuration Loading Philosophy:
// The configuration loader embodies the principle that ALL transformation behavior
// must be defined in JSON configuration files, never in Go code. This ensures that:
//   - New output formats can be added without code changes or deployments
//   - Field mappings can be modified by compliance teams without developer involvement
//   - Transformation logic can be debugged by examining configuration files
//   - Business rules are transparent and auditable by non-technical stakeholders
//
// Configuration Structure:
// Each transformation configuration consists of:
//   - Format identification (format_name) for tracking and organization
//   - Field mappings that define input-to-output data transformations
//   - Lookup tables for value mapping (enum conversion, code standardization)
//   - Validation rules ensuring data integrity and compliance requirements
//
// Validation Strategy:
// The loader implements comprehensive validation that catches configuration errors
// before they can affect production transformations:
//   - Structural validation: Required fields, proper JSON format, data types
//   - Reference validation: Lookup table dependencies, transformation type support
//   - Business logic validation: Transformation-specific configuration requirements
//   - Cross-reference validation: Ensuring referenced resources actually exist
//
// Error Handling Approach:
// Validation errors are collected and reported comprehensively rather than failing
// on the first error. This provides configuration authors with complete feedback
// about all issues that need to be addressed.
//
// Usage Patterns:
//
//	configLoader := NewConfigLoader()
//	config, err := configLoader.LoadFromFile("nibrs_mapping.json")
//	if err != nil {
//	    // Handle validation errors with detailed feedback
//	}
//	// Use validated configuration for transformation
package core

import (
	"encoding/json"
	"fmt"
	"os"
)

// ConfigLoader handles loading and validating transformation configurations from various sources.
//
// This component serves as the gateway for all transformation configuration operations,
// ensuring that only valid, well-formed configurations are used in the transformation
// pipeline. It provides multiple loading methods (file, string, bytes) while maintaining
// consistent validation across all input sources.
//
// Core Responsibilities:
//   - Parse JSON configuration from multiple input sources (files, strings, byte arrays)
//   - Validate configuration structure and content according to business rules
//   - Provide detailed error reporting for configuration issues
//   - Ensure transformation-specific configuration requirements are met
//   - Validate lookup table references and dependencies
//
// Business Context:
// Configuration loading is critical for ETL operations because invalid configurations
// can cause data corruption, compliance violations, or system failures. The loader
// acts as a quality gate, preventing problematic configurations from reaching the
// transformation engine.
//
// Thread Safety:
// ConfigLoader instances are thread-safe and can be used concurrently across
// multiple goroutines. Each validation operation is independent and stateless.
type ConfigLoader struct{}

// NewConfigLoader creates a new configuration loader instance.
//
// The returned loader is ready for immediate use and provides thread-safe
// configuration loading and validation capabilities. Multiple instances
// can be created if needed, though a single instance is typically sufficient
// for most applications.
//
// Returns:
//   - *ConfigLoader: A new configuration loader ready for use
//
// Example Usage:
//
//	loader := NewConfigLoader()
//	config, err := loader.LoadFromFile("transformation_config.json")
func NewConfigLoader() *ConfigLoader {
	return &ConfigLoader{}
}

// LoadFromFile loads and validates a transformation configuration from a JSON file.
//
// This method provides the primary interface for loading configuration files from
// the filesystem. It handles all aspects of file loading, JSON parsing, and
// comprehensive validation to ensure the configuration is ready for use in
// transformation operations.
//
// File Loading Process:
// 1. Read the entire file from the filesystem using the provided path
// 2. Parse the JSON content into a MappingConfig structure
// 3. Perform comprehensive validation of all configuration elements
// 4. Return the validated configuration ready for transformation use
//
// Validation Scope:
// The method validates all aspects of the configuration including:
//   - Required fields presence and format
//   - Field mapping completeness and correctness
//   - Transformation type support and configuration
//   - Lookup table definitions and references
//   - Cross-references between field mappings and lookup tables
//
// Error Handling:
// Detailed error messages are provided for all failure scenarios:
//   - File system errors (file not found, permission issues, etc.)
//   - JSON parsing errors (syntax errors, invalid structure)
//   - Configuration validation errors (missing fields, invalid references)
//
// Parameters:
//   - filePath: Absolute or relative path to the JSON configuration file
//
// Returns:
//   - *MappingConfig: Fully validated configuration ready for transformation use
//   - error: Detailed error information if loading or validation fails
//
// Common Use Cases:
//   - Loading NIBRS transformation configurations for compliance reporting
//   - Loading custom format configurations for agency-specific requirements
//   - Loading test configurations for validation and debugging
//
// Example Usage:
//
//	loader := NewConfigLoader()
//	config, err := loader.LoadFromFile("configs/nibrs_mapping.json")
//	if err != nil {
//	    log.Fatalf("Failed to load configuration: %v", err)
//	}
//	// Use validated configuration for transformation operations
//
// File Format Example:
//
//	{
//	  "format_name": "NIBRS_XML_2024",
//	  "field_mappings": [
//	    {
//	      "name": "victim_sex_mapping",
//	      "input_path": "entities[*].demographics.sex",
//	      "transformation": transformationLookup,
//	      "output_path": "victims[*].sex_code",
//	      "config": {"lookup_table": "sex_codes"}
//	    }
//	  ],
//	  "lookup_tables": {
//	    "sex_codes": {"SEX_MALE": "M", "SEX_FEMALE": "F"}
//	  }
//	}
func (loader *ConfigLoader) LoadFromFile(filePath string) (*MappingConfig, error) {
	// Read the file from filesystem with detailed error context
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, fmt.Errorf("failed to read config file '%s': %w", filePath, err)
	}

	// Parse JSON content into configuration structure
	var config MappingConfig
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse JSON config file '%s': %w", filePath, err)
	}

	// Perform comprehensive validation of all configuration elements
	if err := loader.ValidateConfig(&config); err != nil {
		return nil, fmt.Errorf("invalid configuration in file '%s': %w", filePath, err)
	}

	return &config, nil
}

// LoadFromJSON loads and validates a transformation configuration from JSON byte data.
//
// This method provides configuration loading for scenarios where JSON data is already
// available in memory as a byte array. It's commonly used when configuration data
// comes from APIs, databases, or other non-file sources.
//
// Processing Steps:
// 1. Parse the provided JSON byte data into a MappingConfig structure
// 2. Perform the same comprehensive validation as file-based loading
// 3. Return the validated configuration ready for transformation operations
//
// Use Cases:
//   - Loading configurations from HTTP API responses
//   - Processing configurations stored in databases
//   - Loading configurations from embedded resources
//   - Processing configurations received over network protocols
//   - Testing with programmatically generated configurations
//
// Parameters:
//   - jsonData: Byte array containing valid JSON configuration data
//
// Returns:
//   - *MappingConfig: Fully validated configuration ready for transformation use
//   - error: Detailed error information if parsing or validation fails
//
// Example Usage:
//
//	configBytes := []byte(`{
//	  "format_name": "TEST_FORMAT",
//	  "field_mappings": [...]
//	}`)
//	config, err := loader.LoadFromJSON(configBytes)
//	if err != nil {
//	    return fmt.Errorf("invalid configuration: %w", err)
//	}
func (loader *ConfigLoader) LoadFromJSON(jsonData []byte) (*MappingConfig, error) {
	// Parse JSON byte data into configuration structure
	var config MappingConfig
	if err := json.Unmarshal(jsonData, &config); err != nil {
		return nil, fmt.Errorf("failed to parse JSON config: %w", err)
	}

	// Apply the same comprehensive validation as file-based loading
	if err := loader.ValidateConfig(&config); err != nil {
		return nil, fmt.Errorf("invalid configuration: %w", err)
	}

	return &config, nil
}

// LoadFromString loads and validates a transformation configuration from a JSON string.
//
// This convenience method provides configuration loading for scenarios where JSON
// configuration data is available as a string. It internally converts the string
// to bytes and delegates to LoadFromJSON for consistent processing.
//
// Common Use Cases:
//   - Loading configurations from environment variables
//   - Processing configuration strings from command-line arguments
//   - Testing with string literals in test cases
//   - Loading configurations from configuration management systems
//   - Processing user-provided configuration strings in web interfaces
//
// Parameters:
//   - jsonString: String containing valid JSON configuration data
//
// Returns:
//   - *MappingConfig: Fully validated configuration ready for transformation use
//   - error: Detailed error information if parsing or validation fails
//
// Example Usage:
//
//	configStr := `{"format_name": "NIBRS_XML_2024", "field_mappings": [...]}`
//	config, err := loader.LoadFromString(configStr)
//	if err != nil {
//	    log.Printf("Configuration validation failed: %v", err)
//	    return
//	}
//	// Use validated configuration
func (loader *ConfigLoader) LoadFromString(jsonString string) (*MappingConfig, error) {
	return loader.LoadFromJSON([]byte(jsonString))
}

// ValidateConfig performs comprehensive validation of a transformation configuration.
//
// This method serves as the central validation engine for all transformation configurations,
// ensuring that configurations meet both structural and business requirements before being
// used in production transformation operations. It implements a multi-layered validation
// approach that catches errors early and provides detailed feedback.
//
// Validation Layers:
// 1. Structural Validation: Ensures required fields are present and properly formatted
// 2. Reference Validation: Verifies that all references (lookup tables, etc.) exist
// 3. Business Logic Validation: Checks transformation-specific configuration requirements
// 4. Cross-Reference Validation: Ensures consistency between related configuration elements
//
// Validation Strategy:
// The method collects ALL validation errors rather than failing on the first error.
// This approach provides configuration authors with complete feedback about all issues
// that need to be addressed, improving the configuration development experience.
//
// Business Context:
// Configuration validation is critical for preventing:
//   - Data corruption from malformed transformations
//   - Runtime failures due to missing configuration elements
//   - Compliance violations from incorrect field mappings
//   - Production incidents caused by invalid transformation logic
//
// Validation Rules Applied:
//   - format_name: Required, must be non-empty string for identification
//   - field_mappings: Required, must contain at least one mapping definition
//   - Each field mapping: Must have name, input_path, transformation, output_path
//   - Transformation types: Must be supported by the transformation engine
//   - Lookup tables: Referenced tables must exist and contain mapping data
//   - Transformation configs: Must meet type-specific configuration requirements
//
// Error Reporting:
// Validation errors are collected into a structured error response that includes:
//   - Field path information for precise error location
//   - Descriptive error messages for easy understanding
//   - Error context (field values, expected formats, etc.)
//
// Parameters:
//   - config: MappingConfig to validate against all business and structural rules
//
// Returns:
//   - error: ConfigValidationError containing all validation issues found, or nil if valid
//
// Example Usage:
//
//	if err := loader.ValidateConfig(config); err != nil {
//	    if validationErr, ok := err.(*ConfigValidationError); ok {
//	        for _, fieldErr := range validationErr.GetErrors() {
//	            log.Printf("Field %s: %s", fieldErr.Field, fieldErr.Message)
//	        }
//	    }
//	    return fmt.Errorf("configuration validation failed: %w", err)
//	}
//	// Configuration is valid and ready for use
func (loader *ConfigLoader) ValidateConfig(config *MappingConfig) error {
	var errors []ValidationError

	// Validate format name
	if config.FormatName == "" {
		errors = append(errors, ValidationError{
			Field:   "format_name",
			Message: "format_name is required and cannot be empty",
		})
	}

	// Validate field mappings
	if len(config.FieldMappings) == 0 {
		errors = append(errors, ValidationError{
			Field:   "field_mappings",
			Message: "field_mappings is required and cannot be empty",
		})
	}

	// Validate each field mapping
	for mappingIndex, mapping := range config.FieldMappings {
		fieldErrors := loader.validateFieldMapping(mapping, mappingIndex)
		errors = append(errors, fieldErrors...)
	}

	// Validate lookup tables (if any lookup transformations are used)
	usedLookupTables := loader.getUsedLookupTables(config.FieldMappings)
	for _, tableName := range usedLookupTables {
		if _, exists := config.LookupTables[tableName]; !exists {
			errors = append(errors, ValidationError{
				Field:   "lookup_tables",
				Message: fmt.Sprintf("lookup table '%s' is used but not defined", tableName),
				Value:   tableName,
			})
		}
	}

	// Validate lookup table contents
	for tableName, table := range config.LookupTables {
		if len(table) == 0 {
			errors = append(errors, ValidationError{
				Field:   "lookup_tables",
				Message: fmt.Sprintf("lookup table '%s' is empty", tableName),
				Value:   tableName,
			})
		}
	}

	// Validate transformation mode if specified
	if config.TransformationMode != "" {
		validModes := []string{"copy", "in_place"}
		if !loader.isStringInSlice(config.TransformationMode, validModes) {
			errors = append(errors, ValidationError{
				Field:   "transformation_mode",
				Message: fmt.Sprintf("invalid transformation mode: %s (must be 'copy' or 'in_place')", config.TransformationMode),
				Value:   config.TransformationMode,
			})
		}
	}

	// Return validation errors if any
	if len(errors) > 0 {
		return &ConfigValidationError{Errors: errors}
	}

	return nil
}

// validateFieldMapping validates a single field mapping
func (loader *ConfigLoader) validateFieldMapping(mapping FieldMapping, index int) []ValidationError {
	var errors []ValidationError
	prefix := fmt.Sprintf("field_mappings[%d]", index)

	// Validate name
	if mapping.Name == "" {
		errors = append(errors, ValidationError{
			Field:   prefix + ".name",
			Message: "name is required and cannot be empty",
		})
	}

	// Validate input path
	if mapping.InputPath == "" {
		errors = append(errors, ValidationError{
			Field:   prefix + ".input_path",
			Message: "input_path is required and cannot be empty",
		})
	}

	// Validate transformation type
	if mapping.Transformation == "" {
		errors = append(errors, ValidationError{
			Field:   prefix + ".transformation",
			Message: "transformation is required and cannot be empty",
		})
	}

	// Validate transformation type is supported
	supportedTransformations := []string{
		"direct", transformationLookup, transformationFilter, "format", "date_format",
		"add_sequence", "group_by", "sort",
	}
	if !loader.isStringInSlice(mapping.Transformation, supportedTransformations) {
		errors = append(errors, ValidationError{
			Field:   prefix + ".transformation",
			Message: fmt.Sprintf("unsupported transformation type: %s", mapping.Transformation),
			Value:   mapping.Transformation,
		})
	}

	// Validate output path
	if mapping.OutputPath == "" {
		errors = append(errors, ValidationError{
			Field:   prefix + ".output_path",
			Message: "output_path is required and cannot be empty",
		})
	}

	// Validate transformation-specific configuration
	transformationErrors := loader.validateTransformationConfig(mapping, index)
	errors = append(errors, transformationErrors...)

	return errors
}

const (
	transformationLookup = "lookup"
	transformationFilter = "filter"
)

// validateTransformationConfig validates transformation-specific configuration
func (loader *ConfigLoader) validateTransformationConfig(mapping FieldMapping, index int) []ValidationError {
	var errors []ValidationError
	prefix := fmt.Sprintf("field_mappings[%d].config", index)

	switch mapping.Transformation {
	case transformationLookup:
		if mapping.Config == nil {
			errors = append(errors, ValidationError{
				Field:   prefix,
				Message: "lookup transformation requires config",
			})
		} else {
			if _, exists := mapping.Config["lookup_table"]; !exists {
				errors = append(errors, ValidationError{
					Field:   prefix + ".lookup_table",
					Message: "lookup transformation requires lookup_table",
				})
			}
		}

	case transformationFilter:
		if mapping.Config == nil {
			errors = append(errors, ValidationError{
				Field:   prefix,
				Message: "filter transformation requires config",
			})
		} else {
			if _, exists := mapping.Config["field"]; !exists {
				errors = append(errors, ValidationError{
					Field:   prefix + ".field",
					Message: "filter transformation requires field",
				})
			}
			if _, exists := mapping.Config["operator"]; !exists {
				errors = append(errors, ValidationError{
					Field:   prefix + ".operator",
					Message: "filter transformation requires operator",
				})
			}
			if _, exists := mapping.Config["value"]; !exists {
				errors = append(errors, ValidationError{
					Field:   prefix + ".value",
					Message: "filter transformation requires value",
				})
			}
		}

	case "format":
		if mapping.Config == nil {
			errors = append(errors, ValidationError{
				Field:   prefix,
				Message: "format transformation requires config",
			})
		} else {
			if _, exists := mapping.Config["format_pattern"]; !exists {
				errors = append(errors, ValidationError{
					Field:   prefix + ".format_pattern",
					Message: "format transformation requires format_pattern",
				})
			}
		}

	case "date_format":
		if mapping.Config == nil {
			errors = append(errors, ValidationError{
				Field:   prefix,
				Message: "date_format transformation requires config",
			})
		} else {
			if _, exists := mapping.Config["date_format"]; !exists {
				errors = append(errors, ValidationError{
					Field:   prefix + ".date_format",
					Message: "date_format transformation requires date_format",
				})
			}
		}

	case "add_sequence":
		if mapping.Config == nil {
			errors = append(errors, ValidationError{
				Field:   prefix,
				Message: "add_sequence transformation requires config",
			})
		} else {
			if _, exists := mapping.Config["sequence_field"]; !exists {
				errors = append(errors, ValidationError{
					Field:   prefix + ".sequence_field",
					Message: "add_sequence transformation requires sequence_field",
				})
			}
		}

	case "group_by":
		if mapping.Config == nil {
			errors = append(errors, ValidationError{
				Field:   prefix,
				Message: "group_by transformation requires config",
			})
		} else {
			if _, exists := mapping.Config["group_field"]; !exists {
				errors = append(errors, ValidationError{
					Field:   prefix + ".group_field",
					Message: "group_by transformation requires group_field",
				})
			}
		}

	case "sort":
		if mapping.Config == nil {
			errors = append(errors, ValidationError{
				Field:   prefix,
				Message: "sort transformation requires config",
			})
		} else {
			if _, exists := mapping.Config["sort_field"]; !exists {
				errors = append(errors, ValidationError{
					Field:   prefix + ".sort_field",
					Message: "sort transformation requires sort_field",
				})
			}
		}

	case "direct":
		// Direct transformation doesn't require config
		break

	default:
		errors = append(errors, ValidationError{
			Field:   prefix,
			Message: fmt.Sprintf("unknown transformation type: %s", mapping.Transformation),
			Value:   mapping.Transformation,
		})
	}

	return errors
}

// getUsedLookupTables extracts all lookup table names used in field mappings
func (loader *ConfigLoader) getUsedLookupTables(mappings []FieldMapping) []string {
	var tables []string
	seen := make(map[string]bool)

	for _, mapping := range mappings {
		if mapping.Transformation == transformationLookup && mapping.Config != nil {
			if tableName, exists := mapping.Config["lookup_table"]; exists {
				if tableNameStr, ok := tableName.(string); ok {
					if !seen[tableNameStr] {
						tables = append(tables, tableNameStr)
						seen[tableNameStr] = true
					}
				}
			}
		}
	}

	return tables
}

// isStringInSlice checks if a string is in a slice of strings
func (loader *ConfigLoader) isStringInSlice(str string, slice []string) bool {
	for _, sliceElement := range slice {
		if sliceElement == str {
			return true
		}
	}
	return false
}

// ConfigValidationError represents multiple validation errors
type ConfigValidationError struct {
	Errors []ValidationError
}

// Error implements the error interface
func (e *ConfigValidationError) Error() string {
	if len(e.Errors) == 1 {
		return fmt.Sprintf("config validation failed: %s", e.Errors[0].Message)
	}
	return fmt.Sprintf("config validation failed with %d errors", len(e.Errors))
}

// GetErrors returns all validation errors
func (e *ConfigValidationError) GetErrors() []ValidationError {
	return e.Errors
}
