// Package core provides the foundational transformation infrastructure for converting
// extracted report data into various output formats (NIBRS XML, UCR, custom formats).
//
// The path resolution system enables sophisticated data navigation through complex
// nested data structures using intuitive path expressions. This system forms the
// foundation of the configuration-driven transformation approach by allowing
// transformations to specify exactly where to find input data and where to place
// output data without writing custom code.
//
// Path Resolution Philosophy:
// The path resolver embodies the principle of generic, reusable data navigation
// that works with any data structure. It provides a simple, SQL-like syntax for
// accessing nested data while maintaining high performance and reliability.
//
// Supported Path Syntax:
//   - Simple fields: "field_name"
//   - Nested objects: "parent.child.grandchild"
//   - Array indexing: "array[0]", "array[1]"
//   - Wildcard expansion: "array[*]" (processes all array elements)
//   - Complex navigation: "entities[*].demographics.sex"
//   - Mixed patterns: "reports[0].sections[*].data.field"
//
// Performance Characteristics:
// The resolver is optimized for transformation workloads with:
//   - Efficient path parsing and caching
//   - Minimal memory allocation during navigation
//   - Fast reflection-based field access
//   - Graceful handling of missing or null data
//
// Error Handling Strategy:
// The resolver provides detailed error information for debugging:
//   - Path parsing errors with syntax feedback
//   - Navigation errors with context about where navigation failed
//   - Type mismatch errors when operations don't match data types
//   - Missing field errors with suggestions for similar field names
//
// Thread Safety:
// PathResolver instances are stateless and thread-safe. Multiple goroutines
// can use the same resolver instance concurrently without synchronization.
//
// Usage Patterns:
//
//	resolver := NewPathResolver()
//
//	// Extract data from complex structures
//	value, err := resolver.GetValue(data, "entities[*].demographics.age")
//
//	// Set values in output structures
//	err = resolver.SetValue(output, "victims[0].age", value)
package core

import (
	"fmt"
	"reflect"
	"strconv"
	"strings"
)

const (
	segmentTypeField  = "field"
	segmentTypeIndex  = "index"
	segmentTypeFilter = "filter"
)

// PathResolver provides sophisticated navigation capabilities for complex data structures.
//
// This component enables the transformation system to access and modify data at any
// depth within nested structures using simple, intuitive path expressions. It forms
// the core navigation engine that allows configuration-driven transformations to
// specify data locations without requiring custom code.
//
// Core Capabilities:
//   - Parse and navigate complex path expressions with mixed syntax
//   - Handle arrays, objects, and primitive values uniformly
//   - Support wildcard operations for processing multiple array elements
//   - Create nested structures automatically when setting values
//   - Provide detailed error information for debugging navigation issues
//
// Business Context:
// Path resolution is essential for transformation operations because report data
// has complex, nested structures. The resolver allows transformations to:
//   - Extract specific fields from deeply nested extraction data
//   - Place transformed values at precise locations in output structures
//   - Process arrays of entities, assets, or other repeated data
//   - Handle optional fields and missing data gracefully
//
// Path Expression Examples:
//   - "report.id" → Access report ID field
//   - "entities[0].demographics.sex" → First entity's sex field
//   - "entities[*].name.first" → All entities' first names
//   - "sections[*].data.field" → Field from all section data
//   - "metadata.extraction_timestamp" → Extraction timestamp
//
// Type Support:
// The resolver works with any Go data type including:
//   - map[string]interface{} (JSON-like structures)
//   - Structs with exported fields
//   - Arrays and slices of any type
//   - Pointers (automatically dereferenced)
//   - Primitive types (strings, numbers, booleans)
//
// Thread Safety:
// PathResolver instances are completely stateless and thread-safe. The same
// instance can be used concurrently across multiple goroutines without any
// synchronization requirements.
type PathResolver struct{}

// NewPathResolver creates a new path resolver instance ready for navigation operations.
//
// The returned resolver is stateless and thread-safe, making it suitable for
// concurrent use across multiple transformation operations. A single resolver
// instance can handle unlimited navigation requests efficiently.
//
// Returns:
//   - *PathResolver: A new path resolver ready for immediate use
//
// Example Usage:
//
//	resolver := NewPathResolver()
//	value, err := resolver.GetValue(data, "entities[*].demographics.sex")
func NewPathResolver() *PathResolver {
	return &PathResolver{}
}

// GetValue extracts values from complex data structures using intuitive path expressions.
//
// This method provides the primary interface for data extraction in transformation
// operations. It supports sophisticated navigation patterns including nested object
// access, array indexing, and wildcard expansion for processing multiple elements.
//
// Path Expression Syntax:
//   - Simple field access: "field_name"
//   - Nested navigation: "parent.child.grandchild"
//   - Array indexing: "array[0]", "array[5]"
//   - Wildcard expansion: "array[*]" (returns all elements)
//   - Complex patterns: "entities[*].demographics.sex"
//   - Mixed operations: "reports[0].sections[*].data.value"
//
// Wildcard Behavior:
// When a wildcard (*) is encountered, the resolver processes all array elements
// and returns an array of results. If additional path segments follow the wildcard,
// they are applied to each array element, and the results are collected.
//
// Type Handling:
// The method works with various data types:
//   - map[string]interface{}: Standard JSON-like structures
//   - Go structs: Accesses exported fields by name (case-insensitive)
//   - Arrays/slices: Supports indexing and wildcard operations
//   - Pointers: Automatically dereferenced for transparent access
//   - Nil values: Handled gracefully with appropriate error messages
//
// Error Conditions:
//   - Invalid path syntax (malformed array notation, empty segments)
//   - Missing fields or array indices out of bounds
//   - Type mismatches (trying to index non-arrays, access fields on primitives)
//   - Nil pointer dereference attempts
//
// Parameters:
//   - data: The data structure to navigate (can be any type)
//   - path: Path expression specifying the data location to extract
//
// Returns:
//   - interface{}: The extracted value (single value or array for wildcards)
//   - error: Detailed error information if navigation fails
//
// Common Use Cases:
//   - Extract victim demographics: "entities[*].demographics.sex"
//   - Get report metadata: "metadata.extraction_timestamp"
//   - Access array elements: "assets[0].name"
//   - Process all sections: "sections[*].type"
//
// Example Usage:
//
//	// Extract all entity ages
//	ages, err := resolver.GetValue(data, "entities[*].demographics.age")
//	if err != nil {
//	    return fmt.Errorf("failed to extract ages: %w", err)
//	}
//
//	// Extract specific report field
//	reportId, err := resolver.GetValue(data, "reports[0].id")
//	if err != nil {
//	    return fmt.Errorf("failed to extract report ID: %w", err)
//	}
func (r *PathResolver) GetValue(data interface{}, path string) (interface{}, error) {
	if data == nil {
		return nil, fmt.Errorf("data is nil")
	}

	if path == "" {
		return data, nil
	}

	segments, err := r.parsePath(path)
	if err != nil {
		return nil, fmt.Errorf("failed to parse path '%s': %w", path, err)
	}

	return r.navigatePath(data, segments)
}

// SetValue places values at specific locations in data structures using path expressions.
//
// This method provides the output generation interface for transformation operations,
// allowing transformed values to be placed at precise locations within output data
// structures. It automatically creates nested structures as needed, enabling
// transformations to build complex output formats from simple configurations.
//
// Structure Creation Behavior:
// When setting values at paths that don't exist, the resolver automatically creates
// the necessary intermediate structures:
//   - Missing object fields are created as empty maps
//   - Nested paths are built incrementally from root to target
//   - Only map[string]interface{} structures are currently supported for creation
//
// Supported Operations:
//   - Set simple field values: "field_name"
//   - Create nested structures: "parent.child.grandchild"
//   - Currently limited to map[string]interface{} target structures
//   - Automatic creation of intermediate path segments
//
// Current Limitations:
//   - Array operations (indexing, wildcards) are not yet supported for setting
//   - Only works with map[string]interface{} as the target data type
//   - Cannot set values in struct fields or other complex types
//   - Future versions will expand support for more complex setting operations
//
// Business Context:
// Value setting is essential for transformation output generation. It allows
// transformations to construct properly formatted output structures by placing
// transformed field values at their correct locations according to output format
// specifications (NIBRS XML, UCR, custom formats).
//
// Parameters:
//   - data: Target data structure (must be map[string]interface{})
//   - path: Path expression specifying where to place the value
//   - value: The value to place at the specified path location
//
// Returns:
//   - error: Detailed error information if path creation or value setting fails
//
// Error Conditions:
//   - Invalid path expressions (empty path, malformed syntax)
//   - Unsupported target data types (non-map structures)
//   - Path conflicts with existing data structures
//   - Intermediate path creation failures
//
// Common Use Cases:
//   - Set NIBRS victim data: "victims[0].sex_code"
//   - Create report metadata: "metadata.transformation_timestamp"
//   - Build nested output: "administrative.incident.number"
//   - Set lookup results: "offenses[0].ucrb_code"
//
// Example Usage:
//
//	output := make(map[string]interface{})
//
//	// Create nested structure and set value
//	err := resolver.SetValue(output, "victim.demographics.sex", "M")
//	if err != nil {
//	    return fmt.Errorf("failed to set victim sex: %w", err)
//	}
//
//	// Result: output["victim"]["demographics"]["sex"] = "M"
//	// with all intermediate maps created automatically
func (r *PathResolver) SetValue(data interface{}, path string, value interface{}) error {
	if path == "" {
		return fmt.Errorf("path cannot be empty")
	}

	segments, err := r.parsePath(path)
	if err != nil {
		return fmt.Errorf("failed to parse path '%s': %w", path, err)
	}

	return r.setValueAtPath(data, segments, value)
}

// SetValueInPlace sets a value at a specific path in data structures, supporting both maps and typed structs.
//
// This method extends the original SetValue functionality to support in-place modification
// of typed data structures, including protobuf types and Go structs. It can modify the
// original data structure directly without creating new objects, providing better memory
// efficiency and performance for large datasets.
//
// Supported Data Types:
//   - map[string]interface{}: Standard JSON-like structures (existing functionality)
//   - Go structs: Modifies exported fields using reflection
//   - Pointers to structs: Automatically dereferences and modifies the target struct
//   - Nested structures: Supports complex path navigation with in-place modification
//
// Path Navigation Capabilities:
//   - Simple field access: "field_name"
//   - Nested objects: "parent.child.grandchild"
//   - Array indexing: "array[0]", "array[1]" (for slice/array fields)
//   - Mixed patterns: "report.sections[0].data"
//
// Safety Considerations:
// The method includes safety checks to prevent corruption of source data:
//   - Validates that target fields are settable (exported, not read-only)
//   - Ensures type compatibility between value and target field
//   - Provides detailed error messages for debugging
//   - Maintains type safety throughout the modification process
//
// Parameters:
//   - data: Target data structure to modify (can be maps, structs, or pointers)
//   - path: Path expression specifying where to place the value
//   - value: The value to place at the specified path location
//
// Returns:
//   - error: Detailed error information if path navigation or value setting fails
//
// Error Conditions:
//   - Invalid path expressions or unreachable paths
//   - Type mismatches between value and target field
//   - Attempts to modify unexported or read-only fields
//   - Navigation failures in complex nested structures
//
// Example Usage:
//
//	// Modify protobuf struct in place
//	report := &reportsv2.Report{Id: "123"}
//	err := resolver.SetValueInPlace(report, "id", "new-456")
//
//	// Modify nested struct field
//	err := resolver.SetValueInPlace(extractedData, "report.sections[0].title", "Updated Title")
//
//	// Modify map field (backward compatible)
//	data := map[string]interface{}{"field": "value"}
//	err := resolver.SetValueInPlace(data, "field", "new-value")
func (r *PathResolver) SetValueInPlace(data interface{}, path string, value interface{}) error {
	if path == "" {
		return fmt.Errorf("path cannot be empty")
	}

	segments, pathParseError := r.parsePath(path)
	if pathParseError != nil {
		return fmt.Errorf("failed to parse path '%s': %w", path, pathParseError)
	}

	return r.setValueAtPathInPlace(data, segments, value)
}

// parsePath parses a path string into individual segments
func (r *PathResolver) parsePath(path string) ([]PathSegment, error) {
	var segments []PathSegment

	// Split by dots, but handle array notation
	parts := r.splitPath(path)

	for _, part := range parts {
		if part == "" {
			continue
		}

		// Check if this part contains array notation
		if strings.Contains(part, "[") {
			// Handle array notation like "field[0]" or "field[*]"
			fieldSegment, arraySegment, err := r.parseArrayNotation(part)
			if err != nil {
				return nil, err
			}

			if fieldSegment != nil {
				segments = append(segments, *fieldSegment)
			}
			segments = append(segments, *arraySegment)
		} else {
			// Simple field access
			segments = append(segments, PathSegment{
				Type:       "field",
				Value:      part,
				IsWildcard: false,
			})
		}
	}

	return segments, nil
}

// splitPath splits a path by dots while preserving array notation
func (r *PathResolver) splitPath(path string) []string {
	// Split by dots but preserve array notation by manually parsing
	var parts []string
	var current strings.Builder
	inBrackets := false

	for _, char := range path {
		switch char {
		case '[':
			inBrackets = true
			current.WriteRune(char)
		case ']':
			inBrackets = false
			current.WriteRune(char)
		case '.':
			if inBrackets {
				current.WriteRune(char)
			} else {
				parts = append(parts, current.String())
				current.Reset()
			}
		default:
			current.WriteRune(char)
		}
	}

	if current.Len() > 0 {
		parts = append(parts, current.String())
	}

	return parts
}

// parseArrayNotation parses array notation like "field[0]" or "field[*]"
func (r *PathResolver) parseArrayNotation(part string) (*PathSegment, *PathSegment, error) {
	if !strings.Contains(part, "[") || !strings.Contains(part, "]") {
		return nil, nil, fmt.Errorf("invalid array notation: %s", part)
	}

	// Extract field name and array index
	openBracket := strings.Index(part, "[")
	closeBracket := strings.Index(part, "]")

	if openBracket >= closeBracket {
		return nil, nil, fmt.Errorf("invalid array notation: %s", part)
	}

	fieldName := part[:openBracket]
	arrayIndex := part[openBracket+1 : closeBracket]

	var fieldSegment *PathSegment
	if fieldName != "" {
		fieldSegment = &PathSegment{
			Type:       "field",
			Value:      fieldName,
			IsWildcard: false,
		}
	}

	var arraySegment *PathSegment
	switch {
	case arrayIndex == "*":
		arraySegment = &PathSegment{
			Type:       "wildcard",
			Value:      "*",
			IsWildcard: true,
		}
	case strings.HasPrefix(arrayIndex, "?"):
		// Handle JSONPath filter expression
		filter, err := r.parseJSONPathFilter(arrayIndex)
		if err != nil {
			return nil, nil, fmt.Errorf("failed to parse JSONPath filter '%s': %w", arrayIndex, err)
		}
		arraySegment = &PathSegment{
			Type:             "filter",
			Value:            arrayIndex,
			IsWildcard:       false, // Filter segments are not wildcards
			FilterExpression: filter,
		}
	default:
		// Try to parse as integer index
		if _, err := strconv.Atoi(arrayIndex); err != nil {
			return nil, nil, fmt.Errorf("invalid array index '%s': %w", arrayIndex, err)
		}
		arraySegment = &PathSegment{
			Type:       "index",
			Value:      arrayIndex,
			IsWildcard: false,
		}
	}

	return fieldSegment, arraySegment, nil
}

// parseJSONPathFilter parses a JSONPath filter expression into a structured format
//
// JSONPath filters follow the pattern: ?(@.field operator value)
// Examples:
//   - ?(@.entity.entity_type == 'ENTITY_TYPE_PERSON')
//   - ?(@.data.age >= 18)
//   - ?(@.role != 'SUSPECT')
func (r *PathResolver) parseJSONPathFilter(filterExpr string) (*JSONPathFilter, error) {
	// Remove the leading '?' and trim whitespace
	if !strings.HasPrefix(filterExpr, "?") {
		return nil, fmt.Errorf("filter expression must start with '?'")
	}

	expr := strings.TrimSpace(filterExpr[1:])

	// Remove outer parentheses if present
	if strings.HasPrefix(expr, "(") && strings.HasSuffix(expr, ")") {
		expr = strings.TrimSpace(expr[1 : len(expr)-1])
	}

	// Find the operator by looking for comparison operators
	operators := []string{"==", "!=", ">=", "<=", ">", "<", "contains", "starts_with", "ends_with", "="}

	var operator string
	var operatorIndex int = -1

	// Find the first operator in the expression
	for _, op := range operators {
		if index := strings.Index(expr, op); index != -1 {
			// Make sure this is a whole operator, not part of a string literal
			if r.isValidOperatorPosition(expr, index, op) {
				if operatorIndex == -1 || index < operatorIndex {
					operator = op
					operatorIndex = index
				}
			}
		}
	}

	if operatorIndex == -1 {
		return nil, fmt.Errorf("no valid operator found in filter expression")
	}

	// Split the expression into field path and value
	fieldPart := strings.TrimSpace(expr[:operatorIndex])
	valuePart := strings.TrimSpace(expr[operatorIndex+len(operator):])

	// Parse the field path (remove leading @. if present)
	if strings.HasPrefix(fieldPart, "@.") {
		fieldPart = fieldPart[2:]
	} else if fieldPart == "@" {
		return nil, fmt.Errorf("filter must specify a field path after @")
	}

	// Parse the value (handle string literals, numbers, and booleans)
	value := r.parseFilterValue(valuePart)

	return &JSONPathFilter{
		FieldPath: fieldPart,
		Operator:  operator,
		Value:     value,
	}, nil
}

// isValidOperatorPosition checks if an operator at the given position is valid
// (not inside a string literal)
func (r *PathResolver) isValidOperatorPosition(expr string, index int, _ string) bool {
	// Simple check: count single quotes before the operator position
	// If odd number of quotes, we're inside a string literal
	quotes := 0
	for i := 0; i < index; i++ {
		if expr[i] == '\'' && (i == 0 || expr[i-1] != '\\') {
			quotes++
		}
	}
	return quotes%2 == 0 // Even number of quotes means we're not in a string
}

// parseFilterValue parses a filter value from string representation to appropriate Go type
func (r *PathResolver) parseFilterValue(valueStr string) interface{} {
	valueStr = strings.TrimSpace(valueStr)

	// Handle string literals (single or double quotes)
	if (strings.HasPrefix(valueStr, "'") && strings.HasSuffix(valueStr, "'")) ||
		(strings.HasPrefix(valueStr, "\"") && strings.HasSuffix(valueStr, "\"")) {
		// Remove quotes and handle escaped quotes
		return strings.ReplaceAll(valueStr[1:len(valueStr)-1], "\\'", "'")
	}

	// Handle boolean values
	if valueStr == "true" {
		return true
	}
	if valueStr == "false" {
		return false
	}

	// Handle null
	if valueStr == "null" {
		return nil
	}

	// Try to parse as integer
	if intVal, err := strconv.Atoi(valueStr); err == nil {
		return intVal
	}

	// Try to parse as float
	if floatVal, err := strconv.ParseFloat(valueStr, 64); err == nil {
		return floatVal
	}

	// If nothing else matches, treat as unquoted string
	return valueStr
}

// navigateFilter filters an array based on a JSONPath filter expression
func (r *PathResolver) navigateFilter(data interface{}, filter *JSONPathFilter) (interface{}, error) {
	if filter == nil {
		return nil, fmt.Errorf("filter expression is nil")
	}

	// Get the slice/array value using reflection
	value := reflect.ValueOf(data)

	// Handle pointers
	if value.Kind() == reflect.Ptr {
		if value.IsNil() {
			return nil, nil
		}
		value = value.Elem()
	}

	// Ensure we have an array/slice
	if value.Kind() != reflect.Slice && value.Kind() != reflect.Array {
		return nil, fmt.Errorf("filter can only be applied to arrays/slices, got %s", value.Kind())
	}

	// Create a slice to store filtered results
	var filteredResults []interface{}

	// Iterate through array elements and apply filter
	for i := 0; i < value.Len(); i++ {
		element := value.Index(i).Interface()

		// Evaluate filter condition for this element
		matches, err := r.evaluateFilterCondition(element, filter)
		if err != nil {
			return nil, fmt.Errorf("failed to evaluate filter condition for element %d: %w", i, err)
		}

		if matches {
			filteredResults = append(filteredResults, element)
		}
	}

	return filteredResults, nil
}

// evaluateFilterCondition evaluates a filter condition against a single array element
func (r *PathResolver) evaluateFilterCondition(element interface{}, filter *JSONPathFilter) (bool, error) {
	// Extract the field value from the element using the field path
	fieldValue, err := r.GetValue(element, filter.FieldPath)
	if err != nil {
		// If we can't get the field value, treat as non-match
		return false, nil
	}

	// Perform the comparison based on the operator
	return r.compareValues(fieldValue, filter.Operator, filter.Value)
}

// compareValues compares two values using the specified operator
func (r *PathResolver) compareValues(fieldValue interface{}, operator string, filterValue interface{}) (bool, error) {
	// Handle nil values
	if fieldValue == nil && filterValue == nil {
		return operator == "==" || operator == "=", nil
	}
	if fieldValue == nil || filterValue == nil {
		return operator == "!=", nil
	}

	switch operator {
	case "==", "=":
		return r.equalValues(fieldValue, filterValue), nil

	case "!=":
		return !r.equalValues(fieldValue, filterValue), nil

	case ">":
		return r.compareNumericValues(fieldValue, filterValue, func(a, b float64) bool { return a > b })

	case ">=":
		return r.compareNumericValues(fieldValue, filterValue, func(a, b float64) bool { return a >= b })

	case "<":
		return r.compareNumericValues(fieldValue, filterValue, func(a, b float64) bool { return a < b })

	case "<=":
		return r.compareNumericValues(fieldValue, filterValue, func(a, b float64) bool { return a <= b })

	case "contains":
		return r.stringContains(fieldValue, filterValue)

	case "starts_with":
		return r.stringStartsWith(fieldValue, filterValue)

	case "ends_with":
		return r.stringEndsWith(fieldValue, filterValue)

	default:
		return false, fmt.Errorf("unsupported operator: %s", operator)
	}
}

// equalValues compares two values for equality, handling type conversions
func (r *PathResolver) equalValues(a, b interface{}) bool {
	// Direct equality check
	if a == b {
		return true
	}

	// Convert both to strings and compare (handles various type mismatches)
	aStr := fmt.Sprintf("%v", a)
	bStr := fmt.Sprintf("%v", b)
	return aStr == bStr
}

// compareNumericValues compares two values as numbers
func (r *PathResolver) compareNumericValues(a, b interface{}, compareFn func(float64, float64) bool) (bool, error) {
	aFloat, err := r.toFloat64(a)
	if err != nil {
		return false, fmt.Errorf("cannot convert field value to number: %v", a)
	}

	bFloat, err := r.toFloat64(b)
	if err != nil {
		return false, fmt.Errorf("cannot convert filter value to number: %v", b)
	}

	return compareFn(aFloat, bFloat), nil
}

// toFloat64 converts various numeric types to float64
func (r *PathResolver) toFloat64(value interface{}) (float64, error) {
	switch v := value.(type) {
	case float64:
		return v, nil
	case float32:
		return float64(v), nil
	case int:
		return float64(v), nil
	case int64:
		return float64(v), nil
	case int32:
		return float64(v), nil
	case int16:
		return float64(v), nil
	case int8:
		return float64(v), nil
	case uint:
		return float64(v), nil
	case uint64:
		return float64(v), nil
	case uint32:
		return float64(v), nil
	case uint16:
		return float64(v), nil
	case uint8:
		return float64(v), nil
	case string:
		return strconv.ParseFloat(v, 64)
	default:
		return 0, fmt.Errorf("cannot convert %T to float64", value)
	}
}

// stringContains checks if field value contains the filter value as substring
func (r *PathResolver) stringContains(fieldValue, filterValue interface{}) (bool, error) {
	fieldStr := fmt.Sprintf("%v", fieldValue)
	filterStr := fmt.Sprintf("%v", filterValue)
	return strings.Contains(fieldStr, filterStr), nil
}

// stringStartsWith checks if field value starts with the filter value
func (r *PathResolver) stringStartsWith(fieldValue, filterValue interface{}) (bool, error) {
	fieldStr := fmt.Sprintf("%v", fieldValue)
	filterStr := fmt.Sprintf("%v", filterValue)
	return strings.HasPrefix(fieldStr, filterStr), nil
}

// stringEndsWith checks if field value ends with the filter value
func (r *PathResolver) stringEndsWith(fieldValue, filterValue interface{}) (bool, error) {
	fieldStr := fmt.Sprintf("%v", fieldValue)
	filterStr := fmt.Sprintf("%v", filterValue)
	return strings.HasSuffix(fieldStr, filterStr), nil
}

// navigatePath navigates through data using parsed path segments
func (r *PathResolver) navigatePath(data interface{}, segments []PathSegment) (interface{}, error) {
	current := data

	for segmentIndex, segment := range segments {
		// Handle wildcard with remaining path segments
		if segment.IsWildcard && segmentIndex < len(segments)-1 {
			return r.navigateWildcardWithPath(current, segments[segmentIndex+1:])
		}

		// Handle filter with remaining path segments
		if segment.Type == segmentTypeFilter && segmentIndex < len(segments)-1 {
			return r.navigateFilterWithPath(current, segment.FilterExpression, segments[segmentIndex+1:])
		}

		next, navigationError := r.navigateSegment(current, segment)
		if navigationError != nil {
			return nil, fmt.Errorf("failed to navigate segment %d (%s): %w", segmentIndex, segment.Value, navigationError)
		}
		current = next
	}

	return current, nil
}

// navigateSegment navigates a single path segment
func (r *PathResolver) navigateSegment(data interface{}, segment PathSegment) (interface{}, error) {
	if data == nil {
		return nil, nil // Return nil gracefully instead of error
	}

	switch segment.Type {
	case segmentTypeField:
		return r.navigateField(data, segment.Value)
	case segmentTypeIndex:
		return r.navigateIndex(data, segment.Value)
	case "wildcard":
		return r.navigateWildcard(data)
	case segmentTypeFilter:
		return r.navigateFilter(data, segment.FilterExpression)
	default:
		return nil, fmt.Errorf("unknown segment type: %s", segment.Type)
	}
}

// navigateField navigates to a field in an object
func (r *PathResolver) navigateField(data interface{}, fieldName string) (interface{}, error) {
	value := reflect.ValueOf(data)

	// Handle pointers
	if value.Kind() == reflect.Ptr {
		if value.IsNil() {
			return nil, nil // Return nil for nil pointers instead of error
		}
		value = value.Elem()
	}

	switch value.Kind() {
	case reflect.Map:
		// Handle map[string]interface{}
		if mapValue, ok := data.(map[string]interface{}); ok {
			if result, exists := mapValue[fieldName]; exists {
				return result, nil
			}
			return nil, nil // Return nil for missing fields instead of error
		}
		return nil, fmt.Errorf("unsupported map type")

	case reflect.Struct:
		// Handle struct fields
		fieldValue := value.FieldByName(fieldName)
		if !fieldValue.IsValid() {
			// Try case-insensitive lookup
			for fieldIndex := 0; fieldIndex < value.NumField(); fieldIndex++ {
				field := value.Type().Field(fieldIndex)
				if strings.EqualFold(field.Name, fieldName) {
					fieldValue = value.Field(fieldIndex)
					break
				}
			}
		}

		if !fieldValue.IsValid() {
			return nil, nil // Return nil for missing struct fields instead of error
		}

		return fieldValue.Interface(), nil

	default:
		return nil, fmt.Errorf("cannot navigate field '%s' on type %T", fieldName, data)
	}
}

// navigateIndex navigates to a specific array index
func (r *PathResolver) navigateIndex(data interface{}, indexStr string) (interface{}, error) {
	index, err := strconv.Atoi(indexStr)
	if err != nil {
		return nil, fmt.Errorf("invalid array index '%s': %w", indexStr, err)
	}

	value := reflect.ValueOf(data)

	// Handle pointers
	if value.Kind() == reflect.Ptr {
		if value.IsNil() {
			return nil, fmt.Errorf("nil pointer")
		}
		value = value.Elem()
	}

	switch value.Kind() {
	case reflect.Slice, reflect.Array:
		if index < 0 || index >= value.Len() {
			return nil, nil // Return nil for out of bounds index instead of error
		}
		return value.Index(index).Interface(), nil

	default:
		return nil, fmt.Errorf("cannot navigate index %d on type %T", index, data)
	}
}

// navigateWildcard navigates to all elements in an array
func (r *PathResolver) navigateWildcard(data interface{}) (interface{}, error) {
	// Handle nil data gracefully
	if data == nil {
		return []interface{}{}, nil // Return empty array for nil data
	}

	value := reflect.ValueOf(data)

	// Handle pointers
	if value.Kind() == reflect.Ptr {
		if value.IsNil() {
			return []interface{}{}, nil // Return empty array for nil pointer
		}
		value = value.Elem()
	}

	switch value.Kind() {
	case reflect.Slice, reflect.Array:
		result := make([]interface{}, value.Len())
		for arrayIndex := 0; arrayIndex < value.Len(); arrayIndex++ {
			result[arrayIndex] = value.Index(arrayIndex).Interface()
		}
		return result, nil

	default:
		return nil, fmt.Errorf("cannot navigate wildcard on type %T", data)
	}
}

// navigateWildcardWithPath navigates wildcard with additional path segments
func (r *PathResolver) navigateWildcardWithPath(data interface{}, remainingSegments []PathSegment) (interface{}, error) {
	// Handle nil data gracefully
	if data == nil {
		return []interface{}{}, nil // Return empty array for nil data
	}

	value := reflect.ValueOf(data)

	// Handle pointers
	if value.Kind() == reflect.Ptr {
		if value.IsNil() {
			return []interface{}{}, nil // Return empty array for nil pointer
		}
		value = value.Elem()
	}

	switch value.Kind() {
	case reflect.Slice, reflect.Array:
		var result []interface{}
		for arrayIndex := 0; arrayIndex < value.Len(); arrayIndex++ {
			itemValue := value.Index(arrayIndex).Interface()

			// Navigate the remaining path for this item
			itemResult, navigationError := r.navigatePath(itemValue, remainingSegments)
			if navigationError != nil {
				continue // Skip items that don't match the path
			}
			result = append(result, itemResult)
		}
		return result, nil

	default:
		return nil, fmt.Errorf("cannot navigate wildcard on type %T", data)
	}
}

// navigateFilterWithPath filters an array and then navigates remaining path segments on each filtered element
func (r *PathResolver) navigateFilterWithPath(data interface{}, filter *JSONPathFilter, remainingSegments []PathSegment) (interface{}, error) {
	// Handle nil data gracefully
	if data == nil {
		return []interface{}{}, nil // Return empty array for nil data
	}

	// First, apply the filter to get filtered array
	filteredData, err := r.navigateFilter(data, filter)
	if err != nil {
		return nil, fmt.Errorf("failed to apply filter: %w", err)
	}

	// If no remaining segments, return the filtered data as-is
	if len(remainingSegments) == 0 {
		return filteredData, nil
	}

	// Convert filtered data to slice for navigation
	value := reflect.ValueOf(filteredData)

	// Handle pointers
	if value.Kind() == reflect.Ptr {
		if value.IsNil() {
			return []interface{}{}, nil // Return empty array for nil pointer
		}
		value = value.Elem()
	}

	// Ensure we have an array/slice from the filter
	if value.Kind() != reflect.Slice && value.Kind() != reflect.Array {
		return nil, fmt.Errorf("filter result is not an array/slice, got %s", value.Kind())
	}

	// Navigate remaining path segments on each filtered element
	var result []interface{}
	for arrayIndex := 0; arrayIndex < value.Len(); arrayIndex++ {
		itemValue := value.Index(arrayIndex).Interface()

		// Navigate the remaining path for this filtered item
		itemResult, navigationError := r.navigatePath(itemValue, remainingSegments)
		if navigationError != nil {
			continue // Skip items that don't match the remaining path
		}
		result = append(result, itemResult)
	}

	return result, nil
}

// setValueAtPath sets a value at a specific path in the data structure
func (r *PathResolver) setValueAtPath(data interface{}, segments []PathSegment, value interface{}) error {
	if len(segments) == 0 {
		return fmt.Errorf("no path segments provided")
	}

	// Check if any segment contains a wildcard or filter - if so, use special handling
	for _, segment := range segments {
		if segment.IsWildcard {
			return r.setValueAtWildcardPath(data, segments, value)
		}
		if segment.Type == segmentTypeFilter {
			return r.setValueAtFilterPath(data, segments, value)
		}
	}

	// For now, only support setting values in maps (processed data)
	mapData, ok := data.(map[string]interface{})
	if !ok {
		return fmt.Errorf("can only set values in map[string]interface{}, got %T", data)
	}

	// Navigate to the parent of the target location, creating structures as needed
	current := mapData
	segmentIndex := 0
	for segmentIndex < len(segments)-1 {
		segment := segments[segmentIndex]

		// Check if next segment is an array index
		if segmentIndex+1 < len(segments) && segments[segmentIndex+1].Type == "index" {
			// This field should be an array
			next, navigationError := r.navigateOrCreateArrayField(current, segment, segments[segmentIndex+1])
			if navigationError != nil {
				return fmt.Errorf("failed to navigate array field %d (%s[%s]): %w", segmentIndex, segment.Value, segments[segmentIndex+1].Value, navigationError)
			}
			current = next
			segmentIndex += 2 // Skip both field and index segments
		} else {
			// Regular field navigation
			next, navigationError := r.navigateOrCreateSegment(current, segment)
			if navigationError != nil {
				return fmt.Errorf("failed to navigate segment %d (%s): %w", segmentIndex, segment.Value, navigationError)
			}
			nextMap, ok := next.(map[string]interface{})
			if !ok {
				return fmt.Errorf("intermediate path segment is not a map")
			}
			current = nextMap
			segmentIndex++
		}
	}

	// Set the final value
	if segmentIndex < len(segments) {
		finalSegment := segments[segmentIndex]
		return r.setValueAtSegment(current, finalSegment, value)
	}

	return nil
}

// navigateOrCreateSegment navigates to a segment, creating it if it doesn't exist
func (r *PathResolver) navigateOrCreateSegment(data map[string]interface{}, segment PathSegment) (interface{}, error) {
	switch segment.Type {
	case segmentTypeField:
		if existing, exists := data[segment.Value]; exists {
			return existing, nil
		}
		// Create new map for the field
		newMap := make(map[string]interface{})
		data[segment.Value] = newMap
		return newMap, nil

	default:
		return nil, fmt.Errorf("unsupported segment type for creation: %s", segment.Type)
	}
}

// navigateOrCreateArrayField creates or navigates to an array field and returns the element at the specified index
func (r *PathResolver) navigateOrCreateArrayField(current map[string]interface{}, fieldSegment PathSegment, indexSegment PathSegment) (map[string]interface{}, error) {
	if fieldSegment.Type != "field" || indexSegment.Type != "index" {
		return nil, fmt.Errorf("navigateOrCreateArrayField requires field and index segments")
	}

	// Parse the index
	index, err := strconv.Atoi(indexSegment.Value)
	if err != nil {
		return nil, fmt.Errorf("invalid array index '%s': %w", indexSegment.Value, err)
	}

	// Check if field exists
	var array []interface{}
	if existing, exists := current[fieldSegment.Value]; exists {
		if existingArray, ok := existing.([]interface{}); ok {
			array = existingArray
		} else {
			return nil, fmt.Errorf("field '%s' exists but is not an array", fieldSegment.Value)
		}
	} else {
		// Create new array
		array = make([]interface{}, 0)
	}

	// Extend array if necessary
	for len(array) <= index {
		array = append(array, make(map[string]interface{}))
	}

	// Update the field with the extended array
	current[fieldSegment.Value] = array

	// Return the map at the specified index
	if element, ok := array[index].(map[string]interface{}); ok {
		return element, nil
	}

	// If the element is not a map, make it a map
	newMap := make(map[string]interface{})
	array[index] = newMap
	current[fieldSegment.Value] = array
	return newMap, nil
}

// setValueAtWildcardPath handles setting values at paths containing wildcards
func (r *PathResolver) setValueAtWildcardPath(data interface{}, segments []PathSegment, value interface{}) error {
	// For now, only support setting values in maps (processed data)
	mapData, ok := data.(map[string]interface{})
	if !ok {
		return fmt.Errorf("can only set wildcard values in map[string]interface{}, got %T", data)
	}

	// Find the first wildcard segment
	wildcardIndex := -1
	for i, segment := range segments {
		if segment.IsWildcard {
			wildcardIndex = i
			break
		}
	}

	if wildcardIndex == -1 {
		return fmt.Errorf("no wildcard found in path segments")
	}

	// Navigate to the parent of the wildcard
	current := mapData
	for i := 0; i < wildcardIndex-1; i++ {
		segment := segments[i]
		next, navigationError := r.navigateOrCreateSegment(current, segment)
		if navigationError != nil {
			return fmt.Errorf("failed to navigate to wildcard parent segment %d (%s): %w", i, segment.Value, navigationError)
		}
		nextMap, ok := next.(map[string]interface{})
		if !ok {
			return fmt.Errorf("wildcard parent segment is not a map")
		}
		current = nextMap
	}

	// Get the array field that contains the wildcard
	if wildcardIndex == 0 {
		return fmt.Errorf("wildcard cannot be the first segment")
	}

	arrayFieldSegment := segments[wildcardIndex-1]
	if arrayFieldSegment.Type != "field" {
		return fmt.Errorf("wildcard must follow a field segment")
	}

	// Get or create the array
	var array []interface{}
	if existing, exists := current[arrayFieldSegment.Value]; exists {
		if existingArray, ok := existing.([]interface{}); ok {
			array = existingArray
		} else {
			return fmt.Errorf("field '%s' exists but is not an array for wildcard operation", arrayFieldSegment.Value)
		}
	} else {
		// No array exists, which means no elements to set - this is not an error
		return nil
	}

	// Get remaining segments after the wildcard
	remainingSegments := segments[wildcardIndex+1:]

	// Apply the value to each array element
	for arrayIndex, arrayElement := range array {
		if len(remainingSegments) == 0 {
			// Set the value directly in the array element
			array[arrayIndex] = value
		} else {
			// Navigate further into the array element
			if elementMap, ok := arrayElement.(map[string]interface{}); ok {
				err := r.setValueAtPath(elementMap, remainingSegments, value)
				if err != nil {
					return fmt.Errorf("failed to set value in array element %d: %w", arrayIndex, err)
				}
			} else {
				return fmt.Errorf("array element %d is not a map, cannot navigate further", arrayIndex)
			}
		}
	}

	// Update the array in the parent
	current[arrayFieldSegment.Value] = array

	return nil
}

// setValueAtSegment sets a value at a specific segment
func (r *PathResolver) setValueAtSegment(data map[string]interface{}, segment PathSegment, value interface{}) error {
	switch segment.Type {
	case segmentTypeField:
		data[segment.Value] = value
		return nil

	default:
		return fmt.Errorf("unsupported segment type for setting: %s", segment.Type)
	}
}

// setValueAtPathInPlace sets a value at a specific path in data structures, supporting both maps and typed structs.
//
// This method provides in-place modification capabilities for various data structure types,
// enabling efficient transformation without creating new data structures. It navigates
// through the path segments and modifies the target field directly.
//
// Processing Strategy:
// The method handles different data types during navigation:
//   - Maps: Uses existing map navigation and creation logic
//   - Structs: Uses reflection to access and modify fields
//   - Pointers: Automatically dereferences to access underlying data
//   - Arrays/Slices: Supports indexed access for array elements
//
// Parameters:
//   - data: Target data structure to modify
//   - segments: Parsed path segments for navigation
//   - value: Value to set at the target location
//
// Returns:
//   - error: Navigation or type compatibility errors
func (r *PathResolver) setValueAtPathInPlace(data interface{}, segments []PathSegment, value interface{}) error {
	if len(segments) == 0 {
		return fmt.Errorf("no path segments provided")
	}

	// Check if any segment contains a wildcard or filter - if so, use special handling
	for _, segment := range segments {
		if segment.IsWildcard {
			return r.setValueAtWildcardPathInPlace(data, segments, value)
		}
		if segment.Type == segmentTypeFilter {
			return r.setValueAtFilterPathInPlace(data, segments, value)
		}
	}

	// Navigate to the parent of the target location
	current := data
	for segmentIndex, segment := range segments[:len(segments)-1] {
		next, navigationError := r.navigateOrCreateSegmentInPlace(current, segment)
		if navigationError != nil {
			return fmt.Errorf("failed to navigate segment %d (%s): %w", segmentIndex, segment.Value, navigationError)
		}
		current = next
	}

	// Set the final value
	finalSegment := segments[len(segments)-1]
	return r.setValueAtSegmentInPlace(current, finalSegment, value)
}

// setValueAtWildcardPathInPlace handles setting values at paths containing wildcards for in-place transformations
func (r *PathResolver) setValueAtWildcardPathInPlace(data interface{}, segments []PathSegment, value interface{}) error {
	// Find the first wildcard segment
	wildcardIndex := -1
	for i, segment := range segments {
		if segment.IsWildcard {
			wildcardIndex = i
			break
		}
	}

	if wildcardIndex == -1 {
		return fmt.Errorf("no wildcard found in path segments")
	}

	if wildcardIndex == 0 {
		return fmt.Errorf("wildcard cannot be the first segment")
	}

	// Navigate to the parent of the wildcard
	current := data
	for i := 0; i < wildcardIndex-1; i++ {
		segment := segments[i]
		next, navigationError := r.navigateOrCreateSegmentInPlace(current, segment)
		if navigationError != nil {
			return fmt.Errorf("failed to navigate to wildcard parent segment %d (%s): %w", i, segment.Value, navigationError)
		}
		current = next
	}

	// Get the array field that contains the wildcard
	arrayFieldSegment := segments[wildcardIndex-1]
	if arrayFieldSegment.Type != "field" {
		return fmt.Errorf("wildcard must follow a field segment")
	}

	// Get the array using reflection to handle both maps and structs
	var arrayValue reflect.Value
	var arrayInterface interface{}

	// Handle different data types
	currentValue := reflect.ValueOf(current)
	if currentValue.Kind() == reflect.Ptr {
		if currentValue.IsNil() {
			return fmt.Errorf("nil pointer encountered when accessing array field")
		}
		currentValue = currentValue.Elem()
	}

	switch currentValue.Kind() {
	case reflect.Map:
		// Handle map data
		mapData := currentValue.Interface().(map[string]interface{})
		if existing, exists := mapData[arrayFieldSegment.Value]; exists {
			arrayInterface = existing
		} else {
			// No array exists, nothing to modify
			return nil
		}
	case reflect.Struct:
		// Handle struct data
		fieldValue := currentValue.FieldByName(arrayFieldSegment.Value)
		if !fieldValue.IsValid() {
			// Try case-insensitive lookup
			fieldType := currentValue.Type()
			for i := 0; i < fieldType.NumField(); i++ {
				if strings.EqualFold(fieldType.Field(i).Name, arrayFieldSegment.Value) {
					fieldValue = currentValue.Field(i)
					break
				}
			}
		}
		if !fieldValue.IsValid() {
			return fmt.Errorf("field '%s' not found in struct", arrayFieldSegment.Value)
		}
		arrayInterface = fieldValue.Interface()
	default:
		return fmt.Errorf("unsupported data type for wildcard field access: %T", current)
	}

	// Convert to reflect.Value for array processing
	arrayValue = reflect.ValueOf(arrayInterface)
	if arrayValue.Kind() == reflect.Ptr {
		if arrayValue.IsNil() {
			return nil // Nothing to modify
		}
		arrayValue = arrayValue.Elem()
	}

	// Verify it's an array or slice
	if arrayValue.Kind() != reflect.Slice && arrayValue.Kind() != reflect.Array {
		return fmt.Errorf("field '%s' is not an array or slice for wildcard operation, got %T", arrayFieldSegment.Value, arrayInterface)
	}

	// Get remaining segments after the wildcard
	remainingSegments := segments[wildcardIndex+1:]

	// Check if the value is an array that should be distributed to array elements
	var valueArray []interface{}
	var useDistribution bool

	if valueReflect := reflect.ValueOf(value); valueReflect.Kind() == reflect.Slice || valueReflect.Kind() == reflect.Array {
		// Value is an array - check if it should be distributed
		if valueReflect.Len() == arrayValue.Len() {
			// Array lengths match - this looks like a distributed transformation result
			useDistribution = true
			valueArray = make([]interface{}, valueReflect.Len())
			for i := 0; i < valueReflect.Len(); i++ {
				valueArray[i] = valueReflect.Index(i).Interface()
			}
		}
	}

	// Process each array element
	for arrayIndex := 0; arrayIndex < arrayValue.Len(); arrayIndex++ {
		arrayElement := arrayValue.Index(arrayIndex)

		// Handle interface{} wrapping
		if arrayElement.Kind() == reflect.Interface {
			arrayElement = arrayElement.Elem()
		}

		elementData := arrayElement.Interface()

		// Determine the value to set for this specific array element
		var elementValue interface{}
		if useDistribution && arrayIndex < len(valueArray) {
			// Use the corresponding element from the distributed array
			elementValue = valueArray[arrayIndex]
		} else {
			// Use the entire value (original behavior for non-distributed cases)
			elementValue = value
		}

		if len(remainingSegments) == 0 {
			// Set the value directly in the array element - this requires the array element to be settable
			if !arrayElement.CanSet() {
				return fmt.Errorf("array element %d is not settable", arrayIndex)
			}
			newValue := reflect.ValueOf(elementValue)
			if newValue.Type().AssignableTo(arrayElement.Type()) {
				arrayElement.Set(newValue)
			} else {
				return fmt.Errorf("cannot assign value of type %T to array element of type %s", elementValue, arrayElement.Type())
			}
		} else {
			// Navigate further into the array element for in-place modification
			err := r.setValueAtPathInPlace(elementData, remainingSegments, elementValue)
			if err != nil {
				return fmt.Errorf("failed to set value in array element %d: %w", arrayIndex, err)
			}
		}
	}

	return nil
}

// setValueAtFilterPath handles setting values at paths containing filter expressions
func (r *PathResolver) setValueAtFilterPath(data interface{}, segments []PathSegment, value interface{}) error {
	// Find the first filter segment
	filterIndex := -1
	for i, segment := range segments {
		if segment.Type == segmentTypeFilter {
			filterIndex = i
			break
		}
	}

	if filterIndex == -1 {
		return fmt.Errorf("no filter found in path segments")
	}

	if filterIndex == 0 {
		return fmt.Errorf("filter cannot be the first segment")
	}

	// Navigate to the parent of the filter (to get the array to filter)
	parentSegments := segments[:filterIndex]
	parentData, err := r.navigatePath(data, parentSegments)
	if err != nil {
		return fmt.Errorf("failed to navigate to filter parent: %w", err)
	}

	// Apply the filter to get filtered elements
	filterSegment := segments[filterIndex]
	filteredData, err := r.navigateFilter(parentData, filterSegment.FilterExpression)
	if err != nil {
		return fmt.Errorf("failed to apply filter: %w", err)
	}

	// Get remaining segments after the filter
	remainingSegments := segments[filterIndex+1:]

	// Convert filtered data to slice
	filteredValue := reflect.ValueOf(filteredData)
	if filteredValue.Kind() != reflect.Slice && filteredValue.Kind() != reflect.Array {
		return fmt.Errorf("filter result is not an array/slice")
	}

	// Process each filtered element
	for i := 0; i < filteredValue.Len(); i++ {
		element := filteredValue.Index(i).Interface()

		if len(remainingSegments) == 0 {
			// No remaining segments - would need to replace the entire filtered element
			return fmt.Errorf("cannot replace entire filtered elements in copy mode")
		} else {
			// Set value in each filtered element
			err := r.setValueAtPath(element, remainingSegments, value)
			if err != nil {
				return fmt.Errorf("failed to set value in filtered element %d: %w", i, err)
			}
		}
	}

	return nil
}

// setValueAtFilterPathInPlace handles setting values at paths containing filter expressions for in-place transformations
func (r *PathResolver) setValueAtFilterPathInPlace(data interface{}, segments []PathSegment, value interface{}) error {
	// Find the first filter segment
	filterIndex := -1
	for i, segment := range segments {
		if segment.Type == segmentTypeFilter {
			filterIndex = i
			break
		}
	}

	if filterIndex == -1 {
		return fmt.Errorf("no filter found in path segments")
	}

	if filterIndex == 0 {
		return fmt.Errorf("filter cannot be the first segment")
	}

	// Navigate to the parent of the filter (to get the array to filter)
	current := data
	for i := 0; i < filterIndex; i++ {
		segment := segments[i]
		next, navigationError := r.navigateOrCreateSegmentInPlace(current, segment)
		if navigationError != nil {
			return fmt.Errorf("failed to navigate to filter parent segment %d (%s): %w", i, segment.Value, navigationError)
		}
		current = next
	}

	// Apply the filter to get filtered elements
	filterSegment := segments[filterIndex]
	filteredData, err := r.navigateFilter(current, filterSegment.FilterExpression)
	if err != nil {
		return fmt.Errorf("failed to apply filter: %w", err)
	}

	// Get remaining segments after the filter
	remainingSegments := segments[filterIndex+1:]

	// Convert filtered data to slice
	filteredValue := reflect.ValueOf(filteredData)
	if filteredValue.Kind() != reflect.Slice && filteredValue.Kind() != reflect.Array {
		return fmt.Errorf("filter result is not an array/slice")
	}

	// Check if the value is an array that should be distributed to filtered elements
	var valueArray []interface{}
	var useDistribution bool

	if valueReflect := reflect.ValueOf(value); valueReflect.Kind() == reflect.Slice || valueReflect.Kind() == reflect.Array {
		// Value is an array - check if it should be distributed
		if valueReflect.Len() == filteredValue.Len() {
			// Array lengths match - this looks like a distributed transformation result
			useDistribution = true
			valueArray = make([]interface{}, valueReflect.Len())
			for i := 0; i < valueReflect.Len(); i++ {
				valueArray[i] = valueReflect.Index(i).Interface()
			}
		}
	}

	// Process each filtered element
	for i := 0; i < filteredValue.Len(); i++ {
		element := filteredValue.Index(i).Interface()

		// Determine the value to set for this specific filtered element
		var elementValue interface{}
		if useDistribution && i < len(valueArray) {
			// Use the corresponding element from the distributed array
			elementValue = valueArray[i]
		} else {
			// Use the entire value (original behavior for non-distributed cases)
			elementValue = value
		}

		if len(remainingSegments) == 0 {
			// No remaining segments - would need to replace the entire filtered element
			return fmt.Errorf("cannot replace entire filtered elements - use remaining path segments")
		} else {
			// Set value in each filtered element
			err := r.setValueAtPathInPlace(element, remainingSegments, elementValue)
			if err != nil {
				return fmt.Errorf("failed to set value in filtered element %d: %w", i, err)
			}
		}
	}

	return nil
}

// pathExistsInData checks whether a given path exists in the data structure.
//
// This method provides a safe way to check for path existence without modifying
// the data or throwing errors. It's particularly useful for detecting potential
// conflicts before performing transformations.
//
// Parameters:
//   - data: The data structure to check
//   - pathString: The path expression to evaluate
//
// Returns:
//   - bool: true if the path exists and resolves to a non-nil value
func (r *PathResolver) pathExistsInData(data interface{}, pathString string) bool {
	// Use GetValue to check if the path exists
	value, err := r.GetValue(data, pathString)
	if err != nil {
		// Path doesn't exist or navigation failed
		return false
	}

	// Path exists if we got a non-nil value
	return value != nil
}

// navigateOrCreateSegmentInPlace navigates to a segment, creating it if necessary and possible.
//
// This method extends navigation capabilities to support both map and struct types,
// enabling path navigation through mixed data structures. It automatically handles
// pointer dereferencing and type-specific navigation logic.
//
// Supported Operations:
//   - Map field access and creation (existing functionality)
//   - Struct field access using reflection
//   - Pointer dereferencing for transparent access
//   - Array/slice element access by index
//
// Parameters:
//   - data: Current data context for navigation
//   - segment: Path segment specifying the navigation target
//
// Returns:
//   - interface{}: The target object for the next navigation step
//   - error: Navigation errors or unsupported operations
func (r *PathResolver) navigateOrCreateSegmentInPlace(data interface{}, segment PathSegment) (interface{}, error) {
	// Handle maps (existing functionality)
	if mapData, isMap := data.(map[string]interface{}); isMap {
		return r.navigateOrCreateSegment(mapData, segment)
	}

	// Handle pointers by dereferencing
	dataValue := reflect.ValueOf(data)
	if dataValue.Kind() == reflect.Ptr {
		if dataValue.IsNil() {
			return nil, fmt.Errorf("nil pointer encountered during navigation")
		}
		dataValue = dataValue.Elem()
		data = dataValue.Interface()
	}

	switch segment.Type {
	case segmentTypeField:
		return r.navigateStructField(data, segment.Value)
	case segmentTypeIndex:
		return r.navigateArrayIndex(data, segment.Value)
	case segmentTypeFilter:
		// Filter segments return the filtered array for further navigation
		return r.navigateFilter(data, segment.FilterExpression)
	default:
		return nil, fmt.Errorf("unsupported segment type for in-place navigation: %s", segment.Type)
	}
}

// navigateStructField navigates to a specific field in a struct using reflection.
//
// This method provides type-safe access to struct fields, supporting both exported
// and nested field access. It includes comprehensive error handling for common
// field access scenarios.
//
// Field Access Strategy:
//   - Attempts exact field name match first
//   - Falls back to case-insensitive field name matching
//   - Validates field accessibility and settability
//   - Provides detailed error messages for debugging
//
// Parameters:
//   - data: Struct data to navigate
//   - fieldName: Name of the field to access
//
// Returns:
//   - interface{}: The field value for further navigation
//   - error: Field access errors or type validation failures
func (r *PathResolver) navigateStructField(data interface{}, fieldName string) (interface{}, error) {
	dataValue := reflect.ValueOf(data)

	// Handle pointers
	if dataValue.Kind() == reflect.Ptr {
		if dataValue.IsNil() {
			return nil, fmt.Errorf("nil pointer")
		}
		dataValue = dataValue.Elem()
	}

	if dataValue.Kind() != reflect.Struct {
		return nil, fmt.Errorf("cannot navigate struct field '%s' on non-struct type %T", fieldName, data)
	}

	// Try exact field name match
	fieldValue := dataValue.FieldByName(fieldName)
	if !fieldValue.IsValid() {
		// Try case-insensitive lookup
		structType := dataValue.Type()
		for fieldIndex := 0; fieldIndex < structType.NumField(); fieldIndex++ {
			field := structType.Field(fieldIndex)
			if strings.EqualFold(field.Name, fieldName) {
				fieldValue = dataValue.Field(fieldIndex)
				break
			}
		}
	}

	if !fieldValue.IsValid() {
		return nil, fmt.Errorf("field '%s' not found in struct %T", fieldName, data)
	}

	if !fieldValue.CanInterface() {
		return nil, fmt.Errorf("field '%s' is not accessible", fieldName)
	}

	return fieldValue.Interface(), nil
}

// navigateArrayIndex navigates to a specific index in an array or slice.
//
// This method provides indexed access to array and slice elements, supporting
// both read and write operations on collection types. It includes bounds
// checking and type validation for safe array access.
//
// Index Access Features:
//   - Bounds checking to prevent out-of-range errors
//   - Support for both arrays and slices
//   - Type validation for collection types
//   - Detailed error messages for debugging
//
// Parameters:
//   - data: Array or slice data to navigate
//   - indexStr: String representation of the array index
//
// Returns:
//   - interface{}: The array element for further navigation
//   - error: Index parsing errors or bounds validation failures
func (r *PathResolver) navigateArrayIndex(data interface{}, indexStr string) (interface{}, error) {
	arrayIndex, parseError := strconv.Atoi(indexStr)
	if parseError != nil {
		return nil, fmt.Errorf("invalid array index '%s': %w", indexStr, parseError)
	}

	dataValue := reflect.ValueOf(data)

	// Handle pointers
	if dataValue.Kind() == reflect.Ptr {
		if dataValue.IsNil() {
			return nil, fmt.Errorf("nil pointer")
		}
		dataValue = dataValue.Elem()
	}

	switch dataValue.Kind() {
	case reflect.Slice, reflect.Array:
		if arrayIndex < 0 || arrayIndex >= dataValue.Len() {
			return nil, fmt.Errorf("array index %d out of bounds (length %d)", arrayIndex, dataValue.Len())
		}
		return dataValue.Index(arrayIndex).Interface(), nil

	default:
		return nil, fmt.Errorf("cannot navigate index %d on non-array type %T", arrayIndex, data)
	}
}

// setValueAtSegmentInPlace sets a value at a specific segment, supporting both maps and struct fields.
//
// This method provides the final value assignment step for in-place transformations,
// handling different target types appropriately. It includes comprehensive type
// checking and validation to ensure safe value assignment.
//
// Value Assignment Strategy:
//   - Maps: Direct key assignment (existing functionality)
//   - Struct fields: Reflection-based field assignment with type checking
//   - Type conversion: Automatic conversion for compatible types
//   - Validation: Ensures target field is settable and type-compatible
//
// Parameters:
//   - data: Target data structure for value assignment
//   - segment: Path segment specifying the assignment target
//   - value: Value to assign at the target location
//
// Returns:
//   - error: Assignment errors or type compatibility failures
func (r *PathResolver) setValueAtSegmentInPlace(data interface{}, segment PathSegment, value interface{}) error {
	// Handle maps (existing functionality)
	if mapData, isMap := data.(map[string]interface{}); isMap {
		return r.setValueAtSegment(mapData, segment, value)
	}

	switch segment.Type {
	case segmentTypeField:
		return r.setStructField(data, segment.Value, value)
	case segmentTypeIndex:
		return r.setArrayIndex(data, segment.Value, value)
	default:
		return fmt.Errorf("unsupported segment type for in-place setting: %s", segment.Type)
	}
}

// setStructField sets a value in a struct field using reflection with comprehensive type checking.
//
// This method provides safe, type-checked field assignment for struct types,
// including automatic type conversion where appropriate. It validates field
// accessibility and provides detailed error messages for debugging.
//
// Type Safety Features:
//   - Validates field exists and is settable
//   - Checks type compatibility between value and target field
//   - Attempts automatic type conversion for compatible types
//   - Provides comprehensive error reporting
//
// Parameters:
//   - data: Struct containing the target field
//   - fieldName: Name of the field to modify
//   - value: Value to assign to the field
//
// Returns:
//   - error: Field access errors or type compatibility failures
func (r *PathResolver) setStructField(data interface{}, fieldName string, value interface{}) error {
	dataValue := reflect.ValueOf(data)

	// Handle pointers
	if dataValue.Kind() == reflect.Ptr {
		if dataValue.IsNil() {
			return fmt.Errorf("cannot set field on nil pointer")
		}
		dataValue = dataValue.Elem()
	}

	if dataValue.Kind() != reflect.Struct {
		return fmt.Errorf("cannot set struct field '%s' on non-struct type %T", fieldName, data)
	}

	// Find the field
	var targetField reflect.Value
	structType := dataValue.Type()

	// Try exact field name match
	if _, found := structType.FieldByName(fieldName); found {
		targetField = dataValue.FieldByName(fieldName)
	} else {
		// Try case-insensitive lookup
		for fieldIndex := 0; fieldIndex < structType.NumField(); fieldIndex++ {
			field := structType.Field(fieldIndex)
			if strings.EqualFold(field.Name, fieldName) {
				targetField = dataValue.Field(fieldIndex)
				break
			}
		}
	}

	if !targetField.IsValid() {
		return fmt.Errorf("field '%s' not found in struct %T", fieldName, data)
	}

	if !targetField.CanSet() {
		return fmt.Errorf("field '%s' is not settable (may be unexported)", fieldName)
	}

	// Set the value with type checking
	valueReflect := reflect.ValueOf(value)
	if !valueReflect.Type().AssignableTo(targetField.Type()) {
		// Try converting the value if possible
		if valueReflect.Type().ConvertibleTo(targetField.Type()) {
			convertedValue := valueReflect.Convert(targetField.Type())
			targetField.Set(convertedValue)
			return nil
		}
		return fmt.Errorf("cannot assign value of type %T to field '%s' of type %s", value, fieldName, targetField.Type())
	}

	targetField.Set(valueReflect)
	return nil
}

// setArrayIndex sets a value at a specific index in an array or slice.
//
// This method provides indexed value assignment for collection types, including
// bounds checking and type validation. It supports both arrays and slices with
// comprehensive error handling.
//
// Index Assignment Features:
//   - Bounds checking to prevent out-of-range assignments
//   - Type compatibility validation for element assignment
//   - Support for both arrays and slices
//   - Detailed error reporting for debugging
//
// Parameters:
//   - data: Array or slice containing the target element
//   - indexStr: String representation of the target index
//   - value: Value to assign at the specified index
//
// Returns:
//   - error: Index parsing errors, bounds failures, or type compatibility issues
func (r *PathResolver) setArrayIndex(data interface{}, indexStr string, value interface{}) error {
	arrayIndex, parseError := strconv.Atoi(indexStr)
	if parseError != nil {
		return fmt.Errorf("invalid array index '%s': %w", indexStr, parseError)
	}

	dataValue := reflect.ValueOf(data)

	// Handle pointers
	if dataValue.Kind() == reflect.Ptr {
		if dataValue.IsNil() {
			return fmt.Errorf("cannot set array index on nil pointer")
		}
		dataValue = dataValue.Elem()
	}

	switch dataValue.Kind() {
	case reflect.Slice, reflect.Array:
		if arrayIndex < 0 || arrayIndex >= dataValue.Len() {
			return fmt.Errorf("array index %d out of bounds (length %d)", arrayIndex, dataValue.Len())
		}

		targetElement := dataValue.Index(arrayIndex)
		if !targetElement.CanSet() {
			return fmt.Errorf("array element at index %d is not settable", arrayIndex)
		}

		valueReflect := reflect.ValueOf(value)
		if !valueReflect.Type().AssignableTo(targetElement.Type()) {
			// Try converting the value if possible
			if valueReflect.Type().ConvertibleTo(targetElement.Type()) {
				convertedValue := valueReflect.Convert(targetElement.Type())
				targetElement.Set(convertedValue)
				return nil
			}
			return fmt.Errorf("cannot assign value of type %T to array element of type %s", value, targetElement.Type())
		}

		targetElement.Set(valueReflect)
		return nil

	default:
		return fmt.Errorf("cannot set array index %d on non-array type %T", arrayIndex, data)
	}
}
