package tests

import (
	"testing"
	"workflow/internal/etl/transformation/core"
)

// TestCompleteDatasetTransformation tests that transformations return the complete dataset
func TestCompleteDatasetTransformation(t *testing.T) {
	mappingEngine := core.NewMappingEngine()

	// Original extracted data (simulating what comes from extraction)
	inputData := map[string]interface{}{
		"metadata": map[string]interface{}{
			"extraction_id": "ext-123",
			"timestamp":     "2025-01-15T10:30:00Z",
		},
		"reports": []interface{}{
			map[string]interface{}{
				"id":    "RPT-001",
				"title": "Test Report",
			},
		},
		"entities": []interface{}{
			map[string]interface{}{
				"id":     "ENT-001",
				"type":   "PERSON",
				"gender": "Male",
				"age":    30,
			},
			map[string]interface{}{
				"id":     "ENT-002",
				"type":   "PERSON",
				"gender": "Female",
				"age":    25,
			},
		},
	}

	// Configuration with both in-place and new field transformations
	config := &core.MappingConfig{
		FormatName: "COMPLETE_DATASET_TEST",
		FieldMappings: []core.FieldMapping{
			// In-place transformation: modify gender values directly
			{
				Name:           "gender_normalization",
				InputPath:      "entities[*].gender",
				OutputPath:     "entities[*].gender", // Same = in-place
				Transformation: "lookup",
				Config: map[string]interface{}{
					"lookup_table": "gender_codes",
				},
			},
			// New field transformation: create formatted report title
			{
				Name:           "formatted_report_title",
				InputPath:      "reports[0].title",
				OutputPath:     "formatted_title", // Different = new field
				Transformation: "format",
				Config: map[string]interface{}{
					"format_pattern": "FORMATTED: %s",
				},
			},
			// New field transformation: extract person count
			{
				Name:           "person_count",
				InputPath:      "entities",
				OutputPath:     "total_persons", // Different = new field
				Transformation: "filter",
				Config: map[string]interface{}{
					"field":    "type",
					"operator": "equals",
					"value":    "PERSON",
				},
			},
		},
		LookupTables: map[string]map[string]string{
			"gender_codes": {
				"Male":   "M",
				"Female": "F",
			},
		},
	}

	// Apply transformations
	result, err := mappingEngine.TransformData(inputData, config)
	if err != nil {
		t.Fatalf("Transformation failed: %v", err)
	}

	// Verify complete dataset is returned

	// 1. Original metadata should be preserved
	metadata, ok := result["metadata"].(map[string]interface{})
	if !ok {
		t.Fatal("Metadata should be preserved")
	}
	if metadata["extraction_id"] != "ext-123" {
		t.Errorf("Original metadata should be preserved, got: %v", metadata["extraction_id"])
	}

	// 2. Original reports should be preserved
	reports, ok := result["reports"].([]interface{})
	if !ok {
		t.Fatal("Reports should be preserved")
	}
	if len(reports) != 1 {
		t.Errorf("Expected 1 report, got: %d", len(reports))
	}

	// 3. In-place transformation: genders should be modified
	entities, ok := result["entities"].([]interface{})
	if !ok {
		t.Fatal("Entities should be preserved")
	}

	entity1 := entities[0].(map[string]interface{})
	if entity1["gender"] != "M" {
		t.Errorf("Entity 1 gender should be transformed to 'M', got: %v", entity1["gender"])
	}

	entity2 := entities[1].(map[string]interface{})
	if entity2["gender"] != "F" {
		t.Errorf("Entity 2 gender should be transformed to 'F', got: %v", entity2["gender"])
	}

	// 4. New field transformations should be added
	if formattedTitle, exists := result["formatted_title"]; !exists {
		t.Error("Formatted title new field should be added")
	} else if formattedTitle != "FORMATTED: Test Report" {
		t.Errorf("Formatted title should be 'FORMATTED: Test Report', got: %v", formattedTitle)
	}

	if totalPersons, exists := result["total_persons"]; !exists {
		t.Error("Total persons new field should be added")
	} else {
		personArray := totalPersons.([]interface{})
		if len(personArray) != 2 {
			t.Errorf("Total persons should contain 2 people, got: %d", len(personArray))
		}
	}

	t.Logf("✓ Complete dataset returned with original data + transformations")
	t.Logf("✓ Original fields: metadata, reports, entities")
	t.Logf("✓ In-place modifications: entity genders transformed")
	t.Logf("✓ New fields added: formatted_title, total_persons")
}
