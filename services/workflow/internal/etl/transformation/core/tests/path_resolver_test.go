package tests

import (
	"encoding/json"
	"os"
	"reflect"
	"testing"

	"workflow/internal/etl/transformation/core"
)

const (
	testNewValue = "new_value"
)

// loadTestData loads test data from JSON file for testing with realistic extraction output
func loadTestData() map[string]interface{} {
	data, err := os.ReadFile("testdata/sample_extraction.json")
	if err != nil {
		panic(err)
	}

	var result map[string]interface{}
	if err := json.Unmarshal(data, &result); err != nil {
		panic(err)
	}

	return result
}

// TestPathResolver_GetValue tests the core path navigation functionality.
// This test suite verifies that the PathResolver can navigate through various
// data structures using path expressions like "field.subfield" or "array[0].field".
//
// Test Categories:
// 1. Simple field access - Direct property access
// 2. Nested field access - Multi-level property navigation
// 3. Array operations - Index access, wildcards
// 4. Error handling - Missing fields, invalid paths
func TestPathResolver_GetValue(t *testing.T) {
	resolver := core.NewPathResolver()

	tests := []struct {
		name     string
		data     interface{} // Input data structure to navigate
		path     string      // Path expression to evaluate
		expected interface{} // Expected result value
		wantErr  bool        // Whether an error is expected
	}{
		{
			// Test: Simple field access
			// Input: {"field": "value"}
			// Path: "field"
			// Expected: "value"
			// Description: Tests basic property access at root level
			name:     "simple field access",
			data:     map[string]interface{}{"field": "value"},
			path:     "field",
			expected: "value",
			wantErr:  false,
		},
		{
			// Test: Nested field access
			// Input: {"level1": {"level2": "nested_value"}}
			// Path: "level1.level2"
			// Expected: "nested_value"
			// Description: Tests navigation through multiple object levels using dot notation
			name: "nested field access",
			data: map[string]interface{}{
				"level1": map[string]interface{}{
					"level2": "nested_value",
				},
			},
			path:     "level1.level2",
			expected: "nested_value",
			wantErr:  false,
		},
		{
			// Test: Array index access
			// Input: {"array": ["item0", "item1", "item2"]}
			// Path: "array[1]"
			// Expected: "item1"
			// Description: Tests accessing specific array element by index (0-based)
			name: "array index access",
			data: map[string]interface{}{
				"array": []interface{}{"item0", "item1", "item2"},
			},
			path:     "array[1]",
			expected: "item1",
			wantErr:  false,
		},
		{
			// Test: Array element with nested field
			// Input: {"items": [{"name": "first"}, {"name": "second"}]}
			// Path: "items[0].name"
			// Expected: "first"
			// Description: Tests combining array index access with field navigation
			name: "array first element",
			data: map[string]interface{}{
				"items": []interface{}{
					map[string]interface{}{"name": "first"},
					map[string]interface{}{"name": "second"},
				},
			},
			path:     "items[0].name",
			expected: "first",
			wantErr:  false,
		},
		{
			// Test: Array wildcard access
			// Input: {"items": [{"name": "item1"}, {"name": "item2"}]}
			// Path: "items[*]"
			// Expected: [{"name": "item1"}, {"name": "item2"}]
			// Description: Tests wildcard operator to get all array elements
			name: "array wildcard access",
			data: map[string]interface{}{
				"items": []interface{}{
					map[string]interface{}{"name": "item1"},
					map[string]interface{}{"name": "item2"},
				},
			},
			path: "items[*]",
			expected: []interface{}{
				map[string]interface{}{"name": "item1"},
				map[string]interface{}{"name": "item2"},
			},
			wantErr: false,
		},
		{
			// Test: Array wildcard with nested field extraction
			// Input: {"items": [{"name": "item1"}, {"name": "item2"}]}
			// Path: "items[*].name"
			// Expected: ["item1", "item2"]
			// Description: Tests extracting a specific field from all array elements
			name: "array wildcard with nested field",
			data: map[string]interface{}{
				"items": []interface{}{
					map[string]interface{}{"name": "item1"},
					map[string]interface{}{"name": "item2"},
				},
			},
			path:     "items[*].name",
			expected: []interface{}{"item1", "item2"},
			wantErr:  false, // Now supported with wildcard path navigation
		},
		{
			// Test: Missing field returns nil gracefully
			// Input: {"field": "value"}
			// Path: "missing"
			// Expected: nil (graceful handling)
			// Description: Tests graceful handling when requested field doesn't exist
			name:     "missing field",
			data:     map[string]interface{}{"field": "value"},
			path:     "missing",
			expected: nil,
			wantErr:  false,
		},
		{
			// Test: Missing nested field returns nil gracefully
			// Input: {"field": "value"}
			// Path: "missing.field"
			// Expected: nil (graceful handling)
			// Description: Tests graceful handling when parent field in path doesn't exist
			name:     "missing nested field",
			data:     map[string]interface{}{"field": "value"},
			path:     "missing.field",
			expected: nil,
			wantErr:  false,
		},
		{
			// Test: Array index out of bounds returns nil gracefully
			// Input: {"array": ["item0", "item1"]}
			// Path: "array[5]"
			// Expected: nil (graceful handling)
			// Description: Tests graceful handling for out-of-bounds array indices
			name: "array index out of bounds",
			data: map[string]interface{}{
				"array": []interface{}{"item0", "item1"},
			},
			path:     "array[5]",
			expected: nil,
			wantErr:  false,
		},
		{
			// Test: Invalid array index format
			// Input: {"array": ["item0", "item1"]}
			// Path: "array[invalid]"
			// Expected: error
			// Description: Tests error when array index is not a number
			name: "invalid array index",
			data: map[string]interface{}{
				"array": []interface{}{"item0", "item1"},
			},
			path:     "array[invalid]",
			expected: nil,
			wantErr:  true,
		},
		{
			// Test: Empty path returns entire data
			// Input: {"field": "value"}
			// Path: ""
			// Expected: {"field": "value"}
			// Description: Tests that empty path returns the root data structure
			name:     "empty path",
			data:     map[string]interface{}{"field": "value"},
			path:     "",
			expected: map[string]interface{}{"field": "value"},
			wantErr:  false,
		},
		{
			// Test: Nil data error
			// Input: nil
			// Path: "field"
			// Expected: error
			// Description: Tests error handling when input data is nil
			name:     "nil data",
			data:     nil,
			path:     "field",
			expected: nil,
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := resolver.GetValue(tt.data, tt.path)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.expected) {
				t.Errorf("GetValue() = %v, want %v", got, tt.expected)
			}
		})
	}
}

// TestPathResolver_GetValue_WithRealData tests path resolution with realistic extraction data.
// This test uses actual extraction output structure to verify the path resolver works
// correctly with complex, nested data that matches what the extraction layer produces.
//
// The test data includes:
// - Reports with nested report objects
// - Entities with demographics and extraction metadata
// - Assets with role context
// - Relationships with source/target references
// - Situations with status information
func TestPathResolver_GetValue_WithRealData(t *testing.T) {
	resolver := core.NewPathResolver()

	// Load real test data from sample extraction output
	testData := loadTestData()

	tests := []struct {
		name     string
		path     string      // Path to navigate in extraction data
		expected interface{} // Expected value from extraction
		wantErr  bool        // Whether path should fail
	}{
		{
			name:     "report ID",
			path:     "reports[0].report.id",
			expected: "RPT-2024-001",
			wantErr:  false,
		},
		{
			name:     "report title",
			path:     "reports[0].report.title",
			expected: "Burglary Report",
			wantErr:  false,
		},
		{
			name:     "report created_at",
			path:     "reports[0].report.created_at",
			expected: "2024-01-15T10:30:00Z",
			wantErr:  false,
		},
		{
			name:     "first entity ID",
			path:     "entities[0].entity.id",
			expected: "ENT-123",
			wantErr:  false,
		},
		{
			name:     "first entity demographics sex",
			path:     "entities[0].entity.demographics.sex",
			expected: "SEX_MALE",
			wantErr:  false,
		},
		{
			name:     "first entity demographics age",
			path:     "entities[0].entity.demographics.age",
			expected: float64(25), // JSON numbers are float64
			wantErr:  false,
		},
		{
			name:     "first entity extraction role",
			path:     "entities[0].extractionMetadata.role",
			expected: "VICTIM",
			wantErr:  false,
		},
		{
			name:     "second entity demographics sex",
			path:     "entities[1].entity.demographics.sex",
			expected: "SEX_FEMALE",
			wantErr:  false,
		},
		{
			name:     "second entity extraction role",
			path:     "entities[1].extractionMetadata.role",
			expected: "OFFENDER",
			wantErr:  false,
		},
		{
			name:     "first asset ID",
			path:     "assets[0].asset.id",
			expected: "ASSET-001",
			wantErr:  false,
		},
		{
			name:     "first asset extraction role",
			path:     "assets[0].extractionContext.role",
			expected: "CREATOR",
			wantErr:  false,
		},
		{
			name:     "metadata agency ID",
			path:     "metadata.agency_id",
			expected: "FBI",
			wantErr:  false,
		},
		{
			name:     "metadata entity count",
			path:     "metadata.entity_count",
			expected: float64(3),
			wantErr:  false,
		},
		{
			name:     "situation title",
			path:     "situations[0].title",
			expected: "Burglary Investigation",
			wantErr:  false,
		},
		{
			name:     "relationship type",
			path:     "relationships[0].relation.type",
			expected: "RELATIONSHIP_TYPE_VICTIM_OF",
			wantErr:  false,
		},
		{
			name:     "non-existent path",
			path:     "reports[0].nonexistent.field",
			expected: nil,
			wantErr:  false,
		},
		{
			name:     "out of bounds array access",
			path:     "reports[10].report.id",
			expected: nil,
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := resolver.GetValue(testData, tt.path)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.expected) {
				t.Errorf("GetValue() = %v, want %v", got, tt.expected)
			}
		})
	}
}

// TestPathResolver_SetValue tests the ability to set values at specific paths.
// This functionality is crucial for the transformation system to store processed
// data in the output structure.
//
// Test scenarios:
// 1. Setting simple fields at root level
// 2. Creating nested structures automatically
// 3. Overwriting existing values
// 4. Error handling for invalid paths
func TestPathResolver_SetValue(t *testing.T) {
	resolver := core.NewPathResolver()

	tests := []struct {
		name    string
		data    map[string]interface{}            // Initial data structure
		path    string                            // Path where to set value
		value   interface{}                       // Value to set
		wantErr bool                              // Whether error is expected
		check   func(map[string]interface{}) bool // Validation function
	}{
		{
			name:    "set simple field",
			data:    make(map[string]interface{}),
			path:    "field",
			value:   testValueField,
			wantErr: false,
			check: func(data map[string]interface{}) bool {
				return data["field"] == testValueField
			},
		},
		{
			name:    "set nested field",
			data:    make(map[string]interface{}),
			path:    "level1.level2",
			value:   "nested_value",
			wantErr: false,
			check: func(data map[string]interface{}) bool {
				level1, ok := data["level1"].(map[string]interface{})
				if !ok {
					return false
				}
				return level1["level2"] == "nested_value"
			},
		},
		{
			name:    "set deep nested field",
			data:    make(map[string]interface{}),
			path:    "a.b.c.d",
			value:   "deep_value",
			wantErr: false,
			check: func(data map[string]interface{}) bool {
				a, ok := data["a"].(map[string]interface{})
				if !ok {
					return false
				}
				b, ok := a["b"].(map[string]interface{})
				if !ok {
					return false
				}
				c, ok := b["c"].(map[string]interface{})
				if !ok {
					return false
				}
				return c["d"] == "deep_value"
			},
		},
		{
			name:    "overwrite existing field",
			data:    map[string]interface{}{"field": "old_value"},
			path:    "field",
			value:   testNewValue,
			wantErr: false,
			check: func(data map[string]interface{}) bool {
				return data["field"] == testNewValue
			},
		},
		{
			name:    "empty path",
			data:    make(map[string]interface{}),
			path:    "",
			value:   "value",
			wantErr: true,
			check:   nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := resolver.SetValue(tt.data, tt.path, tt.value)
			if (err != nil) != tt.wantErr {
				t.Errorf("SetValue() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && tt.check != nil {
				if !tt.check(tt.data) {
					t.Errorf("SetValue() did not set value correctly")
				}
			}
		})
	}
}

func TestPathResolver_ParsePath(t *testing.T) {
	resolver := core.NewPathResolver()

	tests := []struct {
		name     string
		path     string
		expected []core.PathSegment
		wantErr  bool
	}{
		{
			name: "simple field",
			path: "field",
			expected: []core.PathSegment{
				{Type: "field", Value: "field", IsWildcard: false},
			},
			wantErr: false,
		},
		{
			name: "nested field",
			path: "level1.level2",
			expected: []core.PathSegment{
				{Type: "field", Value: "level1", IsWildcard: false},
				{Type: "field", Value: "level2", IsWildcard: false},
			},
			wantErr: false,
		},
		{
			name: "array index",
			path: "array[0]",
			expected: []core.PathSegment{
				{Type: "field", Value: "array", IsWildcard: false},
				{Type: "index", Value: "0", IsWildcard: false},
			},
			wantErr: false,
		},
		{
			name: "array wildcard",
			path: "array[*]",
			expected: []core.PathSegment{
				{Type: "field", Value: "array", IsWildcard: false},
				{Type: "wildcard", Value: "*", IsWildcard: true},
			},
			wantErr: false,
		},
		{
			name: "array with nested field",
			path: "array[0].field",
			expected: []core.PathSegment{
				{Type: "field", Value: "array", IsWildcard: false},
				{Type: "index", Value: "0", IsWildcard: false},
				{Type: "field", Value: "field", IsWildcard: false},
			},
			wantErr: false,
		},
		{
			name: "complex path",
			path: "reports[0].report.sections[*].content",
			expected: []core.PathSegment{
				{Type: "field", Value: "reports", IsWildcard: false},
				{Type: "index", Value: "0", IsWildcard: false},
				{Type: "field", Value: "report", IsWildcard: false},
				{Type: "field", Value: "sections", IsWildcard: false},
				{Type: "wildcard", Value: "*", IsWildcard: true},
				{Type: "field", Value: "content", IsWildcard: false},
			},
			wantErr: false,
		},
		{
			name:     "invalid array notation - missing closing bracket",
			path:     "array[0",
			expected: nil,
			wantErr:  true,
		},
		{
			name:     "invalid array notation - missing opening bracket",
			path:     "array0]",
			expected: nil,
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Use reflection to access private method for testing
			method := reflect.ValueOf(resolver).MethodByName("parsePath")
			if !method.IsValid() {
				t.Skip("parsePath method not accessible")
			}

			// This is a hack to test the private method
			// In a real implementation, we might make this method public or use a different approach
			t.Logf("Testing path parsing for: %s", tt.path)
			// For now, just test that the path can be used successfully
			testData := map[string]interface{}{
				"field": "value",
				"array": []interface{}{"item0", "item1"},
				"reports": []interface{}{
					map[string]interface{}{
						"report": map[string]interface{}{
							"sections": []interface{}{
								map[string]interface{}{"content": "section1"},
								map[string]interface{}{"content": "section2"},
							},
						},
					},
				},
			}

			_, err := resolver.GetValue(testData, tt.path)
			if (err != nil) != tt.wantErr {
				t.Errorf("Path parsing via GetValue() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func BenchmarkPathResolver_GetValue(b *testing.B) {
	resolver := core.NewPathResolver()
	data := loadTestData()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = resolver.GetValue(data, "entities[0].entity.demographics.sex")
	}
}

func BenchmarkPathResolver_GetValue_ComplexPath(b *testing.B) {
	resolver := core.NewPathResolver()
	data := loadTestData()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = resolver.GetValue(data, "reports[0].report.created_at")
	}
}

// TestPathResolver_SetValueInPlace tests the in-place value setting functionality.
//
// This test suite verifies that the PathResolver can modify existing data structures
// in-place rather than creating new output structures. This is crucial for memory-efficient
// transformations where the original data structure can be safely modified.
//
// Test scenarios:
// 1. Modifying existing fields in maps
// 2. Adding new fields to existing maps
// 3. Modifying elements in arrays
// 4. Handling nested structures and arrays
// 5. Error handling for invalid operations
func TestPathResolver_SetValueInPlace(t *testing.T) {
	resolver := core.NewPathResolver()

	tests := []struct {
		name    string
		data    interface{}            // Input data to modify in-place
		path    string                 // Path where to set value
		value   interface{}            // Value to set
		wantErr bool                   // Whether error is expected
		check   func(interface{}) bool // Validation function
	}{
		{
			name: "modify existing field in map",
			data: map[string]interface{}{
				"field": "original_value",
			},
			path:    "field",
			value:   "modified_value",
			wantErr: false,
			check: func(data interface{}) bool {
				if m, ok := data.(map[string]interface{}); ok {
					return m["field"] == "modified_value"
				}
				return false
			},
		},
		{
			name: "add new field to existing map",
			data: map[string]interface{}{
				"existing": testValueField,
			},
			path:    "new_field",
			value:   testNewValue,
			wantErr: false,
			check: func(data interface{}) bool {
				if m, ok := data.(map[string]interface{}); ok {
					return m["existing"] == testValueField && m["new_field"] == testNewValue
				}
				return false
			},
		},
		{
			name: "modify nested field in place",
			data: map[string]interface{}{
				"level1": map[string]interface{}{
					"level2": "original",
				},
			},
			path:    "level1.level2",
			value:   "modified",
			wantErr: false,
			check: func(data interface{}) bool {
				if m, ok := data.(map[string]interface{}); ok {
					if level1, ok := m["level1"].(map[string]interface{}); ok {
						return level1["level2"] == "modified"
					}
				}
				return false
			},
		},
		{
			name: "add new nested field in place",
			data: map[string]interface{}{
				"level1": map[string]interface{}{
					"existing": testValueField,
				},
			},
			path:    "level1.new_field",
			value:   testNewValue,
			wantErr: false,
			check: func(data interface{}) bool {
				if m, ok := data.(map[string]interface{}); ok {
					if level1, ok := m["level1"].(map[string]interface{}); ok {
						return level1["existing"] == testValueField && level1["new_field"] == testNewValue
					}
				}
				return false
			},
		},
		{
			name: "create missing nested structure in place",
			data: map[string]interface{}{
				"existing": testValueField,
			},
			path:    "new_level.nested_field",
			value:   "nested_value",
			wantErr: false,
			check: func(data interface{}) bool {
				if m, ok := data.(map[string]interface{}); ok {
					if newLevel, ok := m["new_level"].(map[string]interface{}); ok {
						return m["existing"] == testValueField && newLevel["nested_field"] == "nested_value"
					}
				}
				return false
			},
		},
		{
			name: "modify array element in place",
			data: map[string]interface{}{
				"array": []interface{}{
					map[string]interface{}{"name": "item1"},
					map[string]interface{}{"name": "item2"},
				},
			},
			path:    "array[0].name",
			value:   "modified_item1",
			wantErr: false,
			check: func(data interface{}) bool {
				if m, ok := data.(map[string]interface{}); ok {
					if array, ok := m["array"].([]interface{}); ok {
						if len(array) > 0 {
							if item, ok := array[0].(map[string]interface{}); ok {
								return item["name"] == "modified_item1"
							}
						}
					}
				}
				return false
			},
		},
		{
			name: "add field to array element in place",
			data: map[string]interface{}{
				"array": []interface{}{
					map[string]interface{}{"name": "item1"},
				},
			},
			path:    "array[0].new_field",
			value:   testNewValue,
			wantErr: false,
			check: func(data interface{}) bool {
				if m, ok := data.(map[string]interface{}); ok {
					if array, ok := m["array"].([]interface{}); ok {
						if len(array) > 0 {
							if item, ok := array[0].(map[string]interface{}); ok {
								return item["name"] == "item1" && item["new_field"] == testNewValue
							}
						}
					}
				}
				return false
			},
		},
		{
			name: "complex nested modification",
			data: map[string]interface{}{
				"entities": []interface{}{
					map[string]interface{}{
						"entity": map[string]interface{}{
							"demographics": map[string]interface{}{
								"sex": "SEX_MALE",
							},
						},
					},
				},
			},
			path:    "entities[0].entity.demographics.sex_code",
			value:   "M",
			wantErr: false,
			check: func(data interface{}) bool {
				if m, ok := data.(map[string]interface{}); ok {
					if entities, ok := m["entities"].([]interface{}); ok {
						if len(entities) > 0 {
							if entity, ok := entities[0].(map[string]interface{}); ok {
								if entityData, ok := entity["entity"].(map[string]interface{}); ok {
									if demo, ok := entityData["demographics"].(map[string]interface{}); ok {
										// Original field should be preserved
										if demo["sex"] != "SEX_MALE" {
											return false
										}
										// New field should be added
										return demo["sex_code"] == "M"
									}
								}
							}
						}
					}
				}
				return false
			},
		},
		{
			name:    "error on nil data",
			data:    nil,
			path:    "field",
			value:   "value",
			wantErr: true,
			check:   nil,
		},
		{
			name: "error on array index out of bounds",
			data: map[string]interface{}{
				"array": []interface{}{"item1"},
			},
			path:    "array[5].field",
			value:   "value",
			wantErr: true,
			check:   nil,
		},
		{
			name: "error on invalid array index",
			data: map[string]interface{}{
				"array": []interface{}{"item1"},
			},
			path:    "array[invalid].field",
			value:   "value",
			wantErr: true,
			check:   nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := resolver.SetValueInPlace(tt.data, tt.path, tt.value)
			if (err != nil) != tt.wantErr {
				t.Errorf("SetValueInPlace() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && tt.check != nil {
				if !tt.check(tt.data) {
					t.Errorf("SetValueInPlace() did not modify data correctly")
				}
			}
		})
	}
}

// TestPathResolver_SetValueInPlace_WithRealData tests in-place modification with realistic extraction data.
//
// This test verifies that the PathResolver can handle in-place modifications on complex,
// nested data structures that match the output from the extraction layer.
func TestPathResolver_SetValueInPlace_WithRealData(t *testing.T) {
	resolver := core.NewPathResolver()

	// Load real test data and create a copy for modification
	originalData := loadTestData()

	// Deep copy the data for testing (to avoid modifying the original test data)
	testData := deepCopyTestData(originalData)

	tests := []struct {
		name    string
		path    string
		value   interface{}
		wantErr bool
		verify  func(map[string]interface{}) bool
	}{
		{
			name:    "add NIBRS sex code to first entity",
			path:    "entities[0].entity.demographics.sex_code",
			value:   "M",
			wantErr: false,
			verify: func(data map[string]interface{}) bool {
				if entities, ok := data["entities"].([]interface{}); ok {
					if len(entities) > 0 {
						if entity, ok := entities[0].(map[string]interface{}); ok {
							if entityData, ok := entity["entity"].(map[string]interface{}); ok {
								if demo, ok := entityData["demographics"].(map[string]interface{}); ok {
									// Original sex field should be preserved
									if demo["sex"] != "SEX_MALE" {
										return false
									}
									// New sex_code field should be added
									return demo["sex_code"] == "M"
								}
							}
						}
					}
				}
				return false
			},
		},
		{
			name:    "add sequence number to first entity",
			path:    "entities[0].sequence_number",
			value:   "01",
			wantErr: false,
			verify: func(data map[string]interface{}) bool {
				if entities, ok := data["entities"].([]interface{}); ok {
					if len(entities) > 0 {
						if entity, ok := entities[0].(map[string]interface{}); ok {
							return entity["sequence_number"] == "01"
						}
					}
				}
				return false
			},
		},
		{
			name:    "modify report title",
			path:    "reports[0].report.title",
			value:   "Modified Burglary Report",
			wantErr: false,
			verify: func(data map[string]interface{}) bool {
				if reports, ok := data["reports"].([]interface{}); ok {
					if len(reports) > 0 {
						if report, ok := reports[0].(map[string]interface{}); ok {
							if reportData, ok := report["report"].(map[string]interface{}); ok {
								return reportData["title"] == "Modified Burglary Report"
							}
						}
					}
				}
				return false
			},
		},
		{
			name:    "add transformation metadata",
			path:    "metadata.transformation_applied",
			value:   true,
			wantErr: false,
			verify: func(data map[string]interface{}) bool {
				if metadata, ok := data["metadata"].(map[string]interface{}); ok {
					// Original metadata should be preserved
					if metadata["agency_id"] != "FBI" {
						return false
					}
					// New field should be added
					return metadata["transformation_applied"] == true
				}
				return false
			},
		},
		{
			name:    "add NIBRS compliance flag to all entities",
			path:    "entities[1].nibrs_compliant",
			value:   true,
			wantErr: false,
			verify: func(data map[string]interface{}) bool {
				if entities, ok := data["entities"].([]interface{}); ok {
					if len(entities) > 1 {
						if entity, ok := entities[1].(map[string]interface{}); ok {
							return entity["nibrs_compliant"] == true
						}
					}
				}
				return false
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := resolver.SetValueInPlace(testData, tt.path, tt.value)
			if (err != nil) != tt.wantErr {
				t.Errorf("SetValueInPlace() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !tt.wantErr && tt.verify != nil {
				if !tt.verify(testData) {
					t.Errorf("SetValueInPlace() did not modify data correctly")
				}
			}
		})
	}
}

// TestPathResolver_InPlaceVsCopy_Comparison compares in-place and copy-based operations.
//
// This test verifies that in-place operations produce the same logical results as
// copy-based operations when setting values in new locations (no overwrites).
func TestPathResolver_InPlaceVsCopy_Comparison(t *testing.T) {
	resolver := core.NewPathResolver()

	testCases := []struct {
		name      string
		inputData map[string]interface{}
		path      string
		value     interface{}
	}{
		{
			name: "simple field addition",
			inputData: map[string]interface{}{
				"existing": testValueField,
			},
			path:  "new_field",
			value: testNewValue,
		},
		{
			name: "nested field addition",
			inputData: map[string]interface{}{
				"level1": map[string]interface{}{
					"existing": testValueField,
				},
			},
			path:  "level1.new_field",
			value: "nested_value",
		},
		{
			name: "complex structure addition",
			inputData: map[string]interface{}{
				"entities": []interface{}{
					map[string]interface{}{
						"name": "Entity1",
					},
				},
			},
			path:  "entities[0].transformed_name",
			value: "Transformed Entity1",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create separate copies for each operation
			copyData := deepCopyTestData(tc.inputData)
			inPlaceData := deepCopyTestData(tc.inputData)

			// Perform copy-based operation (using existing SetValue)
			copyOutput := make(map[string]interface{})
			err := resolver.SetValue(copyOutput, tc.path, tc.value)
			if err != nil {
				t.Fatalf("Copy operation failed: %v", err)
			}

			// Perform in-place operation
			err = resolver.SetValueInPlace(inPlaceData, tc.path, tc.value)
			if err != nil {
				t.Fatalf("In-place operation failed: %v", err)
			}

			// Extract the modified value from in-place data
			inPlaceValue, err := resolver.GetValue(inPlaceData, tc.path)
			if err != nil {
				t.Fatalf("Failed to extract value from in-place data: %v", err)
			}

			// Extract the value from copy-based result
			copyValue, err := resolver.GetValue(copyOutput, tc.path)
			if err != nil {
				t.Fatalf("Failed to extract value from copy data: %v", err)
			}

			// Compare the values - they should be identical
			if !reflect.DeepEqual(inPlaceValue, copyValue) {
				t.Errorf("In-place and copy operations produced different values")
				t.Errorf("In-place value: %v", inPlaceValue)
				t.Errorf("Copy value: %v", copyValue)
			}

			// Verify that copy-based operation preserved original input
			if !reflect.DeepEqual(copyData, tc.inputData) {
				t.Errorf("Copy operation should not modify original input")
			}
		})
	}
}

// Benchmark tests for in-place operations
func BenchmarkPathResolver_SetValueInPlace(b *testing.B) {
	resolver := core.NewPathResolver()

	// Create test data
	baseData := map[string]interface{}{
		"entities": []interface{}{
			map[string]interface{}{
				"entity": map[string]interface{}{
					"demographics": map[string]interface{}{
						"sex": "SEX_MALE",
					},
				},
			},
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		// Create a fresh copy for each iteration
		data := deepCopyTestData(baseData)
		_ = resolver.SetValueInPlace(data, "entities[0].entity.demographics.sex_code", "M")
	}
}

func BenchmarkPathResolver_SetValueInPlace_vs_SetValue(b *testing.B) {
	resolver := core.NewPathResolver()

	baseData := map[string]interface{}{
		"entities": []interface{}{
			map[string]interface{}{
				"entity": map[string]interface{}{
					"demographics": map[string]interface{}{
						"sex": "SEX_MALE",
					},
				},
			},
		},
	}

	b.Run("SetValueInPlace", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			data := deepCopyTestData(baseData)
			_ = resolver.SetValueInPlace(data, "entities[0].entity.demographics.sex_code", "M")
		}
	})

	b.Run("SetValue", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			outputData := make(map[string]interface{})
			_ = resolver.SetValue(outputData, "entities[0].entity.demographics.sex_code", "M")
		}
	})
}

func BenchmarkPathResolver_SetValue(b *testing.B) {
	resolver := core.NewPathResolver()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		data := make(map[string]interface{})
		_ = resolver.SetValue(data, "level1.level2.level3", "value")
	}
}

// Helper function to deep copy test data for isolated testing
func deepCopyTestData(data interface{}) map[string]interface{} {
	switch v := data.(type) {
	case map[string]interface{}:
		result := make(map[string]interface{})
		for key, value := range v {
			result[key] = deepCopyValue(value)
		}
		return result
	case []interface{}:
		// For array data at root level, we can't return a map[string]interface{}, so create a wrapper
		result := make([]interface{}, len(v))
		for i, value := range v {
			result[i] = deepCopyValue(value)
		}
		// Return the array wrapped in a map to satisfy the return type
		return map[string]interface{}{"data": result}
	default:
		if m, ok := data.(map[string]interface{}); ok {
			result := make(map[string]interface{})
			for key, value := range m {
				result[key] = deepCopyValue(value)
			}
			return result
		}
		// For non-map root data, return empty map (this shouldn't happen in our tests)
		return make(map[string]interface{})
	}
}

// deepCopyValue creates a deep copy of any value, preserving the original type structure
func deepCopyValue(data interface{}) interface{} {
	switch v := data.(type) {
	case map[string]interface{}:
		result := make(map[string]interface{})
		for key, value := range v {
			result[key] = deepCopyValue(value)
		}
		return result
	case []interface{}:
		result := make([]interface{}, len(v))
		for i, value := range v {
			result[i] = deepCopyValue(value)
		}
		return result
	default:
		// For primitive types, return as-is (they're copied by value)
		return v
	}
}
