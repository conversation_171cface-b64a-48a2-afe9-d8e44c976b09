package tests

import (
	"testing"
	"workflow/internal/etl/transformation/core"
)

func TestJSONPathFilter_BasicFiltering(t *testing.T) {
	resolver := core.NewPathResolver()

	// Test data that matches the extraction structure
	testData := map[string]interface{}{
		"entities": []interface{}{
			map[string]interface{}{
				"entity": map[string]interface{}{
					"entity_type": "ENTITY_TYPE_PERSON",
					"data": map[string]interface{}{
						"race": "Caucasian",
						"age":  30,
					},
				},
			},
			map[string]interface{}{
				"entity": map[string]interface{}{
					"entity_type": "ENTITY_TYPE_VEHICLE",
					"data": map[string]interface{}{
						"make": "Honda",
						"year": 2020,
					},
				},
			},
			map[string]interface{}{
				"entity": map[string]interface{}{
					"entity_type": "ENTITY_TYPE_PERSON",
					"data": map[string]interface{}{
						"race": "Hispanic",
						"age":  25,
					},
				},
			},
		},
	}

	t.Run("filter_person_entities", func(t *testing.T) {
		// Test the exact filter from the NIBRS configuration
		path := "entities[?(@.entity.entity_type == 'ENTITY_TYPE_PERSON')].entity.data.race"

		result, err := resolver.GetValue(testData, path)
		if err != nil {
			t.Fatalf("Failed to get value with JSONPath filter: %v", err)
		}

		// Should return array of race values from person entities only
		races, ok := result.([]interface{})
		if !ok {
			t.Fatalf("Expected array result, got %T", result)
		}

		if len(races) != 2 {
			t.Fatalf("Expected 2 person entities, got %d", len(races))
		}

		// Check that we got the correct race values
		expectedRaces := []string{"Caucasian", "Hispanic"}
		for i, race := range races {
			if race != expectedRaces[i] {
				t.Errorf("Expected race %s, got %v", expectedRaces[i], race)
			}
		}
	})

	t.Run("filter_age_greater_than", func(t *testing.T) {
		// Test numeric comparison
		path := "entities[?(@.entity.data.age > 28)].entity.data.race"

		result, err := resolver.GetValue(testData, path)
		if err != nil {
			t.Fatalf("Failed to get value with age filter: %v", err)
		}

		races, ok := result.([]interface{})
		if !ok {
			t.Fatalf("Expected array result, got %T", result)
		}

		// Should only return entities with age > 28 (only the 30-year-old)
		if len(races) != 1 {
			t.Fatalf("Expected 1 entity with age > 28, got %d", len(races))
		}

		if races[0] != "Caucasian" {
			t.Errorf("Expected Caucasian, got %v", races[0])
		}
	})

	t.Run("filter_no_matches", func(t *testing.T) {
		// Test filter that matches nothing
		path := "entities[?(@.entity.entity_type == 'ENTITY_TYPE_AIRPLANE')].entity.data.race"

		result, err := resolver.GetValue(testData, path)
		if err != nil {
			t.Fatalf("Failed to get value with no-match filter: %v", err)
		}

		races, ok := result.([]interface{})
		if !ok {
			t.Fatalf("Expected array result, got %T", result)
		}

		// Should return empty array
		if len(races) != 0 {
			t.Fatalf("Expected 0 entities, got %d", len(races))
		}
	})
}

func TestJSONPathFilter_ParseExpression(t *testing.T) {
	resolver := core.NewPathResolver()

	tests := []struct {
		name          string
		expression    string
		expectError   bool
		expectedField string
		expectedOp    string
		expectedVal   interface{}
	}{
		{
			name:          "basic_equality_filter",
			expression:    "?(@.entity.entity_type == 'ENTITY_TYPE_PERSON')",
			expectError:   false,
			expectedField: "entity.entity_type",
			expectedOp:    "==",
			expectedVal:   "ENTITY_TYPE_PERSON",
		},
		{
			name:          "numeric_comparison",
			expression:    "?(@.data.age >= 18)",
			expectError:   false,
			expectedField: "data.age",
			expectedOp:    ">=",
			expectedVal:   18,
		},
		{
			name:          "string_contains",
			expression:    "?(@.name contains 'John')",
			expectError:   false,
			expectedField: "name",
			expectedOp:    "contains",
			expectedVal:   "John",
		},
		{
			name:        "invalid_no_operator",
			expression:  "?(@.field)",
			expectError: true,
		},
		{
			name:        "invalid_no_question_mark",
			expression:  "(@.field == 'value')",
			expectError: true,
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			// Use reflection to access the private method for testing
			// Note: This is a bit hacky but allows us to test the parsing logic
			testData := map[string]interface{}{
				"field": []interface{}{
					map[string]interface{}{"entity": map[string]interface{}{"entity_type": "ENTITY_TYPE_PERSON"}},
				},
			}

			path := "field[" + test.expression + "]"
			_, err := resolver.GetValue(testData, path)

			if test.expectError {
				if err == nil {
					t.Errorf("Expected error for expression %s, but got none", test.expression)
				}
			} else {
				if err != nil {
					t.Errorf("Unexpected error for expression %s: %v", test.expression, err)
				}
			}
		})
	}
}
