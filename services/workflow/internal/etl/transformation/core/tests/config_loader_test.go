package tests

import (
	"os"
	"path/filepath"
	"strings"
	"testing"

	"workflow/internal/etl/transformation/core"
)

// TestConfigLoader_LoadFromString tests loading and validating JSON configuration from strings.
//
// This test suite verifies:
// 1. Valid configurations are loaded successfully
// 2. Invalid JSON is rejected with appropriate errors
// 3. Missing required fields are detected
// 4. Transformation-specific validation works
// 5. Lookup table references are validated
//
// The configuration structure being tested:
//
//	{
//	  "format_name": "NIBRS_XML_2023",          // Required: unique format identifier
//	  "field_mappings": [                       // Required: list of transformations
//	    {
//	      "name": "descriptive_name",           // Required: human-readable name
//	      "input_path": "path.to.input",        // Required: where to get data
//	      "transformation": "direct",           // Required: transformation type
//	      "output_path": "path.to.output",      // Required: where to store result
//	      "config": { ... }                     // Optional: transformation-specific config
//	    }
//	  ],
//	  "lookup_tables": {                        // Required: lookup table definitions
//	    "table_name": {
//	      "key": "value"
//	    }
//	  }
//	}
func TestConfigLoader_LoadFromString(t *testing.T) {
	loader := core.NewConfigLoader()

	tests := []struct {
		name        string
		jsonString  string // JSON configuration to load
		expectError bool   // Whether loading should fail
		errorMsg    string // Expected error message substring
	}{
		{
			// Test: Valid minimal configuration
			// Tests that a basic configuration with all required fields loads successfully
			name: "valid configuration",
			jsonString: `{
				"format_name": "NIBRS_XML_2023",
				"field_mappings": [
					{
						"name": "report_id",
						"input_path": "reports[0].report.id",
						"transformation": "direct",
						"output_path": "incident.number"
					}
				],
				"lookup_tables": {}
			}`,
			expectError: false,
		},
		{
			// Test: Valid configuration with lookup transformation
			// Tests that lookup transformations with proper table references validate correctly
			name: "valid configuration with lookup",
			jsonString: `{
				"format_name": "NIBRS_XML_2023",
				"field_mappings": [
					{
						"name": "sex_mapping",
						"input_path": "entities[0].entity.demographics.sex",
						"transformation": "lookup",
						"output_path": "person.sex",
						"config": {
							"lookup_table": "sex_codes"
						}
					}
				],
				"lookup_tables": {
					"sex_codes": {
						"SEX_MALE": "M",
						"SEX_FEMALE": "F"
					}
				}
			}`,
			expectError: false,
		},
		{
			name: "invalid JSON",
			jsonString: `{
				"format_name": "NIBRS_XML_2023",
				"field_mappings": [
					{
						"name": "report_id"
						"input_path": "reports[0].report.id",
			}`,
			expectError: true,
			errorMsg:    "failed to parse JSON config",
		},
		{
			name: "missing format_name",
			jsonString: `{
				"field_mappings": [
					{
						"name": "report_id",
						"input_path": "reports[0].report.id",
						"transformation": "direct",
						"output_path": "incident.number"
					}
				],
				"lookup_tables": {}
			}`,
			expectError: true,
			errorMsg:    "format_name is required",
		},
		{
			name: "empty field_mappings",
			jsonString: `{
				"format_name": "NIBRS_XML_2023",
				"field_mappings": [],
				"lookup_tables": {}
			}`,
			expectError: true,
			errorMsg:    "field_mappings is required",
		},
		{
			name: "missing lookup table",
			jsonString: `{
				"format_name": "NIBRS_XML_2023",
				"field_mappings": [
					{
						"name": "sex_mapping",
						"input_path": "entities[0].entity.demographics.sex",
						"transformation": "lookup",
						"output_path": "person.sex",
						"config": {
							"lookup_table": "sex_codes"
						}
					}
				],
				"lookup_tables": {}
			}`,
			expectError: true,
			errorMsg:    "lookup table 'sex_codes' is used but not defined",
		},
		{
			name: "unsupported transformation",
			jsonString: `{
				"format_name": "NIBRS_XML_2023",
				"field_mappings": [
					{
						"name": "test",
						"input_path": "field",
						"transformation": "unsupported_transform",
						"output_path": "output"
					}
				],
				"lookup_tables": {}
			}`,
			expectError: true,
			errorMsg:    "", // Don't check specific message, just ensure it errors
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config, err := loader.LoadFromString(tt.jsonString)

			if tt.expectError {
				if err == nil {
					t.Errorf("LoadFromString() expected error but got none")
					return
				}
				if tt.errorMsg != "" && !contains(err.Error(), tt.errorMsg) {
					// For validation errors, check if the underlying validation error contains the message
					if validationErr, ok := err.(*core.ConfigValidationError); ok {
						found := false
						for _, validationError := range validationErr.GetErrors() {
							if contains(validationError.Message, tt.errorMsg) {
								found = true
								break
							}
						}
						if !found {
							t.Errorf("LoadFromString() error = %q, want to contain %q", err.Error(), tt.errorMsg)
						}
					} else {
						t.Errorf("LoadFromString() error = %q, want to contain %q", err.Error(), tt.errorMsg)
					}
				}
			} else {
				if err != nil {
					t.Errorf("LoadFromString() unexpected error = %v", err)
					return
				}
				if config == nil {
					t.Errorf("LoadFromString() returned nil config")
				}
			}
		})
	}
}

func TestConfigLoader_LoadFromFile(t *testing.T) {
	loader := core.NewConfigLoader()

	// Create temporary test files
	tempDir, err := os.MkdirTemp("", "config_test")
	if err != nil {
		t.Fatalf("Failed to create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// Valid config file
	validConfig := `{
		"format_name": "NIBRS_XML_2023",
		"field_mappings": [
			{
				"name": "report_id",
				"input_path": "reports[0].report.id",
				"transformation": "direct",
				"output_path": "incident.number"
			}
		],
		"lookup_tables": {}
	}`

	validFile := filepath.Join(tempDir, "valid.json")
	err = os.WriteFile(validFile, []byte(validConfig), 0600)
	if err != nil {
		t.Fatalf("Failed to write valid config file: %v", err)
	}

	// Invalid JSON file
	invalidFile := filepath.Join(tempDir, "invalid.json")
	err = os.WriteFile(invalidFile, []byte("{invalid json"), 0600)
	if err != nil {
		t.Fatalf("Failed to write invalid config file: %v", err)
	}

	tests := []struct {
		name        string
		filePath    string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "valid file",
			filePath:    validFile,
			expectError: false,
		},
		{
			name:        "invalid JSON file",
			filePath:    invalidFile,
			expectError: true,
			errorMsg:    "failed to parse JSON config file",
		},
		{
			name:        "non-existent file",
			filePath:    filepath.Join(tempDir, "nonexistent.json"),
			expectError: true,
			errorMsg:    "failed to read config file",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config, err := loader.LoadFromFile(tt.filePath)

			if tt.expectError {
				if err == nil {
					t.Errorf("LoadFromFile() expected error but got none")
					return
				}
				if tt.errorMsg != "" && !contains(err.Error(), tt.errorMsg) {
					t.Errorf("LoadFromFile() error = %q, want to contain %q", err.Error(), tt.errorMsg)
				}
			} else {
				if err != nil {
					t.Errorf("LoadFromFile() unexpected error = %v", err)
					return
				}
				if config == nil {
					t.Errorf("LoadFromFile() returned nil config")
				}
			}
		})
	}
}

func TestConfigLoader_ValidateConfig(t *testing.T) {
	loader := core.NewConfigLoader()

	tests := []struct {
		name        string
		config      *core.MappingConfig
		expectError bool
		errorCount  int
	}{
		{
			name: "valid config",
			config: &core.MappingConfig{
				FormatName: "NIBRS_XML_2023",
				FieldMappings: []core.FieldMapping{
					{
						Name:           "report_id",
						InputPath:      "reports[0].report.id",
						Transformation: "direct",
						OutputPath:     "incident.number",
					},
				},
				LookupTables: map[string]map[string]string{},
			},
			expectError: false,
		},
		{
			name: "missing format name",
			config: &core.MappingConfig{
				FormatName: "",
				FieldMappings: []core.FieldMapping{
					{
						Name:           "report_id",
						InputPath:      "reports[0].report.id",
						Transformation: "direct",
						OutputPath:     "incident.number",
					},
				},
				LookupTables: map[string]map[string]string{},
			},
			expectError: true,
			errorCount:  1,
		},
		{
			name: "empty field mappings",
			config: &core.MappingConfig{
				FormatName:    "NIBRS_XML_2023",
				FieldMappings: []core.FieldMapping{},
				LookupTables:  map[string]map[string]string{},
			},
			expectError: true,
			errorCount:  1,
		},
		{
			name: "multiple validation errors",
			config: &core.MappingConfig{
				FormatName: "", // Error 1: missing format name
				FieldMappings: []core.FieldMapping{
					{
						Name:           "", // Error 2: missing name
						InputPath:      "", // Error 3: missing input path
						Transformation: "", // Error 4: missing transformation
						OutputPath:     "", // Error 5: missing output path
						// Additional errors may be generated for unsupported transformation
					},
				},
				LookupTables: map[string]map[string]string{},
			},
			expectError: true,
			errorCount:  7, // Adjusted to match actual error count
		},
		{
			name: "lookup transformation missing config",
			config: &core.MappingConfig{
				FormatName: "NIBRS_XML_2023",
				FieldMappings: []core.FieldMapping{
					{
						Name:           "sex_mapping",
						InputPath:      "entities[0].entity.demographics.sex",
						Transformation: "lookup",
						OutputPath:     "person.sex",
						Config:         nil, // Missing config
					},
				},
				LookupTables: map[string]map[string]string{},
			},
			expectError: true,
			errorCount:  1,
		},
		{
			name: "lookup transformation missing lookup_table",
			config: &core.MappingConfig{
				FormatName: "NIBRS_XML_2023",
				FieldMappings: []core.FieldMapping{
					{
						Name:           "sex_mapping",
						InputPath:      "entities[0].entity.demographics.sex",
						Transformation: "lookup",
						OutputPath:     "person.sex",
						Config:         map[string]interface{}{}, // Missing lookup_table
					},
				},
				LookupTables: map[string]map[string]string{},
			},
			expectError: true,
			errorCount:  1,
		},
		{
			name: "filter transformation missing config",
			config: &core.MappingConfig{
				FormatName: "NIBRS_XML_2023",
				FieldMappings: []core.FieldMapping{
					{
						Name:           "filter_victims",
						InputPath:      "entities[*]",
						Transformation: "filter",
						OutputPath:     "victims",
						Config:         nil, // Missing config
					},
				},
				LookupTables: map[string]map[string]string{},
			},
			expectError: true,
			errorCount:  1,
		},
		{
			name: "format transformation missing format_pattern",
			config: &core.MappingConfig{
				FormatName: "NIBRS_XML_2023",
				FieldMappings: []core.FieldMapping{
					{
						Name:           "format_number",
						InputPath:      "incident.number",
						Transformation: "format",
						OutputPath:     "formatted_number",
						Config:         map[string]interface{}{}, // Missing format_pattern
					},
				},
				LookupTables: map[string]map[string]string{},
			},
			expectError: true,
			errorCount:  1,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := loader.ValidateConfig(tt.config)

			if tt.expectError {
				if err == nil {
					t.Errorf("ValidateConfig() expected error but got none")
					return
				}

				if validationErr, ok := err.(*core.ConfigValidationError); ok {
					if len(validationErr.GetErrors()) != tt.errorCount {
						t.Errorf("ValidateConfig() error count = %d, want %d", len(validationErr.GetErrors()), tt.errorCount)
					}
				}
			} else if err != nil {
				t.Errorf("ValidateConfig() unexpected error = %v", err)
			}
		})
	}
}

func TestConfigLoader_GetUsedLookupTables(t *testing.T) {
	loader := core.NewConfigLoader()

	mappings := []core.FieldMapping{
		{
			Name:           "sex_mapping",
			Transformation: "lookup",
			Config: map[string]interface{}{
				"lookup_table": "sex_codes",
			},
		},
		{
			Name:           "race_mapping",
			Transformation: "lookup",
			Config: map[string]interface{}{
				"lookup_table": "race_codes",
			},
		},
		{
			Name:           "direct_mapping",
			Transformation: "direct",
			Config:         nil,
		},
		{
			Name:           "duplicate_sex_mapping",
			Transformation: "lookup",
			Config: map[string]interface{}{
				"lookup_table": "sex_codes", // Duplicate should be ignored
			},
		},
	}

	// Since we can't access private methods directly, we'll test through validation
	config := &core.MappingConfig{
		FormatName:    "TEST",
		FieldMappings: mappings,
		LookupTables:  map[string]map[string]string{
			// Missing sex_codes and race_codes to trigger validation error
		},
	}

	err := loader.ValidateConfig(config)
	if err == nil {
		t.Errorf("Expected validation error for missing lookup tables")
		return
	}

	validationErr, ok := err.(*core.ConfigValidationError)
	if !ok {
		t.Errorf("Expected ConfigValidationError, got %T", err)
		return
	}

	// Check that both missing tables are reported
	errors := validationErr.GetErrors()
	foundSexCodes := false
	foundRaceCodes := false

	for _, e := range errors {
		if contains(e.Message, "sex_codes") {
			foundSexCodes = true
		}
		if contains(e.Message, "race_codes") {
			foundRaceCodes = true
		}
	}

	if !foundSexCodes {
		t.Errorf("Expected error for missing sex_codes lookup table")
	}
	if !foundRaceCodes {
		t.Errorf("Expected error for missing race_codes lookup table")
	}
}

func TestConfigLoader_ValidationErrorMessages(t *testing.T) {
	loader := core.NewConfigLoader()

	// Test single error
	config := &core.MappingConfig{
		FormatName: "",
		FieldMappings: []core.FieldMapping{
			{
				Name:           "test",
				InputPath:      "field",
				Transformation: "direct",
				OutputPath:     "output",
			},
		},
		LookupTables: map[string]map[string]string{},
	}

	err := loader.ValidateConfig(config)
	if err == nil {
		t.Errorf("Expected validation error")
		return
	}

	validationErr, ok := err.(*core.ConfigValidationError)
	if !ok {
		t.Errorf("Expected ConfigValidationError, got %T", err)
		return
	}

	if len(validationErr.GetErrors()) != 1 {
		t.Errorf("Expected 1 error, got %d", len(validationErr.GetErrors()))
	}

	errorMsg := validationErr.Error()
	if !contains(errorMsg, "format_name is required") {
		t.Errorf("Error message should contain validation detail: %s", errorMsg)
	}
}

func TestConfigLoader_LoadFromJSON(t *testing.T) {
	loader := core.NewConfigLoader()

	validJSON := []byte(`{
		"format_name": "NIBRS_XML_2023",
		"field_mappings": [
			{
				"name": "report_id",
				"input_path": "reports[0].report.id",
				"transformation": "direct",
				"output_path": "incident.number"
			}
		],
		"lookup_tables": {}
	}`)

	config, err := loader.LoadFromJSON(validJSON)
	if err != nil {
		t.Errorf("LoadFromJSON() unexpected error = %v", err)
		return
	}

	if config == nil {
		t.Errorf("LoadFromJSON() returned nil config")
		return
	}

	if config.FormatName != "NIBRS_XML_2023" {
		t.Errorf("LoadFromJSON() format name = %s, want %s", config.FormatName, "NIBRS_XML_2023")
	}

	if len(config.FieldMappings) != 1 {
		t.Errorf("LoadFromJSON() field mappings count = %d, want %d", len(config.FieldMappings), 1)
	}
}

// Helper function to check if a string contains a substring
func contains(s, substr string) bool {
	return strings.Contains(s, substr)
}

func BenchmarkConfigLoader_LoadFromString(b *testing.B) {
	loader := core.NewConfigLoader()
	jsonString := `{
		"format_name": "NIBRS_XML_2023",
		"field_mappings": [
			{
				"name": "report_id",
				"input_path": "reports[0].report.id",
				"transformation": "direct",
				"output_path": "incident.number"
			}
		],
		"lookup_tables": {}
	}`

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = loader.LoadFromString(jsonString)
	}
}

// TestConfigLoader_TransformationModeValidation tests the validation of transformation_mode configuration.
//
// This test suite verifies that the configuration loader correctly validates the transformation_mode
// field and provides appropriate error messages for invalid values.
//
// Valid transformation modes:
// - "copy": Create new output structures (default, safe)
// - "in_place": Modify input data directly (memory efficient)
// - Empty/unspecified: Defaults to "copy" mode
//
// Invalid modes should trigger validation errors.
func TestConfigLoader_TransformationModeValidation(t *testing.T) {
	loader := core.NewConfigLoader()

	tests := []struct {
		name               string
		transformationMode string
		expectError        bool
		errorMsg           string
	}{
		{
			name:               "valid copy mode",
			transformationMode: "copy",
			expectError:        false,
		},
		{
			name:               "valid in_place mode",
			transformationMode: "in_place",
			expectError:        false,
		},
		{
			name:               "empty mode (should be valid - defaults to copy)",
			transformationMode: "",
			expectError:        false,
		},
		{
			name:               "invalid mode",
			transformationMode: "invalid_mode",
			expectError:        true,
			errorMsg:           "invalid transformation mode",
		},
		{
			name:               "typo in copy",
			transformationMode: "copi",
			expectError:        true,
			errorMsg:           "invalid transformation mode",
		},
		{
			name:               "typo in in_place",
			transformationMode: "in-place",
			expectError:        true,
			errorMsg:           "invalid transformation mode",
		},
		{
			name:               "uppercase mode",
			transformationMode: "COPY",
			expectError:        true,
			errorMsg:           "invalid transformation mode",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := &core.MappingConfig{
				FormatName:         "TEST_FORMAT",
				TransformationMode: tt.transformationMode,
				FieldMappings: []core.FieldMapping{
					{
						Name:           "test_field",
						InputPath:      "field",
						Transformation: "direct",
						OutputPath:     "output",
					},
				},
				LookupTables: map[string]map[string]string{},
			}

			err := loader.ValidateConfig(config)

			if tt.expectError {
				if err == nil {
					t.Errorf("ValidateConfig() expected error but got none")
					return
				}
				if tt.errorMsg != "" && !contains(err.Error(), tt.errorMsg) {
					t.Errorf("ValidateConfig() error = %q, want to contain %q", err.Error(), tt.errorMsg)
				}
			} else if err != nil {
				t.Errorf("ValidateConfig() unexpected error = %v", err)
			}
		})
	}
}

// TestConfigLoader_LoadFromString_WithTransformationMode tests loading configurations with transformation_mode.
//
// This test verifies that the configuration loader can successfully load and validate
// JSON configurations that include the transformation_mode field.
func TestConfigLoader_LoadFromString_WithTransformationMode(t *testing.T) {
	loader := core.NewConfigLoader()

	tests := []struct {
		name         string
		jsonConfig   string
		expectError  bool
		expectedMode string
	}{
		{
			name: "valid copy mode configuration",
			jsonConfig: `{
				"format_name": "TEST_FORMAT",
				"transformation_mode": "copy",
				"field_mappings": [
					{
						"name": "test_field",
						"input_path": "field",
						"transformation": "direct",
						"output_path": "output"
					}
				],
				"lookup_tables": {}
			}`,
			expectError:  false,
			expectedMode: "copy",
		},
		{
			name: "valid in_place mode configuration",
			jsonConfig: `{
				"format_name": "TEST_FORMAT",
				"transformation_mode": "in_place",
				"field_mappings": [
					{
						"name": "test_field",
						"input_path": "field",
						"transformation": "direct",
						"output_path": "output"
					}
				],
				"lookup_tables": {}
			}`,
			expectError:  false,
			expectedMode: "in_place",
		},
		{
			name: "configuration without transformation_mode (should default)",
			jsonConfig: `{
				"format_name": "TEST_FORMAT",
				"field_mappings": [
					{
						"name": "test_field",
						"input_path": "field",
						"transformation": "direct",
						"output_path": "output"
					}
				],
				"lookup_tables": {}
			}`,
			expectError:  false,
			expectedMode: "",
		},
		{
			name: "invalid transformation_mode in JSON",
			jsonConfig: `{
				"format_name": "TEST_FORMAT",
				"transformation_mode": "invalid_mode",
				"field_mappings": [
					{
						"name": "test_field",
						"input_path": "field",
						"transformation": "direct",
						"output_path": "output"
					}
				],
				"lookup_tables": {}
			}`,
			expectError:  true,
			expectedMode: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config, err := loader.LoadFromString(tt.jsonConfig)

			if tt.expectError {
				if err == nil {
					t.Errorf("LoadFromString() expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("LoadFromString() unexpected error = %v", err)
				return
			}

			if config == nil {
				t.Errorf("LoadFromString() returned nil config")
				return
			}

			if config.TransformationMode != tt.expectedMode {
				t.Errorf("LoadFromString() transformation_mode = %q, want %q", config.TransformationMode, tt.expectedMode)
			}
		})
	}
}

// TestConfigLoader_TransformationModeWithComplexConfig tests transformation mode validation with complex configurations.
//
// This test verifies that transformation mode validation works correctly even with
// complex configurations that include multiple field mappings, lookup tables, etc.
func TestConfigLoader_TransformationModeWithComplexConfig(t *testing.T) {
	loader := core.NewConfigLoader()

	// Test with a realistic NIBRS-style configuration
	complexConfig := `{
		"format_name": "NIBRS_XML_2024",
		"transformation_mode": "in_place",
		"field_mappings": [
			{
				"name": "incident_number",
				"input_path": "reports[0].report.id",
				"transformation": "format",
				"output_path": "incident.number",
				"config": {"format_pattern": "INC-%05d"}
			},
			{
				"name": "victim_sex",
				"input_path": "entities[0].entity.demographics.sex",
				"transformation": "lookup",
				"output_path": "victim.sex_code",
				"config": {"lookup_table": "sex_codes"}
			},
			{
				"name": "filter_victims",
				"input_path": "entities",
				"transformation": "filter",
				"output_path": "victims",
				"config": {
					"field": "extractionMetadata.role",
					"operator": "equals",
					"value": "VICTIM"
				}
			}
		],
		"lookup_tables": {
			"sex_codes": {
				"SEX_MALE": "M",
				"SEX_FEMALE": "F",
				"SEX_UNKNOWN": "U"
			}
		}
	}`

	config, err := loader.LoadFromString(complexConfig)
	if err != nil {
		t.Fatalf("LoadFromString() failed with complex config: %v", err)
	}

	if config.TransformationMode != "in_place" {
		t.Errorf("LoadFromString() transformation_mode = %q, want %q", config.TransformationMode, "in_place")
	}

	// Verify that all other aspects of the configuration are still validated correctly
	if len(config.FieldMappings) != 3 {
		t.Errorf("LoadFromString() field mappings count = %d, want %d", len(config.FieldMappings), 3)
	}

	if len(config.LookupTables) != 1 {
		t.Errorf("LoadFromString() lookup tables count = %d, want %d", len(config.LookupTables), 1)
	}
}

// TestConfigLoader_TransformationModeErrorContext tests that transformation mode errors provide good context.
//
// This test verifies that validation errors for transformation_mode include helpful
// context about what values are acceptable.
func TestConfigLoader_TransformationModeErrorContext(t *testing.T) {
	loader := core.NewConfigLoader()

	config := &core.MappingConfig{
		FormatName:         "TEST_FORMAT",
		TransformationMode: "invalid_mode",
		FieldMappings: []core.FieldMapping{
			{
				Name:           "test_field",
				InputPath:      "field",
				Transformation: "direct",
				OutputPath:     "output",
			},
		},
		LookupTables: map[string]map[string]string{},
	}

	err := loader.ValidateConfig(config)
	if err == nil {
		t.Fatalf("ValidateConfig() expected error but got none")
	}

	validationErr, ok := err.(*core.ConfigValidationError)
	if !ok {
		t.Fatalf("Expected ConfigValidationError, got %T", err)
	}

	errors := validationErr.GetErrors()
	foundTransformationModeError := false

	for _, e := range errors {
		if e.Field == "transformation_mode" {
			foundTransformationModeError = true

			// Check that error message contains helpful information
			if !contains(e.Message, "copy") || !contains(e.Message, "in_place") {
				t.Errorf("Transformation mode error should mention valid options: %s", e.Message)
			}

			// Check that the invalid value is included
			if e.Value != "invalid_mode" {
				t.Errorf("Error value = %v, want %v", e.Value, "invalid_mode")
			}

			break
		}
	}

	if !foundTransformationModeError {
		t.Errorf("Expected transformation_mode validation error")
	}
}

func BenchmarkConfigLoader_ValidateConfig(b *testing.B) {
	loader := core.NewConfigLoader()
	config := &core.MappingConfig{
		FormatName: "NIBRS_XML_2023",
		FieldMappings: []core.FieldMapping{
			{
				Name:           "report_id",
				InputPath:      "reports[0].report.id",
				Transformation: "direct",
				OutputPath:     "incident.number",
			},
		},
		LookupTables: map[string]map[string]string{},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = loader.ValidateConfig(config)
	}
}

func BenchmarkConfigLoader_ValidateConfig_WithTransformationMode(b *testing.B) {
	loader := core.NewConfigLoader()
	config := &core.MappingConfig{
		FormatName:         "NIBRS_XML_2023",
		TransformationMode: "in_place",
		FieldMappings: []core.FieldMapping{
			{
				Name:           "report_id",
				InputPath:      "reports[0].report.id",
				Transformation: "direct",
				OutputPath:     "incident.number",
			},
		},
		LookupTables: map[string]map[string]string{},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_ = loader.ValidateConfig(config)
	}
}
