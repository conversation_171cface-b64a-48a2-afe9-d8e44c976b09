package tests

import (
	"testing"
	"workflow/internal/etl/transformation/core"
)

// TestWildcardElementByElementProcessing tests that wildcard transformations process each array element individually
func TestWildcardElementByElementProcessing(t *testing.T) {
	// Create mapping engine
	mappingEngine := core.NewMappingEngine()

	// Simple test data with 3 people with different genders
	inputData := map[string]interface{}{
		"people": []interface{}{
			map[string]interface{}{
				"name":   "<PERSON>",
				"gender": "Male",
			},
			map[string]interface{}{
				"name":   "<PERSON>",
				"gender": "Female",
			},
			map[string]interface{}{
				"name":   "<PERSON>",
				"gender": "Unknown",
			},
		},
	}

	// Configuration with wildcard in-place transformation
	config := &core.MappingConfig{
		FormatName: "TEST_WILDCARD",
		FieldMappings: []core.FieldMapping{
			{
				Name:           "gender_lookup_inplace",
				InputPath:      "people[*].gender",
				OutputPath:     "people[*].gender", // Same path = in-place
				Transformation: "lookup",
				Config: map[string]interface{}{
					"lookup_table": "gender_codes",
				},
			},
		},
		LookupTables: map[string]map[string]string{
			"gender_codes": {
				"Male":    "M",
				"Female":  "F",
				"Unknown": "U",
			},
		},
	}

	// Apply transformation
	result, err := mappingEngine.TransformData(inputData, config)
	if err != nil {
		t.Fatalf("Transformation failed: %v", err)
	}

	// Verify that each person has their individual transformed gender
	people, ok := result["people"].([]interface{})
	if !ok {
		t.Fatal("Expected people to be an array")
	}

	if len(people) != 3 {
		t.Fatalf("Expected 3 people, got %d", len(people))
	}

	// Check first person (John) - should be "M", not an array
	person1 := people[0].(map[string]interface{})
	gender1 := person1["gender"]
	if gender1 != "M" {
		t.Errorf("Person 1 gender should be 'M', got: %v (type: %T)", gender1, gender1)
	}

	// Check second person (Jane) - should be "F", not an array
	person2 := people[1].(map[string]interface{})
	gender2 := person2["gender"]
	if gender2 != "F" {
		t.Errorf("Person 2 gender should be 'F', got: %v (type: %T)", gender2, gender2)
	}

	// Check third person (Alex) - should be "U", not an array
	person3 := people[2].(map[string]interface{})
	gender3 := person3["gender"]
	if gender3 != "U" {
		t.Errorf("Person 3 gender should be 'U', got: %v (type: %T)", gender3, gender3)
	}

	t.Logf("SUCCESS: Each person has individual transformed gender")
	t.Logf("Person 1: %v", gender1)
	t.Logf("Person 2: %v", gender2)
	t.Logf("Person 3: %v", gender3)
}

// TestWildcardElementByElementVsBatchProcessing compares element-by-element vs batch processing
func TestWildcardElementByElementVsBatchProcessing(t *testing.T) {
	mappingEngine := core.NewMappingEngine()

	// Test data
	inputData := map[string]interface{}{
		"items": []interface{}{
			map[string]interface{}{"value": "A"},
			map[string]interface{}{"value": "B"},
		},
	}

	// Test 1: In-place transformation (should use element-by-element)
	configInPlace := &core.MappingConfig{
		FormatName: "TEST_INPLACE",
		FieldMappings: []core.FieldMapping{
			{
				Name:           "value_transform_inplace",
				InputPath:      "items[*].value",
				OutputPath:     "items[*].value", // Same = in-place
				Transformation: "lookup",
				Config: map[string]interface{}{
					"lookup_table": "value_codes",
				},
			},
		},
		LookupTables: map[string]map[string]string{
			"value_codes": {
				"A": "1",
				"B": "2",
			},
		},
	}

	// Test 2: Copy transformation (uses standard processing)
	configCopy := &core.MappingConfig{
		FormatName: "TEST_COPY",
		FieldMappings: []core.FieldMapping{
			{
				Name:           "value_transform_copy",
				InputPath:      "items[*].value",
				OutputPath:     "transformed_values", // Different = copy
				Transformation: "lookup",
				Config: map[string]interface{}{
					"lookup_table": "value_codes",
				},
			},
		},
		LookupTables: map[string]map[string]string{
			"value_codes": {
				"A": "1",
				"B": "2",
			},
		},
	}

	// Test in-place transformation
	resultInPlace, err := mappingEngine.TransformData(inputData, configInPlace)
	if err != nil {
		t.Fatalf("In-place transformation failed: %v", err)
	}

	// Test copy transformation
	inputDataCopy := map[string]interface{}{
		"items": []interface{}{
			map[string]interface{}{"value": "A"},
			map[string]interface{}{"value": "B"},
		},
	}
	resultCopy, err := mappingEngine.TransformData(inputDataCopy, configCopy)
	if err != nil {
		t.Fatalf("Copy transformation failed: %v", err)
	}

	// Verify in-place result - each item should have individual transformed value
	itemsInPlace := resultInPlace["items"].([]interface{})
	item1InPlace := itemsInPlace[0].(map[string]interface{})
	item2InPlace := itemsInPlace[1].(map[string]interface{})

	if item1InPlace["value"] != "1" {
		t.Errorf("In-place: Item 1 should be '1', got: %v", item1InPlace["value"])
	}
	if item2InPlace["value"] != "2" {
		t.Errorf("In-place: Item 2 should be '2', got: %v", item2InPlace["value"])
	}

	// Verify copy result - should be an array of transformed values
	transformedValues := resultCopy["transformed_values"].([]interface{})
	if len(transformedValues) != 2 {
		t.Fatalf("Copy: Expected 2 transformed values, got %d", len(transformedValues))
	}
	if transformedValues[0] != "1" {
		t.Errorf("Copy: First value should be '1', got: %v", transformedValues[0])
	}
	if transformedValues[1] != "2" {
		t.Errorf("Copy: Second value should be '2', got: %v", transformedValues[1])
	}

	t.Logf("SUCCESS: Both transformation modes work correctly")
	t.Logf("In-place: Item 1 = %v, Item 2 = %v", item1InPlace["value"], item2InPlace["value"])
	t.Logf("Copy: Values = %v", transformedValues)
}
