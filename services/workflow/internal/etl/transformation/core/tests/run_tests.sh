#!/bin/bash

# Colorful test runner for transformation core tests
# Usage: ./run_tests.sh [test_name]

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BLUE}║                        🚀 TRANSFORMATION CORE TEST SUITE 🚀                            ║${NC}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

get_test_description() {
    local test_name=$1
    case "$test_name" in
        # Path Resolution Tests
        "TestPathResolver_GetValue")
            echo "🧭 Path Resolution Engine - Basic navigation through data structures"
            ;;
        "TestPathResolver_GetValue_WithRealData")
            echo "🎯 Path Resolution Engine - Real extraction data navigation"
            ;;
        "TestPathResolver_SetValue")
            echo "✏️  Path Resolution Engine - Setting values in data structures"
            ;;
        "TestPathResolver_ParsePath")
            echo "🔍 Path Resolution Engine - Parsing complex path expressions"
            ;;
        "TestPathResolver_SetValueInPlace")
            echo "🔧 Path Resolution Engine - In-place value modification"
            ;;
        "TestPathResolver_SetValueInPlace_WithRealData")
            echo "🎯 Path Resolution Engine - In-place modification with real data"
            ;;
        "TestPathResolver_InPlaceVsCopy_Comparison")
            echo "⚖️  Path Resolution Engine - Comparing in-place vs copy operations"
            ;;
            
        # Transformation Engine Tests
        "TestTransformationEngine_DirectTransformation")
            echo "➡️  Direct Transformation - Pass-through data without changes"
            ;;
        "TestTransformationEngine_LookupTransformation")
            echo "🔄 Lookup Transformation - Value mapping using lookup tables"
            ;;
        "TestTransformationEngine_FilterTransformation")
            echo "🔍 Filter Transformation - Array filtering with various operators"
            ;;
        "TestTransformationEngine_FormatTransformation")
            echo "📝 Format Transformation - String formatting with patterns"
            ;;
        "TestTransformationEngine_DateFormatTransformation")
            echo "📅 Date Format Transformation - Date string formatting"
            ;;
        "TestTransformationEngine_AddSequenceTransformation")
            echo "🔢 Add Sequence Transformation - Adding sequence numbers to arrays"
            ;;
        "TestTransformationEngine_GroupByTransformation")
            echo "📊 Group By Transformation - Grouping array items by field"
            ;;
        "TestTransformationEngine_SortTransformation")
            echo "🔀 Sort Transformation - Sorting arrays by field values"
            ;;
        "TestTransformationEngine_UnknownTransformation")
            echo "❌ Unknown Transformation - Error handling for invalid types"
            ;;
            
        # Config Loader Tests
        "TestConfigLoader_LoadFromString")
            echo "📋 Config Loader - Loading configuration from JSON strings"
            ;;
        "TestConfigLoader_LoadFromFile")
            echo "📁 Config Loader - Loading configuration from files"
            ;;
        "TestConfigLoader_ValidateConfig")
            echo "✅ Config Loader - Configuration validation"
            ;;
        "TestConfigLoader_GetUsedLookupTables")
            echo "🔍 Config Loader - Lookup table usage detection"
            ;;
        "TestConfigLoader_ValidationErrorMessages")
            echo "📝 Config Loader - Validation error message formatting"
            ;;
        "TestConfigLoader_LoadFromJSON")
            echo "📋 Config Loader - Loading from JSON bytes"
            ;;
        "TestConfigLoader_TransformationModeValidation")
            echo "🔄 Config Loader - Transformation mode validation"
            ;;
        "TestConfigLoader_LoadFromString_WithTransformationMode")
            echo "📋 Config Loader - Loading with transformation mode"
            ;;
        "TestConfigLoader_TransformationModeWithComplexConfig")
            echo "🔧 Config Loader - Complex configuration with transformation mode"
            ;;
        "TestConfigLoader_TransformationModeErrorContext")
            echo "📝 Config Loader - Transformation mode error messages"
            ;;
            
        # Mapping Engine Tests
        "TestMappingEngine_TransformData")
            echo "🔧 Mapping Engine - Core transformation orchestration"
            ;;
        "TestMappingEngine_TransformWithConfig")
            echo "📋 Mapping Engine - Transformation with JSON configuration"
            ;;
        "TestMappingEngine_GetSupportedTransformations")
            echo "📊 Mapping Engine - Supported transformation types"
            ;;
        "TestMappingEngine_ValidateFieldMapping")
            echo "✅ Mapping Engine - Field mapping validation"
            ;;
        "TestMappingEngine_WithRealExtractionData")
            echo "🎯 Mapping Engine - Real extraction data transformation"
            ;;
        "TestMappingEngine_ErrorHandling")
            echo "🚨 Mapping Engine - Error handling scenarios"
            ;;
        "TestMappingEngine_TransformationMode")
            echo "🔄 Mapping Engine - Transformation mode handling"
            ;;
        "TestMappingEngine_InPlaceTransformation")
            echo "🔧 Mapping Engine - In-place transformation testing"
            ;;
        "TestMappingEngine_InPlaceVsCopyComparison")
            echo "⚖️  Mapping Engine - In-place vs copy equivalence"
            ;;
        "TestMappingEngine_InPlaceTransformationWithConfig")
            echo "📋 Mapping Engine - In-place transformation with JSON config"
            ;;
        "TestMappingEngine_InPlaceTransformationPerformance")
            echo "⚡ Mapping Engine - In-place transformation performance"
            ;;
            
        # Integration Tests
        "TestNIBRSTransformationIntegration")
            echo "🔗 NIBRS Integration Test - Complete transformation pipeline from Hero to NIBRS"
            ;;
        "TestTransformationErrorHandling")
            echo "🚨 Error Handling Tests - Invalid configurations and missing data scenarios"
            ;;
        "TestComplexPathTransformations")
            echo "🎯 Complex Path Tests - Advanced path operations with wildcards and filtering"
            ;;
        "TestEdgeCaseIntegration")
            echo "🌟 Edge Case Integration - Large datasets and unicode handling"
            ;;
            
        # Edge Case Tests
        "TestCircularReferenceDetection")
            echo "🔄 Edge Cases - Circular reference detection and handling"
            ;;
        "TestMemoryPressure")
            echo "💾 Edge Cases - Memory pressure and large dataset handling"
            ;;
        "TestConcurrentTransformations")
            echo "🔀 Edge Cases - Concurrent transformation safety"
            ;;
        "TestExtremeValues")
            echo "🔥 Edge Cases - Extreme and boundary value handling"
            ;;
        "TestPathologicalCases")
            echo "⚠️  Edge Cases - Pathological and malformed input handling"
            ;;
            
        # Warning Tests
        "TestMappingEngineWarnings")
            echo "⚠️  Warning Mechanism - Capturing issues handled gracefully"
            ;;
        "TestWarningFormatAndContent")
            echo "📝 Warning Format - Verifying warning message quality"
            ;;
            
        # Wildcard Tests
        "TestWildcardElementByElementProcessing")
            echo "🎯 Wildcard Processing - Element-by-element transformation testing"
            ;;
        "TestWildcardElementByElementVsBatchProcessing")
            echo "⚖️  Wildcard Processing - Comparing element-by-element vs batch processing"
            ;;
        "TestNIBRSGenderTransformationDebug")
            echo "🔍 NIBRS Debug - Gender transformation debugging"
            ;;
        "TestWildcardDetectionInNIBRS")
            echo "🎯 NIBRS Debug - Wildcard detection in NIBRS-style paths"
            ;;
        "TestCompleteDatasetTransformation")
            echo "📦 Complete Dataset - Return entire transformed dataset"
            ;;
            
        # JSONPath Filter Tests
        "TestJSONPathFilter_BasicFiltering")
            echo "🔗 JSONPath Filters - Basic array filtering with expressions"
            ;;
        "TestJSONPathFilter_ParseExpression")
            echo "📝 JSONPath Filters - Expression parsing and validation"
            ;;
            
        # Benchmark Tests
        "BenchmarkNIBRSTransformation")
            echo "⚡ Performance Benchmark - NIBRS transformation pipeline speed test"
            ;;
        "BenchmarkPathResolver_GetValue")
            echo "⚡ Performance Benchmark - Path resolution speed"
            ;;
        "BenchmarkPathResolver_SetValue")
            echo "⚡ Performance Benchmark - Value setting speed"
            ;;
        "BenchmarkTransformationEngine_DirectTransformation")
            echo "⚡ Performance Benchmark - Direct transformation speed"
            ;;
        "BenchmarkTransformationEngine_LookupTransformation")
            echo "⚡ Performance Benchmark - Lookup transformation speed"
            ;;
        "BenchmarkTransformationEngine_FilterTransformation")
            echo "⚡ Performance Benchmark - Filter transformation speed"
            ;;
        "BenchmarkMappingEngine_TransformData")
            echo "⚡ Performance Benchmark - Complete transformation speed"
            ;;
        "BenchmarkMappingEngine_CopyTransformation")
            echo "⚡ Performance Benchmark - Copy-based transformation speed"
            ;;
        "BenchmarkMappingEngine_InPlaceTransformation")
            echo "⚡ Performance Benchmark - In-place transformation speed"
            ;;
        "BenchmarkConfigLoader_LoadFromString")
            echo "⚡ Performance Benchmark - Config loading speed"
            ;;
        "BenchmarkConfigLoader_ValidateConfig")
            echo "⚡ Performance Benchmark - Config validation speed"
            ;;
            
        *)
            echo ""
            ;;
    esac
}

print_test_category() {
    local test_name=$1
    local description=$(get_test_description "$test_name")
    
    if [[ -n $description ]]; then
        echo -e "${CYAN}┌─ $description${NC}"
    fi
}

print_test_result() {
    local line=$1
    
    if [[ $line =~ ^[[:space:]]*---[[:space:]]*PASS:[[:space:]]*([^[:space:]]+) ]]; then
        local test_name="${BASH_REMATCH[1]}"
        local short_name=$(echo "$test_name" | sed 's/.*\///')
        echo -e "${GREEN}    ✓ $short_name${NC}"
    elif [[ $line =~ ^[[:space:]]*---[[:space:]]*FAIL:[[:space:]]*([^[:space:]]+) ]]; then
        local test_name="${BASH_REMATCH[1]}"
        local short_name=$(echo "$test_name" | sed 's/.*\///')
        echo -e "${RED}    ✗ $short_name${NC}"
    elif [[ $line =~ ^===[[:space:]]*RUN[[:space:]]*([^[:space:]]+) ]]; then
        local test_name="${BASH_REMATCH[1]}"
        # Only print category for top-level tests (no '/' in name after removing package prefix)
        if [[ ! $test_name =~ / ]]; then
            print_test_category "$test_name"
        fi
    fi
}

print_summary() {
    local exit_code=$1
    echo ""
    if [[ $exit_code -eq 0 ]]; then
        echo -e "${GREEN}╔══════════════════════════════════════════════════════════════════════════════════════════╗${NC}"
        echo -e "${GREEN}║                              🎉 ALL TESTS PASSED! 🎉                                   ║${NC}"
        echo -e "${GREEN}╚══════════════════════════════════════════════════════════════════════════════════════════╝${NC}"
    else
        echo -e "${RED}╔══════════════════════════════════════════════════════════════════════════════════════════╗${NC}"
        echo -e "${RED}║                               ❌ TESTS FAILED ❌                                         ║${NC}"
        echo -e "${RED}╚══════════════════════════════════════════════════════════════════════════════════════════╝${NC}"
    fi
}

# Get directory of this script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

# Change to the workflow directory (go up 4 levels from tests/)
cd "$SCRIPT_DIR/../../../../.."

# Print header
print_header

# Determine test filter
TEST_FILTER=""
if [[ $# -gt 0 ]]; then
    TEST_FILTER="-run $1"
    echo -e "${YELLOW}🔍 Running filtered tests: $1${NC}"
    echo ""
fi

# Run tests and process output
echo -e "${PURPLE}🧪 Running transformation core tests...${NC}"
echo ""

# Capture both stdout and stderr, and the exit code
output=$(go test -v ./internal/etl/transformation/core/tests/ $TEST_FILTER 2>&1)
exit_code=$?

# Process each line of output
while IFS= read -r line; do
    print_test_result "$line"
done <<< "$output"

# Print summary
print_summary $exit_code

# If tests failed, show the raw output for debugging
if [[ $exit_code -ne 0 ]]; then
    echo ""
    echo -e "${RED}🔍 Raw test output for debugging:${NC}"
    echo -e "${WHITE}$output${NC}"
fi

exit $exit_code