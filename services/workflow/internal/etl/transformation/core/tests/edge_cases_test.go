package tests

import (
	"fmt"
	"math"
	"runtime"
	"strings"
	"sync"
	"testing"
	"time"

	"workflow/internal/etl/transformation/core"
)

const (
	testValueField = "value"
)

// TestCircularReferenceDetection tests the system's behavior with circular references.
//
// This test suite verifies that the transformation engine handles circular reference
// scenarios gracefully without causing infinite loops or stack overflows.
//
// Test scenarios:
// - Self-referencing paths in transformations
// - Circular dependency chains in field mappings
// - Recursive data structures
func TestCircularReferenceDetection(t *testing.T) {
	engine := core.NewMappingEngine()

	t.Run("self-referencing transformation", func(t *testing.T) {
		// Create data with potential circular structure
		circularData := make(map[string]interface{})
		circularData["field1"] = "value1"
		circularData["self"] = circularData // Self-reference

		config := &core.MappingConfig{
			FormatName: "CIRCULAR_TEST",
			FieldMappings: []core.FieldMapping{
				{
					Name:           "extract_self",
					InputPath:      "self.field1",
					Transformation: "direct",
					OutputPath:     "output.field",
				},
			},
			LookupTables: map[string]map[string]string{},
		}

		// Should handle gracefully without infinite loop
		_, err := engine.TransformData(circularData, config)
		if err == nil {
			// If no error, verify it handled the circular reference correctly
			t.Log("Circular reference handled gracefully")
		}
	})

	t.Run("circular dependency in field mappings", func(t *testing.T) {
		// Test when output of one transformation feeds into another creating a cycle
		inputData := map[string]interface{}{
			"initial": "value",
		}

		// Note: Current implementation processes mappings sequentially,
		// so true circular dependencies in mappings aren't possible.
		// This tests that the engine doesn't create unintended circular structures.
		config := &core.MappingConfig{
			FormatName: "DEPENDENCY_TEST",
			FieldMappings: []core.FieldMapping{
				{
					Name:           "step1",
					InputPath:      "initial",
					Transformation: "format",
					OutputPath:     "intermediate",
					Config: map[string]interface{}{
						"format_pattern": "STEP1-%s",
					},
				},
				{
					Name:           "step2",
					InputPath:      "intermediate",
					Transformation: "format",
					OutputPath:     "final",
					Config: map[string]interface{}{
						"format_pattern": "STEP2-%s",
					},
				},
			},
			LookupTables: map[string]map[string]string{},
		}

		result, err := engine.TransformData(inputData, config)
		if err != nil {
			t.Fatalf("Failed to handle dependent transformations: %v", err)
		}

		// Verify the transformations were applied in order
		if final, ok := result["final"].(string); !ok || final != "STEP2-STEP1-value" {
			t.Errorf("Dependent transformations not applied correctly: %v", result)
		}
	})

	t.Run("deeply nested circular structure", func(t *testing.T) {
		// Create a deeply nested structure with circular reference
		level1 := make(map[string]interface{})
		level2 := make(map[string]interface{})
		level3 := make(map[string]interface{})

		level1["child"] = level2
		level2["child"] = level3
		level3["child"] = level1 // Circular reference back to level1

		level1["data"] = "test_value"

		config := &core.MappingConfig{
			FormatName: "DEEP_CIRCULAR_TEST",
			FieldMappings: []core.FieldMapping{
				{
					Name:           "extract_deep",
					InputPath:      "child.child.child.data",
					Transformation: "direct",
					OutputPath:     "output",
				},
			},
			LookupTables: map[string]map[string]string{},
		}

		// Should handle gracefully - may error or resolve to the circular reference
		_, err := engine.TransformData(level1, config)
		if err != nil {
			t.Logf("Circular reference detected and handled: %v", err)
		}
	})

	t.Run("array containing self-reference", func(t *testing.T) {
		// Create array that contains reference to parent structure
		parentData := map[string]interface{}{
			"id": "parent",
		}

		// Create array that includes the parent
		parentData["items"] = []interface{}{
			map[string]interface{}{"name": "item1"},
			parentData, // Self-reference in array
			map[string]interface{}{"name": "item2"},
		}

		config := &core.MappingConfig{
			FormatName: "ARRAY_CIRCULAR_TEST",
			FieldMappings: []core.FieldMapping{
				{
					Name:           "extract_items",
					InputPath:      "items[*].name",
					Transformation: "direct",
					OutputPath:     "names",
				},
			},
			LookupTables: map[string]map[string]string{},
		}

		// Should handle the circular reference in array gracefully
		result, err := engine.TransformData(parentData, config)
		if err != nil {
			t.Logf("Array circular reference handled with error: %v", err)
		} else {
			// Don't log the full result as it contains circular references
			// Instead, check specific fields that should be safe
			if names, ok := result["names"].([]interface{}); ok {
				t.Logf("Array circular reference handled, extracted %d names", len(names))
			} else {
				t.Logf("Array circular reference handled, result type: %T", result)
			}
		}
	})
}

// TestMemoryPressure tests the system's behavior under memory pressure with large datasets.
//
// This test suite verifies that the transformation engine can handle very large
// datasets efficiently without running out of memory or degrading performance
// unacceptably.
//
// Test scenarios:
// - Very large arrays (millions of elements)
// - Deeply nested structures
// - Large string values
// - Memory usage tracking
func TestMemoryPressure(t *testing.T) {
	// Skip these tests in short mode as they can be resource intensive
	if testing.Short() {
		t.Skip("Skipping memory pressure tests in short mode")
	}

	engine := core.NewMappingEngine()

	t.Run("very large array transformation", func(t *testing.T) {
		// Create a large dataset with 100,000 entities
		const entityCount = 100000
		largeData := make(map[string]interface{})
		entities := make([]interface{}, entityCount)

		for i := 0; i < entityCount; i++ {
			entities[i] = map[string]interface{}{
				"id":   fmt.Sprintf("ENT-%d", i),
				"type": "PERSON",
				"name": fmt.Sprintf("Person %d", i),
				"age":  i % 100,
			}
		}
		largeData["entities"] = entities

		// Simple transformation to test memory handling
		config := &core.MappingConfig{
			FormatName: "LARGE_ARRAY_TEST",
			FieldMappings: []core.FieldMapping{
				{
					Name:           "filter_adults",
					InputPath:      "entities",
					Transformation: "filter",
					OutputPath:     "adults",
					Config: map[string]interface{}{
						"field":    "age",
						"operator": "greater_than",
						"value":    18,
					},
				},
			},
			LookupTables: map[string]map[string]string{},
		}

		// Track memory before transformation
		var memBefore runtime.MemStats
		runtime.ReadMemStats(&memBefore)

		start := time.Now()
		result, err := engine.TransformData(largeData, config)
		duration := time.Since(start)

		if err != nil {
			t.Fatalf("Large array transformation failed: %v", err)
		}

		// Track memory after transformation
		var memAfter runtime.MemStats
		runtime.ReadMemStats(&memAfter)

		// Calculate memory growth
		memGrowth := memAfter.Alloc - memBefore.Alloc
		memGrowthMB := float64(memGrowth) / 1024 / 1024

		t.Logf("Processed %d entities in %v", entityCount, duration)
		t.Logf("Memory growth: %.2f MB", memGrowthMB)

		// Verify result contains filtered data
		if adults, ok := result["adults"].([]interface{}); ok {
			t.Logf("Filtered to %d adults", len(adults))

			// Memory growth should be reasonable (not duplicating entire dataset multiple times)
			if memGrowthMB > 500 {
				t.Logf("Warning: High memory growth detected: %.2f MB", memGrowthMB)
			}
		}
	})

	t.Run("deeply nested structure transformation", func(t *testing.T) {
		// Create a deeply nested structure (100 levels deep)
		const nestingDepth = 100
		deepData := make(map[string]interface{})
		current := deepData

		for i := 0; i < nestingDepth; i++ {
			next := make(map[string]interface{})
			current[fmt.Sprintf("level%d", i)] = next
			current = next
		}
		current[testValueField] = "deep_value"

		// Build the path to the deepest value
		pathParts := make([]string, nestingDepth+1)
		for i := 0; i < nestingDepth; i++ {
			pathParts[i] = fmt.Sprintf("level%d", i)
		}
		pathParts[nestingDepth] = testValueField
		deepPath := strings.Join(pathParts, ".")

		config := &core.MappingConfig{
			FormatName: "DEEP_NESTING_TEST",
			FieldMappings: []core.FieldMapping{
				{
					Name:           "extract_deep",
					InputPath:      deepPath,
					Transformation: "direct",
					OutputPath:     "extracted",
				},
			},
			LookupTables: map[string]map[string]string{},
		}

		start := time.Now()
		result, err := engine.TransformData(deepData, config)
		duration := time.Since(start)

		if err != nil {
			t.Fatalf("Deep nesting transformation failed: %v", err)
		}

		t.Logf("Processed %d levels of nesting in %v", nestingDepth, duration)

		if extracted, ok := result["extracted"].(string); !ok || extracted != "deep_value" {
			t.Errorf("Failed to extract deeply nested value")
		}
	})

	t.Run("large string value transformation", func(t *testing.T) {
		// Create data with very large string values (10MB each)
		const stringSize = 10 * 1024 * 1024 // 10MB
		largeString := strings.Repeat("A", stringSize)

		largeData := map[string]interface{}{
			"entities": []interface{}{
				map[string]interface{}{
					"id":          "1",
					"description": largeString,
				},
				map[string]interface{}{
					"id":          "2",
					"description": largeString,
				},
			},
		}

		config := &core.MappingConfig{
			FormatName: "LARGE_STRING_TEST",
			FieldMappings: []core.FieldMapping{
				{
					Name:           "process_descriptions",
					InputPath:      "entities[*].description",
					Transformation: "direct",
					OutputPath:     "descriptions",
				},
			},
			LookupTables: map[string]map[string]string{},
		}

		var memBefore runtime.MemStats
		runtime.ReadMemStats(&memBefore)

		result, err := engine.TransformData(largeData, config)

		var memAfter runtime.MemStats
		runtime.ReadMemStats(&memAfter)

		if err != nil {
			t.Fatalf("Large string transformation failed: %v", err)
		}

		memGrowthMB := float64(memAfter.Alloc-memBefore.Alloc) / 1024 / 1024
		t.Logf("Memory growth for large strings: %.2f MB", memGrowthMB)

		if descriptions, ok := result["descriptions"].([]interface{}); ok {
			t.Logf("Processed %d large strings", len(descriptions))
		}
	})

	t.Run("memory efficiency of in-place transformation", func(t *testing.T) {
		// Test memory efficiency of in-place vs copy transformation
		const entityCount = 50000
		testData := make(map[string]interface{})
		entities := make([]interface{}, entityCount)

		for i := 0; i < entityCount; i++ {
			entities[i] = map[string]interface{}{
				"id":     fmt.Sprintf("ENT-%d", i),
				"status": "ACTIVE",
				"value":  i,
			}
		}
		testData["entities"] = entities

		// Test copy mode memory usage
		copyConfig := &core.MappingConfig{
			FormatName:         "MEMORY_COPY_TEST",
			TransformationMode: "copy",
			FieldMappings: []core.FieldMapping{
				{
					Name:           "add_formatted_id",
					InputPath:      "entities[0].id",
					Transformation: "format",
					OutputPath:     "entities[0].formatted_id",
					Config: map[string]interface{}{
						"format_pattern": "FORMATTED-%s",
					},
				},
			},
			LookupTables: map[string]map[string]string{},
		}

		runtime.GC()
		var copyMemBefore runtime.MemStats
		runtime.ReadMemStats(&copyMemBefore)

		copyData := deepCopyTestData(testData)
		_, err := engine.TransformData(copyData, copyConfig)

		runtime.GC()
		var copyMemAfter runtime.MemStats
		runtime.ReadMemStats(&copyMemAfter)

		if err != nil {
			t.Fatalf("Copy transformation failed: %v", err)
		}

		copyMemGrowthMB := float64(copyMemAfter.Alloc-copyMemBefore.Alloc) / 1024 / 1024

		// Test in-place mode memory usage
		inPlaceConfig := &core.MappingConfig{
			FormatName:         "MEMORY_INPLACE_TEST",
			TransformationMode: "in_place",
			FieldMappings:      copyConfig.FieldMappings,
			LookupTables:       copyConfig.LookupTables,
		}

		runtime.GC()
		var inPlaceMemBefore runtime.MemStats
		runtime.ReadMemStats(&inPlaceMemBefore)

		inPlaceData := deepCopyTestData(testData)
		_, err = engine.TransformData(inPlaceData, inPlaceConfig)

		runtime.GC()
		var inPlaceMemAfter runtime.MemStats
		runtime.ReadMemStats(&inPlaceMemAfter)

		if err != nil {
			t.Fatalf("In-place transformation failed: %v", err)
		}

		inPlaceMemGrowthMB := float64(inPlaceMemAfter.Alloc-inPlaceMemBefore.Alloc) / 1024 / 1024

		t.Logf("Copy mode memory growth: %.2f MB", copyMemGrowthMB)
		t.Logf("In-place mode memory growth: %.2f MB", inPlaceMemGrowthMB)

		// In-place should use significantly less memory
		if inPlaceMemGrowthMB > copyMemGrowthMB*0.5 {
			t.Logf("Note: In-place transformation did not show significant memory savings")
		}
	})
}

// TestConcurrentTransformations tests the thread safety of transformations.
//
// This test suite verifies that the transformation engine can handle concurrent
// transformations safely without race conditions or data corruption.
//
// Test scenarios:
// - Multiple goroutines transforming different data
// - Multiple goroutines using the same configuration
// - Concurrent access to lookup tables
// - Race condition detection
func TestConcurrentTransformations(t *testing.T) {
	engine := core.NewMappingEngine()

	t.Run("concurrent transformations with different data", func(t *testing.T) {
		// Shared configuration
		config := &core.MappingConfig{
			FormatName: "CONCURRENT_TEST",
			FieldMappings: []core.FieldMapping{
				{
					Name:           "transform_name",
					InputPath:      "name",
					Transformation: "format",
					OutputPath:     "formatted_name",
					Config: map[string]interface{}{
						"format_pattern": "USER-%s",
					},
				},
				{
					Name:           "map_status",
					InputPath:      "status",
					Transformation: "lookup",
					OutputPath:     "status_code",
					Config: map[string]interface{}{
						"lookup_table": "status_codes",
					},
				},
			},
			LookupTables: map[string]map[string]string{
				"status_codes": {
					"ACTIVE":   "A",
					"INACTIVE": "I",
					"PENDING":  "P",
				},
			},
		}

		// Number of concurrent transformations
		const concurrency = 100
		var wg sync.WaitGroup
		errors := make(chan error, concurrency)
		results := make(chan map[string]interface{}, concurrency)

		// Launch concurrent transformations
		for i := 0; i < concurrency; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()

				// Each goroutine has its own data
				data := map[string]interface{}{
					"name":   fmt.Sprintf("User%d", id),
					"status": []string{"ACTIVE", "INACTIVE", "PENDING"}[id%3],
				}

				result, err := engine.TransformData(data, config)
				if err != nil {
					errors <- err
					return
				}
				results <- result
			}(i)
		}

		// Wait for all transformations to complete
		wg.Wait()
		close(errors)
		close(results)

		// Check for any errors
		errorCount := 0
		for err := range errors {
			errorCount++
			t.Errorf("Concurrent transformation error: %v", err)
		}

		// Verify results
		resultCount := 0
		for result := range results {
			resultCount++

			// Verify each result has expected structure
			if _, ok := result["formatted_name"].(string); !ok {
				t.Error("Missing or invalid formatted_name in concurrent result")
			}
			if _, ok := result["status_code"].(string); !ok {
				t.Error("Missing or invalid status_code in concurrent result")
			}
		}

		t.Logf("Completed %d concurrent transformations with %d errors", resultCount, errorCount)

		if errorCount > 0 {
			t.Errorf("Concurrent transformations had %d errors", errorCount)
		}
	})

	t.Run("concurrent access to shared lookup tables", func(t *testing.T) {
		// Configuration with large lookup table
		lookupTable := make(map[string]string)
		for i := 0; i < 1000; i++ {
			lookupTable[fmt.Sprintf("KEY%d", i)] = fmt.Sprintf("VAL%d", i)
		}

		config := &core.MappingConfig{
			FormatName: "LOOKUP_CONCURRENT_TEST",
			FieldMappings: []core.FieldMapping{
				{
					Name:           "lookup_value",
					InputPath:      "key",
					Transformation: "lookup",
					OutputPath:     "value",
					Config: map[string]interface{}{
						"lookup_table": "large_table",
					},
				},
			},
			LookupTables: map[string]map[string]string{
				"large_table": lookupTable,
			},
		}

		const concurrency = 50
		var wg sync.WaitGroup
		successCount := 0
		var mu sync.Mutex

		// Launch concurrent lookups
		for i := 0; i < concurrency; i++ {
			wg.Add(1)
			go func(id int) {
				defer wg.Done()

				// Each goroutine looks up multiple keys
				for j := 0; j < 20; j++ {
					data := map[string]interface{}{
						"key": fmt.Sprintf("KEY%d", (id*20+j)%1000),
					}

					result, err := engine.TransformData(data, config)
					if err != nil {
						t.Errorf("Lookup transformation error: %v", err)
						return
					}

					// Verify the lookup was correct
					expectedValue := fmt.Sprintf("VAL%d", (id*20+j)%1000)
					if value, ok := result["value"].(string); !ok || value != expectedValue {
						t.Errorf("Incorrect lookup result: expected %s, got %v", expectedValue, value)
						return
					}
				}

				mu.Lock()
				successCount++
				mu.Unlock()
			}(i)
		}

		wg.Wait()
		t.Logf("Completed %d concurrent lookup transformations successfully", successCount)
	})

	t.Run("concurrent in-place transformations", func(t *testing.T) {
		// Test that in-place transformations on separate data structures are safe
		config := &core.MappingConfig{
			FormatName:         "INPLACE_CONCURRENT_TEST",
			TransformationMode: "in_place",
			FieldMappings: []core.FieldMapping{
				{
					Name:           "modify_field",
					InputPath:      "field",
					Transformation: "format",
					OutputPath:     "field",
					Config: map[string]interface{}{
						"format_pattern": "MODIFIED-%s",
					},
				},
			},
			LookupTables: map[string]map[string]string{},
		}

		const concurrency = 50
		var wg sync.WaitGroup
		dataStructures := make([]map[string]interface{}, concurrency)

		// Create separate data structures for each goroutine
		for i := 0; i < concurrency; i++ {
			dataStructures[i] = map[string]interface{}{
				"field": fmt.Sprintf("value%d", i),
				"id":    i,
			}
		}

		// Transform concurrently
		results := make([]map[string]interface{}, concurrency)
		for i := 0; i < concurrency; i++ {
			wg.Add(1)
			go func(index int) {
				defer wg.Done()

				result, err := engine.TransformData(dataStructures[index], config)
				if err != nil {
					t.Errorf("In-place transformation error: %v", err)
				}
				results[index] = result
			}(i)
		}

		wg.Wait()

		// Verify all transformations were applied correctly
		for i := 0; i < concurrency; i++ {
			expectedField := fmt.Sprintf("MODIFIED-value%d", i)
			if results[i] != nil {
				if field, ok := results[i]["field"].(string); !ok || field != expectedField {
					t.Errorf("In-place transformation incorrect for index %d: expected %s, got %v",
						i, expectedField, results[i]["field"])
				}
			}
		}

		t.Log("Concurrent in-place transformations completed successfully")
	})

	t.Run("stress test with mixed operations", func(t *testing.T) {
		// Comprehensive stress test with various transformation types
		config := &core.MappingConfig{
			FormatName: "STRESS_TEST",
			FieldMappings: []core.FieldMapping{
				{
					Name:           "format_id",
					InputPath:      "id",
					Transformation: "format",
					OutputPath:     "formatted_id",
					Config: map[string]interface{}{
						"format_pattern": "ID-%05d",
					},
				},
				{
					Name:           "lookup_type",
					InputPath:      "type",
					Transformation: "lookup",
					OutputPath:     "type_code",
					Config: map[string]interface{}{
						"lookup_table": "type_codes",
					},
				},
				{
					Name:           "filter_items",
					InputPath:      "items",
					Transformation: "filter",
					OutputPath:     "active_items",
					Config: map[string]interface{}{
						"field":    "status",
						"operator": "equals",
						"value":    "active",
					},
				},
			},
			LookupTables: map[string]map[string]string{
				"type_codes": {
					"PERSON":  "P",
					"VEHICLE": "V",
					"WEAPON":  "W",
				},
			},
		}

		const iterations = 1000
		successCount := 0
		var mu sync.Mutex

		// Use a worker pool pattern
		workers := 10
		jobs := make(chan int, iterations)
		var wg sync.WaitGroup

		// Start workers
		for w := 0; w < workers; w++ {
			wg.Add(1)
			go func() {
				defer wg.Done()
				for id := range jobs {
					data := map[string]interface{}{
						"id":   id,
						"type": []string{"PERSON", "VEHICLE", "WEAPON"}[id%3],
						"items": []interface{}{
							map[string]interface{}{"name": "item1", "status": "active"},
							map[string]interface{}{"name": "item2", "status": "inactive"},
							map[string]interface{}{"name": "item3", "status": "active"},
						},
					}

					_, err := engine.TransformData(data, config)
					if err == nil {
						mu.Lock()
						successCount++
						mu.Unlock()
					}
				}
			}()
		}

		// Send jobs
		for i := 0; i < iterations; i++ {
			jobs <- i
		}
		close(jobs)

		// Wait for completion
		wg.Wait()

		t.Logf("Stress test completed: %d/%d successful transformations", successCount, iterations)

		if successCount < iterations {
			t.Errorf("Some transformations failed: %d/%d", successCount, iterations)
		}
	})
}

// TestExtremeValues tests the system's behavior with extreme input values.
//
// This test suite verifies that the transformation engine handles edge cases
// related to extreme values gracefully.
//
// Test scenarios:
// - Maximum integer values
// - Very long strings
// - Unicode and special characters
// - Empty and nil values
// - Type boundary conditions
func TestExtremeValues(t *testing.T) {
	engine := core.NewMappingEngine()

	t.Run("maximum numeric values", func(t *testing.T) {
		extremeData := map[string]interface{}{
			"max_int":      math.MaxInt64,
			"min_int":      math.MinInt64,
			"max_float":    math.MaxFloat64,
			"min_float":    math.SmallestNonzeroFloat64,
			"infinity":     math.Inf(1),
			"neg_infinity": math.Inf(-1),
			"nan":          math.NaN(),
		}

		config := &core.MappingConfig{
			FormatName: "EXTREME_NUMERIC_TEST",
			FieldMappings: []core.FieldMapping{
				{
					Name:           "format_max_int",
					InputPath:      "max_int",
					Transformation: "format",
					OutputPath:     "formatted_max_int",
					Config: map[string]interface{}{
						"format_pattern": "MAX: %d",
					},
				},
				{
					Name:           "format_infinity",
					InputPath:      "infinity",
					Transformation: "format",
					OutputPath:     "formatted_infinity",
					Config: map[string]interface{}{
						"format_pattern": "INF: %f",
					},
				},
				{
					Name:           "direct_nan",
					InputPath:      "nan",
					Transformation: "direct",
					OutputPath:     "nan_value",
				},
			},
			LookupTables: map[string]map[string]string{},
		}

		result, err := engine.TransformData(extremeData, config)
		if err != nil {
			t.Logf("Extreme numeric transformation error (may be expected): %v", err)
		} else {
			t.Logf("Extreme numeric values handled: %v", result)
		}
	})

	t.Run("unicode and special characters", func(t *testing.T) {
		unicodeData := map[string]interface{}{
			"emoji":      "🚔👮‍♀️🚨", // Police-related emojis
			"chinese":    "警察局",
			"arabic":     "شرطة",
			"special":    "\n\t\r\x00",
			"zero_width": "test\u200Btest",          // Zero-width space
			"rtl":        "\u200fמימין לשמאל\u200f", // Right-to-left text with RLM markers
		}

		config := &core.MappingConfig{
			FormatName: "UNICODE_TEST",
			FieldMappings: []core.FieldMapping{
				{
					Name:           "process_emoji",
					InputPath:      "emoji",
					Transformation: "format",
					OutputPath:     "formatted_emoji",
					Config: map[string]interface{}{
						"format_pattern": "Police: %s",
					},
				},
				{
					Name:           "lookup_chinese",
					InputPath:      "chinese",
					Transformation: "lookup",
					OutputPath:     "chinese_lookup",
					Config: map[string]interface{}{
						"lookup_table":  "unicode_table",
						"default_value": "UNKNOWN_UNICODE",
					},
				},
			},
			LookupTables: map[string]map[string]string{
				"unicode_table": {
					"警察局": "POLICE_STATION",
				},
			},
		}

		result, err := engine.TransformData(unicodeData, config)
		if err != nil {
			t.Fatalf("Unicode transformation failed: %v", err)
		}

		t.Logf("Unicode transformation result: %v", result)

		// Verify unicode handling
		if emoji, ok := result["formatted_emoji"].(string); ok {
			if !strings.Contains(emoji, "🚔") {
				t.Error("Unicode emoji not preserved in transformation")
			}
		}
	})

	t.Run("empty and nil value handling", func(t *testing.T) {
		emptyData := map[string]interface{}{
			"empty_string": "",
			"nil_value":    nil,
			"empty_array":  []interface{}{},
			"empty_map":    map[string]interface{}{},
			"zero_int":     0,
			"false_bool":   false,
		}

		config := &core.MappingConfig{
			FormatName: "EMPTY_VALUES_TEST",
			FieldMappings: []core.FieldMapping{
				{
					Name:           "format_empty_string",
					InputPath:      "empty_string",
					Transformation: "format",
					OutputPath:     "formatted_empty",
					Config: map[string]interface{}{
						"format_pattern": "Value: '%s'",
					},
				},
				{
					Name:           "lookup_nil",
					InputPath:      "nil_value",
					Transformation: "lookup",
					OutputPath:     "nil_lookup",
					Config: map[string]interface{}{
						"lookup_table":  "nil_table",
						"default_value": "NULL_VALUE",
					},
				},
				{
					Name:           "filter_empty_array",
					InputPath:      "empty_array",
					Transformation: "filter",
					OutputPath:     "filtered_empty",
					Config: map[string]interface{}{
						"field":    "any",
						"operator": "equals",
						"value":    "something",
					},
				},
			},
			LookupTables: map[string]map[string]string{
				"nil_table": {},
			},
		}

		result, err := engine.TransformData(emptyData, config)
		if err != nil {
			t.Logf("Empty value transformation error: %v", err)
		} else {
			t.Logf("Empty value transformation result: %v", result)

			// Verify empty string formatting
			if formatted, ok := result["formatted_empty"].(string); ok {
				if formatted != "Value: ''" {
					t.Errorf("Empty string not formatted correctly: %s", formatted)
				}
			}
		}
	})

	t.Run("very long strings", func(t *testing.T) {
		// Create strings of various extreme lengths
		veryLongString := strings.Repeat("A", 1000000) // 1MB string
		repeatingPattern := strings.Repeat("PATTERN", 10000)

		longData := map[string]interface{}{
			"long_string":    veryLongString,
			"pattern_string": repeatingPattern,
		}

		config := &core.MappingConfig{
			FormatName: "LONG_STRING_TEST",
			FieldMappings: []core.FieldMapping{
				{
					Name:           "substring_long",
					InputPath:      "long_string",
					Transformation: "format",
					OutputPath:     "truncated",
					Config: map[string]interface{}{
						"format_pattern": "%.100s...", // Truncate to 100 chars
					},
				},
				{
					Name:           "direct_pattern",
					InputPath:      "pattern_string",
					Transformation: "direct",
					OutputPath:     "pattern_copy",
				},
			},
			LookupTables: map[string]map[string]string{},
		}

		start := time.Now()
		result, err := engine.TransformData(longData, config)
		duration := time.Since(start)

		if err != nil {
			t.Fatalf("Long string transformation failed: %v", err)
		}

		t.Logf("Long string transformation completed in %v", duration)

		// Verify truncation worked
		if truncated, ok := result["truncated"].(string); ok {
			if len(truncated) != 103 { // 100 chars + "..."
				t.Errorf("String truncation failed, length: %d", len(truncated))
			}
		}
	})

	t.Run("type boundary conditions", func(t *testing.T) {
		// Test values at type boundaries
		boundaryData := map[string]interface{}{
			"int_string":   "12345",
			"float_string": "123.45",
			"bool_string":  "true",
			"string_int":   12345,
			"string_float": 123.45,
			"string_bool":  true,
			"mixed_array":  []interface{}{1, "two", 3.0, true, nil},
			"nested_extreme": map[string]interface{}{
				"level1": map[string]interface{}{
					"level2": []interface{}{
						map[string]interface{}{"value": math.MaxInt32},
					},
				},
			},
		}

		config := &core.MappingConfig{
			FormatName: "TYPE_BOUNDARY_TEST",
			FieldMappings: []core.FieldMapping{
				{
					Name:           "format_int_as_string",
					InputPath:      "string_int",
					Transformation: "format",
					OutputPath:     "int_formatted",
					Config: map[string]interface{}{
						"format_pattern": "Number: %d",
					},
				},
				{
					Name:           "format_mixed_array",
					InputPath:      "mixed_array",
					Transformation: "direct",
					OutputPath:     "mixed_copy",
				},
				{
					Name:           "extract_nested_extreme",
					InputPath:      "nested_extreme.level1.level2[0].value",
					Transformation: "format",
					OutputPath:     "extreme_formatted",
					Config: map[string]interface{}{
						"format_pattern": "Max: %d",
					},
				},
			},
			LookupTables: map[string]map[string]string{},
		}

		result, err := engine.TransformData(boundaryData, config)
		if err != nil {
			t.Fatalf("Type boundary transformation failed: %v", err)
		}

		t.Logf("Type boundary transformation result: %v", result)

		// Verify mixed array handling
		if mixed, ok := result["mixed_copy"].([]interface{}); ok {
			if len(mixed) != 5 {
				t.Errorf("Mixed array not copied correctly, length: %d", len(mixed))
			}
		}
	})
}

// TestPathologicalCases tests pathological cases that might break the system.
//
// This test suite verifies that the transformation engine handles pathological
// cases that could potentially cause issues.
func TestPathologicalCases(t *testing.T) {
	engine := core.NewMappingEngine()

	t.Run("malformed path expressions", func(t *testing.T) {
		testData := map[string]interface{}{
			"field": "value",
		}

		malformedPaths := []string{
			"field[",              // Unclosed bracket
			"field]",              // Unexpected bracket
			"field[[0]]",          // Double brackets
			"field[*][*][*]",      // Multiple wildcards
			"field..",             // Empty path segment
			"field.[0]",           // Invalid syntax
			"[0]field",            // Starting with index
			"field[999999999999]", // Huge index
		}

		for _, path := range malformedPaths {
			config := &core.MappingConfig{
				FormatName: "MALFORMED_PATH_TEST",
				FieldMappings: []core.FieldMapping{
					{
						Name:           "test_malformed",
						InputPath:      path,
						Transformation: "direct",
						OutputPath:     "output",
					},
				},
				LookupTables: map[string]map[string]string{},
			}

			_, err := engine.TransformData(testData, config)
			if err == nil {
				t.Logf("Malformed path '%s' did not produce error (may be valid)", path)
			} else {
				t.Logf("Malformed path '%s' handled with error: %v", path, err)
			}
		}
	})

	t.Run("recursive filter conditions", func(t *testing.T) {
		// Create data where filter result could reference itself
		recursiveData := map[string]interface{}{
			"items": []interface{}{
				map[string]interface{}{
					"id":     "1",
					"parent": "2",
					"active": true,
				},
				map[string]interface{}{
					"id":     "2",
					"parent": "1",
					"active": true,
				},
			},
		}

		config := &core.MappingConfig{
			FormatName: "RECURSIVE_FILTER_TEST",
			FieldMappings: []core.FieldMapping{
				{
					Name:           "filter_active",
					InputPath:      "items",
					Transformation: "filter",
					OutputPath:     "active_items",
					Config: map[string]interface{}{
						"field":    "active",
						"operator": "equals",
						"value":    true,
					},
				},
			},
			LookupTables: map[string]map[string]string{},
		}

		result, err := engine.TransformData(recursiveData, config)
		if err != nil {
			t.Fatalf("Recursive filter failed: %v", err)
		}

		if activeItems, ok := result["active_items"].([]interface{}); ok {
			if len(activeItems) != 2 {
				t.Errorf("Filter did not return expected results: %d items", len(activeItems))
			}
		}
	})

	t.Run("conflicting output paths", func(t *testing.T) {
		// Test when multiple transformations write to same or overlapping paths
		conflictData := map[string]interface{}{
			"field1": "value1",
			"field2": "value2",
		}

		config := &core.MappingConfig{
			FormatName: "CONFLICT_PATH_TEST",
			FieldMappings: []core.FieldMapping{
				{
					Name:           "first_write",
					InputPath:      "field1",
					Transformation: "direct",
					OutputPath:     "output.value",
				},
				{
					Name:           "second_write",
					InputPath:      "field2",
					Transformation: "direct",
					OutputPath:     "output.value", // Same path - will overwrite
				},
				{
					Name:           "nested_conflict",
					InputPath:      "field1",
					Transformation: "direct",
					OutputPath:     "output", // Parent path of previous
				},
			},
			LookupTables: map[string]map[string]string{},
		}

		result, err := engine.TransformData(conflictData, config)
		if err != nil {
			t.Logf("Conflicting paths produced error: %v", err)
		} else {
			t.Logf("Conflicting paths result: %v", result)
			// The last transformation should win
		}
	})

	t.Run("lookup table with special keys", func(t *testing.T) {
		// Test lookup tables with unusual keys
		specialData := map[string]interface{}{
			"keys": []interface{}{
				"",                    // Empty string
				" ",                   // Space
				"key with spaces",     // Spaces in key
				"key\nwith\nnewlines", // Newlines
				"key\twith\ttabs",     // Tabs
				"unicode🔑",            // Unicode
			},
		}

		config := &core.MappingConfig{
			FormatName: "SPECIAL_LOOKUP_TEST",
			FieldMappings: []core.FieldMapping{
				{
					Name:           "lookup_empty",
					InputPath:      "keys[0]",
					Transformation: "lookup",
					OutputPath:     "result0",
					Config: map[string]interface{}{
						"lookup_table":  "special_table",
						"default_value": "NOT_FOUND",
					},
				},
				{
					Name:           "lookup_unicode",
					InputPath:      "keys[5]",
					Transformation: "lookup",
					OutputPath:     "result5",
					Config: map[string]interface{}{
						"lookup_table": "special_table",
					},
				},
			},
			LookupTables: map[string]map[string]string{
				"special_table": {
					"":                    "EMPTY_KEY",
					" ":                   "SPACE_KEY",
					"key with spaces":     "SPACES_VALUE",
					"key\nwith\nnewlines": "NEWLINES_VALUE",
					"key\twith\ttabs":     "TABS_VALUE",
					"unicode🔑":            "UNICODE_VALUE",
				},
			},
		}

		result, err := engine.TransformData(specialData, config)
		if err != nil {
			t.Fatalf("Special lookup failed: %v", err)
		}

		// Verify special key lookups work
		if r0, ok := result["result0"].(string); !ok || r0 != "EMPTY_KEY" {
			t.Errorf("Empty string lookup failed: %v", r0)
		}
		if r5, ok := result["result5"].(string); !ok || r5 != "UNICODE_VALUE" {
			t.Errorf("Unicode lookup failed: %v", r5)
		}
	})
}
