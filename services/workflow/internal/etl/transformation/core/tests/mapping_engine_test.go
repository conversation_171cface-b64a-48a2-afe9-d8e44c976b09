package tests

import (
	"fmt"
	"reflect"
	"strings"
	"testing"
	"time"

	"workflow/internal/etl/transformation/core"
)

// TestMappingEngine_TransformData tests the core mapping engine functionality.
//
// This test suite verifies that the mapping engine correctly orchestrates:
// 1. Configuration validation
// 2. Input data extraction using path resolution
// 3. Value transformation using transformation engine
// 4. Output data generation using path resolution
//
// The tests cover:
// - Simple field mappings with direct transformation
// - Complex transformations with lookup tables
// - Multiple field mappings in a single configuration
// - Error handling for invalid configurations
// - Error handling for missing input data
//
// Example transformation workflow:
// Input: {"entities": [{"entity": {"demographics": {"sex": "SEX_MALE"}}}]}
// Config: Transform entities[0].entity.demographics.sex using lookup to person.sex
// Output: {"person": {"sex": "M"}}
func TestMappingEngine_TransformData(t *testing.T) {
	engine := core.NewMappingEngine()

	tests := []struct {
		name        string
		inputData   interface{}            // Source data to transform
		config      *core.MappingConfig    // Transformation configuration
		expected    map[string]interface{} // Expected output structure
		expectError bool                   // Whether transformation should fail
	}{
		{
			// Test: Simple direct transformation
			// Input: {"field": "value"}
			// Config: Copy field to output_field using direct transformation
			// Expected: {"output_field": "value"}
			// Description: Tests basic field copying without modification
			name: "simple direct transformation",
			inputData: map[string]interface{}{
				"field": testValueField,
			},
			config: &core.MappingConfig{
				FormatName: "TEST_FORMAT",
				FieldMappings: []core.FieldMapping{
					{
						Name:           "copy_field",
						InputPath:      "field",
						Transformation: "direct",
						OutputPath:     "output_field",
					},
				},
				LookupTables: map[string]map[string]string{},
			},
			expected: map[string]interface{}{
				"field":        testValueField, // Original data preserved
				"output_field": testValueField, // Transformed field added
			},
			expectError: false,
		},
		{
			// Test: Lookup transformation with sex codes
			// Input: {"demographics": {"sex": "SEX_MALE"}}
			// Config: Transform demographics.sex using sex_codes lookup table
			// Expected: {"person": {"sex_code": "M"}}
			// Description: Tests value mapping using lookup tables
			name: "lookup transformation",
			inputData: map[string]interface{}{
				"demographics": map[string]interface{}{
					"sex": "SEX_MALE",
				},
			},
			config: &core.MappingConfig{
				FormatName: "TEST_FORMAT",
				FieldMappings: []core.FieldMapping{
					{
						Name:           "map_sex",
						InputPath:      "demographics.sex",
						Transformation: "lookup",
						OutputPath:     "person.sex_code",
						Config: map[string]interface{}{
							"lookup_table": "sex_codes",
						},
					},
				},
				LookupTables: map[string]map[string]string{
					"sex_codes": {
						"SEX_MALE":    "M",
						"SEX_FEMALE":  "F",
						"SEX_UNKNOWN": "U",
					},
				},
			},
			expected: map[string]interface{}{
				"demographics": map[string]interface{}{ // Original data preserved
					"sex": "SEX_MALE",
				},
				"person": map[string]interface{}{ // Transformed field added
					"sex_code": "M",
				},
			},
			expectError: false,
		},
		{
			// Test: Multiple field mappings in one transformation
			// Input: {"report": {"id": 123, "title": "Test Report"}}
			// Config: Transform multiple fields with different transformations
			// Expected: {"incident": {"number": "INC-00123", "title": "Test Report"}}
			// Description: Tests processing multiple field mappings sequentially
			name: "multiple field mappings",
			inputData: map[string]interface{}{
				"report": map[string]interface{}{
					"id":    123,
					"title": "Test Report",
				},
			},
			config: &core.MappingConfig{
				FormatName: "TEST_FORMAT",
				FieldMappings: []core.FieldMapping{
					{
						Name:           "format_id",
						InputPath:      "report.id",
						Transformation: "format",
						OutputPath:     "incident.number",
						Config: map[string]interface{}{
							"format_pattern": "INC-%05d",
						},
					},
					{
						Name:           "copy_title",
						InputPath:      "report.title",
						Transformation: "direct",
						OutputPath:     "incident.title",
					},
				},
				LookupTables: map[string]map[string]string{},
			},
			expected: map[string]interface{}{
				"report": map[string]interface{}{ // Original data preserved
					"id":    123,
					"title": "Test Report",
				},
				"incident": map[string]interface{}{ // Transformed fields added
					"number": "INC-00123",
					"title":  "Test Report",
				},
			},
			expectError: false,
		},
		{
			// Test: Complex nested transformation
			// Input: {"entities": [{"entity": {"demographics": {"age": 25}}, "extractionMetadata": {"role": "VICTIM"}}]}
			// Config: Extract entity age and role, transform both
			// Expected: {"victim": {"age": 25, "role": "V"}}
			// Description: Tests complex path navigation and nested output structure
			name: "complex nested transformation",
			inputData: map[string]interface{}{
				"entities": []interface{}{
					map[string]interface{}{
						"entity": map[string]interface{}{
							"demographics": map[string]interface{}{
								"age": 25,
							},
						},
						"extractionMetadata": map[string]interface{}{
							"role": "VICTIM",
						},
					},
				},
			},
			config: &core.MappingConfig{
				FormatName: "TEST_FORMAT",
				FieldMappings: []core.FieldMapping{
					{
						Name:           "extract_age",
						InputPath:      "entities[0].entity.demographics.age",
						Transformation: "direct",
						OutputPath:     "victim.age",
					},
					{
						Name:           "map_role",
						InputPath:      "entities[0].extractionMetadata.role",
						Transformation: "lookup",
						OutputPath:     "victim.role",
						Config: map[string]interface{}{
							"lookup_table": "role_codes",
						},
					},
				},
				LookupTables: map[string]map[string]string{
					"role_codes": {
						"VICTIM":   "V",
						"OFFENDER": "O",
						"WITNESS":  "W",
					},
				},
			},
			expected: map[string]interface{}{
				"entities": []interface{}{ // Original data preserved
					map[string]interface{}{
						"entity": map[string]interface{}{
							"demographics": map[string]interface{}{
								"age": 25,
							},
						},
						"extractionMetadata": map[string]interface{}{
							"role": "VICTIM",
						},
					},
				},
				"victim": map[string]interface{}{ // Transformed fields added
					"age":  25,
					"role": "V",
				},
			},
			expectError: false,
		},
		{
			// Test: Filter transformation
			// Input: {"entities": [{"role": "VICTIM"}, {"role": "OFFENDER"}, {"role": "VICTIM"}]}
			// Config: Filter entities to get only victims
			// Expected: {"victims": [{"role": "VICTIM"}, {"role": "VICTIM"}]}
			// Description: Tests array filtering functionality
			name: "filter transformation",
			inputData: map[string]interface{}{
				"entities": []interface{}{
					map[string]interface{}{"role": "VICTIM"},
					map[string]interface{}{"role": "OFFENDER"},
					map[string]interface{}{"role": "VICTIM"},
				},
			},
			config: &core.MappingConfig{
				FormatName: "TEST_FORMAT",
				FieldMappings: []core.FieldMapping{
					{
						Name:           "filter_victims",
						InputPath:      "entities",
						Transformation: "filter",
						OutputPath:     "victims",
						Config: map[string]interface{}{
							"field":    "role",
							"operator": "equals",
							"value":    "VICTIM",
						},
					},
				},
				LookupTables: map[string]map[string]string{},
			},
			expected: map[string]interface{}{
				"entities": []interface{}{ // Original data preserved
					map[string]interface{}{"role": "VICTIM"},
					map[string]interface{}{"role": "OFFENDER"},
					map[string]interface{}{"role": "VICTIM"},
				},
				"victims": []interface{}{ // Transformed field added
					map[string]interface{}{"role": "VICTIM"},
					map[string]interface{}{"role": "VICTIM"},
				},
			},
			expectError: false,
		},
		{
			// Test: Invalid configuration - missing required field
			// Input: {"field": "value"}
			// Config: Field mapping missing required transformation field
			// Expected: Error during validation
			// Description: Tests configuration validation catches missing required fields
			name: "invalid configuration - missing transformation",
			inputData: map[string]interface{}{
				"field": testValueField,
			},
			config: &core.MappingConfig{
				FormatName: "TEST_FORMAT",
				FieldMappings: []core.FieldMapping{
					{
						Name:       "invalid_mapping",
						InputPath:  "field",
						OutputPath: "output",
						// Missing Transformation field
					},
				},
				LookupTables: map[string]map[string]string{},
			},
			expected:    nil,
			expectError: true,
		},
		{
			// Test: Missing input data
			// Input: {"field": "value"}
			// Config: Try to access non-existent field
			// Expected: Graceful handling with nil value in output
			// Description: Tests graceful handling for missing input data
			name: "missing input data",
			inputData: map[string]interface{}{
				"field": testValueField,
			},
			config: &core.MappingConfig{
				FormatName: "TEST_FORMAT",
				FieldMappings: []core.FieldMapping{
					{
						Name:           "missing_field",
						InputPath:      "nonexistent_field",
						Transformation: "direct",
						OutputPath:     "output",
					},
				},
				LookupTables: map[string]map[string]string{},
			},
			expected: map[string]interface{}{
				"field":  "value", // Original data preserved
				"output": nil,     // Transformed field added (nil because source doesn't exist)
			},
			expectError: false,
		},
		{
			// Test: Missing lookup table
			// Input: {"field": "value"}
			// Config: Reference non-existent lookup table
			// Expected: Error during validation
			// Description: Tests validation catches missing lookup table references
			name: "missing lookup table",
			inputData: map[string]interface{}{
				"field": testValueField,
			},
			config: &core.MappingConfig{
				FormatName: "TEST_FORMAT",
				FieldMappings: []core.FieldMapping{
					{
						Name:           "lookup_missing",
						InputPath:      "field",
						Transformation: "lookup",
						OutputPath:     "output",
						Config: map[string]interface{}{
							"lookup_table": "nonexistent_table",
						},
					},
				},
				LookupTables: map[string]map[string]string{},
			},
			expected:    nil,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := engine.TransformData(tt.inputData, tt.config)

			if tt.expectError {
				if err == nil {
					t.Errorf("TransformData() expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("TransformData() unexpected error = %v", err)
				return
			}

			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("TransformData() = %v, want %v", result, tt.expected)
			}
		})
	}
}

// TestMappingEngine_TransformWithConfig tests configuration loading and transformation.
//
// This test verifies that the mapping engine can load configuration from JSON
// strings and apply them to input data in a single operation.
//
// Test scenarios:
// - Valid JSON configuration with successful transformation
// - Invalid JSON configuration with parsing error
// - Valid JSON with invalid transformation configuration
func TestMappingEngine_TransformWithConfig(t *testing.T) {
	engine := core.NewMappingEngine()

	tests := []struct {
		name        string
		inputData   interface{}            // Source data to transform
		configJSON  string                 // JSON configuration string
		expected    map[string]interface{} // Expected output structure
		expectError bool                   // Whether operation should fail
	}{
		{
			// Test: Valid JSON configuration
			// Input: {"name": "John"}
			// Config: JSON string with direct transformation
			// Expected: {"person": {"name": "John"}}
			// Description: Tests end-to-end configuration loading and transformation
			name: "valid JSON configuration",
			inputData: map[string]interface{}{
				"name": "John",
			},
			configJSON: `{
				"format_name": "TEST_FORMAT",
				"field_mappings": [
					{
						"name": "copy_name",
						"input_path": "name",
						"transformation": "direct",
						"output_path": "person.name"
					}
				],
				"lookup_tables": {}
			}`,
			expected: map[string]interface{}{
				"name": "John", // Original data preserved
				"person": map[string]interface{}{ // Transformed field added
					"name": "John",
				},
			},
			expectError: false,
		},
		{
			// Test: Invalid JSON configuration
			// Input: {"name": "John"}
			// Config: Malformed JSON string
			// Expected: Error during JSON parsing
			// Description: Tests error handling for invalid JSON
			name: "invalid JSON configuration",
			inputData: map[string]interface{}{
				"name": "John",
			},
			configJSON: `{
				"format_name": "TEST_FORMAT",
				"field_mappings": [
					{
						"name": "copy_name"
						"input_path": "name",
			}`,
			expected:    nil,
			expectError: true,
		},
		{
			// Test: Valid JSON with invalid transformation
			// Input: {"name": "John"}
			// Config: JSON with unsupported transformation type
			// Expected: Error during transformation validation
			// Description: Tests validation of transformation configuration
			name: "valid JSON with invalid transformation",
			inputData: map[string]interface{}{
				"name": "John",
			},
			configJSON: `{
				"format_name": "TEST_FORMAT",
				"field_mappings": [
					{
						"name": "invalid_transform",
						"input_path": "name",
						"transformation": "unsupported_type",
						"output_path": "person.name"
					}
				],
				"lookup_tables": {}
			}`,
			expected:    nil,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := engine.TransformWithConfig(tt.inputData, tt.configJSON)

			if tt.expectError {
				if err == nil {
					t.Errorf("TransformWithConfig() expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("TransformWithConfig() unexpected error = %v", err)
				return
			}

			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("TransformWithConfig() = %v, want %v", result, tt.expected)
			}
		})
	}
}

// TestMappingEngine_GetSupportedTransformations tests the supported transformation types list.
//
// This test verifies that the mapping engine returns the correct list of
// supported transformation types for configuration validation and documentation.
func TestMappingEngine_GetSupportedTransformations(t *testing.T) {
	engine := core.NewMappingEngine()

	supportedTypes := engine.GetSupportedTransformations()

	// Expected transformation types based on current implementation
	expectedTypes := []string{
		"direct",
		"lookup",
		"filter",
		"format",
		"date_format",
		"add_sequence",
		"group_by",
		"sort",
	}

	if len(supportedTypes) != len(expectedTypes) {
		t.Errorf("GetSupportedTransformations() returned %d types, expected %d", len(supportedTypes), len(expectedTypes))
	}

	// Check each expected type is present
	for _, expectedType := range expectedTypes {
		found := false
		for _, supportedType := range supportedTypes {
			if supportedType == expectedType {
				found = true
				break
			}
		}
		if !found {
			t.Errorf("GetSupportedTransformations() missing expected type: %s", expectedType)
		}
	}
}

// TestMappingEngine_ValidateFieldMapping tests field mapping validation.
//
// This test verifies that the mapping engine correctly validates individual
// field mappings for:
// - Required fields (name, input_path, transformation, output_path)
// - Supported transformation types
// - Transformation-specific configuration requirements
// - Lookup table references
func TestMappingEngine_ValidateFieldMapping(t *testing.T) {
	engine := core.NewMappingEngine()

	// Test configuration with lookup tables
	config := &core.MappingConfig{
		FormatName: "TEST_FORMAT",
		LookupTables: map[string]map[string]string{
			"sex_codes": {
				"SEX_MALE":   "M",
				"SEX_FEMALE": "F",
			},
		},
	}

	tests := []struct {
		name        string
		mapping     core.FieldMapping // Field mapping to validate
		expectError bool              // Whether validation should fail
		errorMsg    string            // Expected error message substring
	}{
		{
			// Test: Valid direct transformation
			// Mapping: Complete field mapping with all required fields
			// Expected: No validation errors
			// Description: Tests validation passes for correct configuration
			name: "valid direct transformation",
			mapping: core.FieldMapping{
				Name:           "test_direct",
				InputPath:      "input.field",
				Transformation: "direct",
				OutputPath:     "output.field",
			},
			expectError: false,
		},
		{
			// Test: Valid lookup transformation
			// Mapping: Lookup transformation with valid table reference
			// Expected: No validation errors
			// Description: Tests validation passes for lookup with existing table
			name: "valid lookup transformation",
			mapping: core.FieldMapping{
				Name:           "test_lookup",
				InputPath:      "input.field",
				Transformation: "lookup",
				OutputPath:     "output.field",
				Config: map[string]interface{}{
					"lookup_table": "sex_codes",
				},
			},
			expectError: false,
		},
		{
			// Test: Missing name field
			// Mapping: Field mapping without required name
			// Expected: Validation error
			// Description: Tests validation catches missing required fields
			name: "missing name",
			mapping: core.FieldMapping{
				InputPath:      "input.field",
				Transformation: "direct",
				OutputPath:     "output.field",
			},
			expectError: true,
			errorMsg:    "name is required",
		},
		{
			// Test: Missing input path
			// Mapping: Field mapping without input_path
			// Expected: Validation error
			// Description: Tests validation catches missing input_path
			name: "missing input path",
			mapping: core.FieldMapping{
				Name:           "test_mapping",
				Transformation: "direct",
				OutputPath:     "output.field",
			},
			expectError: true,
			errorMsg:    "input_path is required",
		},
		{
			// Test: Missing transformation type
			// Mapping: Field mapping without transformation
			// Expected: Validation error
			// Description: Tests validation catches missing transformation
			name: "missing transformation",
			mapping: core.FieldMapping{
				Name:       "test_mapping",
				InputPath:  "input.field",
				OutputPath: "output.field",
			},
			expectError: true,
			errorMsg:    "transformation is required",
		},
		{
			// Test: Missing output path
			// Mapping: Field mapping without output_path
			// Expected: Validation error
			// Description: Tests validation catches missing output_path
			name: "missing output path",
			mapping: core.FieldMapping{
				Name:           "test_mapping",
				InputPath:      "input.field",
				Transformation: "direct",
			},
			expectError: true,
			errorMsg:    "output_path is required",
		},
		{
			// Test: Unsupported transformation type
			// Mapping: Field mapping with invalid transformation
			// Expected: Validation error
			// Description: Tests validation catches unsupported transformation types
			name: "unsupported transformation",
			mapping: core.FieldMapping{
				Name:           "test_mapping",
				InputPath:      "input.field",
				Transformation: "unsupported_type",
				OutputPath:     "output.field",
			},
			expectError: true,
			errorMsg:    "unsupported transformation type",
		},
		{
			// Test: Lookup transformation missing config
			// Mapping: Lookup transformation without configuration
			// Expected: Validation error
			// Description: Tests validation catches missing lookup configuration
			name: "lookup missing config",
			mapping: core.FieldMapping{
				Name:           "test_lookup",
				InputPath:      "input.field",
				Transformation: "lookup",
				OutputPath:     "output.field",
			},
			expectError: true,
			errorMsg:    "lookup transformation requires config",
		},
		{
			// Test: Lookup transformation missing table reference
			// Mapping: Lookup transformation without lookup_table config
			// Expected: Validation error
			// Description: Tests validation catches missing lookup_table config
			name: "lookup missing table reference",
			mapping: core.FieldMapping{
				Name:           "test_lookup",
				InputPath:      "input.field",
				Transformation: "lookup",
				OutputPath:     "output.field",
				Config:         map[string]interface{}{},
			},
			expectError: true,
			errorMsg:    "lookup transformation requires lookup_table config",
		},
		{
			// Test: Lookup transformation with non-existent table
			// Mapping: Lookup transformation referencing missing table
			// Expected: Validation error
			// Description: Tests validation catches references to non-existent tables
			name: "lookup non-existent table",
			mapping: core.FieldMapping{
				Name:           "test_lookup",
				InputPath:      "input.field",
				Transformation: "lookup",
				OutputPath:     "output.field",
				Config: map[string]interface{}{
					"lookup_table": "nonexistent_table",
				},
			},
			expectError: true,
			errorMsg:    "lookup table 'nonexistent_table' is used but not defined",
		},
		{
			// Test: Filter transformation missing config
			// Mapping: Filter transformation without configuration
			// Expected: Validation error
			// Description: Tests validation catches missing filter configuration
			name: "filter missing config",
			mapping: core.FieldMapping{
				Name:           "test_filter",
				InputPath:      "input.field",
				Transformation: "filter",
				OutputPath:     "output.field",
			},
			expectError: true,
			errorMsg:    "filter transformation requires config",
		},
		{
			// Test: Format transformation missing pattern
			// Mapping: Format transformation without format_pattern
			// Expected: Validation error
			// Description: Tests validation catches missing format_pattern
			name: "format missing pattern",
			mapping: core.FieldMapping{
				Name:           "test_format",
				InputPath:      "input.field",
				Transformation: "format",
				OutputPath:     "output.field",
				Config:         map[string]interface{}{},
			},
			expectError: true,
			errorMsg:    "format transformation requires format_pattern config",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := engine.ValidateFieldMapping(tt.mapping, config)

			if tt.expectError {
				if err == nil {
					t.Errorf("ValidateFieldMapping() expected error but got none")
					return
				}
				if tt.errorMsg != "" && !containsSubstring(err.Error(), tt.errorMsg) {
					t.Errorf("ValidateFieldMapping() error = %q, want to contain %q", err.Error(), tt.errorMsg)
				}
			} else if err != nil {
				t.Errorf("ValidateFieldMapping() unexpected error = %v", err)
			}
		})
	}
}

// TestMappingEngine_WithRealExtractionData tests the mapping engine with realistic extraction data.
//
// This test uses sample extraction output to verify that the mapping engine
// can handle complex, nested data structures typical of the ETL extraction phase.
//
// The test demonstrates:
// - Navigating complex nested structures
// - Applying multiple transformations
// - Producing structured output suitable for NIBRS format
func TestMappingEngine_WithRealExtractionData(t *testing.T) {
	engine := core.NewMappingEngine()

	// Load real extraction data from test file
	extractionData := loadTestData()

	// Configuration for transforming extraction data to NIBRS-like format
	config := &core.MappingConfig{
		FormatName: "NIBRS_SAMPLE",
		FieldMappings: []core.FieldMapping{
			{
				Name:           "incident_number",
				InputPath:      "reports[0].report.id",
				Transformation: "direct",
				OutputPath:     "incident.number",
			},
			{
				Name:           "incident_date",
				InputPath:      "reports[0].report.created_at",
				Transformation: "date_format",
				OutputPath:     "incident.date",
				Config: map[string]interface{}{
					"date_format": "2006-01-02",
				},
			},
			{
				Name:           "victim_sex",
				InputPath:      "entities[0].entity.demographics.sex",
				Transformation: "lookup",
				OutputPath:     "victim.sex_code",
				Config: map[string]interface{}{
					"lookup_table": "sex_codes",
				},
			},
			{
				Name:           "victim_age",
				InputPath:      "entities[0].entity.demographics.age",
				Transformation: "direct",
				OutputPath:     "victim.age",
			},
			{
				Name:           "offender_sex",
				InputPath:      "entities[1].entity.demographics.sex",
				Transformation: "lookup",
				OutputPath:     "offender.sex_code",
				Config: map[string]interface{}{
					"lookup_table": "sex_codes",
				},
			},
		},
		LookupTables: map[string]map[string]string{
			"sex_codes": {
				"SEX_MALE":    "M",
				"SEX_FEMALE":  "F",
				"SEX_UNKNOWN": "U",
			},
		},
	}

	// Transform the data
	result, err := engine.TransformData(extractionData, config)
	if err != nil {
		t.Fatalf("TransformData() unexpected error = %v", err)
	}

	// Verify that original data is preserved
	if _, hasOriginal := result["reports"]; !hasOriginal {
		t.Errorf("TransformData() missing original 'reports' field")
	}
	if _, hasOriginal := result["entities"]; !hasOriginal {
		t.Errorf("TransformData() missing original 'entities' field")
	}

	// Verify transformed fields were added
	incident, hasIncident := result["incident"].(map[string]interface{})
	if !hasIncident {
		t.Errorf("TransformData() missing transformed 'incident' field")
	} else {
		if incident["number"] != "RPT-2024-001" {
			t.Errorf("TransformData() incident.number = %v, want %v", incident["number"], "RPT-2024-001")
		}
		if incident["date"] != "2024-01-15" {
			t.Errorf("TransformData() incident.date = %v, want %v", incident["date"], "2024-01-15")
		}
	}

	victim, hasVictim := result["victim"].(map[string]interface{})
	if !hasVictim {
		t.Errorf("TransformData() missing transformed 'victim' field")
	} else {
		if victim["sex_code"] != "M" {
			t.Errorf("TransformData() victim.sex_code = %v, want %v", victim["sex_code"], "M")
		}
		if victim["age"] != float64(25) {
			t.Errorf("TransformData() victim.age = %v, want %v", victim["age"], float64(25))
		}
	}

	offender, hasOffender := result["offender"].(map[string]interface{})
	if !hasOffender {
		t.Errorf("TransformData() missing transformed 'offender' field")
	} else if offender["sex_code"] != "F" {
		t.Errorf("TransformData() offender.sex_code = %v, want %v", offender["sex_code"], "F")
	}
}

// TestMappingEngine_ErrorHandling tests comprehensive error handling scenarios.
//
// This test verifies that the mapping engine handles various error conditions
// gracefully and provides informative error messages.
func TestMappingEngine_ErrorHandling(t *testing.T) {
	engine := core.NewMappingEngine()

	tests := []struct {
		name        string
		inputData   interface{}
		config      *core.MappingConfig
		expectError bool
		errorMsg    string
	}{
		{
			// Test: Nil input data
			// Input: nil
			// Config: Valid configuration
			// Expected: Error during input validation
			// Description: Tests error handling for nil input
			name:      "nil input data",
			inputData: nil,
			config: &core.MappingConfig{
				FormatName: "TEST_FORMAT",
				FieldMappings: []core.FieldMapping{
					{
						Name:           "test_field",
						InputPath:      "field",
						Transformation: "direct",
						OutputPath:     "output",
					},
				},
				LookupTables: map[string]map[string]string{},
			},
			expectError: true,
			errorMsg:    "input data cannot be nil",
		},
		{
			// Test: Invalid transformation configuration
			// Input: Valid data
			// Config: Transformation with invalid config
			// Expected: Error during transformation
			// Description: Tests error handling for invalid transformation config
			name: "invalid transformation config",
			inputData: map[string]interface{}{
				"field": testValueField,
			},
			config: &core.MappingConfig{
				FormatName: "TEST_FORMAT",
				FieldMappings: []core.FieldMapping{
					{
						Name:           "test_lookup",
						InputPath:      "field",
						Transformation: "lookup",
						OutputPath:     "output",
						Config: map[string]interface{}{
							"lookup_table": "missing_table",
						},
					},
				},
				LookupTables: map[string]map[string]string{},
			},
			expectError: true,
			errorMsg:    "configuration validation failed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := engine.TransformData(tt.inputData, tt.config)

			if tt.expectError {
				if err == nil {
					t.Errorf("TransformData() expected error but got none")
					return
				}
				if tt.errorMsg != "" && !containsSubstring(err.Error(), tt.errorMsg) {
					t.Errorf("TransformData() error = %q, want to contain %q", err.Error(), tt.errorMsg)
				}
			} else if err != nil {
				t.Errorf("TransformData() unexpected error = %v", err)
			}
		})
	}
}

// Benchmark tests for performance measurement
func BenchmarkMappingEngine_TransformData(b *testing.B) {
	engine := core.NewMappingEngine()

	inputData := map[string]interface{}{
		"field": testValueField,
		"nested": map[string]interface{}{
			"field": "nested_value",
		},
	}

	config := &core.MappingConfig{
		FormatName: "BENCHMARK_FORMAT",
		FieldMappings: []core.FieldMapping{
			{
				Name:           "copy_field",
				InputPath:      "field",
				Transformation: "direct",
				OutputPath:     "output.field",
			},
			{
				Name:           "copy_nested",
				InputPath:      "nested.field",
				Transformation: "direct",
				OutputPath:     "output.nested_field",
			},
		},
		LookupTables: map[string]map[string]string{},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = engine.TransformData(inputData, config)
	}
}

func BenchmarkMappingEngine_LookupTransformation(b *testing.B) {
	engine := core.NewMappingEngine()

	inputData := map[string]interface{}{
		"sex": "SEX_MALE",
	}

	config := &core.MappingConfig{
		FormatName: "BENCHMARK_FORMAT",
		FieldMappings: []core.FieldMapping{
			{
				Name:           "lookup_sex",
				InputPath:      "sex",
				Transformation: "lookup",
				OutputPath:     "sex_code",
				Config: map[string]interface{}{
					"lookup_table": "sex_codes",
				},
			},
		},
		LookupTables: map[string]map[string]string{
			"sex_codes": {
				"SEX_MALE":    "M",
				"SEX_FEMALE":  "F",
				"SEX_UNKNOWN": "U",
			},
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = engine.TransformData(inputData, config)
	}
}

// TestMappingEngine_TransformationMode tests the transformation mode configuration option.
//
// This test suite verifies that the mapping engine correctly handles the transformation_mode
// configuration and delegates to the appropriate transformation method based on the mode.
//
// Test scenarios:
// - Default mode (empty/unspecified) should use copy-based transformation
// - "copy" mode should use copy-based transformation
// - "in_place" mode should use in-place transformation
// - Invalid modes should trigger validation errors
func TestMappingEngine_TransformationMode(t *testing.T) {
	engine := core.NewMappingEngine()

	tests := []struct {
		name               string
		inputData          interface{}
		transformationMode string
		expectedBehavior   string
		expectError        bool
		errorMsg           string
	}{
		{
			name: "default mode (empty) uses copy transformation",
			inputData: map[string]interface{}{
				"field": testValueField,
			},
			transformationMode: "", // Empty mode should default to copy
			expectedBehavior:   "copy",
			expectError:        false,
		},
		{
			name: "explicit copy mode uses copy transformation",
			inputData: map[string]interface{}{
				"field": testValueField,
			},
			transformationMode: "copy",
			expectedBehavior:   "copy",
			expectError:        false,
		},
		{
			name: "in_place mode uses in-place transformation",
			inputData: map[string]interface{}{
				"field": testValueField,
			},
			transformationMode: "in_place",
			expectedBehavior:   "in_place",
			expectError:        false,
		},
		{
			name: "invalid mode triggers validation error",
			inputData: map[string]interface{}{
				"field": testValueField,
			},
			transformationMode: "invalid_mode",
			expectedBehavior:   "",
			expectError:        true,
			errorMsg:           "invalid transformation mode",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			config := &core.MappingConfig{
				FormatName:         "TEST_FORMAT",
				TransformationMode: tt.transformationMode,
				FieldMappings: []core.FieldMapping{
					{
						Name:           "copy_field",
						InputPath:      "field",
						Transformation: "direct",
						OutputPath:     "output_field",
					},
				},
				LookupTables: map[string]map[string]string{},
			}

			result, err := engine.TransformData(tt.inputData, config)

			if tt.expectError {
				if err == nil {
					t.Errorf("TransformData() expected error but got none")
					return
				}
				if tt.errorMsg != "" && !containsSubstring(err.Error(), tt.errorMsg) {
					t.Errorf("TransformData() error = %q, want to contain %q", err.Error(), tt.errorMsg)
				}
				return
			}

			if err != nil {
				t.Errorf("TransformData() unexpected error = %v", err)
				return
			}

			// Verify the transformation was successful
			if result == nil {
				t.Errorf("TransformData() returned nil result")
				return
			}

			// For copy mode, verify original data is preserved
			if tt.expectedBehavior == "copy" {
				// Original input should be unchanged
				originalValue, exists := tt.inputData.(map[string]interface{})["field"]
				if !exists || originalValue != testValueField {
					t.Errorf("Copy mode should preserve original input data")
				}
			}

			// Verify output contains both original and transformed data
			expectedOutput := map[string]interface{}{
				"field":        testValueField, // Original data preserved
				"output_field": testValueField, // Transformed data added
			}

			if !reflect.DeepEqual(result, expectedOutput) {
				t.Errorf("TransformData() = %v, want %v", result, expectedOutput)
			}
		})
	}
}

// TestMappingEngine_InPlaceTransformation tests the in-place transformation functionality.
//
// This test suite specifically tests in-place transformation methods to ensure they:
// - Modify input data structures directly
// - Preserve type safety and data integrity
// - Handle complex nested structures correctly
// - Provide proper error handling
func TestMappingEngine_InPlaceTransformation(t *testing.T) {
	engine := core.NewMappingEngine()

	tests := []struct {
		name        string
		inputData   interface{}
		config      *core.MappingConfig
		verifyInput func(interface{}) bool // Function to verify input was modified
		expectError bool
		errorMsg    string
	}{
		{
			name: "simple in-place field modification",
			inputData: map[string]interface{}{
				"field": "original_value",
			},
			config: &core.MappingConfig{
				FormatName: "TEST_FORMAT",
				FieldMappings: []core.FieldMapping{
					{
						Name:           "modify_field",
						InputPath:      "field",
						Transformation: "format",
						OutputPath:     "field", // Same path for in-place modification
						Config: map[string]interface{}{
							"format_pattern": "MODIFIED-%s",
						},
					},
				},
				LookupTables: map[string]map[string]string{},
			},
			verifyInput: func(data interface{}) bool {
				if m, ok := data.(map[string]interface{}); ok {
					return m["field"] == "MODIFIED-original_value"
				}
				return false
			},
			expectError: false,
		},
		{
			name: "nested structure in-place modification",
			inputData: map[string]interface{}{
				"person": map[string]interface{}{
					"demographics": map[string]interface{}{
						"sex": "SEX_MALE",
					},
				},
			},
			config: &core.MappingConfig{
				FormatName: "TEST_FORMAT",
				FieldMappings: []core.FieldMapping{
					{
						Name:           "transform_sex",
						InputPath:      "person.demographics.sex",
						Transformation: "lookup",
						OutputPath:     "person.demographics.sex_code",
						Config: map[string]interface{}{
							"lookup_table": "sex_codes",
						},
					},
				},
				LookupTables: map[string]map[string]string{
					"sex_codes": {
						"SEX_MALE":   "M",
						"SEX_FEMALE": "F",
					},
				},
			},
			verifyInput: func(data interface{}) bool {
				if m, ok := data.(map[string]interface{}); ok {
					if person, ok := m["person"].(map[string]interface{}); ok {
						if demo, ok := person["demographics"].(map[string]interface{}); ok {
							// Original field should still exist
							if demo["sex"] != "SEX_MALE" {
								return false
							}
							// New field should be added
							return demo["sex_code"] == "M"
						}
					}
				}
				return false
			},
			expectError: false,
		},
		{
			name: "array element in-place modification",
			inputData: map[string]interface{}{
				"entities": []interface{}{
					map[string]interface{}{
						"type": "PERSON",
						"name": "John Doe",
					},
					map[string]interface{}{
						"type": "VEHICLE",
						"name": "Honda Civic",
					},
				},
			},
			config: &core.MappingConfig{
				FormatName: "TEST_FORMAT",
				FieldMappings: []core.FieldMapping{
					{
						Name:           "add_sequence_number",
						InputPath:      "entities[0].name",
						Transformation: "format",
						OutputPath:     "entities[0].sequence_id",
						Config: map[string]interface{}{
							"format_pattern": "ENT-01",
						},
					},
				},
				LookupTables: map[string]map[string]string{},
			},
			verifyInput: func(data interface{}) bool {
				if m, ok := data.(map[string]interface{}); ok {
					if entities, ok := m["entities"].([]interface{}); ok {
						if len(entities) > 0 {
							if entity, ok := entities[0].(map[string]interface{}); ok {
								// Original fields should be preserved
								if entity["type"] != "PERSON" || entity["name"] != "John Doe" {
									return false
								}
								// New field should be added
								return entity["sequence_id"] == "ENT-01"
							}
						}
					}
				}
				return false
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Perform in-place transformation
			result, err := engine.TransformDataInPlace(tt.inputData, tt.config)

			if tt.expectError {
				if err == nil {
					t.Errorf("TransformDataInPlace() expected error but got none")
					return
				}
				if tt.errorMsg != "" && !containsSubstring(err.Error(), tt.errorMsg) {
					t.Errorf("TransformDataInPlace() error = %q, want to contain %q", err.Error(), tt.errorMsg)
				}
				return
			}

			if err != nil {
				t.Errorf("TransformDataInPlace() unexpected error = %v", err)
				return
			}

			// Verify the result points to the same modified input data
			// Note: We can't use direct comparison for maps, so we'll verify by checking the address
			resultPtr := fmt.Sprintf("%p", result)
			inputPtr := fmt.Sprintf("%p", tt.inputData)
			if resultPtr != inputPtr {
				t.Errorf("TransformDataInPlace() should return reference to modified input data")
			}

			// Verify the input data was modified as expected
			if tt.verifyInput != nil && !tt.verifyInput(tt.inputData) {
				t.Errorf("TransformDataInPlace() did not modify input data as expected")
			}
		})
	}
}

// TestMappingEngine_InPlaceVsCopyComparison tests equivalence between in-place and copy transformations.
//
// This test verifies that in-place and copy transformations produce equivalent results
// when the input and output paths are different (no conflicts).
func TestMappingEngine_InPlaceVsCopyComparison(t *testing.T) {
	engine := core.NewMappingEngine()

	testCases := []struct {
		name      string
		inputData interface{}
		config    *core.MappingConfig
	}{
		{
			name: "simple field transformation comparison",
			inputData: map[string]interface{}{
				"source_field": "test_value",
			},
			config: &core.MappingConfig{
				FormatName: "TEST_FORMAT",
				FieldMappings: []core.FieldMapping{
					{
						Name:           "transform_field",
						InputPath:      "source_field",
						Transformation: "format",
						OutputPath:     "target_field",
						Config: map[string]interface{}{
							"format_pattern": "FORMATTED-%s",
						},
					},
				},
				LookupTables: map[string]map[string]string{},
			},
		},
		{
			name: "lookup transformation comparison",
			inputData: map[string]interface{}{
				"demographics": map[string]interface{}{
					"sex": "SEX_FEMALE",
				},
			},
			config: &core.MappingConfig{
				FormatName: "TEST_FORMAT",
				FieldMappings: []core.FieldMapping{
					{
						Name:           "map_sex",
						InputPath:      "demographics.sex",
						Transformation: "lookup",
						OutputPath:     "person.sex_code",
						Config: map[string]interface{}{
							"lookup_table": "sex_codes",
						},
					},
				},
				LookupTables: map[string]map[string]string{
					"sex_codes": {
						"SEX_MALE":   "M",
						"SEX_FEMALE": "F",
					},
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create separate copies of input data for each transformation
			inputDataCopy := deepCopyData(tc.inputData)
			inputDataInPlace := deepCopyData(tc.inputData)

			// Perform copy-based transformation
			copyConfig := &core.MappingConfig{
				FormatName:         tc.config.FormatName,
				TransformationMode: "copy",
				FieldMappings:      tc.config.FieldMappings,
				LookupTables:       tc.config.LookupTables,
			}
			copyResult, err := engine.TransformData(inputDataCopy, copyConfig)
			if err != nil {
				t.Fatalf("Copy transformation failed: %v", err)
			}

			// Perform in-place transformation
			inPlaceConfig := &core.MappingConfig{
				FormatName:         tc.config.FormatName,
				TransformationMode: "in_place",
				FieldMappings:      tc.config.FieldMappings,
				LookupTables:       tc.config.LookupTables,
			}
			inPlaceResult, err := engine.TransformData(inputDataInPlace, inPlaceConfig)
			if err != nil {
				t.Fatalf("In-place transformation failed: %v", err)
			}

			// Both copy and in-place now return complete datasets
			// So we can directly compare the results
			if !reflect.DeepEqual(copyResult, inPlaceResult) {
				t.Errorf("Copy and in-place transformations produced different results")
				t.Errorf("Copy result: %v", copyResult)
				t.Errorf("In-place result: %v", inPlaceResult)
			}

			// Verify that copy transformation preserved original input
			originalInput := deepCopyData(tc.inputData)
			if !reflect.DeepEqual(inputDataCopy, originalInput) {
				t.Errorf("Copy transformation should not modify original input data")
			}
		})
	}
}

// TestMappingEngine_InPlaceTransformationWithConfig tests in-place transformation with JSON config.
//
// This test verifies that the TransformWithConfig and TransformWithConfigFile methods
// correctly handle the transformation_mode configuration option.
func TestMappingEngine_InPlaceTransformationWithConfig(t *testing.T) {
	engine := core.NewMappingEngine()

	tests := []struct {
		name        string
		inputData   interface{}
		configJSON  string
		expectError bool
		verifyInput func(interface{}) bool
	}{
		{
			name: "in-place transformation via JSON config",
			inputData: map[string]interface{}{
				"field": "original",
			},
			configJSON: `{
				"format_name": "TEST_FORMAT",
				"transformation_mode": "in_place",
				"field_mappings": [
					{
						"name": "modify_field",
						"input_path": "field",
						"transformation": "format",
						"output_path": "field",
						"config": {"format_pattern": "UPDATED-%s"}
					}
				],
				"lookup_tables": {}
			}`,
			expectError: false,
			verifyInput: func(data interface{}) bool {
				if m, ok := data.(map[string]interface{}); ok {
					return m["field"] == "UPDATED-original"
				}
				return false
			},
		},
		{
			name: "copy transformation via JSON config",
			inputData: map[string]interface{}{
				"source": testValueField,
			},
			configJSON: `{
				"format_name": "TEST_FORMAT",
				"transformation_mode": "copy",
				"field_mappings": [
					{
						"name": "copy_field",
						"input_path": "source",
						"transformation": "direct",
						"output_path": "target"
					}
				],
				"lookup_tables": {}
			}`,
			expectError: false,
			verifyInput: func(data interface{}) bool {
				// For copy mode, result should have both original and transformed data
				if m, ok := data.(map[string]interface{}); ok {
					// Should still have original field unchanged
					if m["source"] != testValueField {
						return false
					}
					// Should also have target field in result
					return m["target"] == testValueField
				}
				return false
			},
		},
		{
			name: "invalid transformation mode in JSON config",
			inputData: map[string]interface{}{
				"field": testValueField,
			},
			configJSON: `{
				"format_name": "TEST_FORMAT",
				"transformation_mode": "invalid_mode",
				"field_mappings": [
					{
						"name": "test_field",
						"input_path": "field",
						"transformation": "direct",
						"output_path": "output"
					}
				],
				"lookup_tables": {}
			}`,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := engine.TransformWithConfig(tt.inputData, tt.configJSON)

			if tt.expectError {
				if err == nil {
					t.Errorf("TransformWithConfig() expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("TransformWithConfig() unexpected error = %v", err)
				return
			}

			if result == nil {
				t.Errorf("TransformWithConfig() returned nil result")
				return
			}

			// Verify result data state based on transformation mode
			if tt.verifyInput != nil && !tt.verifyInput(result) {
				t.Errorf("TransformWithConfig() result data verification failed")
			}
		})
	}
}

// TestMappingEngine_InPlaceTransformationPerformance tests performance characteristics of in-place transformation.
//
// This test verifies that in-place transformation provides better memory efficiency
// compared to copy-based transformation for large datasets.
func TestMappingEngine_InPlaceTransformationPerformance(t *testing.T) {
	engine := core.NewMappingEngine()

	// Create a larger dataset for performance testing
	largeDataset := make(map[string]interface{})
	entities := make([]interface{}, 1000)
	for i := 0; i < 1000; i++ {
		entities[i] = map[string]interface{}{
			"id":   i,
			"name": fmt.Sprintf("Entity%d", i),
			"type": "PERSON",
		}
	}
	largeDataset["entities"] = entities

	config := &core.MappingConfig{
		FormatName: "PERFORMANCE_TEST",
		FieldMappings: []core.FieldMapping{
			{
				Name:           "add_prefix",
				InputPath:      "entities[0].name",
				Transformation: "format",
				OutputPath:     "entities[0].formatted_name",
				Config: map[string]interface{}{
					"format_pattern": "FORMATTED-%s",
				},
			},
		},
		LookupTables: map[string]map[string]string{},
	}

	t.Run("copy transformation performance", func(t *testing.T) {
		copyData := deepCopyData(largeDataset)
		copyConfig := &core.MappingConfig{
			FormatName:         config.FormatName,
			TransformationMode: "copy",
			FieldMappings:      config.FieldMappings,
			LookupTables:       config.LookupTables,
		}

		start := time.Now()
		_, err := engine.TransformData(copyData, copyConfig)
		copyDuration := time.Since(start)

		if err != nil {
			t.Fatalf("Copy transformation failed: %v", err)
		}

		t.Logf("Copy transformation took: %v", copyDuration)
	})

	t.Run("in-place transformation performance", func(t *testing.T) {
		inPlaceData := deepCopyData(largeDataset)
		inPlaceConfig := &core.MappingConfig{
			FormatName:         config.FormatName,
			TransformationMode: "in_place",
			FieldMappings:      config.FieldMappings,
			LookupTables:       config.LookupTables,
		}

		start := time.Now()
		_, err := engine.TransformData(inPlaceData, inPlaceConfig)
		inPlaceDuration := time.Since(start)

		if err != nil {
			t.Fatalf("In-place transformation failed: %v", err)
		}

		t.Logf("In-place transformation took: %v", inPlaceDuration)

		// Verify that the input data was modified
		if inPlaceMap, ok := inPlaceData.(map[string]interface{}); ok {
			if entities, ok := inPlaceMap["entities"].([]interface{}); ok {
				if len(entities) > 0 {
					if entity, ok := entities[0].(map[string]interface{}); ok {
						if entity["formatted_name"] != "FORMATTED-Entity0" {
							t.Errorf("In-place transformation did not modify data as expected")
						}
					}
				}
			}
		}
	})
}

// Benchmark tests for in-place transformation performance
func BenchmarkMappingEngine_CopyTransformation(b *testing.B) {
	engine := core.NewMappingEngine()

	inputData := map[string]interface{}{
		"field1": "value1",
		"field2": "value2",
		"nested": map[string]interface{}{
			"field3": "value3",
		},
	}

	config := &core.MappingConfig{
		FormatName:         "BENCHMARK_COPY",
		TransformationMode: "copy",
		FieldMappings: []core.FieldMapping{
			{
				Name:           "copy_field1",
				InputPath:      "field1",
				Transformation: "direct",
				OutputPath:     "output1",
			},
			{
				Name:           "copy_nested",
				InputPath:      "nested.field3",
				Transformation: "direct",
				OutputPath:     "output3",
			},
		},
		LookupTables: map[string]map[string]string{},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		testData := deepCopyData(inputData)
		_, _ = engine.TransformData(testData, config)
	}
}

func BenchmarkMappingEngine_InPlaceTransformation(b *testing.B) {
	engine := core.NewMappingEngine()

	inputData := map[string]interface{}{
		"field1": "value1",
		"field2": "value2",
		"nested": map[string]interface{}{
			"field3": "value3",
		},
	}

	config := &core.MappingConfig{
		FormatName:         "BENCHMARK_INPLACE",
		TransformationMode: "in_place",
		FieldMappings: []core.FieldMapping{
			{
				Name:           "modify_field1",
				InputPath:      "field1",
				Transformation: "format",
				OutputPath:     "output1",
				Config: map[string]interface{}{
					"format_pattern": "MODIFIED-%s",
				},
			},
			{
				Name:           "modify_nested",
				InputPath:      "nested.field3",
				Transformation: "format",
				OutputPath:     "output3",
				Config: map[string]interface{}{
					"format_pattern": "NESTED-%s",
				},
			},
		},
		LookupTables: map[string]map[string]string{},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		testData := deepCopyData(inputData)
		_, _ = engine.TransformData(testData, config)
	}
}

// Helper functions for testing

// deepCopyData creates a deep copy of interface{} data structures
func deepCopyData(data interface{}) interface{} {
	switch v := data.(type) {
	case map[string]interface{}:
		result := make(map[string]interface{})
		for key, value := range v {
			result[key] = deepCopyData(value)
		}
		return result
	case []interface{}:
		result := make([]interface{}, len(v))
		for i, value := range v {
			result[i] = deepCopyData(value)
		}
		return result
	default:
		return v // Primitive types are copied by value
	}
}

// Helper function to check if a string contains a substring
func containsSubstring(s, substr string) bool {
	return strings.Contains(s, substr)
}
