# Transformation Examples

## Real-World Transformation Scenarios

### Example 1: Converting Entity to NIBRS Person

**Input Data (from extraction):**
```json
{
  "entities": [
    {
      "entity": {
        "id": "ENT-123",
        "demographics": {
          "sex": "SEX_MALE",
          "age": 25,
          "race": "RACE_WHITE"
        }
      },
      "extractionMetadata": {
        "role": "VICTIM"
      }
    }
  ]
}
```

**Transformation Configuration:**
```json
{
  "field_mappings": [
    {
      "name": "victim_sex",
      "input_path": "entities[0].entity.demographics.sex",
      "transformation": "lookup",
      "output_path": "victim.sex_code",
      "config": {
        "lookup_table": "sex_codes"
      }
    },
    {
      "name": "victim_age",
      "input_path": "entities[0].entity.demographics.age",
      "transformation": "direct",
      "output_path": "victim.age"
    },
    {
      "name": "victim_race",
      "input_path": "entities[0].entity.demographics.race",
      "transformation": "lookup",
      "output_path": "victim.race_code",
      "config": {
        "lookup_table": "race_codes"
      }
    }
  ],
  "lookup_tables": {
    "sex_codes": {
      "SEX_MALE": "M",
      "SEX_FEMALE": "F",
      "SEX_UNKNOWN": "U"
    },
    "race_codes": {
      "RACE_WHITE": "W",
      "RACE_BLACK": "B",
      "RACE_ASIAN": "A"
    }
  }
}
```

**Output Data:**
```json
{
  "victim": {
    "sex_code": "M",
    "age": 25,
    "race_code": "W"
  }
}
```

### Example 2: Filtering and Formatting Incident Data

**Input Data:**
```json
{
  "reports": [{
    "report": {
      "id": "RPT-2024-001",
      "created_at": "2024-01-15T10:30:00Z",
      "incident_number": 12345
    }
  }],
  "entities": [
    {"extractionMetadata": {"role": "VICTIM"}},
    {"extractionMetadata": {"role": "OFFENDER"}},
    {"extractionMetadata": {"role": "VICTIM"}}
  ]
}
```

**Transformation Configuration:**
```json
{
  "field_mappings": [
    {
      "name": "incident_number",
      "input_path": "reports[0].report.incident_number",
      "transformation": "format",
      "output_path": "incident.number",
      "config": {
        "format_pattern": "INC-%07d"
      }
    },
    {
      "name": "report_date",
      "input_path": "reports[0].report.created_at",
      "transformation": "date_format",
      "output_path": "incident.date",
      "config": {
        "date_format": "01/02/2006"
      }
    },
    {
      "name": "victims_only",
      "input_path": "entities",
      "transformation": "filter",
      "output_path": "victims",
      "config": {
        "field": "extractionMetadata.role",
        "operator": "equals",
        "value": "VICTIM"
      }
    },
    {
      "name": "add_victim_sequence",
      "input_path": "victims",
      "transformation": "add_sequence",
      "output_path": "numbered_victims",
      "config": {
        "sequence_field": "victim_seq",
        "start": 1,
        "format": "%02d"
      }
    }
  ]
}
```

**Output Data:**
```json
{
  "incident": {
    "number": "INC-0012345",
    "date": "01/15/2024"
  },
  "victims": [
    {"extractionMetadata": {"role": "VICTIM"}},
    {"extractionMetadata": {"role": "VICTIM"}}
  ],
  "numbered_victims": [
    {"extractionMetadata": {"role": "VICTIM"}, "victim_seq": "01"},
    {"extractionMetadata": {"role": "VICTIM"}, "victim_seq": "02"}
  ]
}
```

### Example 3: Complex Path Navigation with Wildcards

**Input Data:**
```json
{
  "sections": [
    {
      "type": "ARREST",
      "arrests": [
        {
          "arrestee": {
            "entity_id": "ENT-123",
            "charges": ["BURGLARY", "THEFT"]
          }
        },
        {
          "arrestee": {
            "entity_id": "ENT-456",
            "charges": ["ASSAULT"]
          }
        }
      ]
    }
  ]
}
```

**Transformation Configuration:**
```json
{
  "field_mappings": [
    {
      "name": "all_arrestee_ids",
      "input_path": "sections[0].arrests[*].arrestee.entity_id",
      "transformation": "direct",
      "output_path": "arrestee_ids"
    },
    {
      "name": "all_charges",
      "input_path": "sections[0].arrests[*].arrestee.charges",
      "transformation": "direct",
      "output_path": "all_charges_nested"
    }
  ]
}
```

**Output Data:**
```json
{
  "arrestee_ids": ["ENT-123", "ENT-456"],
  "all_charges_nested": [
    ["BURGLARY", "THEFT"],
    ["ASSAULT"]
  ]
}
```

## Testing These Examples

Each example above has corresponding test cases in the test files:

1. **Lookup transformations** - See `TestTransformationEngine_LookupTransformation`
2. **Filter transformations** - See `TestTransformationEngine_FilterTransformation`
3. **Format transformations** - See `TestTransformationEngine_FormatTransformation`
4. **Path navigation** - See `TestPathResolver_GetValue_WithRealData`

The tests verify:
- Correct value extraction
- Proper transformation application
- Accurate output placement
- Error handling for missing data
- Edge cases and boundaries