package tests

import (
	"strings"
	"testing"

	"workflow/internal/etl/transformation/core"
)

// TestMappingEngineWarnings verifies that warnings are properly captured for gracefully handled issues
func TestMappingEngineWarnings(t *testing.T) {
	engine := core.NewMappingEngine()

	t.Run("missing_field_warnings", func(t *testing.T) {
		inputData := map[string]interface{}{
			"existing": "value",
		}

		config := &core.MappingConfig{
			FormatName: "TEST",
			FieldMappings: []core.FieldMapping{
				{
					Name:           "copy_existing",
					InputPath:      "existing",
					Transformation: "direct",
					OutputPath:     "out.existing",
				},
				{
					Name:           "copy_missing",
					InputPath:      "missing",
					Transformation: "direct",
					OutputPath:     "out.missing",
				},
			},
		}

		result, err := engine.TransformDataWithWarnings(inputData, config)
		if err != nil {
			t.Fatalf("Unexpected error: %v", err)
		}

		// Should succeed
		if !result.Success {
			t.Error("Expected Success=true")
		}

		// Should have warnings
		if len(result.Warnings) == 0 {
			t.<PERSON>rror("Expected warnings for missing field")
		}

		// Check warning mentions the missing field
		foundWarning := false
		for _, warning := range result.Warnings {
			t.Logf("Warning: %s", warning)
			if strings.Contains(warning, "missing") && strings.Contains(warning, "copy_missing") {
				foundWarning = true
			}
		}
		if !foundWarning {
			t.Error("Expected warning to mention the missing field and mapping name")
		}

		// Check result has original data preserved
		if result.Data["existing"] != "value" {
			t.Errorf("Expected original field existing='value', got %v", result.Data["existing"])
		}

		// Check output has both fields (existing with value, missing with nil)
		if out, ok := result.Data["out"].(map[string]interface{}); ok {
			if out["existing"] != "value" {
				t.Errorf("Expected out.existing='value', got %v", out["existing"])
			}
			// Missing field creates output with nil (graceful handling)
			if out["missing"] != nil {
				t.Errorf("Expected out.missing=nil, got %v", out["missing"])
			}
		} else {
			t.Error("Expected output structure not found")
		}
	})

	t.Run("nested_missing_field_warnings", func(t *testing.T) {
		inputData := map[string]interface{}{
			"level1": map[string]interface{}{
				"level2": "value",
			},
		}

		config := &core.MappingConfig{
			FormatName: "TEST",
			FieldMappings: []core.FieldMapping{
				{
					Name:           "nested_missing",
					InputPath:      "level1.level3.level4",
					Transformation: "direct",
					OutputPath:     "result",
				},
			},
		}

		result, err := engine.TransformDataWithWarnings(inputData, config)
		if err != nil {
			t.Fatalf("Unexpected error: %v", err)
		}

		// Should have warning about the specific path
		foundPathWarning := false
		for _, warning := range result.Warnings {
			t.Logf("Warning: %s", warning)
			if strings.Contains(warning, "level1.level3.level4") {
				foundPathWarning = true
			}
		}
		if !foundPathWarning {
			t.Error("Expected warning to mention the full missing path")
		}
	})

	t.Run("array_out_of_bounds_warnings", func(t *testing.T) {
		inputData := map[string]interface{}{
			"items": []interface{}{"first", "second"},
		}

		config := &core.MappingConfig{
			FormatName: "TEST",
			FieldMappings: []core.FieldMapping{
				{
					Name:           "valid_index",
					InputPath:      "items[1]",
					Transformation: "direct",
					OutputPath:     "valid",
				},
				{
					Name:           "out_of_bounds",
					InputPath:      "items[10]",
					Transformation: "direct",
					OutputPath:     "invalid",
				},
			},
		}

		result, err := engine.TransformDataWithWarnings(inputData, config)
		if err != nil {
			t.Fatalf("Unexpected error: %v", err)
		}

		// Should have warning about out of bounds
		foundWarning := false
		for _, warning := range result.Warnings {
			t.Logf("Warning: %s", warning)
			if strings.Contains(warning, "items[10]") && strings.Contains(warning, "out_of_bounds") {
				foundWarning = true
			}
		}
		if !foundWarning {
			t.Error("Expected warning about out of bounds array access")
		}

		// Check original data is preserved
		if items, ok := result.Data["items"].([]interface{}); ok {
			if len(items) != 2 || items[0] != "first" || items[1] != "second" {
				t.Errorf("Expected original items array to be preserved")
			}
		}

		// Valid field should have value
		if result.Data["valid"] != "second" {
			t.Errorf("Expected valid='second', got %v", result.Data["valid"])
		}
		// Invalid field creates output with nil when source is out of bounds
		if result.Data["invalid"] != nil {
			t.Errorf("Expected invalid=nil when source is out of bounds, got %v", result.Data["invalid"])
		}
	})

	t.Run("lookup_missing_input_warnings", func(t *testing.T) {
		inputData := map[string]interface{}{
			"status": "ACTIVE",
		}

		config := &core.MappingConfig{
			FormatName: "TEST",
			FieldMappings: []core.FieldMapping{
				{
					Name:           "lookup_missing",
					InputPath:      "missing_status",
					Transformation: "lookup",
					OutputPath:     "status_code",
					Config: map[string]interface{}{
						"lookup_table": "status_map",
					},
				},
			},
			LookupTables: map[string]map[string]string{
				"status_map": {
					"ACTIVE": "A",
				},
			},
		}

		result, err := engine.TransformDataWithWarnings(inputData, config)
		if err != nil {
			t.Fatalf("Unexpected error: %v", err)
		}

		// Should have warning about missing input
		if len(result.Warnings) == 0 {
			t.Error("Expected warning about missing input for lookup")
		}

		// Check original data is preserved
		if result.Data["status"] != "ACTIVE" {
			t.Errorf("Expected original status='ACTIVE', got %v", result.Data["status"])
		}

		// Output should be nil when input is missing
		if result.Data["status_code"] != nil {
			t.Errorf("Expected status_code=nil when input is missing, got %v", result.Data["status_code"])
		}
	})

	t.Run("multiple_warnings_collection", func(t *testing.T) {
		inputData := map[string]interface{}{
			"name": "John",
		}

		config := &core.MappingConfig{
			FormatName: "TEST",
			FieldMappings: []core.FieldMapping{
				{
					Name:           "copy_name",
					InputPath:      "name",
					Transformation: "direct",
					OutputPath:     "person.name",
				},
				{
					Name:           "copy_age",
					InputPath:      "age",
					Transformation: "direct",
					OutputPath:     "person.age",
				},
				{
					Name:           "copy_email",
					InputPath:      "contact.email",
					Transformation: "direct",
					OutputPath:     "person.email",
				},
				{
					Name:           "copy_phone",
					InputPath:      "contact.phone",
					Transformation: "direct",
					OutputPath:     "person.phone",
				},
			},
		}

		result, err := engine.TransformDataWithWarnings(inputData, config)
		if err != nil {
			t.Fatalf("Unexpected error: %v", err)
		}

		// Should have multiple warnings
		if len(result.Warnings) < 3 {
			t.Errorf("Expected at least 3 warnings, got %d", len(result.Warnings))
		}

		// Each missing field should generate a warning
		expectedMissing := []string{"age", "contact.email", "contact.phone"}
		for _, missing := range expectedMissing {
			found := false
			for _, warning := range result.Warnings {
				if strings.Contains(warning, missing) {
					found = true
					break
				}
			}
			if !found {
				t.Errorf("Expected warning for missing field '%s'", missing)
			}
		}

		// Name should be copied, others should be nil
		if person, ok := result.Data["person"].(map[string]interface{}); ok {
			if person["name"] != "John" {
				t.Errorf("Expected name='John', got %v", person["name"])
			}
			if person["age"] != nil || person["email"] != nil || person["phone"] != nil {
				t.Error("Expected nil for missing fields")
			}
		}
	})
}

// TestWarningFormatAndContent verifies warnings contain useful information
func TestWarningFormatAndContent(t *testing.T) {
	engine := core.NewMappingEngine()

	inputData := map[string]interface{}{
		"data": "test",
	}

	config := &core.MappingConfig{
		FormatName: "TEST",
		FieldMappings: []core.FieldMapping{
			{
				Name:           "important_mapping",
				InputPath:      "very.deeply.nested.field",
				Transformation: "direct",
				OutputPath:     "output",
			},
		},
	}

	result, err := engine.TransformDataWithWarnings(inputData, config)
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}

	if len(result.Warnings) == 0 {
		t.Fatal("Expected at least one warning")
	}

	warning := result.Warnings[0]
	t.Logf("Warning format: %s", warning)

	// Warning should contain:
	// 1. The mapping name
	if !strings.Contains(warning, "important_mapping") {
		t.Error("Warning should mention the mapping name")
	}

	// 2. The path that failed
	if !strings.Contains(warning, "very.deeply.nested.field") {
		t.Error("Warning should mention the failed path")
	}

	// 3. Some indication of what went wrong
	if !strings.Contains(warning, "no data found") && !strings.Contains(warning, "failed to extract") {
		t.Error("Warning should indicate what went wrong")
	}
}
