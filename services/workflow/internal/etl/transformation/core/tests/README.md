# Transformation Core Tests Documentation

## Overview

This directory contains comprehensive tests for the transformation core system. The tests are designed to be:
- **Easy to understand** - Clear documentation on what each test does
- **Self-contained** - Each test has clear input/output examples
- **Comprehensive** - Cover all success and error scenarios

## Test Organization

### 1. Path Resolver Tests (`path_resolver_test.go`)

Tests the navigation engine that extracts and sets values in data structures.

**Key Test Categories:**
- **Simple field access**: `data.field`
- **Nested navigation**: `data.level1.level2`
- **Array operations**: `data.array[0]`, `data.array[*]`
- **Complex paths**: `entities[*].entity.demographics.sex`
- **Error handling**: Missing fields, invalid indices

**Example Test Case:**
```go
// Input: {"items": [{"name": "item1"}, {"name": "item2"}]}
// Path: "items[*].name"
// Output: ["item1", "item2"]
```

### 2. Transformation Tests (`transformations_test.go`)

Tests each transformation type with real-world scenarios.

**Transformation Types:**

#### Direct Transformation
- **Purpose**: Pass data through unchanged
- **Use Case**: Copying field values as-is
- **Example**: `report.id` → `incident.number`

#### Lookup Transformation
- **Purpose**: Map values using lookup tables
- **Use Case**: Convert codes to descriptions
- **Example**: `"SEX_MALE"` → `"M"`

#### Filter Transformation
- **Purpose**: Filter arrays by criteria
- **Use Case**: Extract only victims from entities
- **Operators**: equals, contains, starts_with, in, etc.

#### Format Transformation
- **Purpose**: Format values with printf patterns
- **Use Case**: Pad numbers, format IDs
- **Example**: `123` → `"INC-00123"`

#### Date Format Transformation
- **Purpose**: Convert date formats
- **Use Case**: ISO dates to NIBRS format
- **Example**: `"2024-01-15T10:30:00Z"` → `"2024-01-15"`

#### Add Sequence Transformation
- **Purpose**: Add sequence numbers to arrays
- **Use Case**: Number items in lists
- **Example**: Add `sequence: "01"` to each array item

#### Group By Transformation
- **Purpose**: Group array items by field
- **Use Case**: Group entities by role
- **Example**: Group victims and offenders separately

#### Sort Transformation
- **Purpose**: Sort arrays by field
- **Use Case**: Order by age, name, etc.
- **Example**: Sort entities by age ascending

### 3. Configuration Loader Tests (`config_loader_test.go`)

Tests loading and validating transformation configurations.

**Test Scenarios:**
- Valid configurations load successfully
- Invalid JSON is rejected
- Missing required fields are detected
- Transformation-specific validation
- Lookup table reference validation

**Configuration Structure:**
```json
{
  "format_name": "NIBRS_XML_2023",
  "field_mappings": [
    {
      "name": "descriptive_name",
      "input_path": "path.to.input",
      "transformation": "direct",
      "output_path": "path.to.output",
      "config": { /* transformation-specific */ }
    }
  ],
  "lookup_tables": {
    "table_name": {
      "key": "value"
    }
  }
}
```

## Running Tests

### Run All Tests
```bash
./run_tests.sh
```

### Run Specific Test Suite
```bash
./run_tests.sh TestPathResolver
./run_tests.sh TestTransformationEngine
./run_tests.sh TestConfigLoader
```

### Run Individual Test
```bash
./run_tests.sh TestTransformationEngine_LookupTransformation
```

## Test Output

The custom test runner provides color-coded output:
- ✅ Green checkmarks for passed tests
- ❌ Red X marks for failed tests
- 🎯 Descriptive icons for each test category
- Clear grouping of related tests

## Adding New Tests

When adding new tests:

1. **Document the purpose** - Add header comment explaining what's being tested
2. **Provide examples** - Show input/output in comments
3. **Cover edge cases** - Test error conditions
4. **Use descriptive names** - Test names should explain the scenario
5. **Add inline comments** - Explain complex test logic

Example template:
```go
// TestFeatureName tests the specific feature behavior.
//
// Purpose: What this feature does
// Use Case: When you would use it
//
// Example Configuration:
// { ... }
func TestFeatureName(t *testing.T) {
    tests := []struct {
        name     string      // Descriptive test case name
        input    interface{} // Input data
        expected interface{} // Expected output
        wantErr  bool       // Whether error is expected
    }{
        {
            // Test: Specific scenario
            // Input: { ... }
            // Expected: { ... }
            // Description: What this test verifies
            name: "test case name",
            ...
        },
    }
}
```

## Performance Testing

Benchmark tests are included for performance-critical operations:
- Path resolution performance
- Transformation execution speed
- Configuration loading time

Run benchmarks:
```bash
go test -bench=. ./internal/etl/transformation/core/tests/
```