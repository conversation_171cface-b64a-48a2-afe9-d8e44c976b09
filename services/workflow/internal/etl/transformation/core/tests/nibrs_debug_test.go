package tests

import (
	"testing"
	"workflow/internal/etl/transformation/core"
)

// TestNIBRSGenderTransformationDebug debugs the specific NIBRS gender transformation issue
func TestNIBRSGenderTransformationDebug(t *testing.T) {
	mappingEngine := core.NewMappingEngine()

	// Simulate the NIBRS data structure after PersonEntities filter
	inputData := map[string]interface{}{
		"entities": []interface{}{
			map[string]interface{}{
				"entity": map[string]interface{}{
					"entity_type": "ENTITY_TYPE_PERSON",
					"data": map[string]interface{}{
						"firstName": "John",
						"gender":    "Male",
					},
				},
			},
			map[string]interface{}{
				"entity": map[string]interface{}{
					"entity_type": "ENTITY_TYPE_PERSON",
					"data": map[string]interface{}{
						"firstName": "Jane",
						"gender":    "Female",
					},
				},
			},
		},
	}

	// First step: Filter to create PersonEntities (like NIBRS does)
	// Second step: Transform genders in place
	config := &core.MappingConfig{
		FormatName: "NIBRS_DEBUG",
		FieldMappings: []core.FieldMapping{
			{
				Name:           "person_entities_filter",
				InputPath:      "entities",
				OutputPath:     "PersonEntities",
				Transformation: "filter",
				Config: map[string]interface{}{
					"field":    "entity.entity_type",
					"operator": "equals",
					"value":    "ENTITY_TYPE_PERSON",
				},
			},
			{
				Name:           "victim_genders_lookup_inplace",
				InputPath:      "PersonEntities[*].entity.data.gender",
				OutputPath:     "PersonEntities[*].entity.data.gender",
				Transformation: "lookup",
				Config: map[string]interface{}{
					"lookup_table": "sex_codes",
				},
			},
		},
		LookupTables: map[string]map[string]string{
			"sex_codes": {
				"Male":   "M",
				"Female": "F",
			},
		},
	}

	// Apply transformation
	result, err := mappingEngine.TransformData(inputData, config)
	if err != nil {
		t.Fatalf("Transformation failed: %v", err)
	}

	// Debug: Print the full result
	t.Logf("Full result: %+v", result)

	// Check PersonEntities array
	personEntities, ok := result["PersonEntities"].([]interface{})
	if !ok {
		t.Fatal("PersonEntities should be an array")
	}

	t.Logf("PersonEntities length: %d", len(personEntities))

	// Check each person's gender
	for i, person := range personEntities {
		personMap := person.(map[string]interface{})
		entity := personMap["entity"].(map[string]interface{})
		data := entity["data"].(map[string]interface{})
		gender := data["gender"]

		t.Logf("Person %d gender: %v (type: %T)", i, gender, gender)

		// This should be individual values like "M", "F", not arrays
		if genderArray, isArray := gender.([]interface{}); isArray {
			t.Errorf("Person %d gender is an array %v, should be individual value", i, genderArray)
		} else {
			t.Logf("✓ Person %d has individual gender: %v", i, gender)
		}
	}
}

// TestWildcardDetectionInNIBRS tests if wildcard detection works for NIBRS-style paths
func TestWildcardDetectionInNIBRS(t *testing.T) {
	mappingEngine := core.NewMappingEngine()

	// Test the containsWildcard method directly by creating a simple test
	inputData := map[string]interface{}{
		"PersonEntities": []interface{}{
			map[string]interface{}{
				"entity": map[string]interface{}{
					"data": map[string]interface{}{
						"gender": "Male",
					},
				},
			},
		},
	}

	config := &core.MappingConfig{
		FormatName: "WILDCARD_DETECTION_TEST",
		FieldMappings: []core.FieldMapping{
			{
				Name:           "test_wildcard_detection",
				InputPath:      "PersonEntities[*].entity.data.gender",
				OutputPath:     "PersonEntities[*].entity.data.gender",
				Transformation: "lookup",
				Config: map[string]interface{}{
					"lookup_table": "sex_codes",
				},
			},
		},
		LookupTables: map[string]map[string]string{
			"sex_codes": {
				"Male": "M",
			},
		},
	}

	result, err := mappingEngine.TransformData(inputData, config)
	if err != nil {
		t.Fatalf("Transformation failed: %v", err)
	}

	// Check if element-by-element processing was used
	personEntities := result["PersonEntities"].([]interface{})
	person := personEntities[0].(map[string]interface{})
	entity := person["entity"].(map[string]interface{})
	data := entity["data"].(map[string]interface{})
	gender := data["gender"]

	if gender != "M" {
		t.Errorf("Expected 'M', got: %v (type: %T)", gender, gender)
	} else {
		t.Logf("✓ Wildcard detection and element-by-element processing working correctly")
	}
}
