package tests

import (
	"reflect"
	"testing"

	"workflow/internal/etl/transformation/core"
)

// TestTransformationEngine_DirectTransformation tests the "direct" transformation type.
//
// Purpose: Direct transformation passes input data through without any modification.
// Use Case: When you want to copy a field value as-is to the output.
//
// Example Configuration:
//
//	{
//	  "name": "copy_report_id",
//	  "input_path": "reports[0].report.id",
//	  "transformation": "direct",
//	  "output_path": "incident.number"
//	}
func TestTransformationEngine_DirectTransformation(t *testing.T) {
	engine := core.NewTransformationEngine()

	tests := []struct {
		name     string
		input    interface{} // Input value to transform
		expected interface{} // Expected output (should be identical to input)
		wantErr  bool        // Direct transformation never errors
	}{
		{
			name:     "string input",
			input:    "hello",
			expected: "hello",
			wantErr:  false,
		},
		{
			name:     "integer input",
			input:    42,
			expected: 42,
			wantErr:  false,
		},
		{
			name:     "map input",
			input:    map[string]interface{}{"key": "value"},
			expected: map[string]interface{}{"key": "value"},
			wantErr:  false,
		},
		{
			name:     "array input",
			input:    []interface{}{"item1", "item2"},
			expected: []interface{}{"item1", "item2"},
			wantErr:  false,
		},
		{
			name:     "nil input",
			input:    nil,
			expected: nil,
			wantErr:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := engine.ApplyTransformation(tt.input, "direct", map[string]interface{}{})
			if (err != nil) != tt.wantErr {
				t.Errorf("DirectTransformation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("DirectTransformation() = %v, want %v", result, tt.expected)
			}
		})
	}
}

// TestTransformationEngine_LookupTransformation tests the "lookup" transformation type.
//
// Purpose: Maps input values to different output values using a lookup table.
// Use Case: Converting enum values, codes to descriptions, or any value mapping.
//
// Example Configuration:
//
//	{
//	  "name": "map_sex_code",
//	  "input_path": "entities[0].entity.demographics.sex",
//	  "transformation": "lookup",
//	  "output_path": "person.sex_code",
//	  "config": {
//	    "lookup_table": "sex_codes",
//	    "default_value": "U"  // Optional: used when value not found
//	  }
//	}
//
// With lookup table:
//
//	"sex_codes": {
//	  "SEX_MALE": "M",
//	  "SEX_FEMALE": "F",
//	  "SEX_UNKNOWN": "U"
//	}
func TestTransformationEngine_LookupTransformation(t *testing.T) {
	engine := core.NewTransformationEngine()

	// Sample lookup table for sex codes
	lookupTable := map[string]string{
		"M": "MALE",
		"F": "FEMALE",
		"U": "UNKNOWN",
	}

	tests := []struct {
		name     string
		input    interface{}            // Input value to look up
		config   map[string]interface{} // Transformation configuration
		expected interface{}            // Expected mapped value
		wantErr  bool                   // Whether lookup should fail
	}{
		{
			name:  "successful lookup",
			input: "M",
			config: map[string]interface{}{
				"lookup_table":      "sex_codes",
				"lookup_table_data": lookupTable,
			},
			expected: "MALE",
			wantErr:  false,
		},
		{
			name:  "lookup with default value",
			input: "X",
			config: map[string]interface{}{
				"lookup_table":      "sex_codes",
				"lookup_table_data": lookupTable,
				"default_value":     "OTHER",
			},
			expected: "OTHER",
			wantErr:  false,
		},
		{
			name:  "lookup not found without default",
			input: "X",
			config: map[string]interface{}{
				"lookup_table":      "sex_codes",
				"lookup_table_data": lookupTable,
			},
			expected: nil,
			wantErr:  true,
		},
		{
			name:  "non-string input",
			input: 123,
			config: map[string]interface{}{
				"lookup_table":      "sex_codes",
				"lookup_table_data": lookupTable,
			},
			expected: nil,
			wantErr:  true,
		},
		{
			name:  "missing lookup_table_data config",
			input: "M",
			config: map[string]interface{}{
				"lookup_table": "sex_codes",
			},
			expected: nil,
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := engine.ApplyTransformation(tt.input, "lookup", tt.config)
			if (err != nil) != tt.wantErr {
				t.Errorf("LookupTransformation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("LookupTransformation() = %v, want %v", result, tt.expected)
			}
		})
	}
}

// TestTransformationEngine_FilterTransformation tests the "filter" transformation type.
//
// Purpose: Filters array elements based on specified criteria.
// Use Case: Extracting only victims from entities, active reports, etc.
//
// Example Configuration:
//
//	{
//	  "name": "filter_victims",
//	  "input_path": "entities[*]",
//	  "transformation": "filter",
//	  "output_path": "victims",
//	  "config": {
//	    "field": "extractionMetadata.role",
//	    "operator": "equals",        // equals, not_equals, contains, starts_with, ends_with, in, not_in
//	    "value": "VICTIM"
//	  }
//	}
//
// Supported Operators:
// - equals: Exact match
// - not_equals: Not equal to value
// - contains: String contains substring
// - starts_with: String starts with prefix
// - ends_with: String ends with suffix
// - in: Value is in array
// - not_in: Value is not in array
func TestTransformationEngine_FilterTransformation(t *testing.T) {
	engine := core.NewTransformationEngine()

	// Sample data: array of people with roles
	inputData := []interface{}{
		map[string]interface{}{"name": "John", "age": 25, "role": "VICTIM"},
		map[string]interface{}{"name": "Jane", "age": 30, "role": "OFFENDER"},
		map[string]interface{}{"name": "Bob", "age": 35, "role": "VICTIM"},
	}

	tests := []struct {
		name     string
		input    interface{}            // Array to filter
		config   map[string]interface{} // Filter configuration
		expected interface{}            // Expected filtered array
		wantErr  bool                   // Whether filter should fail
	}{
		{
			name:  "filter by equals",
			input: inputData,
			config: map[string]interface{}{
				"field":    "role",
				"operator": "equals",
				"value":    "VICTIM",
			},
			expected: []interface{}{
				map[string]interface{}{"name": "John", "age": 25, "role": "VICTIM"},
				map[string]interface{}{"name": "Bob", "age": 35, "role": "VICTIM"},
			},
			wantErr: false,
		},
		{
			name:  "filter by not_equals",
			input: inputData,
			config: map[string]interface{}{
				"field":    "role",
				"operator": "not_equals",
				"value":    "VICTIM",
			},
			expected: []interface{}{
				map[string]interface{}{"name": "Jane", "age": 30, "role": "OFFENDER"},
			},
			wantErr: false,
		},
		{
			name:  "filter by contains",
			input: inputData,
			config: map[string]interface{}{
				"field":    "name",
				"operator": "contains",
				"value":    "o",
			},
			expected: []interface{}{
				map[string]interface{}{"name": "John", "age": 25, "role": "VICTIM"},
				map[string]interface{}{"name": "Bob", "age": 35, "role": "VICTIM"},
			},
			wantErr: false,
		},
		{
			name:  "filter by starts_with",
			input: inputData,
			config: map[string]interface{}{
				"field":    "name",
				"operator": "starts_with",
				"value":    "J",
			},
			expected: []interface{}{
				map[string]interface{}{"name": "John", "age": 25, "role": "VICTIM"},
				map[string]interface{}{"name": "Jane", "age": 30, "role": "OFFENDER"},
			},
			wantErr: false,
		},
		{
			name:  "filter by ends_with",
			input: inputData,
			config: map[string]interface{}{
				"field":    "name",
				"operator": "ends_with",
				"value":    "e",
			},
			expected: []interface{}{
				map[string]interface{}{"name": "Jane", "age": 30, "role": "OFFENDER"},
			},
			wantErr: false,
		},
		{
			name:  "filter by in",
			input: inputData,
			config: map[string]interface{}{
				"field":    "name",
				"operator": "in",
				"value":    []interface{}{"John", "Bob"},
			},
			expected: []interface{}{
				map[string]interface{}{"name": "John", "age": 25, "role": "VICTIM"},
				map[string]interface{}{"name": "Bob", "age": 35, "role": "VICTIM"},
			},
			wantErr: false,
		},
		{
			name:  "filter by not_in",
			input: inputData,
			config: map[string]interface{}{
				"field":    "name",
				"operator": "not_in",
				"value":    []interface{}{"John", "Bob"},
			},
			expected: []interface{}{
				map[string]interface{}{"name": "Jane", "age": 30, "role": "OFFENDER"},
			},
			wantErr: false,
		},
		{
			name:  "non-array input",
			input: "not an array",
			config: map[string]interface{}{
				"field":    "role",
				"operator": "equals",
				"value":    "VICTIM",
			},
			expected: nil,
			wantErr:  true,
		},
		{
			name:  "unknown operator",
			input: inputData,
			config: map[string]interface{}{
				"field":    "role",
				"operator": "unknown",
				"value":    "VICTIM",
			},
			expected: nil,
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := engine.ApplyTransformation(tt.input, "filter", tt.config)
			if (err != nil) != tt.wantErr {
				t.Errorf("FilterTransformation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("FilterTransformation() = %v, want %v", result, tt.expected)
			}
		})
	}
}

// TestTransformationEngine_FormatTransformation tests the "format" transformation type.
//
// Purpose: Formats values using printf-style format patterns.
// Use Case: Padding numbers, formatting strings, creating formatted IDs.
//
// Example Configuration:
//
//	{
//	  "name": "format_incident_number",
//	  "input_path": "incident.number",
//	  "transformation": "format",
//	  "output_path": "formatted_incident_number",
//	  "config": {
//	    "format_pattern": "INC-%05d"   // Formats 123 as "INC-00123"
//	  }
//	}
//
// Common Format Patterns:
// - %d: Integer
// - %03d: Zero-padded integer (3 digits)
// - %.2f: Float with 2 decimal places
// - %s: String
func TestTransformationEngine_FormatTransformation(t *testing.T) {
	engine := core.NewTransformationEngine()

	tests := []struct {
		name     string
		input    interface{}            // Value to format
		config   map[string]interface{} // Format configuration
		expected interface{}            // Expected formatted string
		wantErr  bool                   // Whether format should fail
	}{
		{
			name:  "format integer",
			input: 42,
			config: map[string]interface{}{
				"format_pattern": "%03d",
			},
			expected: "042",
			wantErr:  false,
		},
		{
			name:  "format float",
			input: 3.14159,
			config: map[string]interface{}{
				"format_pattern": "%.2f",
			},
			expected: "3.14",
			wantErr:  false,
		},
		{
			name:  "format string",
			input: "hello",
			config: map[string]interface{}{
				"format_pattern": "Message: %s",
			},
			expected: "Message: hello",
			wantErr:  false,
		},
		{
			name:     "missing format_pattern",
			input:    42,
			config:   map[string]interface{}{},
			expected: nil,
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := engine.ApplyTransformation(tt.input, "format", tt.config)
			if (err != nil) != tt.wantErr {
				t.Errorf("FormatTransformation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("FormatTransformation() = %v, want %v", result, tt.expected)
			}
		})
	}
}

// TestTransformationEngine_DateFormatTransformation tests the "date_format" transformation type.
//
// Purpose: Converts date strings from one format to another.
// Use Case: Converting ISO dates to NIBRS format, formatting for display.
//
// Example Configuration:
//
//	{
//	  "name": "format_report_date",
//	  "input_path": "reports[0].report.created_at",
//	  "transformation": "date_format",
//	  "output_path": "incident.report_date",
//	  "config": {
//	    "date_format": "2006-01-02"   // Go date format for YYYY-MM-DD
//	  }
//	}
//
// Common Date Formats (Go time package):
// - "2006-01-02": YYYY-MM-DD
// - "01/02/2006": MM/DD/YYYY
// - "2006-01-02T15:04:05": ISO date time
// - "Jan 2, 2006": Month DD, YYYY
func TestTransformationEngine_DateFormatTransformation(t *testing.T) {
	engine := core.NewTransformationEngine()

	tests := []struct {
		name     string
		input    interface{}            // Date string to format (RFC3339)
		config   map[string]interface{} // Date format configuration
		expected interface{}            // Expected formatted date string
		wantErr  bool                   // Whether format should fail
	}{
		{
			name:  "format RFC3339 to simple date",
			input: "2024-01-15T10:30:00Z",
			config: map[string]interface{}{
				"date_format": "2006-01-02",
			},
			expected: "2024-01-15",
			wantErr:  false,
		},
		{
			name:  "format RFC3339 to custom format",
			input: "2024-01-15T10:30:00Z",
			config: map[string]interface{}{
				"date_format": "01/02/2006 15:04",
			},
			expected: "01/15/2024 10:30",
			wantErr:  false,
		},
		{
			name:  "non-string input",
			input: 123,
			config: map[string]interface{}{
				"date_format": "2006-01-02",
			},
			expected: nil,
			wantErr:  true,
		},
		{
			name:  "invalid date format",
			input: "not a date",
			config: map[string]interface{}{
				"date_format": "2006-01-02",
			},
			expected: nil,
			wantErr:  true,
		},
		{
			name:     "missing date_format",
			input:    "2024-01-15T10:30:00Z",
			config:   map[string]interface{}{},
			expected: nil,
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := engine.ApplyTransformation(tt.input, "date_format", tt.config)
			if (err != nil) != tt.wantErr {
				t.Errorf("DateFormatTransformation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("DateFormatTransformation() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestTransformationEngine_AddSequenceTransformation(t *testing.T) {
	engine := core.NewTransformationEngine()

	inputData := []interface{}{
		map[string]interface{}{"name": "John"},
		map[string]interface{}{"name": "Jane"},
		map[string]interface{}{"name": "Bob"},
	}

	tests := []struct {
		name     string
		input    interface{}
		config   map[string]interface{}
		expected interface{}
		wantErr  bool
	}{
		{
			name:  "add sequence starting from 1",
			input: inputData,
			config: map[string]interface{}{
				"sequence_field": "id",
				"start":          1,
				"format":         "%d",
			},
			expected: []interface{}{
				map[string]interface{}{"name": "John", "id": "1"},
				map[string]interface{}{"name": "Jane", "id": "2"},
				map[string]interface{}{"name": "Bob", "id": "3"},
			},
			wantErr: false,
		},
		{
			name:  "add sequence starting from 10 with padding",
			input: inputData,
			config: map[string]interface{}{
				"sequence_field": "seq",
				"start":          10,
				"format":         "%03d",
			},
			expected: []interface{}{
				map[string]interface{}{"name": "John", "seq": "010"},
				map[string]interface{}{"name": "Jane", "seq": "011"},
				map[string]interface{}{"name": "Bob", "seq": "012"},
			},
			wantErr: false,
		},
		{
			name:  "default start and format",
			input: inputData,
			config: map[string]interface{}{
				"sequence_field": "num",
			},
			expected: []interface{}{
				map[string]interface{}{"name": "John", "num": "1"},
				map[string]interface{}{"name": "Jane", "num": "2"},
				map[string]interface{}{"name": "Bob", "num": "3"},
			},
			wantErr: false,
		},
		{
			name:  "non-array input",
			input: "not an array",
			config: map[string]interface{}{
				"sequence_field": "id",
			},
			expected: nil,
			wantErr:  true,
		},
		{
			name:  "missing sequence_field",
			input: inputData,
			config: map[string]interface{}{
				"start": 1,
			},
			expected: nil,
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Make a deep copy of input to avoid mutation between tests
			var inputCopy interface{}
			if inputArray, ok := tt.input.([]interface{}); ok {
				var arrayCopy []interface{}
				for _, item := range inputArray {
					if itemMap, ok := item.(map[string]interface{}); ok {
						copyMap := make(map[string]interface{})
						for k, v := range itemMap {
							copyMap[k] = v
						}
						arrayCopy = append(arrayCopy, copyMap)
					} else {
						arrayCopy = append(arrayCopy, item)
					}
				}
				inputCopy = arrayCopy
			} else {
				inputCopy = tt.input
			}

			result, err := engine.ApplyTransformation(inputCopy, "add_sequence", tt.config)
			if (err != nil) != tt.wantErr {
				t.Errorf("AddSequenceTransformation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("AddSequenceTransformation() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestTransformationEngine_GroupByTransformation(t *testing.T) {
	engine := core.NewTransformationEngine()

	inputData := []interface{}{
		map[string]interface{}{"name": "John", "role": "VICTIM"},
		map[string]interface{}{"name": "Jane", "role": "OFFENDER"},
		map[string]interface{}{"name": "Bob", "role": "VICTIM"},
	}

	tests := []struct {
		name     string
		input    interface{}
		config   map[string]interface{}
		expected interface{}
		wantErr  bool
	}{
		{
			name:  "group by role",
			input: inputData,
			config: map[string]interface{}{
				"group_field": "role",
			},
			expected: []interface{}{
				map[string]interface{}{
					"group_key": "VICTIM",
					"items": []interface{}{
						map[string]interface{}{"name": "John", "role": "VICTIM"},
						map[string]interface{}{"name": "Bob", "role": "VICTIM"},
					},
				},
				map[string]interface{}{
					"group_key": "OFFENDER",
					"items": []interface{}{
						map[string]interface{}{"name": "Jane", "role": "OFFENDER"},
					},
				},
			},
			wantErr: false,
		},
		{
			name:  "non-array input",
			input: "not an array",
			config: map[string]interface{}{
				"group_field": "role",
			},
			expected: nil,
			wantErr:  true,
		},
		{
			name:     "missing group_field",
			input:    inputData,
			config:   map[string]interface{}{},
			expected: nil,
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := engine.ApplyTransformation(tt.input, "group_by", tt.config)
			if (err != nil) != tt.wantErr {
				t.Errorf("GroupByTransformation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				// For group_by, we need to check if the result contains the expected groups
				// Order might be different due to map iteration
				resultArray, ok := result.([]interface{})
				if !ok {
					t.Errorf("GroupByTransformation() result is not an array")
					return
				}

				expectedArray, ok := tt.expected.([]interface{})
				if !ok {
					t.Errorf("Expected result is not an array")
					return
				}

				if len(resultArray) != len(expectedArray) {
					t.Errorf("GroupByTransformation() result length = %d, want %d", len(resultArray), len(expectedArray))
					return
				}

				// Check if all expected groups are present
				for _, expectedGroup := range expectedArray {
					found := false
					for _, resultGroup := range resultArray {
						if reflect.DeepEqual(resultGroup, expectedGroup) {
							found = true
							break
						}
					}
					if !found {
						t.Errorf("GroupByTransformation() missing expected group: %v", expectedGroup)
					}
				}
			}
		})
	}
}

func TestTransformationEngine_SortTransformation(t *testing.T) {
	engine := core.NewTransformationEngine()

	inputData := []interface{}{
		map[string]interface{}{"name": "John", "age": 25},
		map[string]interface{}{"name": "Jane", "age": 30},
		map[string]interface{}{"name": "Bob", "age": 20},
	}

	tests := []struct {
		name     string
		input    interface{}
		config   map[string]interface{}
		expected interface{}
		wantErr  bool
	}{
		{
			name:  "sort by age ascending",
			input: inputData,
			config: map[string]interface{}{
				"sort_field": "age",
				"sort_order": "asc",
			},
			expected: []interface{}{
				map[string]interface{}{"name": "Bob", "age": 20},
				map[string]interface{}{"name": "John", "age": 25},
				map[string]interface{}{"name": "Jane", "age": 30},
			},
			wantErr: false,
		},
		{
			name:  "sort by age descending",
			input: inputData,
			config: map[string]interface{}{
				"sort_field": "age",
				"sort_order": "desc",
			},
			expected: []interface{}{
				map[string]interface{}{"name": "Jane", "age": 30},
				map[string]interface{}{"name": "John", "age": 25},
				map[string]interface{}{"name": "Bob", "age": 20},
			},
			wantErr: false,
		},
		{
			name:  "sort by name ascending (default order)",
			input: inputData,
			config: map[string]interface{}{
				"sort_field": "name",
			},
			expected: []interface{}{
				map[string]interface{}{"name": "Bob", "age": 20},
				map[string]interface{}{"name": "Jane", "age": 30},
				map[string]interface{}{"name": "John", "age": 25},
			},
			wantErr: false,
		},
		{
			name:  "non-array input",
			input: "not an array",
			config: map[string]interface{}{
				"sort_field": "age",
			},
			expected: nil,
			wantErr:  true,
		},
		{
			name:  "missing sort_field",
			input: inputData,
			config: map[string]interface{}{
				"sort_order": "asc",
			},
			expected: nil,
			wantErr:  true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := engine.ApplyTransformation(tt.input, "sort", tt.config)
			if (err != nil) != tt.wantErr {
				t.Errorf("SortTransformation() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("SortTransformation() = %v, want %v", result, tt.expected)
			}
		})
	}
}

func TestTransformationEngine_UnknownTransformation(t *testing.T) {
	engine := core.NewTransformationEngine()

	result, err := engine.ApplyTransformation("test", "unknown_transformation", map[string]interface{}{})
	if err == nil {
		t.Errorf("Expected error for unknown transformation type")
	}
	if result != nil {
		t.Errorf("Expected nil result for unknown transformation type")
	}
}

func BenchmarkTransformationEngine_DirectTransformation(b *testing.B) {
	engine := core.NewTransformationEngine()
	input := map[string]interface{}{"test": "value"}
	config := map[string]interface{}{}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = engine.ApplyTransformation(input, "direct", config)
	}
}

func BenchmarkTransformationEngine_LookupTransformation(b *testing.B) {
	engine := core.NewTransformationEngine()
	input := "M"
	config := map[string]interface{}{
		"lookup_table": "sex_codes",
		"lookup_table_data": map[string]string{
			"M": "MALE",
			"F": "FEMALE",
		},
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = engine.ApplyTransformation(input, "lookup", config)
	}
}

func BenchmarkTransformationEngine_FilterTransformation(b *testing.B) {
	engine := core.NewTransformationEngine()
	input := []interface{}{
		map[string]interface{}{"name": "John", "role": "VICTIM"},
		map[string]interface{}{"name": "Jane", "role": "OFFENDER"},
		map[string]interface{}{"name": "Bob", "role": "VICTIM"},
	}
	config := map[string]interface{}{
		"field":    "role",
		"operator": "equals",
		"value":    "VICTIM",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, _ = engine.ApplyTransformation(input, "filter", config)
	}
}
