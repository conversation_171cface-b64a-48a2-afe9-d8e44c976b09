# Data Transformation Guide

This guide will help you create mapping configurations to transform your extracted data into any format you need. No programming knowledge required!

## Table of Contents
- [What is a Mapping Configuration?](#what-is-a-mapping-configuration)
- [Basic Structure](#basic-structure)
- [Transformation Types](#transformation-types)
- [Filter Operations](#filter-operations)
- [Lookup Tables](#lookup-tables)
- [Path Syntax](#path-syntax)
- [Complete Example](#complete-example)

## What is a Mapping Configuration?

A mapping configuration is like a recipe that tells the system how to transform your raw data into the format you need. It defines:
- Which data to extract (input paths)
- How to transform it (transformation type)
- Where to put the result (output paths)

Think of it as a set of instructions: "Take the date from here, format it like this, and put it there."

## Basic Structure

Every mapping configuration has these main sections:

```json
{
  "format_name": "My Report Format",
  "description": "What this configuration does",
  "version": "1.0",
  "field_mappings": [
    // Your transformation rules go here
  ],
  "lookup_tables": {
    // Your value mappings go here
  }
}
```

## Transformation Types

### 1. `direct` - Copy As-Is

Simply copies data from one place to another without changes.

**Input Data:**
```json
{
  "reports": [{
    "report": {
      "title": "Incident Report #123",
      "id": "abc-123"
    }
  }]
}
```

**Mapping:**
```json
{
  "name": "report_title",
  "description": "Copy report title without changes",
  "input_path": "reports[0].report.title",
  "transformation": "direct",
  "output_path": "ReportTitle",
  "config": {}
}
```

**Output:**
```json
{
  "ReportTitle": "Incident Report #123"
}
```

### 2. `format` - Format Text

Formats text using printf-style patterns:
- `%s` - String (text)
- `%d` - Integer
- `%.0f` - Float without decimals (useful for converting numbers to strings)
- `%02d` - Integer with leading zeros (width 2)
- `%.2f` - Float with 2 decimal places

**Input Data:**
```json
{
  "reports": [{
    "report": {
      "id": "abc-123",
      "org_id": 12345
    }
  }]
}
```

**Mappings:**
```json
[
  {
    "name": "incident_number",
    "description": "Add prefix to report ID",
    "input_path": "reports[0].report.id",
    "transformation": "format",
    "output_path": "IncidentNumber",
    "config": {
      "format_pattern": "INC-%s"
    }
  },
  {
    "name": "org_id_string",
    "description": "Convert number to text",
    "input_path": "reports[0].report.org_id",
    "transformation": "format",
    "output_path": "OrgIdString",
    "config": {
      "format_pattern": "%.0f"
    }
  }
]
```

**Output:**
```json
{
  "IncidentNumber": "INC-abc-123",
  "OrgIdString": "12345"
}
```

### 3. `date_format` - Format Dates

Converts dates from one format to another.

**Input Data:**
```json
{
  "reports": [{
    "report": {
      "created_at": "2024-07-14T15:30:00Z"
    }
  }]
}
```

**Mapping:**
```json
{
  "name": "report_date",
  "description": "Convert to YYYY-MM-DD format",
  "input_path": "reports[0].report.created_at",
  "transformation": "date_format",
  "output_path": "ReportDate",
  "config": {
    "date_format": "2006-01-02"
  }
}
```

**Output:**
```json
{
  "ReportDate": "2024-07-14"
}
```

**Common Date Formats:**
- `2006-01-02` → 2024-07-14
- `01/02/2006` → 07/14/2024
- `Jan 2, 2006` → Jul 14, 2024
- `15:04:05` → 15:30:00 (time only)

### 4. `lookup` - Map Values

Converts values using a lookup table (like a dictionary).

**Input Data:**
```json
{
  "entities": [{
    "entity": {
      "data": {
        "gender": "Male"
      }
    }
  }]
}
```

**Mapping:**
```json
{
  "name": "gender_code",
  "description": "Convert gender to single letter code",
  "input_path": "entities[0].entity.data.gender",
  "transformation": "lookup",
  "output_path": "GenderCode",
  "config": {
    "lookup_table": "sex_codes",
    "default_value": "U"
  }
}
```

**Lookup Table:**
```json
{
  "lookup_tables": {
    "sex_codes": {
      "Male": "M",
      "Female": "F",
      "Unknown": "U"
    }
  }
}
```

**Output:**
```json
{
  "GenderCode": "M"
}
```

### 5. `filter` - Extract Matching Items

Filters arrays to get only items that match your criteria.

**Input Data:**
```json
{
  "entities": [
    {"entity": {"entity_type": "ENTITY_TYPE_PERSON", "data": {"age": 25}}},
    {"entity": {"entity_type": "ENTITY_TYPE_VEHICLE", "data": {}}},
    {"entity": {"entity_type": "ENTITY_TYPE_PERSON", "data": {"age": 17}}}
  ]
}
```

**Mapping:**
```json
{
  "name": "person_entities",
  "description": "Get only person entities",
  "input_path": "entities",
  "transformation": "filter",
  "output_path": "PersonEntities",
  "config": {
    "field": "entity.entity_type",
    "operator": "equals",
    "value": "ENTITY_TYPE_PERSON"
  }
}
```

**Output:**
```json
{
  "PersonEntities": [
    {"entity": {"entity_type": "ENTITY_TYPE_PERSON", "data": {"age": 25}}},
    {"entity": {"entity_type": "ENTITY_TYPE_PERSON", "data": {"age": 17}}}
  ]
}
```

### 6. `add_sequence` - Add Numbers

Adds sequence numbers to items in an array.

**Input Data:**
```json
{
  "PersonEntities": [
    {"entity": {"data": {"name": "John Doe"}}},
    {"entity": {"data": {"name": "Jane Smith"}}}
  ]
}
```

**Mapping:**
```json
{
  "name": "persons_with_numbers",
  "description": "Add sequence numbers starting at 1",
  "input_path": "PersonEntities",
  "transformation": "add_sequence",
  "output_path": "PersonsWithSequence",
  "config": {
    "sequence_field": "SequenceNumber",
    "start": 1,
    "format": "%02d"
  }
}
```

**Output:**
```json
{
  "PersonsWithSequence": [
    {"SequenceNumber": "01", "entity": {"data": {"name": "John Doe"}}},
    {"SequenceNumber": "02", "entity": {"data": {"name": "Jane Smith"}}}
  ]
}
```

### 7. `sort` - Order Items

Sorts arrays by a specific field.

**Input Data:**
```json
{
  "PersonEntities": [
    {"entity": {"data": {"age": 35, "name": "Bob"}}},
    {"entity": {"data": {"age": 25, "name": "Alice"}}},
    {"entity": {"data": {"age": 30, "name": "Charlie"}}}
  ]
}
```

**Mapping:**
```json
{
  "name": "sorted_by_age",
  "description": "Sort people by age (youngest first)",
  "input_path": "PersonEntities",
  "transformation": "sort",
  "output_path": "SortedByAge",
  "config": {
    "sort_field": "entity.data.age",
    "sort_order": "asc"
  }
}
```

**Output:**
```json
{
  "SortedByAge": [
    {"entity": {"data": {"age": 25, "name": "Alice"}}},
    {"entity": {"data": {"age": 30, "name": "Charlie"}}},
    {"entity": {"data": {"age": 35, "name": "Bob"}}}
  ]
}
```

### 8. `group_by` - Group Similar Items

Groups array items by a common field.

**Input Data:**
```json
{
  "entities": [
    {"entity": {"entity_type": "ENTITY_TYPE_PERSON", "data": {"name": "John"}}},
    {"entity": {"entity_type": "ENTITY_TYPE_VEHICLE", "data": {"make": "Toyota"}}},
    {"entity": {"entity_type": "ENTITY_TYPE_PERSON", "data": {"name": "Jane"}}}
  ]
}
```

**Mapping:**
```json
{
  "name": "grouped_by_type",
  "description": "Group entities by their type",
  "input_path": "entities",
  "transformation": "group_by",
  "output_path": "GroupedEntities",
  "config": {
    "group_field": "entity.entity_type"
  }
}
```

**Output:**
```json
{
  "GroupedEntities": {
    "ENTITY_TYPE_PERSON": [
      {"entity": {"entity_type": "ENTITY_TYPE_PERSON", "data": {"name": "John"}}},
      {"entity": {"entity_type": "ENTITY_TYPE_PERSON", "data": {"name": "Jane"}}}
    ],
    "ENTITY_TYPE_VEHICLE": [
      {"entity": {"entity_type": "ENTITY_TYPE_VEHICLE", "data": {"make": "Toyota"}}}
    ]
  }
}
```

## Filter Operations

When using the `filter` transformation, you can use these operators:

### Comparison Operators

| Operator | Description | Example |
|----------|-------------|---------|
| `equals` | Exact match | age equals 25 |
| `not_equals` | Not an exact match | status not_equals "inactive" |
| `greater_than` | Greater than value | age greater_than 18 |
| `greater_than_or_equal` | Greater than or equal | age greater_than_or_equal 21 |
| `less_than` | Less than value | age less_than 65 |
| `less_than_or_equal` | Less than or equal | age less_than_or_equal 17 |

### Text Operators

| Operator | Description | Example |
|----------|-------------|---------|
| `contains` | Contains text | name contains "Smith" |
| `starts_with` | Starts with text | title starts_with "Detective" |
| `ends_with` | Ends with text | email ends_with "@email.com" |

### List Operators

| Operator | Description | Example |
|----------|-------------|---------|
| `in` | Value is in list | type in ["PERSON", "VEHICLE"] |
| `not_in` | Value is not in list | status not_in ["UNKNOWN", "ERROR"] |

### Filter Examples

**Filter adults (18+):**
```json
{
  "field": "entity.data.age",
  "operator": "greater_than_or_equal",
  "value": 18
}
```

**Filter email addresses from specific domain:**
```json
{
  "field": "entity.data.email",
  "operator": "ends_with",
  "value": "@police.gov"
}
```

**Filter specific entity types:**
```json
{
  "field": "entity.entity_type",
  "operator": "in",
  "value": ["ENTITY_TYPE_PERSON", "ENTITY_TYPE_VEHICLE"]
}
```

## Path Syntax

### Basic Paths

Access nested data using dots:
```
reports[0].report.title
entities[0].entity.data.firstName
```

### Array Access

**First item:** `reports[0]`  
**All items (wildcard):** `entities[*]`  
**Filtered items:** `entities[?(@.entity.entity_type == 'ENTITY_TYPE_PERSON')]`

### Wildcard Support

Wildcards (`[*]`) extract values from all array items:

**Input Data:**
```json
{
  "entities": [
    {"entity": {"data": {"firstName": "John"}}},
    {"entity": {"data": {"firstName": "Jane"}}},
    {"entity": {"data": {"firstName": "Bob"}}}
  ]
}
```

**Using wildcard:**
```json
"input_path": "entities[*].entity.data.firstName"
```

**Result:** `["John", "Jane", "Bob"]`

### JSONPath Filter Expressions

Filter arrays using JSONPath expressions with `[?(@...)]`:

**Basic equality filter:**
```json
"input_path": "entities[?(@.entity.entity_type == 'ENTITY_TYPE_PERSON')]"
```

**Filter by nested field:**
```json
"input_path": "entities[?(@.entity.data.age >= 18)]"
```

**Complex filters:**
```json
// Get race values for person entities only
"input_path": "entities[?(@.entity.entity_type == 'ENTITY_TYPE_PERSON')].entity.data.race"
```

### In-Place Updates with Wildcards

Update values without creating new fields by using the same path for input and output:

**Update all genders:**
```json
{
  "input_path": "entities[*].entity.data.gender",
  "output_path": "entities[*].entity.data.gender",
  "transformation": "lookup",
  "config": {
    "lookup_table": "sex_codes",
    "default_value": "U"
  }
}
```

This transforms all gender values in the original array without creating a new field.

### Examples

**Get first report's title:**
```json
"input_path": "reports[0].report.title"
```

**Get all entity first names:**
```json
"input_path": "entities[*].entity.data.firstName"
```

**Get ages of person entities only:**
```json
"input_path": "entities[?(@.entity.entity_type == 'ENTITY_TYPE_PERSON')].entity.data.age"
```

**Update specific filtered items in-place:**
```json
"input_path": "entities[?(@.entity.entity_type == 'ENTITY_TYPE_PERSON')].entity.data.race",
"output_path": "entities[?(@.entity.entity_type == 'ENTITY_TYPE_PERSON')].entity.data.race"
```

## Lookup Tables

Lookup tables are like dictionaries that convert one value to another.

### Example: Agency Names
```json
{
  "lookup_tables": {
    "agency_names": {
      "1": "Metropolitan Police Department",
      "2": "State Police Department",
      "3": "County Sheriff's Office"
    }
  }
}
```

### Example: Gender Codes
```json
{
  "lookup_tables": {
    "sex_codes": {
      "Male": "M",
      "Female": "F",
      "Unknown": "U",
      "": "U",
      "null": "U"
    }
  }
}
```

### Using Lookup Tables

1. Reference the table name in your mapping:
```json
{
  "transformation": "lookup",
  "config": {
    "lookup_table": "sex_codes",
    "default_value": "U"
  }
}
```

2. If the input value isn't found, it uses the `default_value`

## Complete Example

Here's a complete mapping configuration that transforms incident report data:

```json
{
  "format_name": "Police_Incident_Report",
  "description": "Transform raw data into incident report format",
  "version": "1.0",
  "field_mappings": [
    {
      "name": "report_date",
      "description": "Format report creation date",
      "input_path": "reports[0].report.created_at",
      "transformation": "date_format",
      "output_path": "ReportDate",
      "config": {
        "date_format": "2006-01-02"
      }
    },
    {
      "name": "incident_number",
      "description": "Format incident number with prefix",
      "input_path": "reports[0].report.id",
      "transformation": "format",
      "output_path": "IncidentNumber",
      "config": {
        "format_pattern": "INC-%s"
      }
    },
    {
      "name": "person_entities",
      "description": "Extract all person entities",
      "input_path": "entities",
      "transformation": "filter",
      "output_path": "PersonEntities",
      "config": {
        "field": "entity.entity_type",
        "operator": "equals",
        "value": "ENTITY_TYPE_PERSON"
      }
    },
    {
      "name": "adult_persons",
      "description": "Filter persons 18 and older",
      "input_path": "PersonEntities",
      "transformation": "filter",
      "output_path": "AdultPersons",
      "config": {
        "field": "entity.data.age",
        "operator": "greater_than_or_equal",
        "value": 18
      }
    },
    {
      "name": "persons_with_sequence",
      "description": "Add sequence numbers to persons",
      "input_path": "AdultPersons",
      "transformation": "add_sequence",
      "output_path": "PersonsWithSequence",
      "config": {
        "sequence_field": "SequenceNumber",
        "start": 1,
        "format": "%02d"
      }
    },
    {
      "name": "gender_codes",
      "description": "Convert gender to codes",
      "input_path": "PersonsWithSequence[*].entity.data.gender",
      "transformation": "lookup",
      "output_path": "PersonsWithSequence[*].entity.data.gender",
      "config": {
        "lookup_table": "sex_codes",
        "default_value": "U"
      }
    }
  ],
  "lookup_tables": {
    "sex_codes": {
      "Male": "M",
      "Female": "F",
      "Unknown": "U"
    }
  }
}
```

## Tips for Success

1. **Start Simple**: Begin with basic `direct` transformations
2. **Test Incrementally**: Add one transformation at a time
3. **Use Descriptive Names**: Make your field names clear
4. **Handle Missing Data**: Always set `default_value` for lookups
5. **Check Your Paths**: Verify input paths match your data structure
6. **Order Matters**: Transformations run in sequence - filter before add_sequence
7. **Chain Transformations**: Use output from one transformation as input for the next
8. **Use Wildcards**: Process all array items efficiently with `[*]`
9. **Filter Early**: Apply filters before other transformations to reduce processing

## Common Patterns

### Extract and Transform Filtered Data
1. Filter to get specific items
2. Add sequence numbers
3. Apply lookups for code conversions

### Convert IDs to Names
1. Format numeric IDs to strings if needed
2. Use lookup table to get names

### Date Handling
1. Extract date from source
2. Format to required output format

### In-Place Updates
Use the same path for input and output:
```json
{
  "input_path": "entities[*].entity.data.gender",
  "output_path": "entities[*].entity.data.gender"
}
```

This updates the values without moving them to a new location.

## Advanced Features

### Transformation Chaining

Use the output of one transformation as input for another:

```json
[
  {
    "name": "person_entities",
    "input_path": "entities",
    "transformation": "filter",
    "output_path": "PersonEntities",
    "config": {
      "field": "entity.entity_type",
      "operator": "equals",
      "value": "ENTITY_TYPE_PERSON"
    }
  },
  {
    "name": "adult_persons",
    "input_path": "PersonEntities",  // Uses output from previous transformation
    "transformation": "filter",
    "output_path": "AdultPersons",
    "config": {
      "field": "entity.data.age",
      "operator": "greater_than_or_equal",
      "value": 18
    }
  }
]
```

### Processing All Array Items

Combine wildcards with transformations to process entire arrays:

```json
{
  "name": "all_names_uppercase",
  "input_path": "entities[*].entity.data.firstName",
  "transformation": "format",
  "output_path": "AllFirstNames",
  "config": {
    "format_pattern": "%s"
  }
}
```

### Complex Path Resolution

The system handles missing intermediate fields gracefully:
- Returns empty/null for missing paths
- Continues processing other valid paths
- Use default values in lookups to handle missing data

### Format Pattern Reference

Common printf-style patterns:
- `%s` - String as-is
- `%d` - Integer (whole number)
- `%.0f` - Float with no decimals
- `%02d` - Two-digit number with leading zero
- `%05d` - Five-digit number with leading zeros
- `%.2f` - Float with 2 decimal places
- `INC-%s` - Add prefix to string
- `%s-%d` - Combine string and number