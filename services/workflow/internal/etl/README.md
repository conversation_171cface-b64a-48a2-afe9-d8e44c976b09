# ETL Pipeline for Law Enforcement Data Standards

This ETL (Extract, Transform, Load) pipeline transforms incident reports into compliance data standards (NIBRS, Clery, UCR, NIEM) and submits them to government agencies.

## 📋 Table of Contents

- [Overview](#overview)
- [Architecture](#architecture)
- [Core Concepts](#core-concepts)
- [API Usage](#api-usage)
- [Key Features](#key-features)
- [Template System](#template-system)
- [Security Features](#security-features)
- [Configuration](#configuration)
- [Development Guide](#development-guide)
- [Testing](#testing)
- [Benefits](#benefits)

---

## Overview

The ETL system processes complete incident reports with all related data (entities, situations, assets) and transforms them into standardized formats required by various law enforcement and regulatory agencies.

### Supported Data Standards

- **NIBRS XML** - FBI National Incident-Based Reporting System

### Target Agencies

- **FBI** - NIBRS submissions
- **Department of Justice** - Various federal reporting
- **State Police** - State-level crime reporting
- **Universities** - Campus crime reporting
- **Local Law Enforcement** - Municipal and county reporting

## Architecture

```
services/workflow/internal/etl/
├── types.go                                   # Core types and interfaces
├── errors.go                                  # ETL errors and error codes
│
├── api/                                       # gRPC API layer (following orders pattern)
│   ├── connect/                               # Connect RPC handlers
│       └── etl_server.go                      # ETL service Connect handlers
│
├── data/                                      # Data persistence (following orders pattern)
│   ├── etl_repo.go                            # ETL repository interface
│   ├── postgres_etl_repo.go                   # PostgreSQL ETL repository implementation
│   ├── job_repository.go                      # ETL job storage and retrieval
│   ├── stats_repository.go                    # Job statistics storage
│   └── config_repository.go                   # Configuration storage
│
├── usecase/                                   # Business logic (following orders pattern)
│   ├── etl_usecase.go                         # Main ETL use cases
│   ├── extraction/                            # Extract complete report data
│   │   ├── report_extractor.go               # Extract report with all relationships
│   │   ├── entity_extractor.go               # Extract people, vehicles, properties
│   │   ├── situation_extractor.go            # Extract incident situations
│   │   ├── asset_extractor.go                # Extract responder/member assets
│   │   └── composite_extractor.go            # Orchestrate all extractions
│   ├── transformation/                        # Template-based transformation (THE strategy)
│   │   ├── strategy.go                        # Transformation strategy interface
│   │   ├── template_engine.go                 # Core template engine (render templates)
│   │   ├── template_loader.go                 # Template loading and caching
│   │   ├── template_registry.go               # Template registration and discovery
│   │   ├── function_registry.go               # Template function registration
│   │   └── validation.go                      # Template output validation
│   ├── loading/                               # Submit data to agencies
│   │   ├── strategy.go                        # Loading strategy interface
│   │   ├── clients/                           # Transport mechanisms
│   │   │   ├── http_client.go                 # HTTP/HTTPS submission
│   │   │   ├── https_mtls_client.go           # Mutual TLS authentication
│   │   │   ├── ftp_client.go                  # FTP submission
│   │   │   └── sftp_client.go                 # Secure FTP submission
│   │   └── strategies/                        # Agency-specific submission logic
│   │       ├── fbi_strategy.go                # FBI submission (NIBRS, UCR)
│   │       ├── doj_strategy.go                # Department of Justice
│   │       ├── state_police_strategy.go       # State Police agencies
│   │       └── university_strategy.go         # University/Clery submissions
│   └── pipeline/                              # Pipeline orchestration
│       ├── orchestrator.go                    # Main ETL pipeline orchestrator
│       ├── job_manager.go                     # Job lifecycle and progress tracking
│       ├── retry_manager.go                   # Retry logic and exponential backoff
│       ├── error_classifier.go               # Error classification (retryable vs permanent)
│       └── stats_calculator.go                # Job statistics and monitoring
│
├── templates/                                 # Template implementations (secure, self-contained)
│   ├── nibrs/                                 # NIBRS data standard
│   │   ├── nibrs.xml.tmpl                     # Main NIBRS XML template (syntax validated)
│   │   └── codes.json                         # NIBRS code mappings (schema validated)
│   ├── clery/                                 # Clery Act data standard
│   │   ├── clery.json.tmpl                    # Main Clery JSON template (syntax validated)
│   │   └── codes.json                         # Clery code mappings (schema validated)
│   ├── ucr/                                   # UCR data standard
│   │   ├── ucr.csv.tmpl                       # Main UCR CSV template (syntax validated)
│   │   └── codes.json                         # UCR code mappings (schema validated)
│   └── common/                                # Common utilities (shared across standards)
│       └── validation.go                      # Common validation functions
│
├── schemas/                                   # JSON schemas for validation
│   ├── codes_nibrs.json                       # NIBRS codes schema
│   ├── codes_clery.json                       # Clery codes schema
│   ├── agencies.json                          # Agency config schema
│   └── templates.json                         # Template config schema
│
├── config/                                    # Configuration management
│   ├── agency_config.go                       # Agency configurations
│   ├── standards_config.go                    # Data standards configurations
│   └── pipeline_config.go                     # Pipeline configurations
│
└── routes.go                                  # HTTP routes setup (following orders pattern)
```

## Core Concepts

### ETL Job Lifecycle

1. **PENDING** - Job created and queued
2. **EXTRACTING** - Gathering complete report data from services
3. **TRANSFORMING** - Converting to target data standard format
4. **LOADING** - Submitting to agency endpoint
5. **COMPLETED** - Successfully submitted
6. **FAILED** - Error occurred (with detailed error information)
7. **CANCELLED** - User cancelled the job

### Processing Modes

- **Specific Reports** - Process individual reports by ID
- **Date Range** - Bulk process reports within date range
- **Filtered** - Process with case type and organization filters
- **Preview Mode** - Generate content without submitting

### Retry Mechanism

- **Automatic Retries** - Failed jobs automatically retry with exponential backoff
- **Error Classification** - Only retryable errors (network, timeouts) trigger retries
- **Manual Retries** - Users can force retry failed jobs
- **Configurable Limits** - Max retry attempts configurable per job
- **Retry Tracking** - Full audit trail of retry attempts and reasons

---

## Data Model Reference

### Enums

#### JobStatus
Tracks the lifecycle of ETL jobs:

| Status | Value | Description |
|--------|-------|-------------|
| JOB_STATUS_UNSPECIFIED | 0 | Unspecified status |
| JOB_STATUS_PENDING | 1 | Job queued, waiting to start |
| JOB_STATUS_EXTRACTING | 2 | Extracting report data from services |
| JOB_STATUS_TRANSFORMING | 3 | Transforming to agency format |
| JOB_STATUS_LOADING | 4 | Sending/loading data to agency |
| JOB_STATUS_COMPLETED | 5 | Successfully completed |
| JOB_STATUS_FAILED | 6 | Failed permanently |
| JOB_STATUS_CANCELLED | 7 | Cancelled by user |

#### OutputFormat
Data standards/formats for law enforcement reporting:

| Format | Value | Description |
|--------|-------|-------------|
| OUTPUT_FORMAT_UNSPECIFIED | 0 | Unspecified format |
| OUTPUT_FORMAT_NIBRS_XML | 1 | FBI NIBRS XML format |

---

## Messages

### ETLJob
Main entity tracking processing of reports for data standards compliance:

| Field | Type | Description |
|-------|------|-------------|
| id | string | Unique job ID |
| orgId | int32 | Organization ID for multi-tenant isolation |
| agencyId | string | Target agency/recipient (e.g., "FBI", "STATE_POLICE") |
| outputFormat | OutputFormat | Data standard/format (NIBRS, Clery, etc.) |
| status | JobStatus | Current status |
| reportIds | repeated string | Reports to process |
| dateFrom | string | ISO8601 date range start (optional) |
| dateTo | string | ISO8601 date range end (optional) |
| caseTypes | repeated string | Filter by case types (optional) |
| totalReports | int32 | Total reports to process |
| reportsProcessed | int32 | Reports successfully processed |
| reportsFailed | int32 | Reports that failed processing |
| reportsSkipped | int32 | Reports skipped (e.g., invalid data) |
| createdAt | string | ISO8601 timestamp when job was created |
| startedAt | string | ISO8601 timestamp when job started |
| completedAt | string | ISO8601 timestamp when job completed |
| lastUpdatedAt | string | ISO8601 timestamp of last status update |
| errorMessage | string | Primary error message if failed |
| errors | repeated JobError | Detailed error list |
| generatedContent | bytes | Generated XML/JSON/CSV for inspection |
| contentType | string | MIME type of generated content |
| contentSizeBytes | int64 | Size of generated content |
| agencySubmissionId | string | Agency's tracking ID (if submitted) |
| submissionResponse | string | Agency's response message |
| submittedAt | string | ISO8601 timestamp when submitted to agency |
| retryCount | int32 | Number of retry attempts made |
| maxRetries | int32 | Maximum retry attempts allowed |
| lastRetryAt | string | ISO8601 timestamp of last retry attempt |
| autoRetryEnabled | bool | Whether automatic retries are enabled |
| createdByAssetId | string | Who created the job |
| previewOnly | bool | Generated but not sent to agency |
| jobName | string | Optional human-readable job name |

### JobError
Detailed error information for jobs:

| Field | Type | Description |
|-------|------|-------------|
| errorCode | string | Error code (e.g., "INVALID_DATA", "NETWORK_ERROR") |
| errorMessage | string | Human-readable error message |
| reportId | string | Specific report ID that caused error (optional) |
| fieldPath | string | Field path that caused error (optional) |
| occurredAt | string | ISO8601 timestamp when error occurred |
| isRetryable | bool | Whether this error type is retryable |
| retryAttempt | int32 | Which retry attempt this error occurred on (0 = initial) |

### AgencyInfo
Agency/recipient information with supported data standards:

| Field | Type | Description |
|-------|------|-------------|
| id | string | Agency ID (e.g., "FBI", "STATE_POLICE") |
| name | string | Display name |
| supportedFormats | repeated OutputFormat | Supported data standards |
| enabled | bool | Whether agency is enabled |
| agencyType | string | Type: "FEDERAL", "STATE", "LOCAL", "UNIVERSITY" |

### ValidationError
Detailed validation error information:

| Field | Type | Description |
|-------|------|-------------|
| fieldPath | string | Field path that failed validation |
| errorCode | string | Error code (e.g., "REQUIRED_FIELD") |
| errorMessage | string | Human-readable error message |
| expectedValue | string | Expected value or format (optional) |
| actualValue | string | Actual value that failed (optional) |

### ReportValidationResult
Validation result for a single report:

| Field | Type | Description |
|-------|------|-------------|
| reportId | string | Report ID |
| valid | bool | Whether report is valid |
| errors | repeated ValidationError | Validation errors |
| warnings | repeated string | Validation warnings |

### AgencyStats
Per-agency statistics:

| Field | Type | Description |
|-------|------|-------------|
| agencyId | string | Agency ID |
| agencyName | string | Agency name |
| jobCount | int32 | Number of jobs for this agency |
| reportCount | int32 | Number of reports processed |
| contentSizeBytes | int64 | Total content size for agency |
| lastSubmissionAt | string | ISO8601 timestamp of last submission |

---

## Overview of Endpoints

The ETL Module provides the following endpoints for transforming incident reports into compliance data standards and submitting them to government agencies:

### Core ETL Operations
1. **[ProcessReports](#1-processreports)**
2. **[ExtractReportData](#2-extractreportdata)**
3. **[TestReportMapping](#3-testreportmapping)**
4. **[TestReportTransformation](#4-testreporttransformation)**

### Job Management
5. **[GetJob](#5-getjob)**
6. **[ListJobs](#6-listjobs)**
7. **[CancelJob](#7-canceljob)**
8. **[RetryJob](#8-retryjob)**
9. **[DownloadJobContent](#9-downloadjobcontent)**

### Utilities
10. **[ListAgencies](#10-listagencies)**
11. **[GetJobStats](#11-getjobstats)**
12. **[ValidateReports](#12-validatereports)**
13. **[UpdateJobProgress](#13-updatejobprogress)**
14. **[GetAgencyStats](#14-getagencystats)**

---

### 1. ProcessReports

**Method:** `ProcessReports`  
**Route:** `POST /hero.etl.v1.ETLService/ProcessReports`

Transform reports to compliance data standards and submit to agencies. Supports both specific reports and date range queries with filtering.

#### Notes
- **Transaction Safety**: The operation creates ETL job with atomicity
- **Progress Tracking**: Real-time job status updates with progress counters
- **Processing Modes**: Supports specific reports OR date range (not both)
- **Preview Mode**: Can generate content without submitting to agency

#### Use Cases
- NIBRS reporting: "Send these 3 incident reports to FBI in NIBRS XML format"
- Clery reporting: "Send all campus crimes from last month to DOE in Clery XML"
- UCR reporting: "Send all crimes from this quarter to State Police in UCR format"

#### Request

**ProcessReportsRequest:**

| Field | Type | Description |
|-------|------|-------------|
| agencyId | string | Target agency/recipient ID |
| outputFormat | OutputFormat | Data standard/format (NIBRS, Clery, etc.) |
| previewOnly | bool | Generate but don't send to agency |
| jobName | string | Optional human-readable job name |
| reportIds | repeated string | Specific reports to process |
| dateFrom | string | ISO8601 start date (optional) |
| dateTo | string | ISO8601 end date (optional) |
| caseTypes | repeated string | Filter by case types (optional) |
| skipInvalidReports | bool | Skip reports with validation errors |
| includeDraftReports | bool | Include draft/incomplete reports |
| maxRetries | int32 | Maximum retry attempts (default: 3) |
| autoRetryEnabled | bool | Enable automatic retries on failure (default: true) |

**Sample Request (JSON):**
```json
{
  "agencyId": "FBI",
  "outputFormat": "OUTPUT_FORMAT_NIBRS_XML",
  "reportIds": ["report-123", "report-456"],
  "jobName": "Weekly NIBRS Submission",
  "previewOnly": false,
  "maxRetries": 5,
  "autoRetryEnabled": true
}
```

#### Response

**ProcessReportsResponse:**

| Field | Type | Description |
|-------|------|-------------|
| job | ETLJob | Created job |
| estimatedReportCount | int32 | Estimated number of reports (if using date range) |

**Sample Response (JSON):**
```json
{
  "job": {
    "id": "job-uuid-789",
    "orgId": 123,
    "agencyId": "FBI",
    "outputFormat": "OUTPUT_FORMAT_NIBRS_XML",
    "status": "JOB_STATUS_PENDING",
    "reportIds": ["report-123", "report-456"],
    "totalReports": 2,
    "reportsProcessed": 0,
    "reportsFailed": 0,
    "reportsSkipped": 0,
    "createdAt": "2024-01-15T10:30:00Z",
    "jobName": "Weekly NIBRS Submission",
    "createdByAssetId": "asset-001",
    "previewOnly": false
  },
  "estimatedReportCount": 2
}
```

---

### 2. ExtractReportData

**Method:** `ExtractReportData`  
**Route:** `POST /hero.etl.v1.ETLService/ExtractReportData`

Extract raw report data without transformation.

#### Notes
- **No Transformation**: Returns raw extracted data in JSON format
- **Debug Tool**: Essential for understanding what data is being extracted
- **Complete Data**: Includes all entities, assets, situations, and relationships

#### Use Cases
- "Show me all the raw data extracted from this report"
- "Debug what data sets are being extracted from report XYZ"
- "Inspect extracted entities, assets, and relationships"

#### Request

**ExtractReportDataRequest:**

| Field | Type | Description |
|-------|------|-------------|
| reportId | string | Single report to extract data from |

**Sample Request (JSON):**
```json
{
  "reportId": "2d38c610-16a7-4cb4-b3f5-7015e7311e0e"
}
```

#### Response

**ExtractReportDataResponse:**

| Field | Type | Description |
|-------|------|-------------|
| extractedData | google.protobuf.Struct | Complete extracted data as structured JSON |
| extractionTime | string | ISO8601 timestamp when extraction was done |
| warnings | repeated string | Extraction warnings |
| success | bool | Whether extraction succeeded |
| errorMessage | string | Error if failed |
| dataSetsCount | int32 | Number of data sets extracted |
| dataSetNames | repeated string | Names of extracted data sets |

**Sample Response (JSON):**
```json
{
  "extractedData": {
    "reports": [...],
    "entities": [...],
    "situations": [...],
    "assets": [...],
    "relationships": [...]
  },
  "extractionTime": "2024-01-15T10:30:00Z",
  "warnings": [],
  "success": true,
  "dataSetsCount": 5,
  "dataSetNames": ["reports", "entities", "situations", "assets", "relationships"]
}
```

---

### 3. TestReportMapping

**Method:** `TestReportMapping`  
**Route:** `POST /hero.etl.v1.ETLService/TestReportMapping`

Preview mapping configuration transformation without template formatting.

#### Notes
- **Intermediate Step**: Shows data after mapping but before template formatting
- **Configuration Testing**: Test custom mapping configurations via API
- **Template Development**: Essential for understanding available fields for templates

#### Use Cases
- "Show me the data structure after mapping config transformation"
- "Debug what fields are available for template development"
- "Test custom mapping configuration without files"

#### Request

**TestReportMappingRequest:**

| Field | Type | Description |
|-------|------|-------------|
| reportId | string | Single report to test |
| outputFormat | OutputFormat | Output format/standard (NIBRS, UCR, etc.) |
| mappingConfigJson | string | Optional: Override mapping config (JSON string) |

**Sample Request (JSON):**
```json
{
  "reportId": "2d38c610-16a7-4cb4-b3f5-7015e7311e0e",
  "outputFormat": "OUTPUT_FORMAT_NIBRS_XML",
  "mappingConfigJson": "{\"field_mappings\":[{\"input_path\":\"reports[0].report.id\",\"transformation\":\"format\",\"output_path\":\"IncidentNumber\",\"config\":{\"format_pattern\":\"INC-%s\"}}]}"
}
```

#### Response

**TestReportMappingResponse:**

| Field | Type | Description |
|-------|------|-------------|
| mappedData | google.protobuf.Struct | JSON of config-mapped data |
| contentType | string | MIME type (application/json) |
| mappingConfigUsed | string | Config file path or "custom-config" |
| transformationTime | string | ISO8601 timestamp |
| warnings | repeated string | Mapping warnings |
| success | bool | Whether mapping succeeded |
| errorMessage | string | Error if failed |

**Sample Response (JSON):**
```json
{
  "mappedData": {
    "IncidentNumber": "INC-2d38c610-16a7-4cb4-b3f5-7015e7311e0e",
    "ReportDate": "2024-01-15",
    "PersonEntities": [...]
  },
  "contentType": "application/json",
  "mappingConfigUsed": "custom-config",
  "transformationTime": "2024-01-15T10:32:00Z",
  "warnings": [],
  "success": true
}
```

---

### 4. TestReportTransformation

**Method:** `TestReportTransformation`  
**Route:** `POST /hero.etl.v1.ETLService/TestReportTransformation`

Complete transformation pipeline - extract, map, and format.

#### Notes
- **Full Pipeline**: Executes complete transformation including template formatting
- **Custom Configurations**: Supports custom mapping config and template content
- **Preview Mode**: Test transformations without creating jobs

#### Use Cases
- "Show me what this report looks like in NIBRS XML format before sending"
- "Test custom template with custom mapping configuration"
- "Preview output with modified transformation rules"

#### Request

**TestReportTransformationRequest:**

| Field | Type | Description |
|-------|------|-------------|
| reportId | string | Single report to test |
| outputFormat | OutputFormat | Target format (NIBRS_XML, etc.) |
| validate | bool | Run validation on output (optional) |
| mappingConfigJson | string | Optional: Override mapping config (JSON string) |
| templateContent | string | Optional: Override template content |

**Sample Request (JSON):**
```json
{
  "reportId": "2d38c610-16a7-4cb4-b3f5-7015e7311e0e",
  "outputFormat": "OUTPUT_FORMAT_NIBRS_XML",
  "mappingConfigJson": "{\"field_mappings\":[...]}",
  "templateContent": "<?xml version=\"1.0\"?>\n<Report>\n  <ID>{{.IncidentNumber}}</ID>\n</Report>"
}
```

#### Response

**TestReportTransformationResponse:**

| Field | Type | Description |
|-------|------|-------------|
| transformedContent | bytes | Generated content |
| contentType | string | MIME type |
| transformationTime | string | ISO8601 timestamp |
| warnings | repeated string | Transformation warnings |
| success | bool | Whether transformation succeeded |
| errorMessage | string | Error if failed |
| validationErrors | repeated ValidationError | Detailed validation errors |
| readableContent | string | Human-readable version |
| mappingConfigUsed | string | Mapping config file path or "custom-config" |
| templateUsed | string | Template file path or "custom-template" |

**Sample Response (JSON):**
```json
{
  "transformedContent": "PD94bWwgdmVyc2lvbj0iMS4wIj8+...",
  "contentType": "application/xml",
  "transformationTime": "2024-01-15T10:35:00Z",
  "warnings": [],
  "success": true,
  "readableContent": "<?xml version=\"1.0\"?>\n<Report>\n  <ID>INC-2d38c610-16a7-4cb4-b3f5-7015e7311e0e</ID>\n</Report>",
  "mappingConfigUsed": "custom-config",
  "templateUsed": "custom-template"
}
```

---

### 5. GetJob

**Method:** `GetJob`  
**Route:** `POST /hero.etl.v1.ETLService/GetJob`

Get current status and details of an ETL job.

#### Request

**GetJobRequest:**

| Field | Type | Description |
|-------|------|-------------|
| jobId | string | Job ID |

**Sample Request (JSON):**
```json
{
  "jobId": "job-uuid-789"
}
```

#### Response

**GetJobResponse:**

| Field | Type | Description |
|-------|------|-------------|
| job | ETLJob | Job details |

**Sample Response (JSON):**
```json
{
  "job": {
    "id": "job-uuid-789",
    "orgId": 123,
    "agencyId": "FBI",
    "outputFormat": "OUTPUT_FORMAT_NIBRS_XML",
    "status": "JOB_STATUS_COMPLETED",
    "reportIds": ["report-123", "report-456"],
    "totalReports": 2,
    "reportsProcessed": 2,
    "reportsFailed": 0,
    "reportsSkipped": 0,
    "createdAt": "2024-01-15T10:30:00Z",
    "startedAt": "2024-01-15T10:31:00Z",
    "completedAt": "2024-01-15T10:35:00Z",
    "lastUpdatedAt": "2024-01-15T10:35:00Z",
    "contentType": "application/xml",
    "contentSizeBytes": 15420,
    "agencySubmissionId": "FBI-SUB-20240115-001",
    "submissionResponse": "Successfully received",
    "submittedAt": "2024-01-15T10:35:00Z",
    "jobName": "Weekly NIBRS Submission",
    "createdByAssetId": "asset-001"
  }
}
```

---

### 6. ListJobs

**Method:** `ListJobs`  
**Route:** `POST /hero.etl.v1.ETLService/ListJobs`

List ETL jobs with filtering and pagination.

#### Request

**ListJobsRequest:**

| Field | Type | Description |
|-------|------|-------------|
| agencyId | string | Filter by agency (optional) |
| status | JobStatus | Filter by status (optional) |
| createdFrom | string | ISO8601 start of creation date range (optional) |
| createdTo | string | ISO8601 end of creation date range (optional) |
| pageSize | int32 | Max results per page |
| pageToken | string | Pagination token |

**Sample Request (JSON):**
```json
{
  "agencyId": "FBI",
  "status": "JOB_STATUS_FAILED",
  "createdFrom": "2024-01-01T00:00:00Z",
  "createdTo": "2024-01-31T23:59:59Z",
  "pageSize": 50
}
```

#### Response

**ListJobsResponse:**

| Field | Type | Description |
|-------|------|-------------|
| jobs | repeated ETLJob | Jobs |
| nextPageToken | string | Next page token |

**Sample Response (JSON):**
```json
{
  "jobs": [
    {
      "id": "job-uuid-456",
      "orgId": 123,
      "agencyId": "FBI",
      "outputFormat": "OUTPUT_FORMAT_NIBRS_XML",
      "status": "JOB_STATUS_FAILED",
      "reportIds": ["report-789"],
      "totalReports": 1,
      "reportsProcessed": 0,
      "reportsFailed": 1,
      "createdAt": "2024-01-10T14:20:00Z",
      "errorMessage": "Invalid offense code mapping",
      "errors": [
        {
          "errorCode": "INVALID_DATA",
          "errorMessage": "Unknown offense code: CUSTOM_ASSAULT",
          "reportId": "report-789",
          "fieldPath": "offenses[0].code",
          "occurredAt": "2024-01-10T14:21:00Z"
        }
      ]
    }
  ],
  "nextPageToken": ""
}
```

---

### 7. CancelJob

**Method:** `CancelJob`  
**Route:** `POST /hero.etl.v1.ETLService/CancelJob`

Cancel a running or pending ETL job.

#### Request

**CancelJobRequest:**

| Field | Type | Description |
|-------|------|-------------|
| jobId | string | Job to cancel |

**Sample Request (JSON):**
```json
{
  "jobId": "job-uuid-789"
}
```

#### Response

**CancelJobResponse:**

| Field | Type | Description |
|-------|------|-------------|
| job | ETLJob | Updated job |

**Sample Response (JSON):**
```json
{
  "job": {
    "id": "job-uuid-789",
    "orgId": 123,
    "agencyId": "FBI",
    "outputFormat": "OUTPUT_FORMAT_NIBRS_XML",
    "status": "JOB_STATUS_CANCELLED",
    "reportIds": ["report-123", "report-456"],
    "totalReports": 2,
    "reportsProcessed": 1,
    "reportsFailed": 0,
    "reportsSkipped": 0,
    "createdAt": "2024-01-15T10:30:00Z",
    "startedAt": "2024-01-15T10:31:00Z",
    "lastUpdatedAt": "2024-01-15T10:33:00Z",
    "jobName": "Weekly NIBRS Submission",
    "createdByAssetId": "asset-001"
  }
}
```

---

### 8. RetryJob

**Method:** `RetryJob`  
**Route:** `POST /hero.etl.v1.ETLService/RetryJob`

Retry a failed ETL job with automatic retry logic and error classification.

#### Notes
- **Automatic Retry Logic**: Uses exponential backoff and error classification
- **Retry Limits**: Respects max retry limits (can be overridden)
- **Error Classification**: Only retries jobs with retryable errors
- **Force Retry**: Can force retry even if max retries exceeded

#### Use Cases
- "Retry that failed FBI submission after fixing the network issue"
- "Force retry this job even though it exceeded max retries"
- "Retry with increased retry limit for this complex job"

#### Request

**RetryJobRequest:**

| Field | Type | Description |
|-------|------|-------------|
| jobId | string | Job to retry |
| forceRetry | bool | Force retry even if max retries exceeded |
| overrideMaxRetries | int32 | Override max retries for this attempt (optional) |

**Sample Request (JSON):**
```json
{
  "jobId": "job-uuid-789",
  "forceRetry": false,
  "overrideMaxRetries": 5
}
```

#### Response

**RetryJobResponse:**

| Field | Type | Description |
|-------|------|-------------|
| job | ETLJob | Updated job with retry information |
| retryScheduled | bool | Whether retry was successfully scheduled |
| retryReason | string | Reason if retry was not scheduled |

**Sample Response (JSON):**
```json
{
  "job": {
    "id": "job-uuid-789",
    "orgId": 123,
    "agencyId": "FBI",
    "outputFormat": "OUTPUT_FORMAT_NIBRS_XML",
    "status": "JOB_STATUS_PENDING",
    "reportIds": ["report-123", "report-456"],
    "totalReports": 2,
    "reportsProcessed": 0,
    "reportsFailed": 0,
    "reportsSkipped": 0,
    "retryCount": 1,
    "maxRetries": 5,
    "lastRetryAt": "2024-01-15T10:45:00Z",
    "autoRetryEnabled": true,
    "createdAt": "2024-01-15T10:30:00Z",
    "lastUpdatedAt": "2024-01-15T10:45:00Z",
    "jobName": "Weekly NIBRS Submission - Retry",
    "createdByAssetId": "asset-001"
  },
  "retryScheduled": true,
  "retryReason": "Retry scheduled successfully"
}
```

---

### 9. DownloadJobContent

**Method:** `DownloadJobContent`  
**Route:** `POST /hero.etl.v1.ETLService/DownloadJobContent`

Download the generated XML/JSON/CSV from a completed job.

#### Request

**DownloadJobContentRequest:**

| Field | Type | Description |
|-------|------|-------------|
| jobId | string | Job ID |

**Sample Request (JSON):**
```json
{
  "jobId": "job-uuid-789"
}
```

#### Response

**DownloadJobContentResponse:**

| Field | Type | Description |
|-------|------|-------------|
| content | bytes | Generated content |
| contentType | string | MIME type |
| filename | string | Suggested filename |
| sizeBytes | int64 | Content size |

**Sample Response (JSON):**
```json
{
  "content": "PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4K...",
  "contentType": "application/xml",
  "filename": "NIBRS_FBI_20240115_103500.xml",
  "sizeBytes": 15420
}
```

---

### 10. ListAgencies

**Method:** `ListAgencies`  
**Route:** `POST /hero.etl.v1.ETLService/ListAgencies`

Get list of available agencies and their supported formats.

#### Request

**ListAgenciesRequest:**

| Field | Type | Description |
|-------|------|-------------|
| | | No parameters needed |

**Sample Request (JSON):**
```json
{}
```

#### Response

**ListAgenciesResponse:**

| Field | Type | Description |
|-------|------|-------------|
| agencies | repeated AgencyInfo | Available agencies |

**Sample Response (JSON):**
```json
{
  "agencies": [
    {
      "id": "FBI",
      "name": "Federal Bureau of Investigation",
      "supportedFormats": ["OUTPUT_FORMAT_NIBRS_XML"],
      "enabled": true,
      "agencyType": "FEDERAL"
    },
    {
      "id": "UNIVERSITY_POLICE",
      "name": "University Police Department",
      "supportedFormats": ["OUTPUT_FORMAT_NIBRS_XML"],
      "enabled": true,
      "agencyType": "UNIVERSITY"
    }
  ]
}
```

---

### 11. GetJobStats

**Method:** `GetJobStats`  
**Route:** `POST /hero.etl.v1.ETLService/GetJobStats`

Get statistical overview of ETL jobs.

#### Use Cases
- "Show me ETL job statistics for this month"
- "How many NIBRS jobs have we completed this quarter?"
- "What's our success rate for FBI submissions?"

#### Request

**GetJobStatsRequest:**

| Field | Type | Description |
|-------|------|-------------|
| agencyId | string | Filter by agency (optional) |
| outputFormat | OutputFormat | Filter by format (optional) |
| dateFrom | string | ISO8601 start date (optional) |
| dateTo | string | ISO8601 end date (optional) |

**Sample Request (JSON):**
```json
{
  "agencyId": "FBI",
  "outputFormat": "OUTPUT_FORMAT_NIBRS_XML",
  "dateFrom": "2024-01-01T00:00:00Z",
  "dateTo": "2024-01-31T23:59:59Z"
}
```

#### Response

**GetJobStatsResponse:**

| Field | Type | Description |
|-------|------|-------------|
| totalJobs | int32 | Total jobs in period |
| completedJobs | int32 | Successfully completed jobs |
| failedJobs | int32 | Failed jobs |
| cancelledJobs | int32 | Cancelled jobs |
| runningJobs | int32 | Currently running jobs |
| totalReportsProcessed | int32 | Total reports processed |
| totalContentSizeBytes | int64 | Total content generated |
| agencyStats | repeated AgencyStats | Per-agency statistics |

**Sample Response (JSON):**
```json
{
  "totalJobs": 150,
  "completedJobs": 140,
  "failedJobs": 8,
  "cancelledJobs": 2,
  "runningJobs": 0,
  "totalReportsProcessed": 3500,
  "totalContentSizeBytes": 52428800,
  "agencyStats": [
    {
      "agencyId": "FBI",
      "agencyName": "Federal Bureau of Investigation",
      "jobCount": 120,
      "reportCount": 2800,
      "contentSizeBytes": 41943040,
      "lastSubmissionAt": "2024-01-31T18:30:00Z"
    },
    {
      "agencyId": "STATE_POLICE",
      "agencyName": "State Police Department",
      "jobCount": 30,
      "reportCount": 700,
      "contentSizeBytes": 10485760,
      "lastSubmissionAt": "2024-01-31T16:15:00Z"
    }
  ]
}
```

---

### 12. ValidateReports

**Method:** `ValidateReports`  
**Route:** `POST /hero.etl.v1.ETLService/ValidateReports`

Validate reports against data standard before processing.

#### Use Cases
- "Check if these reports are valid for NIBRS before submitting"
- "Validate campus incidents against Clery requirements"

#### Request

**ValidateReportsRequest:**

| Field | Type | Description |
|-------|------|-------------|
| reportIds | repeated string | Reports to validate |
| outputFormat | OutputFormat | Target data standard |
| agencyId | string | Target agency (optional) |

**Sample Request (JSON):**
```json
{
  "reportIds": ["report-123", "report-456"],
  "outputFormat": "OUTPUT_FORMAT_NIBRS_XML",
  "agencyId": "FBI"
}
```

#### Response

**ValidateReportsResponse:**

| Field | Type | Description |
|-------|------|-------------|
| results | repeated ReportValidationResult | Validation results per report |
| validReports | int32 | Number of valid reports |
| invalidReports | int32 | Number of invalid reports |
| allValid | bool | Whether all reports are valid |

**Sample Response (JSON):**
```json
{
  "results": [
    {
      "reportId": "report-123",
      "valid": true,
      "errors": [],
      "warnings": []
    },
    {
      "reportId": "report-456",
      "valid": false,
      "errors": [
        {
          "fieldPath": "offenses[0].code",
          "errorCode": "REQUIRED_FIELD",
          "errorMessage": "Offense code is required for NIBRS reporting",
          "expectedValue": "Valid NIBRS offense code",
          "actualValue": ""
        }
      ],
      "warnings": ["Location code not specified, will use default"]
    }
  ],
  "validReports": 1,
  "invalidReports": 1,
  "allValid": false
}
```

---

### 13. UpdateJobProgress

**Method:** `UpdateJobProgress`  
**Route:** `POST /hero.etl.v1.ETLService/UpdateJobProgress`

Update job progress counters.

#### Use Cases
- "External job processor updating progress: 50 out of 100 reports processed"
- "Distributed ETL system reporting partial completion"
- "Real-time progress updates for long-running jobs"

#### Request

**UpdateJobProgressRequest:**

| Field | Type | Description |
|-------|------|-------------|
| jobId | string | Job ID to update |
| totalReports | int32 | Total reports to process |
| reportsProcessed | int32 | Reports successfully processed |
| reportsFailed | int32 | Reports that failed processing |
| reportsSkipped | int32 | Reports skipped (e.g., invalid data) |
| progressNote | string | Optional progress note or status message |

**Sample Request (JSON):**
```json
{
  "jobId": "job-uuid-789",
  "totalReports": 100,
  "reportsProcessed": 50,
  "reportsFailed": 2,
  "reportsSkipped": 0,
  "progressNote": "Processing batch 2 of 4"
}
```

#### Response

**UpdateJobProgressResponse:**

| Field | Type | Description |
|-------|------|-------------|
| job | ETLJob | Updated job with new progress |
| progressUpdated | bool | Whether progress was successfully updated |

**Sample Response (JSON):**
```json
{
  "job": {
    "id": "job-uuid-789",
    "orgId": 123,
    "agencyId": "FBI",
    "outputFormat": "OUTPUT_FORMAT_NIBRS_XML",
    "status": "JOB_STATUS_TRANSFORMING",
    "reportIds": [],
    "totalReports": 100,
    "reportsProcessed": 50,
    "reportsFailed": 2,
    "reportsSkipped": 0,
    "createdAt": "2024-01-15T10:30:00Z",
    "startedAt": "2024-01-15T10:31:00Z",
    "lastUpdatedAt": "2024-01-15T10:45:00Z"
  },
  "progressUpdated": true
}
```

---

### 14. GetAgencyStats

**Method:** `GetAgencyStats`  
**Route:** `POST /hero.etl.v1.ETLService/GetAgencyStats`

Get detailed statistics for a specific agency.

#### Use Cases
- "Show me FBI submission statistics for this quarter"
- "Generate agency-specific dashboard data"
- "Track performance metrics per agency"

#### Request

**GetAgencyStatsRequest:**

| Field | Type | Description |
|-------|------|-------------|
| agencyId | string | Agency ID (required) |
| dateFrom | string | ISO8601 start date (optional) |
| dateTo | string | ISO8601 end date (optional) |
| includeJobDetails | bool | Include detailed job breakdown (optional) |

**Sample Request (JSON):**
```json
{
  "agencyId": "FBI",
  "dateFrom": "2024-01-01T00:00:00Z",
  "dateTo": "2024-03-31T23:59:59Z",
  "includeJobDetails": true
}
```

#### Response

**GetAgencyStatsResponse:**

| Field | Type | Description |
|-------|------|-------------|
| agencyStats | AgencyStats | Agency statistics |
| recentJobs | repeated JobSummary | Recent jobs for this agency (if includeJobDetails=true) |
| generatedAt | string | ISO8601 timestamp when stats were generated |

**Sample Response (JSON):**
```json
{
  "agencyStats": {
    "agencyId": "FBI",
    "agencyName": "Federal Bureau of Investigation",
    "jobCount": 350,
    "reportCount": 8200,
    "contentSizeBytes": 125829120,
    "lastSubmissionAt": "2024-03-31T22:15:00Z"
  },
  "recentJobs": [
    {
      "jobId": "job-uuid-999",
      "status": "JOB_STATUS_COMPLETED",
      "outputFormat": "OUTPUT_FORMAT_NIBRS_XML",
      "reportsProcessed": 150,
      "createdAt": "2024-03-31T20:00:00Z",
      "completedAt": "2024-03-31T22:15:00Z",
      "contentSizeBytes": 2097152
    },
    {
      "jobId": "job-uuid-998",
      "status": "JOB_STATUS_COMPLETED",
      "outputFormat": "OUTPUT_FORMAT_NIBRS_XML",
      "reportsProcessed": 200,
      "createdAt": "2024-03-30T18:00:00Z",
      "completedAt": "2024-03-30T20:30:00Z",
      "contentSizeBytes": 3145728
    }
  ],
  "generatedAt": "2024-04-01T09:00:00Z"
}
```

---

## Key Features

### Comprehensive Data Extraction
- Extracts complete report with all relationships
- Includes entities (people, vehicles, properties, organizations)
- Includes situations, assets, and file attachments
- Maintains data integrity and relationships

### Template-Based Transformation Strategy
- **Templates AS Strategy** - Templates implement the strategy pattern for each data standard
- **Multi-Format Output** - XML (NIBRS), JSON (Clery), CSV (UCR), etc. based on agency requirements
- **Separates Logic from Code** - Non-developers can modify transformation logic
- **Template Functions** - Custom date formatting, field mapping, validation, code lookups
- **Format-Agnostic** - Same report data generates different formats per agency needs
- **Live Preview** - Test any template format before submission
- **Standard-Specific Validation** - Each template validated against its format requirements

### Intelligent Retry System
- **Error Classification** - Automatic classification of retryable vs permanent errors
- **Exponential Backoff** - Smart retry timing (1s, 2s, 4s, 8s, 16s)
- **Configurable Limits** - Per-job and global retry limits
- **Manual Override** - Force retry failed jobs or increase retry limits
- **Retry Tracking** - Full audit trail of all retry attempts
- **Error Types**:
  - **Retryable**: Network errors, timeouts, temporary agency unavailability, rate limits
  - **Permanent**: Invalid credentials, malformed data, unsupported formats, authorization failures

### Reliable Submission
- Multiple transport methods (HTTP, HTTPS, FTP, SFTP)
- Mutual TLS authentication support
- Intelligent retry logic with error classification
- Agency response tracking

### Progress Tracking
- Real-time job status updates
- Progress counters (processed, failed, skipped)
- Detailed error reporting with field-level information
- Content inspection and download

### Validation & Quality Assurance
- Pre-processing validation
- Data standard compliance checking
- Preview mode for content review
- Detailed validation error reporting

## Template System

### How Templates Work

The ETL system uses **Go's built-in `text/template`** engine as the transformation strategy implementation. Templates transform JSON report data into the specific format required by each agency/standard (XML, JSON, CSV, etc.). Templates are stored as separate `.tmpl` files that can be modified without code changes.

**Templates = Strategy Pattern Implementation**
- Each template file implements the transformation strategy for a specific data standard
- NIBRS uses XML templates (FBI requirement)
- Future standards may use JSON, CSV, or other formats
- Same template engine, different output formats

### Template Examples

#### XML Template (NIBRS - FBI Requirement)
```xml
<!-- nibrs.xml.tmpl -->
<?xml version="1.0" encoding="UTF-8"?>
<NIBRSSubmission version="4.2" xmlns="http://fbi.gov/cjis/nibrs/4.2">
{{range .Reports}}
<Incident>
    <IncidentNumber>{{.ID}}</IncidentNumber>
    <IncidentDate>{{.IncidentTime | formatDate}}</IncidentDate>
    {{if .ClearanceIndicator}}
    <ClearanceIndicator>{{.ClearanceIndicator}}</ClearanceIndicator>
    {{end}}
    
    {{range .Offenses}}
    <Offense>
        <OffenseCode>{{.Code | nibrsOffenseCode}}</OffenseCode>
        {{if .AttemptedCompleted}}
        <OffenseAttemptedCompleted>{{.AttemptedCompleted}}</OffenseAttemptedCompleted>
        {{end}}
        {{if .Location}}
        <LocationCode>{{.Location | nibrsLocationCode}}</LocationCode>
        {{end}}
    </Offense>
    {{end}}
</Incident>
{{end}}
</NIBRSSubmission>
```

#### JSON Template (Clery - DOE Format)
```json
<!-- clery.json.tmpl -->
{
  "submission": {
    "version": "2.0",
    "institution_id": "{{.InstitutionID}}",
    "incidents": [
      {{range $i, $report := .Reports}}
      {{if $i}},{{end}}
      {
        "incident_id": "{{$report.ID}}",
        "incident_date": "{{$report.IncidentTime | formatDate}}",
        "location_code": "{{$report.LocationCode | cleryLocationCode}}",
        "geography_code": "{{$report.GeographyArea | cleryGeographyCode}}",
        "offense_codes": [
          {{range $j, $offense := $report.Offenses}}
          {{if $j}},{{end}}"{{$offense.Code | cleryOffenseCategory}}"
          {{end}}
        ]
      }
      {{end}}
    ]
  }
}
```

#### CSV Template (UCR - State Format)
```csv
<!-- ucr.csv.tmpl -->
IncidentID,Date,OffenseClass,JurisdictionCode,ClearanceCode
{{range .Reports}}{{.ID}},{{.IncidentTime | formatDate}},{{range .Offenses}}{{.Code | ucrOffenseClass}}{{end}},{{.JurisdictionID | ucrJurisdictionCode}},{{.ClearanceIndicator | ucrClearanceCode}}
{{end}}
```

### Template Features

- **Loops**: `{{range .Reports}}...{{end}}` - Iterate over report collections
- **Conditionals**: `{{if .Field}}...{{end}}` - Include XML only if data exists
- **Functions**: `{{.Date | formatDate}}` - Transform data with custom functions
- **Variables**: `{{$index := .}}` - Create variables for complex logic
- **Nested Data**: `{{.Person.Name.First}}` - Access nested JSON structures

### Template Functions (Common + Standard-Specific)

#### Common Functions (Available to All Templates)
```go
// Date/Time formatting
formatDate      // Convert ISO8601 to standard format
formatTime      // Time formatting
formatDateTime  // Combined date/time formatting

// String manipulation
upper, lower    // Case conversion
truncate        // String truncation with max length
padLeft, padRight // String padding
clean           // Remove special characters
default         // Default values for empty fields

// General utilities
isEmpty         // Check if field is empty/null
notEmpty        // Opposite of isEmpty
contains        // String contains check
replace         // String replacement
```

#### Standard-Specific Functions (Loaded Per Template)
```go
// NIBRS-specific functions (only in nibrs.xml.tmpl)
nibrsOffenseCode     // Convert internal codes to NIBRS offense codes
nibrsLocationCode    // Map locations to NIBRS location codes
nibrsPropertyType    // Property type mappings
nibrsRelationship    // Relationship code mappings
nibrsValidateIncident // NIBRS-specific validation rules

// Clery-specific functions (only in clery.json.tmpl)
cleryLocationCode    // Campus location mappings
cleryOffenseCategory // Clery offense categorization  
cleryGeographyCode   // Geographic area codes
cleryValidateReport  // Clery compliance validation

// UCR-specific functions (only in ucr.csv.tmpl)
ucrOffenseClass      // UCR offense classification
ucrClearanceCode     // UCR clearance mappings
ucrJurisdictionCode  // Jurisdiction code mappings
```

---

## Security Features

The ETL system implements defense-in-depth security with multiple layers of protection.

## Secure Template System - Developer Deep Dive

### Core Security Architecture

**Secure template system: validation + sanitization + compile-time registration.**

```go
// Core types with security features
type TemplateEngine struct {
    templates       map[string]*template.Template // Compiled templates cache
    functionReg     *FunctionRegistry            // Compile-time function registry
    codeLoader      *ValidatedCodeTableLoader    // Schema-validated JSON loader
    cache           *SecureTemplateCache         // Secure template cache
    validator       *validator.Validate          // Input validation
    sanitizer       *InputSanitizer             // Input sanitization
    schemaValidator *SchemaValidator             // Configuration schema validation
    mutex           sync.RWMutex                // Thread-safe operations
}

type FunctionRegistry struct {
    common       template.FuncMap  // Common functions (compile-time registered)
    standards    map[string]template.FuncMap // Per-standard functions (compile-time registered)
    allowedFuncs map[string]bool   // Security: whitelist of allowed functions
}

type SecureTemplateCache struct {
    compiled     map[string]*template.Template // Compiled templates
    mtx          sync.RWMutex                  // Thread-safe access
    ttl          time.Duration                 // Cache TTL
    maxSize      int64                         // Maximum cache size
    sizeTracking map[string]int64              // Track template sizes
}
```

### Function Registration System

**Compile-time function registration for security and reliability:**

```go
// 1. Common functions - available to ALL templates (registered at compile-time)
func (r *FunctionRegistry) RegisterCommonFunctions() {
    r.common = template.FuncMap{
        "formatDate": func(dateStr string) string {
            // Input sanitization
            sanitized := html.EscapeString(strings.TrimSpace(dateStr))
            t, err := time.Parse(time.RFC3339, sanitized)
            if err != nil {
                return "INVALID_DATE"
            }
            return t.Format("01/02/2006")
        },
        "upper": func(s string) string {
            return strings.ToUpper(html.EscapeString(s))
        },
        "lower": func(s string) string {
            return strings.ToLower(html.EscapeString(s))
        },
        "isEmpty": func(s string) bool { 
            return strings.TrimSpace(s) == "" 
        },
        "default": func(def, val string) string {
            cleanVal := html.EscapeString(strings.TrimSpace(val))
            if cleanVal == "" { 
                return html.EscapeString(def) 
            }
            return cleanVal
        },
    }
}

// 2. Standard-specific functions - registered at compile-time (NO runtime loading)
func init() {
    // Register all standard functions at startup
    RegisterNIBRSFunctions()
    RegisterCleryFunctions()
    RegisterUCRFunctions()
}

func RegisterNIBRSFunctions() {
    nibrsFunc := template.FuncMap{
        "nibrsOffenseCode": func(code string) string {
            // Input sanitization
            sanitized := html.EscapeString(strings.TrimSpace(code))
            // Business logic: convert internal offense codes to NIBRS codes
            mapping := getValidatedCodeTable("nibrs_offense_codes")
            if nibrsCode, exists := mapping[sanitized]; exists {
                return nibrsCode
            }
            log.Warnf("Unknown NIBRS offense code: %s", sanitized)
            return "90Z" // NIBRS standard unknown code
        },
        "nibrsLocationCode": func(location string) string {
            sanitized := html.EscapeString(strings.TrimSpace(location))
            mapping := getValidatedCodeTable("nibrs_location_codes") 
            if code, exists := mapping[sanitized]; exists {
                return code
            }
            return "99" // NIBRS unknown location
        },
    }
    GetFunctionRegistry().RegisterStandardFunctions("nibrs", nibrsFunc)
}
```

### Secure Data Flow & Binding

**Input sanitization and secure data processing:**

```go
// Input: Raw report data (JSON-like structure) with validation
type ReportData struct {
    Reports []Report `json:"reports" validate:"required,dive"`
}

type Report struct {
    ID           string    `json:"id" validate:"required,max=100,alphanum"`
    IncidentTime string    `json:"incident_time" validate:"required,datetime=2006-01-02T15:04:05Z07:00"`
    Offenses     []Offense `json:"offenses" validate:"required,dive"`
    Location     string    `json:"location" validate:"max=200"`
    // ... other fields with validation tags
}

// Processing pipeline with input sanitization
func (e *TemplateEngine) Transform(standardName string, data ReportData) ([]byte, error) {
    // 1. Validate input data structure
    if err := e.validator.Struct(data); err != nil {
        return nil, fmt.Errorf("input validation failed: %w", err)
    }
    
    // 2. Sanitize input data (prevent XSS/injection)
    sanitizedData := e.sanitizeReportData(data)
    
    // 3. Get compiled template (from cache or compile)
    tmpl, err := e.getTemplate(standardName)
    if err != nil {
        return nil, err
    }
    
    // 4. Execute template with sanitized data
    var buf bytes.Buffer
    err = tmpl.Execute(&buf, sanitizedData)
    if err != nil {
        return nil, fmt.Errorf("template execution failed: %w", err)
    }
    
    // 5. Validate output format
    output := buf.Bytes()
    if err := e.validateOutput(standardName, output); err != nil {
        return nil, fmt.Errorf("output validation failed: %w", err)
    }
    
    return output, nil
}

// Sanitize all string fields in report data
func (e *TemplateEngine) sanitizeReportData(data ReportData) ReportData {
    sanitized := ReportData{Reports: make([]Report, len(data.Reports))}
    
    for i, report := range data.Reports {
        sanitized.Reports[i] = Report{
            ID:           html.EscapeString(strings.TrimSpace(report.ID)),
            IncidentTime: html.EscapeString(strings.TrimSpace(report.IncidentTime)),
            Location:     html.EscapeString(strings.TrimSpace(report.Location)),
        }
        
        // Sanitize nested structures
        for j, offense := range report.Offenses {
            sanitized.Reports[i].Offenses[j] = sanitizeOffense(offense)
        }
    }
    return sanitized
}
```

### Template Compilation & Caching

**Secure template compilation with validation:**

```go
func (e *TemplateEngine) getTemplate(standardName string) (*template.Template, error) {
    // Check cache first
    if tmpl, exists := e.templates[standardName]; exists {
        return tmpl, nil
    }
    
    // Compile and validate template
    tmpl, err := e.compileAndValidateTemplate(standardName)
    if err != nil {
        return nil, fmt.Errorf("template compilation failed for %s: %w", standardName, err)
    }
    
    // Cache compiled template
    e.templates[standardName] = tmpl
    return tmpl, nil
}

func (e *TemplateEngine) compileAndValidateTemplate(standardName string) (*template.Template, error) {
    // 1. Validate standard name (prevent directory traversal)
    if !isValidStandardName(standardName) {
        return nil, fmt.Errorf("invalid standard name: %s", standardName)
    }
    
    // 2. Build function map (compile-time registered functions only)
    funcMap := make(template.FuncMap)
    
    // Add common functions (pre-registered)
    for k, v := range e.functionReg.common {
        funcMap[k] = v
    }
    
    // Add standard-specific functions (pre-registered at startup)
    if standardFuncs, exists := e.functionReg.standards[standardName]; exists {
        for k, v := range standardFuncs {
            funcMap[k] = v
        }
    }
    
    // 3. Load and validate code tables with schema validation
    codeTables, err := e.codeLoader.LoadValidatedStandardCodes(standardName)
    if err != nil {
        return nil, fmt.Errorf("code table validation failed: %w", err)
    }
    for tableName, codes := range codeTables {
        funcMap[tableName] = createSanitizedLookupFunc(codes)
    }
    
    // 4. Create and compile template with syntax validation
    templatePath := fmt.Sprintf("templates/%s/%s.tmpl", standardName, standardName)
    if !fileExists(templatePath) {
        return nil, fmt.Errorf("template file not found: %s", templatePath)
    }
    
    tmpl := template.New(standardName).Funcs(funcMap)
    
    // Parse template with syntax validation
    parsedTmpl, err := tmpl.ParseFiles(templatePath)
    if err != nil {
        return nil, fmt.Errorf("template syntax error: %w", err)
    }
    
    // 5. Validate template with sample data during startup
    if err := e.validateTemplateExecution(parsedTmpl, standardName); err != nil {
        return nil, fmt.Errorf("template validation failed: %w", err)
    }
    
    return parsedTmpl, nil
}

// Validate template can execute without errors
func (e *TemplateEngine) validateTemplateExecution(tmpl *template.Template, standardName string) error {
    sampleData := getSampleDataForStandard(standardName)
    var buf bytes.Buffer
    
    // Test execution with sample data
    err := tmpl.Execute(&buf, sampleData)
    if err != nil {
        return fmt.Errorf("template execution test failed: %w", err)
    }
    
    // Validate output format (XML, JSON, CSV)
    output := buf.String()
    return validateOutputFormat(standardName, output)
}
```

### Supporting Different Business Logic

**Three ways to handle logic:**

#### 1. **Simple Logic in Templates** (conditionals, loops)
```xml
<!-- nibrs.xml.tmpl -->
{{range .Reports}}
<Incident>
    <IncidentNumber>{{.ID}}</IncidentNumber>
    {{if .ClearanceIndicator}}
    <ClearanceCode>{{.ClearanceIndicator}}</ClearanceCode>
    {{end}}
    
    {{range $i, $offense := .Offenses}}
    <Offense sequence="{{add $i 1}}">
        <OffenseCode>{{$offense.Code | nibrsOffenseCode}}</OffenseCode>
    </Offense>
    {{end}}
</Incident>
{{end}}
```

#### 2. **Complex Logic in Functions** (code mapping, validation)
```go
// Compile-time registered functions (in main codebase)
func nibrsOffenseCode(internalCode string) string {
    sanitized := html.EscapeString(strings.TrimSpace(internalCode))
    switch {
    case strings.HasPrefix(sanitized, "ASSAULT"):
        if strings.Contains(sanitized, "AGGRAVATED") {
            return "13A" // NIBRS aggravated assault
        }
        return "13B" // NIBRS simple assault
    case strings.HasPrefix(sanitized, "BURGLARY"):
        return "220" // NIBRS burglary
    default:
        return "90Z" // NIBRS other
    }
}

func nibrsValidateIncident(report Report) bool {
    return report.ID != "" && report.IncidentTime != "" && len(report.Offenses) > 0
}
```

#### 3. **Pre-processing in Go** (data transformation before templates)
```go
// For very complex logic, transform data before template
func preprocessNIBRSData(reports []Report) NIBRSData {
    var nibrsData NIBRSData
    for _, report := range reports {
        nibrsIncident := NIBRSIncident{
            ID: sanitize(report.ID),
            Date: convertToNIBRSDate(report.IncidentTime),
            Offenses: groupOffensesByType(report.Offenses),
        }
        nibrsData.Incidents = append(nibrsData.Incidents, nibrsIncident)
    }
    return nibrsData
}
```

### Secure Discovery Implementation 

**Template and code discovery with validation:**

```go
func (e *TemplateEngine) discoverStandard(standardName string) error {
    // Validate standard name (prevent directory traversal)
    if !isValidStandardName(standardName) {
        return fmt.Errorf("invalid standard name: %s", standardName)
    }
    
    basePath := filepath.Join("templates", standardName)
    
    // 1. Discover and validate template file
    templateFile := filepath.Join(basePath, standardName+".tmpl")
    if !fileExists(templateFile) {
        return fmt.Errorf("template not found: %s", templateFile)
    }
    
    // Validate template syntax before registration
    if err := e.validateTemplateFile(templateFile); err != nil {
        return fmt.Errorf("template validation failed for %s: %w", templateFile, err)
    }
    
    // 2. Functions are compile-time registered (NO runtime loading)
    // Security: All functions registered at startup in init() functions
    // This prevents arbitrary code execution from template files
    
    // 3. Discover and validate code tables with schema validation
    codesFile := filepath.Join(basePath, "codes.json")
    if fileExists(codesFile) {
        codes, err := e.loadAndValidateJSONFile(codesFile, standardName)
        if err != nil {
            return fmt.Errorf("code table validation failed for %s: %w", codesFile, err)
        }
        e.codeLoader.RegisterValidatedCodes(standardName, codes)
    }
    
    return nil
}

// Validate standard name to prevent directory traversal attacks
func isValidStandardName(name string) bool {
    // Only allow alphanumeric characters and underscores
    matched, _ := regexp.MatchString(`^[a-zA-Z0-9_]+$`, name)
    return matched && !strings.Contains(name, "..")
}

// Validate JSON file against schema
func (e *TemplateEngine) loadAndValidateJSONFile(filePath, standardName string) (map[string]interface{}, error) {
    // 1. Read file with size limit
    data, err := readFileWithLimit(filePath, 1024*1024) // 1MB limit
    if err != nil {
        return nil, err
    }
    
    // 2. Parse JSON
    var codes map[string]interface{}
    if err := json.Unmarshal(data, &codes); err != nil {
        return nil, fmt.Errorf("invalid JSON: %w", err)
    }
    
    // 3. Validate against schema
    schemaPath := fmt.Sprintf("schemas/codes_%s.json", standardName)
    if err := validateJSONSchema(codes, schemaPath); err != nil {
        return nil, fmt.Errorf("schema validation failed: %w", err)
    }
    
    return codes, nil
}
```

### Performance Considerations

```go
type TemplateCache struct {
    compiled map[string]*template.Template // Compiled templates
    mtx      sync.RWMutex                  // Thread-safe access
    ttl      time.Duration                 // Cache TTL for hot-reload
}

func (c *TemplateCache) Get(key string) (*template.Template, bool) {
    c.mtx.RLock()
    defer c.mtx.RUnlock()
    
    tmpl, exists := c.compiled[key]
    return tmpl, exists
}

// Template hot-reload (DEVELOPMENT ONLY - disabled in production)
func (e *TemplateEngine) watchTemplates() {
    // SECURITY: Only enable in development environment
    if os.Getenv("ENVIRONMENT") == "production" {
        log.Info("Template hot-reload disabled in production")
        return
    }
    
    if os.Getenv("ENABLE_TEMPLATE_RELOAD") != "true" {
        log.Info("Template hot-reload disabled (set ENABLE_TEMPLATE_RELOAD=true to enable)")
        return
    }
    
    watcher, err := fsnotify.NewWatcher()
    if err != nil {
        log.Errorf("Failed to create template watcher: %v", err)
        return
    }
    defer watcher.Close()
    
    err = watcher.Add("templates/")
    if err != nil {
        log.Errorf("Failed to watch templates directory: %v", err)
        return
    }
    
    log.Info("Template hot-reload enabled (development mode)")
    
    for event := range watcher.Events {
        if event.Op&fsnotify.Write == fsnotify.Write {
            // Validate file before clearing cache
            standardName := extractStandardName(event.Name)
            if standardName == "" {
                continue
            }
            
            // Re-validate template syntax before clearing cache
            if err := e.validateTemplateFile(event.Name); err != nil {
                log.Errorf("Template validation failed for %s: %v", event.Name, err)
                continue
            }
            
            // Clear cache for modified template
            e.templateCacheMutex.Lock()
            delete(e.templates, standardName)
            e.templateCacheMutex.Unlock()
            
            log.Infof("Reloaded template: %s", standardName)
        }
    }
}
```

**Key Security & Developer Benefits:**
- **Compile-time Safety**: All functions registered at compile-time (no runtime code execution)
- **Input Sanitization**: All template data sanitized to prevent XSS/injection attacks
- **Schema Validation**: Configuration files validated against JSON schemas
- **Template Syntax Validation**: Template syntax validated at startup before deployment
- **Production Security**: Hot-reload disabled, execution timeouts, size limits
- **Function Isolation**: Standard functions don't pollute global namespace
- **Type Safety**: Go functions provide compile-time checking
- **Performance**: Templates compiled once, cached, reused
- **Testability**: Each function can be unit tested independently
- **Audit Trail**: All template modifications logged and validated

### Function Usage Examples

Same report data, different standard-specific transformations:

```go
// Same input data
report := {
    ID: "RPT-123",
    IncidentTime: "2024-01-15T14:30:00Z",
    OffenseCode: "ASSAULT_SIMPLE",
    LocationCode: "RESIDENCE",
}

// NIBRS template uses NIBRS-specific functions
{{.OffenseCode | nibrsOffenseCode}}    // Returns "13A" (NIBRS code for simple assault)
{{.LocationCode | nibrsLocationCode}}  // Returns "20" (NIBRS residence location code)

// Clery template uses Clery-specific functions  
{{.OffenseCode | cleryOffenseCategory}} // Returns "OFFENSE_AGAINST_PERSONS" (Clery category)
{{.LocationCode | cleryLocationCode}}   // Returns "ON_CAMPUS" (Clery location category)

// UCR template uses UCR-specific functions
{{.OffenseCode | ucrOffenseClass}}      // Returns "ASSAULT" (UCR classification)
{{.LocationCode | ucrJurisdictionCode}} // Returns "MUNICIPAL" (UCR jurisdiction)

// All templates use common functions
{{.IncidentTime | formatDate}}          // Returns "01/15/2024" (common date format)
```

## Configuration

### Agency Configuration with Schema Validation
```yaml
# agencies.yaml - validated against schemas/agencies.json
agencies:
  FBI:
    name: "Federal Bureau of Investigation"                  # validated: required, max_length=100
    type: "FEDERAL"                                         # validated: enum ["FEDERAL", "STATE", "LOCAL"]
    enabled: true                                           # validated: boolean
    supported_formats: ["NIBRS_XML", "UCR_XML"]             # validated: array of known formats
    endpoint: "https://fbi.gov/nibrs/submit"                # validated: https URL, max_length=500
    auth_method: "MTLS"                                     # validated: enum ["MTLS", "BASIC", "BEARER"]
    max_file_size: "10MB"                                   # validated: size format
    timeout: "30s"                                          # validated: duration format
    retry_attempts: 3                                       # validated: integer, min=1, max=5
    template_config:
      nibrs_template: "nibrs.xml.tmpl"                      # validated: file exists
      ucr_template: "ucr.xml.tmpl"                          # validated: file exists
      validation_schema: "schemas/nibrs_4.2.xsd"            # validated: schema file exists
      output_validation: true                               # validated: boolean
```

### Template Configuration with Schema Validation
```yaml
# Configuration with comprehensive validation
templates:
  nibrs:
    template_file: "nibrs/nibrs.xml.tmpl"
    schema_version: "4.2"
    validation_schema: "schemas/nibrs_4.2.xsd"              # XML Schema validation
    code_table_schema: "schemas/codes_nibrs.json"           # JSON Schema for codes
    template_syntax_check: true                             # Validate syntax at startup
    required_fields: ["incident_number", "incident_date", "offense_code"]
    max_template_size: "1MB"                               # Security: limit template size
    cache_enabled: true
    hot_reload_enabled: false                              # Disabled in production
    input_sanitization: true                               # Enable input sanitization
    output_validation: true                                # Validate generated output
    # Functions are compile-time registered (no runtime loading)
    code_tables: "templates/nibrs/codes.json"              # JSON codes with schema validation
    
  clery:
    template_file: "clery/clery.json.tmpl"
    schema_version: "2.0"
    validation_schema: "schemas/clery_schema.json"          # JSON Schema validation
    code_table_schema: "schemas/codes_clery.json"           # JSON Schema for codes
    template_syntax_check: true                             # Validate syntax at startup
    required_fields: ["incident_id", "incident_date", "location_code"]
    max_template_size: "1MB"                               # Security: limit template size
    cache_enabled: true
    hot_reload_enabled: false                              # Disabled in production
    input_sanitization: true                               # Enable input sanitization
    output_validation: true                                # Validate generated output
    code_tables: "templates/clery/codes.json"              # JSON codes with schema validation
      
  ucr:
    template_file: "ucr/ucr.csv.tmpl"
    schema_version: "1.0"
    validation_rules: "schemas/ucr_validation.yaml"        # YAML validation rules
    code_table_schema: "schemas/codes_ucr.json"            # JSON Schema for codes
    template_syntax_check: true                            # Validate syntax at startup
    required_fields: ["incident_id", "date", "offense_class"]
    max_template_size: "1MB"                              # Security: limit template size
    cache_enabled: true
    hot_reload_enabled: false                             # Disabled in production
    input_sanitization: true                              # Enable input sanitization
    output_validation: true                               # Validate generated output
    code_tables: "templates/ucr/codes.json"               # JSON codes with schema validation

# Schema validation configuration
schema_validation:
  enabled: true
  schema_directory: "schemas/"
  fail_on_validation_error: true
  cache_schemas: true
  
# Security configuration
security:
  enable_template_hot_reload: false                       # Production: always false
  max_template_execution_time: "30s"                      # Prevent infinite loops
  input_sanitization_enabled: true                        # Always sanitize inputs
  allowed_template_functions: ["formatDate", "upper", "lower", "isEmpty", "default"]
  template_size_limit: "2MB"                             # Global template size limit
```

## Error Handling & Security

The system provides detailed error information with security considerations:

- **Error Codes** - Standardized error classification (no sensitive data exposure)
- **Field-level Errors** - Specific field validation failures (sanitized error messages)
- **Report-level Errors** - Issues with individual reports (logged securely)
- **Job-level Errors** - Pipeline or system errors (stack traces filtered)
- **Template Errors** - Syntax validation and execution errors (safe error reporting)
- **Schema Validation Errors** - Configuration validation failures (detailed but secure)
- **Security Errors** - Input sanitization failures, unauthorized access attempts
- **Timestamps** - When errors occurred for debugging (UTC timezone)
- **Error Sanitization** - All error messages sanitized to prevent information disclosure

## Monitoring & Statistics

- Job success/failure rates
- Processing performance metrics
- Per-agency submission statistics
- Content size and volume tracking
- Historical trend analysis

## Development & Security

### Schema Files Structure

**All configuration files must have corresponding JSON schemas:**

```bash
schemas/
├── agencies.json              # Schema for agencies.yaml
├── templates.json             # Schema for templates.yaml
├── pipeline.json              # Schema for pipeline.yaml
├── codes_nibrs.json           # Schema for templates/nibrs/codes.json
├── codes_clery.json           # Schema for templates/clery/codes.json
├── codes_ucr.json             # Schema for templates/ucr/codes.json
├── nibrs_4.2.xsd             # XML Schema for NIBRS output validation
├── clery_schema.json         # JSON Schema for Clery output validation
└── ucr_validation.yaml       # YAML validation rules for UCR output
```

### Schema Validation Implementation

```go
// Schema validation at startup
func (s *SchemaValidator) ValidateAllConfigurations() error {
    configs := map[string]string{
        "agencies.yaml":   "schemas/agencies.json",
        "templates.yaml":  "schemas/templates.json",
        "pipeline.yaml":   "schemas/pipeline.json",
    }
    
    for configFile, schemaFile := range configs {
        if err := s.ValidateYAMLAgainstSchema(configFile, schemaFile); err != nil {
            return fmt.Errorf("configuration validation failed for %s: %w", configFile, err)
        }
    }
    
    // Validate code table schemas
    return s.ValidateCodeTableSchemas()
}

// Validate code tables against their schemas
func (s *SchemaValidator) ValidateCodeTableSchemas() error {
    standards := []string{"nibrs", "clery", "ucr"}
    
    for _, standard := range standards {
        codesFile := fmt.Sprintf("templates/%s/codes.json", standard)
        schemaFile := fmt.Sprintf("schemas/codes_%s.json", standard)
        
        if err := s.ValidateJSONAgainstSchema(codesFile, schemaFile); err != nil {
            return fmt.Errorf("code table validation failed for %s: %w", standard, err)
        }
    }
    
    return nil
}
```

### Adding a New Data Standard (Secure Self-Contained Approach)

**One directory per standard with security validation:**

1. **Create Standard Directory**: `mkdir templates/my_standard/`
2. **Add Required Files with Validation**:
   ```bash
   templates/my_standard/
   ├── my_standard.xml.tmpl     # Template file (XML/JSON/CSV) - syntax validated
   └── codes.json               # Code mappings - schema validated
   
   schemas/
   ├── codes_my_standard.json   # JSON schema for code validation
   └── my_standard_output.xsd   # Output format schema (XML/JSON)
   ```
3. **Compile-time Function Registration**: Add functions in Go code (NOT in separate files)
   ```go
   // Add to main codebase - NO runtime loading
   func init() {
       RegisterMyStandardFunctions()
   }
   
   func RegisterMyStandardFunctions() {
       funcs := template.FuncMap{
           "myStandardCode": func(code string) string {
               return html.EscapeString(lookupValidatedCode("my_standard", code))
           },
       }
       GetFunctionRegistry().RegisterStandardFunctions("my_standard", funcs)
   }
   ```
4. **Template Configuration with Security**: Add comprehensive config with validation
5. **Schema Creation**: Create JSON schemas for codes and output validation
6. **Update Proto**: Add output format enum to `etl.proto`
7. **Security Testing**: Test with malicious inputs, validate all outputs
8. **Integration Testing**: Use `TestTransformation` API with full validation

**Example - Adding "NIEM" Standard (Secure):**
```bash
# 1. Create directory structure
mkdir -p templates/niem/ schemas/

# 2. Create schema-validated template
cat > templates/niem/niem.xml.tmpl << 'EOF'
<?xml version="1.0"?>
<NIEMDocument>
{{range .Reports}}
  <Incident>
    <ID>{{.ID | sanitize}}</ID>
    <Date>{{.IncidentTime | formatDate}}</Date>
    <OffenseCode>{{.OffenseCode | niemOffenseCode}}</OffenseCode>
  </Incident>
{{end}}
</NIEMDocument>
EOF

# 3. Create schema-validated codes
cat > templates/niem/codes.json << 'EOF'
{
  "$schema": "../../../schemas/codes_niem.json",
  "version": "1.0",
  "standard": "NIEM",
  "niem_offenses": {
    "ASSAULT_SIMPLE": "NIEM-ASSAULT-001",
    "BURGLARY": "NIEM-BURGLARY-001"
  }
}
EOF

# 4. Create JSON schema for codes validation
cat > schemas/codes_niem.json << 'EOF'
{
  "$schema": "http://json-schema.org/draft-07/schema#",
  "type": "object",
  "required": ["version", "standard", "niem_offenses"],
  "properties": {
    "version": {
      "type": "string",
      "pattern": "^[0-9]+\\.[0-9]+$"
    },
    "standard": {
      "type": "string",
      "enum": ["NIEM"]
    },
    "niem_offenses": {
      "type": "object",
      "patternProperties": {
        "^[A-Z_]+$": {
          "type": "string",
          "pattern": "^NIEM-[A-Z]+-[0-9]+$"
        }
      }
    }
  }
}
EOF

# 5. Create output validation schema
cat > schemas/niem_output.xsd << 'EOF'
<?xml version="1.0"?>
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:element name="NIEMDocument">
    <xs:complexType>
      <xs:sequence>
        <xs:element name="Incident" maxOccurs="unbounded">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ID" type="xs:string"/>
              <xs:element name="Date" type="xs:date"/>
              <xs:element name="OffenseCode" type="xs:string"/>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>
EOF

# 6. Add compile-time function registration (NO separate functions.go file)
# Add to main Go codebase:
echo 'func init() { RegisterNIEMFunctions() }' >> main_functions.go
```

**Security validated!** All schemas validate at startup.

## 🎯 **Architecture Benefits**

### **Clear Separation of Concerns**

| Component | Responsibility | Location |
|-----------|---------------|----------|
| **Transformation Engine** | Template loading, rendering, caching | `transformation/` |
| **Data Standards** | Template + functions + codes for each standard | `templates/[standard]/` |
| **Common Functions** | Shared utilities (dates, strings, validation) | `templates/common/` |
| **Loading/Submission** | Agency-specific submission logic | `loading/` |

### **Self-Contained Standards**
```
templates/
├── nibrs/          # ✅ Everything NIBRS-related in one place
│   ├── nibrs.xml.tmpl      # Template (syntax validated)
│   └── codes.json          # Code mappings (schema validated)
├── clery/          # ✅ Everything Clery-related in one place
│   ├── clery.json.tmpl     # Template (syntax validated)
│   └── codes.json          # Code mappings (schema validated)
└── common/         # ✅ Shared utilities only
    └── validation.go       # Common validation functions

schemas/            # ✅ Schema validation files
├── codes_nibrs.json        # NIBRS codes schema
├── codes_clery.json        # Clery codes schema
└── agencies.json           # Agency config schema
```

### **Easy Mental Model**
- **Need to modify NIBRS?** → Go to `templates/nibrs/`
- **Need to add Clery codes?** → Go to `templates/clery/codes.json`
- **Need to add new standard?** → Create `templates/new_standard/` directory
- **Need common function?** → Go to `templates/common/`

### **Template Development Workflow**

```bash
# 1. Create template file
touch services/workflow/internal/etl/templates/my_standard.xml.tmpl

# 2. Test template with sample data
grpcurl -d '{"report_id":"test-123","output_format":"MY_STANDARD_XML"}' \
  localhost:8080 hero.etl.v1.ETLService/TestTransformation

# 3. Preview generated XML
grpcurl -d '{"job_id":"job-123"}' \
  localhost:8080 hero.etl.v1.ETLService/DownloadJobContent
```

### Adding a New Agency

1. Create loading strategy in `loading/strategies/`
2. Configure transport client in `loading/clients/`
3. Add agency configuration with template mappings
4. Update supported formats and template references

## Testing

### Template Testing
- **Unit Tests**: Test individual template rendering with sample data for each format
- **Format Validation**: Verify generated output (XML against XSD, JSON against schema, CSV structure)
- **Function Tests**: Test custom template functions (date formatting, lookups, code mappings)
- **Integration Tests**: Complete pipeline with real report data for each standard
- **Mock Agencies**: Test submissions to mock agency endpoints
- **Sample Data Fixtures**: JSON fixtures for each data standard template

### Security Testing Templates

```go
// Test XML template with security validation (NIBRS)
func TestNIBRSXMLTemplateSecure(t *testing.T) {
    templatePath := "templates/nibrs.xml.tmpl"
    
    // Test with clean data
    cleanData := loadSampleReportData()
    output, err := renderSecureTemplate(templatePath, cleanData)
    assert.NoError(t, err)
    assert.Contains(t, output, "<NIBRSSubmission")
    
    // Validate against XSD
    err = validateXML(output, "schemas/nibrs_4.2.xsd")
    assert.NoError(t, err)
    
    // Security test: malicious input sanitization
    maliciousData := loadMaliciousReportData() // Contains XSS, SQL injection attempts
    output, err = renderSecureTemplate(templatePath, maliciousData)
    assert.NoError(t, err)
    assert.NotContains(t, output, "<script>")        // XSS prevention
    assert.NotContains(t, output, "'; DROP TABLE")   // SQL injection prevention
    assert.NotContains(t, output, "{{.}}")          // Template injection prevention
}

// Test JSON template with schema validation (Clery)
func TestCleryJSONTemplateSecure(t *testing.T) {
    templatePath := "templates/clery.json.tmpl"
    sampleData := loadSampleReportData()
    
    output, err := renderSecureTemplate(templatePath, sampleData)
    assert.NoError(t, err)
    
    // Validate JSON structure against schema
    var cleryData map[string]interface{}
    err = json.Unmarshal([]byte(output), &cleryData)
    assert.NoError(t, err)
    
    // Schema validation
    err = validateJSONSchema(cleryData, "schemas/clery_schema.json")
    assert.NoError(t, err)
    
    // Security: ensure no code injection in JSON
    assert.NotContains(t, output, "function(")
    assert.NotContains(t, output, "eval(")
}

// Test CSV template with input validation (UCR)
func TestUCRCSVTemplateSecure(t *testing.T) {
    templatePath := "templates/ucr.csv.tmpl"
    
    // Test with clean data
    cleanData := loadSampleReportData()
    output, err := renderSecureTemplate(templatePath, cleanData)
    assert.NoError(t, err)
    assert.Contains(t, output, "IncidentID,Date,OffenseCode")
    
    // Validate CSV structure
    lines := strings.Split(output, "\n")
    assert.GreaterOrEqual(t, len(lines), 2) // Header + at least one data row
    
    // Security: CSV injection prevention
    maliciousData := loadCSVInjectionData()
    output, err = renderSecureTemplate(templatePath, maliciousData)
    assert.NoError(t, err)
    
    // Ensure no CSV injection formulas
    assert.NotContains(t, output, "=cmd|")
    assert.NotContains(t, output, "+cmd|")
    assert.NotContains(t, output, "-cmd|")
    assert.NotContains(t, output, "@cmd|")
}

// Test template syntax validation
func TestTemplateSyntaxValidation(t *testing.T) {
    tests := []struct {
        name     string
        template string
        isValid  bool
    }{
        {"Valid template", "{{.ID}}", true},
        {"Invalid syntax", "{{.ID", false},
        {"Nested template injection", "{{{{.ID}}}}", false},
        {"Function call", "{{.ID | upper}}", true},
        {"Invalid function", "{{.ID | exec}}", false}, // exec not in whitelist
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            err := validateTemplateSyntax(tt.template)
            if tt.isValid {
                assert.NoError(t, err)
            } else {
                assert.Error(t, err)
            }
        })
    }
}
```

## Template Benefits

### Why Templates As Strategy Implementation

- **🎯 Strategy Pattern**: Templates ARE the strategy - each template implements transformation for a data standard
- **🎨 Format Flexibility**: XML, JSON, CSV, or any text-based format depending on agency requirements
- **👥 Accessibility**: Non-developers can modify transformation logic directly in templates
- **🔧 Maintainability**: Changes don't require code compilation/deployment
- **🧪 Live Testing**: Instant template testing with `TestTransformation` API for any format
- **📋 Compliance**: Templates can be reviewed by compliance teams (XML, JSON structures visible)
- **🔄 Agility**: Rapid adaptation to changing agency requirements and formats
- **📖 Readability**: Output structure is self-documenting and intuitive

### Template vs Traditional Code Strategy Comparison

| Feature | Template Strategy | Traditional Code Strategy |
|---------|-------------------|--------------------------|
| **Implementation** | Template files (.tmpl) | Go code files (.go) |
| **Modification** | Edit template file | Modify Go code |
| **Output Format** | Any (XML, JSON, CSV) | Hardcoded in Go |
| **Testing** | Instant preview | Compile + test |
| **Compliance Review** | Output format visible | Code review needed |
| **Field Mapping** | Visual in template | Hidden in Go logic |
| **Deployment** | File replacement | Full app deployment |
| **Learning Curve** | Template syntax + format | Go programming |
| **Strategy Pattern** | Template = Strategy | Code class = Strategy |
```
