package test

import (
	"context"
	"net/http"
	"os"
	"strings"
	"testing"
	"time"

	assets "proto/hero/assets/v2"
	assetConnect "proto/hero/assets/v2/assetsconnect"
	entity "proto/hero/entity/v1"
	entityConnect "proto/hero/entity/v1/entityconnect"
	reports "proto/hero/reports/v2"
	reportsConnect "proto/hero/reports/v2/reportsconnect"
	situations "proto/hero/situations/v2"
	situationsConnect "proto/hero/situations/v2/situationsconnect"

	"connectrpc.com/connect"
	"google.golang.org/protobuf/types/known/structpb"
)

func TestGenerateFullyHydratedReport(t *testing.T) {
	httpClient := http.DefaultClient
	httpClient.Timeout = 30 * time.Second
	AddAuthHeader(httpClient)

	ctx := context.Background()

	reportsClient := reportsConnect.NewReportServiceClient(httpClient, ServiceBaseURL)
	assetsClient := assetConnect.NewAssetRegistryServiceClient(httpClient, ServiceBaseURL)
	situationsClient := situationsConnect.NewSituationServiceClient(httpClient, ServiceBaseURL)
	entityClient := entityConnect.NewEntityServiceClient(httpClient, ServiceBaseURL)

	var createdAssetIDs []string
	var createdSituationIDs []string
	var createdEntityIDs []string
	var createdReportIDs []string

	// Create assets with more detailed information and optional fields populated
	authorAsset := &assets.CreateAssetRequest{
		Asset: &assets.Asset{
			Type:               assets.AssetType_ASSET_TYPE_TEST,
			Status:             assets.AssetStatus_ASSET_STATUS_BUSY,
			Name:               "Officer Maria Rodriguez",
			OrgId:              1,
			ContactEmail:       "<EMAIL>",
			ContactNo:          "(*************",
			Latitude:           40.7128,
			Longitude:          -74.0060,
			LocationUpdateTime: "2025-07-07T20:20:00Z",                                                                                                                                                                        // Populate the optional location update time
			AdditionalInfoJson: `{"unit":"Unit 23","shift":"Day Shift","experience":"5 years","specialTraining":["Crisis Negotiation","Firearms Instructor"],"currentAssignment":"Patrol Division","availability":"On Duty"}`, // Add additional structured info as JSON string
		},
	}
	authorResp, err := assetsClient.CreateAsset(ctx, connect.NewRequest(authorAsset))
	if err != nil {
		t.Fatalf("Failed to create author asset: %v", err)
	}
	authorID := authorResp.Msg.Asset.Id
	createdAssetIDs = append(createdAssetIDs, authorID)

	watcherAsset := &assets.CreateAssetRequest{
		Asset: &assets.Asset{
			Type:               assets.AssetType_ASSET_TYPE_TEST,
			Status:             assets.AssetStatus_ASSET_STATUS_AVAILABLE,
			Name:               "Supervisor James Carter",
			OrgId:              1,
			ContactEmail:       "<EMAIL>",
			ContactNo:          "(555) 333-4444",
			Latitude:           40.7580,
			Longitude:          -73.9855,
			LocationUpdateTime: "2025-07-07T20:25:00Z",                                                                                                                                                                                                                                                                                                // Populate the optional location update time
			AdditionalInfoJson: `{"unit":"Supervisor Unit","shift":"Day Shift Supervisor","experience":"15 years","specialTraining":["Leadership Development","Administrative Management","Crisis Command"],"currentAssignment":"Central Division Supervisor","availability":"Available","authority":"Can approve overtime and emergency resources"}`, // Add additional structured info as JSON string
		},
	}
	watcherResp, err := assetsClient.CreateAsset(ctx, connect.NewRequest(watcherAsset))
	if err != nil {
		t.Fatalf("Failed to create watcher asset: %v", err)
	}
	watcherID := watcherResp.Msg.Asset.Id
	createdAssetIDs = append(createdAssetIDs, watcherID)

	// Create situation with enhanced details and all optional fields populated
	situationReq := &situations.CreateSituationRequest{
		Situation: &situations.Situation{
			Title:        "First National Bank Armed Robbery Investigation",
			Description:  "Armed robbery at First National Bank with suspect apprehended after vehicle pursuit. Multiple witnesses, physical evidence recovered including fingerprints and surveillance footage.",
			Type:         situations.SituationType_SITUATION_TYPE_THEFT,
			Status:       situations.SituationStatus_SITUATION_STATUS_ADDRESSING,
			Priority:     1,
			ReporterId:   authorID,
			IncidentTime: "2025-07-03T14:30:00Z",
			// Populate optional fields that were showing as empty
			ContactEmail:       "<EMAIL>",
			ContactNo:          "(*************",
			Address:            "123 Main Street, Metro City, ST 12345",
			Latitude:           40.7589,
			Longitude:          -73.9851,
			ReporterName:       "Officer Maria Rodriguez",
			DueTime:            "2025-07-04T18:00:00Z",
			ResolvedTime:       "", // Will be set when case is resolved
			AdditionalInfoJson: `{"weatherConditions":"Clear, 72°F","visibility":"Excellent","trafficConditions":"Light traffic","emergencyResponse":{"unitsDispatched":4,"responseTime":"3 minutes","ambulanceCalled":false,"fireDepCalled":false},"mediaAttention":true,"publicSafety":{"areaEvacuated":false,"roadsClosed":["Main Street between 1st and 3rd"],"perimeterSet":true,"witnessesSecured":true},"investigationTeam":["Detective Smith - Lead","Officer Rodriguez - First Responder","CSI Team Delta"]}`,
		},
	}

	situationResp, err := situationsClient.CreateSituation(ctx, connect.NewRequest(situationReq))
	if err != nil {
		t.Fatalf("Failed to create situation: %v", err)
	}
	situationID := situationResp.Msg.Situation.Id
	createdSituationIDs = append(createdSituationIDs, situationID)

	// Create entities with comprehensive data
	suspectData, _ := structpb.NewStruct(map[string]interface{}{
		"firstName":           "John",
		"lastName":            "Suspect",
		"middleName":          "Michael",
		"age":                 30,
		"dateOfBirth":         "1994-12-15",
		"height":              "6'0\"",
		"weight":              180,
		"eyeColor":            "Brown",
		"hairColor":           "Black",
		"race":                "Caucasian",
		"gender":              "Male",
		"socialSecurity":      "XXX-XX-1234",
		"driversLicense":      "**********",
		"address":             "456 Oak Street, Metro City, ST 12345",
		"phoneNumber":         "(*************",
		"email":               "<EMAIL>",
		"occupation":          "Unemployed",
		"priorOffenses":       []interface{}{"Petty Theft (2020)", "Disorderly Conduct (2019)"},
		"distinguishingMarks": []interface{}{"Scar on left cheek", "Tattoo on right arm"},
		"emergencyContact": map[string]interface{}{
			"name":         "Mary Suspect",
			"relationship": "Mother",
			"phone":        "(*************",
		},
	})

	suspectReq := &entity.CreateEntityRequest{
		Entity: &entity.Entity{
			EntityType: entity.EntityType_ENTITY_TYPE_PERSON,
			Status:     entity.RecordStatus_RECORD_STATUS_ACTIVE,
			Data:       suspectData,
			Tags:       []string{"suspect", "armed_robbery", "vehicle_theft", "high_priority"},
		},
	}

	suspectResp, err := entityClient.CreateEntity(ctx, connect.NewRequest(suspectReq))
	if err != nil {
		t.Fatalf("Failed to create suspect entity: %v", err)
	}
	suspectID := suspectResp.Msg.Entity.Id
	createdEntityIDs = append(createdEntityIDs, suspectID)

	victimData, _ := structpb.NewStruct(map[string]interface{}{
		"firstName":        "Jane",
		"lastName":         "Victim",
		"middleName":       "Elizabeth",
		"age":              25,
		"dateOfBirth":      "1999-03-22",
		"height":           "5'6\"",
		"weight":           135,
		"eyeColor":         "Blue",
		"hairColor":        "Blonde",
		"race":             "Caucasian",
		"gender":           "Female",
		"address":          "789 Elm Street, Metro City, ST 12345",
		"phoneNumber":      "(*************",
		"email":            "<EMAIL>",
		"occupation":       "Bank Teller",
		"employer":         "First National Bank",
		"workAddress":      "123 Main Street, Metro City, ST 12345",
		"workPhone":        "(*************",
		"injuryStatus":     "Minor psychological trauma",
		"medicalTreatment": "Counseling recommended",
		"emergencyContact": map[string]interface{}{
			"name":         "Robert Victim",
			"relationship": "Father",
			"phone":        "(*************",
		},
	})

	victimReq := &entity.CreateEntityRequest{
		Entity: &entity.Entity{
			EntityType: entity.EntityType_ENTITY_TYPE_PERSON,
			Status:     entity.RecordStatus_RECORD_STATUS_ACTIVE,
			Data:       victimData,
			Tags:       []string{"victim", "bank_employee", "witness", "traumatized"},
		},
	}

	victimResp, err := entityClient.CreateEntity(ctx, connect.NewRequest(victimReq))
	if err != nil {
		t.Fatalf("Failed to create victim entity: %v", err)
	}
	victimID := victimResp.Msg.Entity.Id
	createdEntityIDs = append(createdEntityIDs, victimID)

	// Create witness entity
	witnessData, _ := structpb.NewStruct(map[string]interface{}{
		"firstName":          "David",
		"lastName":           "Wilson",
		"middleName":         "James",
		"age":                32,
		"dateOfBirth":        "1992-05-18",
		"height":             "5'10\"",
		"weight":             170,
		"eyeColor":           "Green",
		"hairColor":          "Brown",
		"race":               "Hispanic",
		"gender":             "Male",
		"address":            "789 Witness Ave, Metro City, ST 12345",
		"phoneNumber":        "(*************",
		"email":              "<EMAIL>",
		"occupation":         "Store Manager",
		"employer":           "Metro Shopping Center",
		"workAddress":        "100 Shopping Plaza, Metro City, ST 12345",
		"workPhone":          "(*************",
		"witnessStatement":   "I saw the suspect enter the bank with a weapon and exit quickly with a bag",
		"witnessReliability": "High - Clear view, good lighting conditions",
		"emergencyContact": map[string]interface{}{
			"name":         "Maria Wilson",
			"relationship": "Spouse",
			"phone":        "(*************",
		},
	})

	witnessReq := &entity.CreateEntityRequest{
		Entity: &entity.Entity{
			EntityType: entity.EntityType_ENTITY_TYPE_PERSON,
			Status:     entity.RecordStatus_RECORD_STATUS_ACTIVE,
			Data:       witnessData,
			Tags:       []string{"witness", "reliable", "clear_view", "cooperative"},
		},
	}

	witnessResp, err := entityClient.CreateEntity(ctx, connect.NewRequest(witnessReq))
	if err != nil {
		t.Fatalf("Failed to create witness entity: %v", err)
	}
	witnessID := witnessResp.Msg.Entity.Id
	createdEntityIDs = append(createdEntityIDs, witnessID)

	// Create officer entity
	officerData, _ := structpb.NewStruct(map[string]interface{}{
		"firstName":       "Detective",
		"lastName":        "Smith",
		"middleName":      "Robert",
		"age":             38,
		"dateOfBirth":     "1986-11-03",
		"badgeNumber":     "12345",
		"department":      "Metro Police Department",
		"rank":            "Detective",
		"yearsService":    10,
		"phoneNumber":     "(*************",
		"email":           "<EMAIL>",
		"specialization":  "Financial Crimes",
		"certification":   []interface{}{"Advanced Criminal Investigation", "Forensic Accounting"},
		"supervisorName":  "Lieutenant Johnson",
		"supervisorBadge": "54321",
		"assignment":      "Central Division - Major Crimes Unit",
		"shift":           "Day Shift (08:00-16:00)",
		"address":         "Metro Police Department, 456 Police Plaza, Metro City, ST 12345",
	})

	officerReq := &entity.CreateEntityRequest{
		Entity: &entity.Entity{
			EntityType: entity.EntityType_ENTITY_TYPE_PERSON,
			Status:     entity.RecordStatus_RECORD_STATUS_ACTIVE,
			Data:       officerData,
			Tags:       []string{"officer", "detective", "financial_crimes", "lead_investigator"},
		},
	}

	officerResp, err := entityClient.CreateEntity(ctx, connect.NewRequest(officerReq))
	if err != nil {
		t.Fatalf("Failed to create officer entity: %v", err)
	}
	officerID := officerResp.Msg.Entity.Id
	createdEntityIDs = append(createdEntityIDs, officerID)

	// Create vehicle entity
	vehicleData, _ := structpb.NewStruct(map[string]interface{}{
		"make":             "Honda",
		"model":            "Civic",
		"year":             2020,
		"licensePlate":     "ABC123",
		"state":            "CA",
		"color":            "Blue",
		"vin":              "1HGBH41JXMN109186",
		"involvement":      "getaway_vehicle",
		"vehicleType":      "passenger_car",
		"bodyStyle":        "sedan",
		"engineSize":       "2.0L",
		"fuelType":         "gasoline",
		"transmission":     "automatic",
		"ownerName":        "Robert Johnson",
		"ownerAddress":     "555 Oak Lane, Metro City, ST 12345",
		"ownerPhone":       "(*************",
		"registrationExp":  "2025-12-31",
		"insuranceCarrier": "Metro Insurance Co.",
		"insurancePolicy":  "INS-987654321",
		"stolenStatus":     "stolen_vehicle",
		"stolenDatetime":   "2025-07-03T13:00:00Z",
		"recoveryStatus":   "recovered",
		"recoveryDatetime": "2025-07-03T14:55:00Z",
		"damageAssessment": "Minor front bumper damage from pursuit",
		"evidenceSeized":   "fingerprints_collected",
	})

	vehicleReq := &entity.CreateEntityRequest{
		Entity: &entity.Entity{
			EntityType: entity.EntityType_ENTITY_TYPE_VEHICLE,
			Status:     entity.RecordStatus_RECORD_STATUS_ACTIVE,
			Data:       vehicleData,
			Tags:       []string{"vehicle", "getaway_car", "stolen", "recovered", "evidence"},
		},
	}

	vehicleResp, err := entityClient.CreateEntity(ctx, connect.NewRequest(vehicleReq))
	if err != nil {
		t.Fatalf("Failed to create vehicle entity: %v", err)
	}
	vehicleID := vehicleResp.Msg.Entity.Id
	createdEntityIDs = append(createdEntityIDs, vehicleID)

	// Create organization entity
	orgData, _ := structpb.NewStruct(map[string]interface{}{
		"name":             "First National Bank",
		"type":             "financial_institution",
		"address":          "123 Main Street, Metro City, ST 12345",
		"phoneNumber":      "(*************",
		"faxNumber":        "(*************",
		"email":            "<EMAIL>",
		"website":          "https://www.firstnationalbank.com",
		"role":             "crime_location",
		"establishedYear":  1985,
		"employeeCount":    25,
		"branchNumber":     "001",
		"routingNumber":    "*********",
		"federalId":        "12-3456789",
		"managerName":      "Sarah Thompson",
		"managerPhone":     "(*************",
		"managerEmail":     "<EMAIL>",
		"securityFeatures": "Security cameras, alarm system, reinforced vault, armed guard",
		"businessHours":    "Monday-Friday 9:00 AM - 5:00 PM, Saturday 9:00 AM - 1:00 PM",
		"lastInspection":   "2025-06-15",
		"insuranceCarrier": "First Security Insurance",
		"insurancePolicy":  "FSI-*********",
	})

	orgReq := &entity.CreateEntityRequest{
		Entity: &entity.Entity{
			EntityType: entity.EntityType_ENTITY_TYPE_ORGANIZATION,
			Status:     entity.RecordStatus_RECORD_STATUS_ACTIVE,
			Data:       orgData,
			Tags:       []string{"organization", "bank", "crime_scene", "financial_institution", "victim_organization"},
		},
	}

	orgResp, err := entityClient.CreateEntity(ctx, connect.NewRequest(orgReq))
	if err != nil {
		t.Fatalf("Failed to create organization entity: %v", err)
	}
	orgID := orgResp.Msg.Entity.Id
	createdEntityIDs = append(createdEntityIDs, orgID)

	// Create comprehensive report with ALL section types
	sections := []*reports.ReportSection{
		{
			Type: reports.SectionType_SECTION_TYPE_NARRATIVE,
			Content: &reports.ReportSection_Narrative{
				Narrative: &reports.NarrativeContent{
					RichText: "On 07/03/2025 at approximately 14:30 hours, I responded to a robbery in progress at First National Bank located at 123 Main Street. Upon arrival, I observed suspect John Suspect fleeing the scene in a blue 2020 Honda Civic license plate ABC123. Victim Jane Victim reported that approximately $5,000 cash was stolen from the bank teller station. Witness David Wilson observed the entire incident and provided a detailed description of the suspect. Physical evidence including fingerprints on the counter and security camera footage was collected. The suspect was apprehended 2 blocks away and the stolen money was recovered. This case involves multiple charges including armed robbery, possession of stolen property, and fleeing from law enforcement.",
				},
			},
		},
		{
			Type: reports.SectionType_SECTION_TYPE_INCIDENT_DETAILS,
			Content: &reports.ReportSection_IncidentDetails{
				IncidentDetails: &reports.IncidentDetailsContent{
					InitialType:                   situations.SituationType_SITUATION_TYPE_THEFT,
					FinalType:                     situations.SituationType_SITUATION_TYPE_THEFT,
					IncidentStartTime:             "2025-07-03T14:30:00Z",
					IncidentEndTime:               "2025-07-03T15:45:00Z",
					ReportedTime:                  "2025-07-03T14:32:00Z",
					IncidentLocationStreetAddress: "123 Main Street",
					IncidentLocationCity:          "City",
					IncidentLocationState:         "ST",
					IncidentLocationZipCode:       "12345",
					IncidentLocationCountry:       "USA",
					Description:                   "Armed robbery at First National Bank with suspect vehicle pursuit and arrest",
					Responders: []*reports.IncidentResponder{
						{
							Id:          "responder-1",
							AssetId:     officerID,
							DisplayName: "Detective Smith",
							Role:        "Lead Investigator",
						},
						{
							Id:          "responder-2",
							AssetId:     authorID,
							DisplayName: "Test Officer",
							Role:        "First Responder",
						},
					},
					ReportingPerson: &reports.ReportingPerson{
						Id:           "reporting-person-1",
						AssetId:      victimID,
						FirstName:    "Jane",
						LastName:     "Victim",
						PhoneNumber:  "(*************",
						ReporterRole: "victim",
					},
					InvolvedAgencies: []*reports.InvolvedAgency{
						{
							Id:                      "agency-1",
							AgencyName:              "Metro Police Department",
							IncidentReferenceNumber: "MPD-2025-001234",
						},
					},
				},
			},
		},
		{
			Type: reports.SectionType_SECTION_TYPE_ENTITY_LIST_PEOPLE,
			Content: &reports.ReportSection_EntityList{
				EntityList: &reports.EntityListContent{
					Title: "People Involved",
					EntityRefs: []*entity.Reference{
						{
							Id:           suspectID,
							Type:         "entity",
							Version:      1,
							DisplayName:  "John Suspect",
							RelationType: "suspect",
						},
						{
							Id:           victimID,
							Type:         "entity",
							Version:      1,
							DisplayName:  "Jane Victim",
							RelationType: "victim",
						},
						{
							Id:           witnessID,
							Type:         "entity",
							Version:      1,
							DisplayName:  "David Wilson",
							RelationType: "witness",
						},
						{
							Id:           officerID,
							Type:         "entity",
							Version:      1,
							DisplayName:  "Detective Smith",
							RelationType: "officer",
						},
					},
				},
			},
		},
		{
			Type: reports.SectionType_SECTION_TYPE_ENTITY_LIST_VEHICLE,
			Content: &reports.ReportSection_EntityList{
				EntityList: &reports.EntityListContent{
					Title: "Vehicles Involved",
					EntityRefs: []*entity.Reference{
						{
							Id:           vehicleID,
							Type:         "entity",
							Version:      1,
							DisplayName:  "2020 Honda Civic (ABC123)",
							RelationType: "getaway_vehicle",
						},
					},
				},
			},
		},
		{
			Type: reports.SectionType_SECTION_TYPE_ENTITY_LIST_ORGANIZATIONS,
			Content: &reports.ReportSection_EntityList{
				EntityList: &reports.EntityListContent{
					Title: "Organizations Involved",
					EntityRefs: []*entity.Reference{
						{
							Id:           orgID,
							Type:         "entity",
							Version:      1,
							DisplayName:  "First National Bank",
							RelationType: "crime_location",
						},
					},
				},
			},
		},
		{
			Type: reports.SectionType_SECTION_TYPE_MEDIA,
			Content: &reports.ReportSection_MediaList{
				MediaList: &reports.MediaContent{
					Title: "Evidence and Media",
					FileRefs: []*reports.FileReference{
						{
							Id:           "file-ref-1",
							FileId:       "dummy-surveillance-footage-123",
							Caption:      "Security camera footage from bank entrance",
							DisplayName:  "entrance_camera_footage.mp4",
							DisplayOrder: 1,
							FileCategory: "incident_video",
						},
						{
							Id:           "file-ref-2",
							FileId:       "dummy-evidence-photo-456",
							Caption:      "Fingerprints collected from teller counter",
							DisplayName:  "fingerprint_evidence.jpg",
							DisplayOrder: 2,
							FileCategory: "incident_photo",
						},
						{
							Id:           "file-ref-3",
							FileId:       "dummy-police-report-789",
							Caption:      "Initial police incident report",
							DisplayName:  "police_incident_report.pdf",
							DisplayOrder: 3,
							FileCategory: "incident_document",
						},
					},
				},
			},
		},
		{
			Type: reports.SectionType_SECTION_TYPE_OFFENSE,
			Content: &reports.ReportSection_OffenseList{
				OffenseList: &reports.OffenseContent{
					Id: "offense-content-1",
					Offenses: []*reports.Offense{
						{
							Id:          "offense-1",
							OffenseType: "Armed Robbery",
							Data: func() *structpb.Struct {
								data, _ := structpb.NewStruct(map[string]interface{}{
									"offenseCode":        "211-PC",
									"offenseDescription": "Armed robbery of financial institution with firearm",
									"offenseLocation":    "123 Main Street, City, ST 12345",
									"offenseDatetime":    "2025-07-03T14:30:00Z",
									"weaponsUsed":        []interface{}{"handgun", "threats"},
									"propertyStolen": map[string]interface{}{
										"description": "US Currency",
										"value":       5000.00,
										"category":    "money",
									},
									"victimInjuries": []interface{}{
										map[string]interface{}{
											"victimId":          victimID,
											"injuryType":        "emotional_trauma",
											"injuryDescription": "Psychological trauma from armed robbery",
											"treatmentRequired": false,
										},
									},
									"suspectActions": []interface{}{"displayed_weapon", "verbal_threats", "fled_scene"},
								})
								return data
							}(),
						},
						{
							Id:          "offense-2",
							OffenseType: "Vehicle Theft",
							Data: func() *structpb.Struct {
								data, _ := structpb.NewStruct(map[string]interface{}{
									"offenseCode":        "487-PC",
									"offenseDescription": "Use of stolen vehicle as getaway car",
									"offenseLocation":    "Various streets during pursuit",
									"offenseDatetime":    "2025-07-03T14:35:00Z",
									"weaponsUsed":        []interface{}{},
									"propertyStolen": map[string]interface{}{
										"description": "2020 Honda Civic",
										"value":       25000.00,
										"category":    "vehicle",
									},
									"suspectActions": []interface{}{"fled_in_vehicle", "reckless_driving"},
								})
								return data
							}(),
						},
					},
				},
			},
		},
		{
			Type: reports.SectionType_SECTION_TYPE_ARREST,
			Content: &reports.ReportSection_ArrestList{
				ArrestList: &reports.ArrestContent{
					Id: "arrest-content-1",
					Arrests: []*reports.Arrest{
						{
							Id:         "arrest-1",
							ArrestType: "on_view_arrest",
							Data: func() *structpb.Struct {
								data, _ := structpb.NewStruct(map[string]interface{}{
									"arresteeId":         suspectID,
									"arrestDatetime":     "2025-07-03T14:50:00Z",
									"arrestLocation":     "Oak Street and 2nd Avenue",
									"arrestingOfficerId": officerID,
									"chargesFiledAt":     "2025-07-03T16:00:00Z",
									"charges": []interface{}{
										map[string]interface{}{
											"chargeCode":        "211-PC",
											"chargeDescription": "Armed Robbery in the First Degree",
											"chargeSeverity":    "felony",
											"bailAmount":        50000.00,
										},
										map[string]interface{}{
											"chargeCode":        "487-PC",
											"chargeDescription": "Grand Theft Auto",
											"chargeSeverity":    "felony",
											"bailAmount":        25000.00,
										},
										map[string]interface{}{
											"chargeCode":        "2800.2-VC",
											"chargeDescription": "Evading Police Officer",
											"chargeSeverity":    "misdemeanor",
											"bailAmount":        5000.00,
										},
									},
									"bookingNumber":      "BK-2025-001234",
									"bookingFacility":    "Metro County Jail",
									"arrestNarrative":    "Suspect was apprehended after a brief vehicle pursuit. Stolen money was recovered from suspect's possession. Suspect was cooperative during arrest and booking process.",
									"evidenceSeized":     []interface{}{"US Currency $5,000", "Replica firearm", "Suspect's clothing"},
									"mirandaRightsGiven": true,
									"mirandaDatetime":    "2025-07-03T14:52:00Z",
								})
								return data
							}(),
						},
					},
				},
			},
		},
	}

	// Convert string slice to interface slice for protobuf compatibility
	keywords := make([]interface{}, 0)
	for _, keyword := range []string{"theft", "robbery", "investigation"} {
		keywords = append(keywords, keyword)
	}

	additionalInfo, err := structpb.NewStruct(map[string]interface{}{
		"caseNumber": "CASE-2025-TEST",
		"priority":   "High",
		"category":   "Criminal",
		"keywords":   keywords,
		"evidence": map[string]interface{}{
			"fingerprints":      true,
			"videoFootage":      true,
			"witnessStatements": 3,
		},
		"location": map[string]interface{}{
			"type":          "financial_institution",
			"securityLevel": "high",
		},
	})
	if err != nil {
		t.Fatalf("Failed to create additional info struct: %v", err)
	}
	t.Logf("Created additional info JSON: %+v", additionalInfo)

	// Create relations between victim and suspect
	relations := []*reports.Relation{
		{
			ObjectA: &reports.ObjectReference{
				ObjectType:  "entity",
				GlobalId:    victimID,
				DisplayName: "Jane Victim",
			},
			ObjectB: &reports.ObjectReference{
				ObjectType:  "entity",
				GlobalId:    suspectID,
				DisplayName: "John Suspect",
			},
			RelationType: "RELATION_TYPE_VICTIM_OFFENDER_ST", // Victim was stranger to offender
			Description:  "Victim-Offender relationship in armed robbery incident",
		},
		{
			ObjectA: &reports.ObjectReference{
				ObjectType:  "entity",
				GlobalId:    witnessID,
				DisplayName: "David Wilson",
			},
			ObjectB: &reports.ObjectReference{
				ObjectType:  "entity",
				GlobalId:    victimID,
				DisplayName: "Jane Victim",
			},
			RelationType: "worked_with", // Custom relation type
			Description:  "Witness knows victim as they work in the same area",
		},
		{
			ObjectA: &reports.ObjectReference{
				ObjectType:  "entity",
				GlobalId:    suspectID,
				DisplayName: "John Suspect",
			},
			ObjectB: &reports.ObjectReference{
				ObjectType:  "entity",
				GlobalId:    vehicleID,
				DisplayName: "2020 Honda Civic",
			},
			RelationType: "used_in_crime", // Custom relation type
			Description:  "Suspect used this vehicle as getaway car",
		},
		{
			ObjectA: &reports.ObjectReference{
				ObjectType:  "entity",
				GlobalId:    orgID,
				DisplayName: "First National Bank",
			},
			ObjectB: &reports.ObjectReference{
				ObjectType:  "entity",
				GlobalId:    victimID,
				DisplayName: "Jane Victim",
			},
			RelationType: "employs", // Custom relation type
			Description:  "Bank employs the victim as a teller",
		},
		{
			ObjectA: &reports.ObjectReference{
				ObjectType:  "asset",
				GlobalId:    officerID,
				DisplayName: "Detective Smith",
			},
			ObjectB: &reports.ObjectReference{
				ObjectType:  "situation",
				GlobalId:    situationID,
				DisplayName: "Armed Robbery Investigation",
			},
			RelationType: "assigned_to", // Using standard relation type from proto comments
			Description:  "Detective assigned as lead investigator",
		},
	}

	reportReq := &reports.CreateReportRequest{
		Report: &reports.Report{
			Title:              "Fully Hydrated Test Report",
			CreatedByAssetId:   authorID,
			AuthorAssetId:      authorID,
			SituationId:        situationID,
			Status:             reports.ReportStatus_REPORT_STATUS_IN_PROGRESS,
			ReportType:         reports.ReportType_REPORT_TYPE_INCIDENT_PRIMARY,
			Sections:           sections,
			Relations:          relations,
			AdditionalInfoJson: additionalInfo,
			WatcherAssetIds:    []string{watcherID},
			CaseId:             "CASE-2025-TEST",
		},
	}

	reportResp, err := reportsClient.CreateReport(ctx, connect.NewRequest(reportReq))
	if err != nil {
		t.Fatalf("Failed to create report: %v", err)
	}
	reportID := reportResp.Msg.Report.Id
	createdReportIDs = append(createdReportIDs, reportID)

	t.Logf("SUCCESS: Created fully hydrated report with ID: %s", reportID)
	t.Logf("Report title: %s", reportResp.Msg.Report.Title)
	t.Logf("Report has %d sections (Narrative, Incident Details, People, Vehicles, Organizations, Media, Offenses, Arrests)", len(reportResp.Msg.Report.Sections))
	t.Logf("Connected to situation: %s", situationID)
	t.Logf("Assets created: author=%s, watcher=%s", authorID, watcherID)
	t.Logf("Entities created: suspect=%s, victim=%s, witness=%s, officer=%s, vehicle=%s, org=%s",
		suspectID, victimID, witnessID, officerID, vehicleID, orgID)
	t.Logf("Total entities connected: %d", len(createdEntityIDs))
	t.Logf("Case details: %s - Priority: High, Category: Criminal", "CASE-2025-TEST")
	t.Logf("Comprehensive test coverage: ALL 6 protobuf content types included")

	// Save IDs to files for cleanup
	saveIDsToFiles(t, createdReportIDs, createdAssetIDs, createdSituationIDs)
}

// saveIDsToFiles saves the created IDs to text files for the cleanup test
func saveIDsToFiles(t *testing.T, reportIDs, assetIDs, situationIDs []string) {
	// Save report IDs
	if len(reportIDs) > 0 {
		existingReports := readExistingIDs("created_report_ids.txt")
		existingReports = append(existingReports, reportIDs...)
		err := os.WriteFile("created_report_ids.txt", []byte(strings.Join(existingReports, "\n")), 0600)
		if err != nil {
			t.Logf("Warning: Failed to save report IDs: %v", err)
		} else {
			t.Logf("Saved %d report IDs to created_report_ids.txt", len(reportIDs))
		}
	}

	// Save asset IDs
	if len(assetIDs) > 0 {
		existingAssets := readExistingIDs("created_asset_ids.txt")
		existingAssets = append(existingAssets, assetIDs...)
		err := os.WriteFile("created_asset_ids.txt", []byte(strings.Join(existingAssets, "\n")), 0600)
		if err != nil {
			t.Logf("Warning: Failed to save asset IDs: %v", err)
		} else {
			t.Logf("Saved %d asset IDs to created_asset_ids.txt", len(assetIDs))
		}
	}

	// Save situation IDs
	if len(situationIDs) > 0 {
		existingSituations := readExistingIDs("created_situation_ids.txt")
		existingSituations = append(existingSituations, situationIDs...)
		err := os.WriteFile("created_situation_ids.txt", []byte(strings.Join(existingSituations, "\n")), 0600)
		if err != nil {
			t.Logf("Warning: Failed to save situation IDs: %v", err)
		} else {
			t.Logf("Saved %d situation IDs to created_situation_ids.txt", len(situationIDs))
		}
	}
}

// readExistingIDs reads existing IDs from a file if it exists
func readExistingIDs(filename string) []string {
	data, err := os.ReadFile(filename)
	if err != nil {
		return []string{} // File doesn't exist or can't be read
	}

	content := strings.TrimSpace(string(data))
	if content == "" {
		return []string{}
	}

	return strings.Split(content, "\n")
}
