# Shared environment variables for services
# Copy this file to services/.env and set the appropriate values. Get the secret from secrets manager under local/services/env

# Twilio Configuration
TWILIO_ACCOUNT_SID="your_twilio_account_sid"
TWILIO_AUTH_TOKEN="your_twilio_auth_token"
TWIML_APP_SID="your_twiml_app_sid"
TWILIO_API_KEY_SID="your_twilio_api_key_sid"
TWILIO_API_KEY_SECRET="your_twilio_api_key_secret"

# Bot Authentication (get from AWS Secrets Manager: bots/bot-basic-auth-lambda-secret/Communications)
BOT_BASIC_AUTH_LAMBDA_SECRET="your_bot_secret"

# Agora Configuration (stale/not used in current implementation, still here for backward compatibility)
AGORA_APP_ID=AGORA_APP_ID
AGORA_APP_CERTIFICATE=AGORA_APP_CERTIFICATE
AGORA_CHAT_APP_ID=AGORA_CHAT_APP_ID
AGORA_CHAT_HOST_URL=AGORA_CHAT_HOST_URL
AGORA_CHAT_APP_NAME=AGORA_CHAT_APP_NAME
AGORA_CHAT_ORG_NAME=AGORA_CHAT_ORG_NAME
