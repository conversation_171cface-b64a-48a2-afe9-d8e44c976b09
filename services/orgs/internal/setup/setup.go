package setup

import (
	cmncontext "common/context"
	"context"
	"database/sql"
	"fmt"
	"log"
	orgRepository "orgs/internal/data"
	"orgs/internal/usecase"
	"os"
	orgs "proto/hero/orgs/v1"
)

const (
	rootAdminEmail = "<EMAIL>"
	rootOrgId      = -1
)

type SetupConfig struct {
	OrgRepo orgRepository.OrgRepository
	DB      *sql.DB
}

func Initialize(cfg SetupConfig) error {
	adminPassword := os.Getenv("ROOT_ADMIN_PASSWORD")
	if adminPassword == "" {
		log.Println("ROOT_ADMIN_PASSWORD not set, skipping root admin creation")
		return nil
	}

	log.Println("Creating meta org and root admin user")

	usecase := usecase.NewOrgUseCase(cfg.DB, cfg.OrgRepo)

	ctx := context.Background()
	// NOTE: This is a hack to get past RLS
	// for this special cold-start bootstrap step
	ctx = context.WithValue(ctx, cmncontext.OrgIdContextKey, int32(rootOrgId))
	ctx = context.WithValue(ctx, cmncontext.OrgIdsContextKey, []int32{int32(rootOrgId)})
	ctx = context.WithValue(ctx, cmncontext.UsernameContextKey, rootAdminEmail)

	// create the meta org
	err := usecase.CreateOrg(ctx, &orgs.Org{
		Id:                 rootOrgId,
		Name:               "Meta Org",
		ServiceType:        orgs.ServiceType_SERVICE_TYPE_DEMO,
		Domains:            []string{"gethero.com"},
		PrimaryPhoneNumber: "+12345678900",
		TwilioNumber:       "+18556976298",
		TwimlAppSid:        "AP367659bb9684ba033a80685ee537ffcc",
		TemplateId:         "cognito-only",
	})
	if err != nil {
		return fmt.Errorf("failed to create meta org: %w", err)
	}

	log.Println("Meta org created successfully")

	// create the root admin user
	_, err = usecase.CreateCognitoUser(ctx, rootOrgId, rootAdminEmail, rootAdminEmail, adminPassword)
	if err != nil {
		return fmt.Errorf("failed to create root admin user: %w", err)
	}

	log.Println("Meta org root admin user created successfully")

	return nil
}
