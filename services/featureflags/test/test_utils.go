package test

import (
	"fmt"
	"net/http"
	"os"
	"strings"
)

const ServiceBaseURL = "http://localhost:9090" // Feature flags service runs on port 9090

// ANSI color codes for console output
const (
	ColorReset  = "\033[0m"
	ColorRed    = "\033[31m"
	ColorGreen  = "\033[32m"
	ColorYellow = "\033[33m"
	ColorBlue   = "\033[34m"
	ColorPurple = "\033[35m"
	ColorCyan   = "\033[36m"
	ColorWhite  = "\033[37m"
	ColorBold   = "\033[1m"
)

// Color utility functions for test output
func ColorSuccess(text string) string {
	return ColorGreen + ColorBold + text + ColorReset
}

func ColorError(text string) string {
	return ColorRed + ColorBold + text + ColorReset
}

func ColorWarning(text string) string {
	return ColorYellow + ColorBold + text + ColorReset
}

func ColorInfo(text string) string {
	return ColorBlue + ColorBold + text + ColorReset
}

func ColorTest(text string) string {
	return ColorCyan + ColorBold + text + ColorReset
}

func ColorSubtest(text string) string {
	return ColorPurple + text + ColorReset
}

// AddAuthHeader adds an authentication header to the HTTP client.
// It reads the token directly from the token.txt file.
func AddAuthHeader(client *http.Client) {
	client.Transport = &AuthTransport{
		base: client.Transport,
	}
}

// AuthTransport is a custom RoundTripper that adds authorization headers.
type AuthTransport struct {
	base http.RoundTripper
}

// RoundTrip implements the http.RoundTripper interface
func (transport *AuthTransport) RoundTrip(request *http.Request) (*http.Response, error) {
	token, err := getAccessToken()
	if err != nil {
		fmt.Printf("Warning: Could not get access token: %v\n", err)
	} else {
		request.Header.Set("Authorization", "Bearer "+token)
	}

	// If the base RoundTripper is nil, use the default one
	if transport.base == nil {
		return http.DefaultTransport.RoundTrip(request)
	}

	return transport.base.RoundTrip(request)
}

// getAccessToken retrieves the access token for authentication
// It reads the token directly from the token.txt file
func getAccessToken() (string, error) {
	// Read from token.txt file
	tokenBytes, err := os.ReadFile("token.txt")
	if err != nil {
		return "", fmt.Errorf("failed to read token.txt: %w", err)
	}

	// Clean the token (remove whitespace)
	token := strings.TrimSpace(string(tokenBytes))
	if token == "" {
		return "", fmt.Errorf("token.txt is empty")
	}

	return token, nil
}
