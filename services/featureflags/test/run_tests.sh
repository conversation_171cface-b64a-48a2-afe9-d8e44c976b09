#!/bin/bash

echo "🚀 Running Feature Flags Service E2E Tests"
echo "==========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default options
NO_CACHE=""

# Check for nocache argument
if [ "$1" == "nocache" ]; then
  NO_CACHE="-count=1"
  echo -e "${YELLOW}Bypassing test cache...${NC}"
fi

# Get directory of this script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
TOKEN_FILE="$SCRIPT_DIR/token.txt"

# Check if token file exists
if [ ! -f "$TOKEN_FILE" ]; then
    echo -e "${RED}❌ Error: Token file '$TOKEN_FILE' not found!${NC}"
    echo "Please create a token.txt file with a valid authentication token"
    echo "You can get a token from the auth service or use a test token"
    exit 1
fi

# Read the token from the file
TOKEN=$(cat "$TOKEN_FILE" | tr -d '\n\r ')

# Verify token is not empty
if [ -z "$TOKEN" ]; then
    echo -e "${RED}❌ Error: No token found in '$TOKEN_FILE'.${NC}"
    exit 1
fi

# Export the token as environment variable
export COGNITO_ACCESS_TOKEN="$TOKEN"

# Check if services are running
echo "Checking if services are running..."

# Check Feature Flags service
if ! curl -s -o /dev/null -w "%{http_code}" http://localhost:9090/health | grep -q "200"; then
    echo -e "${RED}❌ Feature Flags service is not running on port 9090${NC}"
    echo "Please start the service with: docker-compose up featureflags-service"
    exit 1
fi

# Check Assets service (needed for test assets)
if ! curl -s -o /dev/null -w "%{http_code}" http://localhost:9086/health | grep -q "200"; then
    echo -e "${RED}❌ Assets service is not running on port 9086${NC}"
    echo "Please start the service with: docker-compose up workflow-service"
    exit 1
fi

echo -e "${GREEN}✅ All required services are running${NC}"
echo

# Change to the featureflags module directory
cd "$SCRIPT_DIR/../"

# Run the tests
echo "Running E2E tests..."
go test -v $NO_CACHE -timeout 60s ./test -run TestE2E_FeatureFlagsService

# Check test results
if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ All tests passed!${NC}"
else
    echo -e "${RED}❌ Some tests failed${NC}"
    exit 1
fi