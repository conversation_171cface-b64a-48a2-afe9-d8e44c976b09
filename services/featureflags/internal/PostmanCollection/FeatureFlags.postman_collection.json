{"info": {"_postman_id": "af802e03-d134-4773-a932-5cac6d33da9a", "name": "FeatureFlags", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "11771203"}, "item": [{"name": "Local", "item": [{"name": "V1", "item": [{"name": "Is<PERSON>nabled - <PERSON> Org-wide", "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "grant_type", "value": "authorization_code", "type": "string"}, {"key": "redirect_uri", "value": "https://command.gethero.com", "type": "string"}, {"key": "useBrowser", "value": false, "type": "boolean"}, {"key": "clientId", "value": "52s9qm0anac1sohgioj2upgp1s", "type": "string"}, {"key": "scope", "value": "openid", "type": "string"}, {"key": "accessTokenUrl", "value": "https://gethero.auth.us-west-2.amazoncognito.com/oauth2/token", "type": "string"}, {"key": "clientSecret", "value": "", "type": "string"}, {"key": "authUrl", "value": "https://gethero.auth.us-west-2.amazoncognito.com/oauth2/authorize", "type": "string"}, {"key": "password", "value": "abCD@1234", "type": "string"}, {"key": "username", "value": "<EMAIL>", "type": "string"}, {"key": "tokenName", "value": "AWS Cognito", "type": "string"}, {"key": "client_authentication", "value": "body", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"orgId\": 1,\n    \"feature\": \"FEATURE_EXPERIMENTAL_CAMERA\",\n    \"assetId\": \"\"\n}"}, "url": {"raw": "http://localhost:9090/hero.featureflags.v1.FeatureFlagsService/IsEnabled", "protocol": "http", "host": ["localhost"], "port": "9090", "path": ["hero.featureflags.v1.FeatureFlagsService", "IsEnabled"]}}, "response": []}, {"name": "IsEnabled - Check Asset-specific", "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "grant_type", "value": "authorization_code", "type": "string"}, {"key": "redirect_uri", "value": "https://command.gethero.com", "type": "string"}, {"key": "useBrowser", "value": false, "type": "boolean"}, {"key": "clientId", "value": "52s9qm0anac1sohgioj2upgp1s", "type": "string"}, {"key": "scope", "value": "openid", "type": "string"}, {"key": "accessTokenUrl", "value": "https://gethero.auth.us-west-2.amazoncognito.com/oauth2/token", "type": "string"}, {"key": "clientSecret", "value": "", "type": "string"}, {"key": "authUrl", "value": "https://gethero.auth.us-west-2.amazoncognito.com/oauth2/authorize", "type": "string"}, {"key": "password", "value": "abCD@1234", "type": "string"}, {"key": "username", "value": "<EMAIL>", "type": "string"}, {"key": "tokenName", "value": "AWS Cognito", "type": "string"}, {"key": "client_authentication", "value": "body", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"orgId\": 1,\n    \"feature\": \"FEATURE_EXPERIMENTAL_PUSH_TO_TALK\",\n    \"assetId\": \"12345-67890-abcdef\"\n}"}, "url": {"raw": "http://localhost:9090/hero.featureflags.v1.FeatureFlagsService/IsEnabled", "protocol": "http", "host": ["localhost"], "port": "9090", "path": ["hero.featureflags.v1.FeatureFlagsService", "IsEnabled"]}}, "response": []}, {"name": "SetFeatureTarget - Org-wide", "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "grant_type", "value": "authorization_code", "type": "string"}, {"key": "redirect_uri", "value": "https://command.gethero.com", "type": "string"}, {"key": "useBrowser", "value": false, "type": "boolean"}, {"key": "clientId", "value": "52s9qm0anac1sohgioj2upgp1s", "type": "string"}, {"key": "scope", "value": "openid", "type": "string"}, {"key": "accessTokenUrl", "value": "https://gethero.auth.us-west-2.amazoncognito.com/oauth2/token", "type": "string"}, {"key": "clientSecret", "value": "", "type": "string"}, {"key": "authUrl", "value": "https://gethero.auth.us-west-2.amazoncognito.com/oauth2/authorize", "type": "string"}, {"key": "password", "value": "abCD@1234", "type": "string"}, {"key": "username", "value": "<EMAIL>", "type": "string"}, {"key": "tokenName", "value": "AWS Cognito", "type": "string"}, {"key": "client_authentication", "value": "body", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"orgId\": 1,\n    \"feature\": \"FEATURE_EXPERIMENTAL_CAMERA\",\n    \"assetId\": \"\",\n    \"enabled\": true\n}"}, "url": {"raw": "http://localhost:9090/hero.featureflags.v1.FeatureFlagsService/SetFeatureTarget", "protocol": "http", "host": ["localhost"], "port": "9090", "path": ["hero.featureflags.v1.FeatureFlagsService", "SetFeatureTarget"]}}, "response": []}, {"name": "SetMultipleFeatures", "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "grant_type", "value": "authorization_code", "type": "string"}, {"key": "redirect_uri", "value": "https://command.gethero.com", "type": "string"}, {"key": "useBrowser", "value": false, "type": "boolean"}, {"key": "clientId", "value": "52s9qm0anac1sohgioj2upgp1s", "type": "string"}, {"key": "scope", "value": "openid", "type": "string"}, {"key": "accessTokenUrl", "value": "https://gethero.auth.us-west-2.amazoncognito.com/oauth2/token", "type": "string"}, {"key": "clientSecret", "value": "", "type": "string"}, {"key": "authUrl", "value": "https://gethero.auth.us-west-2.amazoncognito.com/oauth2/authorize", "type": "string"}, {"key": "password", "value": "abCD@1234", "type": "string"}, {"key": "username", "value": "<EMAIL>", "type": "string"}, {"key": "tokenName", "value": "AWS Cognito", "type": "string"}, {"key": "client_authentication", "value": "body", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"orgId\": 1,\n    \"assetId\": \"\",\n    \"enableFeatures\": [\"FEATURE_EXPERIMENTAL_CAMERA\", \"FEATURE_EXPERIMENTAL_PUSH_TO_TALK\"],\n    \"disableFeatures\": [\"FEATURE_EXPERIMENTAL_DEMO\"]\n}"}, "url": {"raw": "http://localhost:9090/hero.featureflags.v1.FeatureFlagsService/SetMultipleFeatures", "protocol": "http", "host": ["localhost"], "port": "9090", "path": ["hero.featureflags.v1.FeatureFlagsService", "SetMultipleFeatures"]}}, "response": []}, {"name": "GetEnabledFeatures", "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "grant_type", "value": "authorization_code", "type": "string"}, {"key": "redirect_uri", "value": "https://command.gethero.com", "type": "string"}, {"key": "useBrowser", "value": false, "type": "boolean"}, {"key": "clientId", "value": "52s9qm0anac1sohgioj2upgp1s", "type": "string"}, {"key": "scope", "value": "openid", "type": "string"}, {"key": "accessTokenUrl", "value": "https://gethero.auth.us-west-2.amazoncognito.com/oauth2/token", "type": "string"}, {"key": "clientSecret", "value": "", "type": "string"}, {"key": "authUrl", "value": "https://gethero.auth.us-west-2.amazoncognito.com/oauth2/authorize", "type": "string"}, {"key": "password", "value": "abCD@1234", "type": "string"}, {"key": "username", "value": "<EMAIL>", "type": "string"}, {"key": "tokenName", "value": "AWS Cognito", "type": "string"}, {"key": "client_authentication", "value": "body", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"orgId\": 1,\n    \"assetId\": \"\"\n}"}, "url": {"raw": "http://localhost:9090/hero.featureflags.v1.FeatureFlagsService/GetEnabledFeatures", "protocol": "http", "host": ["localhost"], "port": "9090", "path": ["hero.featureflags.v1.FeatureFlagsService", "GetEnabledFeatures"]}}, "response": []}]}]}], "variable": [{"key": "baseURL", "value": "http://localhost:9090", "type": "default"}]}