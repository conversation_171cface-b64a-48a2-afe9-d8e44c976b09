package featureflags

import (
	"database/sql"
	"log"
	"net/http"

	featureflagsconnect "proto/hero/featureflags/v1/featureflagsconnect"

	featureflagsconnectapi "featureflags/internal/api/connect"
	featureflagsrepository "featureflags/internal/data"
	"featureflags/internal/usecase"
)

// Simple logging middleware
func loggingMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(responseWriter http.ResponseWriter, request *http.Request) {
		log.Printf("FeatureFlags: %s %s", request.Method, request.URL.Path)
		next.ServeHTTP(responseWriter, request)
	})
}

// RegisterRoutes wires all HTTP handlers for the feature flags service onto the given mux.
// It expects a fully-initialized *sql.DB and repository implementation.
func RegisterRoutes(
	mux *http.ServeMux,
	database *sql.DB,
	featureFlagsRepository featureflagsrepository.FeatureFlagsRepository,
) {
	// Construct the domain use-case layer
	featureFlagsUseCase, err := usecase.NewFeatureFlagsUseCase(database, featureFlagsRepository)
	if err != nil {
		log.Fatalf("Failed to initialize Feature Flags Use Case: %v", err)
	}

	// Construct the Connect RPC server
	featureFlagsServer := featureflagsconnectapi.NewFeatureFlagsServer(featureFlagsUseCase, nil)

	// Generate HTTP handler from Connect
	servicePath, serviceHandler := featureflagsconnect.NewFeatureFlagsServiceHandler(featureFlagsServer)

	// Add handler to the mux with logging middleware
	mux.Handle(servicePath, loggingMiddleware(serviceHandler))

	log.Printf("Feature flags service registered at path: %s", servicePath)
}
