package connect

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"log/slog"

	"connectrpc.com/connect"

	featureflags "proto/hero/featureflags/v1"
	featureflagsconnect "proto/hero/featureflags/v1/featureflagsconnect"

	"featureflags/internal/usecase"
)

// FeatureFlagsServer implements all RPCs defined in FeatureFlagsService.
// Each handler validates the request, invokes the corresponding
// business-logic method on FeatureFlagsUseCase, logs the call, and converts
// domain errors into Connect error codes.
type FeatureFlagsServer struct {
	featureflagsconnect.UnimplementedFeatureFlagsServiceHandler

	featureFlagsUseCase *usecase.FeatureFlagsUseCase
	logger              *slog.Logger
}

// NewFeatureFlagsServer constructs a FeatureFlagsServer. If logger is nil, slog.Default()
// is used so that a non-nil logger is always available.
func NewFeatureFlagsServer(featureFlagsUseCase *usecase.FeatureFlagsUseCase, logger *slog.Logger) *FeatureFlagsServer {
	if logger == nil {
		logger = slog.Default()
	}
	return &FeatureFlagsServer{
		featureFlagsUseCase: featureFlagsUseCase,
		logger:              logger.With("component", "FeatureFlagsServer"),
	}
}

// translate domain errors → Connect errors.
func (server *FeatureFlagsServer) asConnectError(domainError error, message string) error {
	if domainError == nil {
		return nil
	}

	// Handle specific errors
	switch {
	case errors.Is(domainError, sql.ErrNoRows):
		return connect.NewError(connect.CodeNotFound, domainError)
	default:
		return connect.NewError(connect.CodeInternal, fmt.Errorf("%s: %w", message, domainError))
	}
}

// IsEnabled checks if a feature is enabled for a specific target
func (server *FeatureFlagsServer) IsEnabled(
	context context.Context,
	request *connect.Request[featureflags.IsEnabledRequest],
) (*connect.Response[featureflags.IsEnabledResponse], error) {
	server.logger.InfoContext(context, "IsEnabled called",
		"org_id", request.Msg.OrgId,
		"feature", request.Msg.Feature,
		"asset_id", request.Msg.AssetId,
	)

	response, err := server.featureFlagsUseCase.IsEnabled(context, request.Msg)
	if err != nil {
		server.logger.ErrorContext(context, "IsEnabled failed", "error", err)
		return nil, server.asConnectError(err, "failed to check feature status")
	}

	server.logger.DebugContext(context, "IsEnabled completed",
		"enabled", response.Enabled,
	)

	return connect.NewResponse(response), nil
}

// SetFeatureTarget enables or disables a feature for a specific target
func (server *FeatureFlagsServer) SetFeatureTarget(
	context context.Context,
	request *connect.Request[featureflags.SetFeatureTargetRequest],
) (*connect.Response[featureflags.SetFeatureTargetResponse], error) {
	server.logger.InfoContext(context, "SetFeatureTarget called",
		"org_id", request.Msg.OrgId,
		"feature", request.Msg.Feature,
		"asset_id", request.Msg.AssetId,
		"enabled", request.Msg.Enabled,
	)

	response, err := server.featureFlagsUseCase.SetFeatureTarget(context, request.Msg)
	if err != nil {
		server.logger.ErrorContext(context, "SetFeatureTarget failed", "error", err)
		return nil, server.asConnectError(err, "failed to set feature target")
	}

	server.logger.InfoContext(context, "SetFeatureTarget completed",
		"success", response.Success,
	)

	return connect.NewResponse(response), nil
}

// SetMultipleFeatures enables/disables multiple features for a target in a single operation
func (server *FeatureFlagsServer) SetMultipleFeatures(
	context context.Context,
	request *connect.Request[featureflags.SetMultipleFeaturesRequest],
) (*connect.Response[featureflags.SetMultipleFeaturesResponse], error) {
	server.logger.InfoContext(context, "SetMultipleFeatures called",
		"org_id", request.Msg.OrgId,
		"asset_id", request.Msg.AssetId,
		"enable_count", len(request.Msg.EnableFeatures),
		"disable_count", len(request.Msg.DisableFeatures),
	)

	response, err := server.featureFlagsUseCase.SetMultipleFeatures(context, request.Msg)
	if err != nil {
		server.logger.ErrorContext(context, "SetMultipleFeatures failed", "error", err)
		return nil, server.asConnectError(err, "failed to set multiple features")
	}

	server.logger.InfoContext(context, "SetMultipleFeatures completed",
		"success", response.Success,
	)

	return connect.NewResponse(response), nil
}

// GetEnabledFeatures returns all enabled features for a target
func (server *FeatureFlagsServer) GetEnabledFeatures(
	context context.Context,
	request *connect.Request[featureflags.GetEnabledFeaturesRequest],
) (*connect.Response[featureflags.GetEnabledFeaturesResponse], error) {
	server.logger.InfoContext(context, "GetEnabledFeatures called",
		"org_id", request.Msg.OrgId,
		"asset_id", request.Msg.AssetId,
	)

	response, err := server.featureFlagsUseCase.GetEnabledFeatures(context, request.Msg)
	if err != nil {
		server.logger.ErrorContext(context, "GetEnabledFeatures failed", "error", err)
		return nil, server.asConnectError(err, "failed to get enabled features")
	}

	server.logger.InfoContext(context, "GetEnabledFeatures completed",
		"feature_count", len(response.EnabledFeatures),
	)

	return connect.NewResponse(response), nil
}
