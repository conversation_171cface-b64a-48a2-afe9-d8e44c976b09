package repository

import (
	"context"
	"database/sql"
	"errors"
	"fmt"

	featureflags "proto/hero/featureflags/v1"
)

// ErrFeatureFlagNotFound indicates a missing feature flag in the database.
var ErrFeatureFlagNotFound = fmt.Errorf("feature flag not found")

// FeatureFlagsRepository defines operations for managing feature flags.
type FeatureFlagsRepository interface {
	// SetFeatureTarget enables or disables a feature for a specific target
	SetFeatureTarget(context context.Context, transaction *sql.Tx, organizationID int32, feature featureflags.Feature, assetID string, enabled bool) error

	// SetMultipleFeatures enables/disables multiple features for a target in a single operation
	SetMultipleFeatures(context context.Context, transaction *sql.Tx, organizationID int32, assetID string, enableFeatures []featureflags.Feature, disableFeatures []featureflags.Feature) error

	// IsEnabled checks if a feature is enabled for a specific target
	IsEnabled(context context.Context, transaction *sql.Tx, organizationID int32, feature featureflags.Feature, assetID string) (enabled bool, reason string, err error)

	// GetEnabledFeatures returns all enabled features for a target
	GetEnabledFeatures(context context.Context, transaction *sql.Tx, organizationID int32, assetID string) ([]featureflags.Feature, error)

	// DeleteFeatureTarget removes a feature setting for a specific target
	DeleteFeatureTarget(context context.Context, transaction *sql.Tx, organizationID int32, feature featureflags.Feature, assetID string) error
}

// NewFeatureFlagsRepository returns a Postgres-backed implementation.
func NewFeatureFlagsRepository(database *sql.DB) (FeatureFlagsRepository, *sql.DB, error) {
	if database == nil {
		return nil, nil, errors.New("database is nil: cannot initialize FeatureFlagsRepository")
	}
	return NewPostgresFeatureFlagsRepository(database), database, nil
}
