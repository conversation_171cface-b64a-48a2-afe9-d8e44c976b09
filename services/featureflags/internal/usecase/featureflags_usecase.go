package usecase

import (
	"context"
	"database/sql"
	"fmt"

	featureflags "proto/hero/featureflags/v1"

	featureFlagsRepository "featureflags/internal/data"
)

// FeatureFlagsUseCase groups business-level operations for feature flags and
// delegates data persistence to repository implementations.
type FeatureFlagsUseCase struct {
	databaseConnection     *sql.DB
	featureFlagsRepository featureFlagsRepository.FeatureFlagsRepository
}

// NewFeatureFlagsUseCase constructs a fully-initialized FeatureFlagsUseCase instance.
func NewFeatureFlagsUseCase(
	databaseConnection *sql.DB,
	featureFlagsRepository featureFlagsRepository.FeatureFlagsRepository,
) (*FeatureFlagsUseCase, error) {
	if databaseConnection == nil {
		return nil, fmt.Errorf("database connection is nil")
	}
	if featureFlagsRepository == nil {
		return nil, fmt.Errorf("feature flags repository must not be nil")
	}

	return &FeatureFlagsUseCase{
		databaseConnection:     databaseConnection,
		featureFlagsRepository: featureFlagsRepository,
	}, nil
}

// executeInTransaction wraps a series of repository calls in a SQL transaction, ensuring
// commit on success and rollback on error or panic.
func (useCase *FeatureFlagsUseCase) executeInTransaction(context context.Context, transactionalWork func(transaction *sql.Tx) error) error {
	transaction, transactionError := useCase.databaseConnection.BeginTx(context, nil)
	if transactionError != nil {
		return fmt.Errorf("failed to begin transaction: %w", transactionError)
	}

	defer func() {
		if recoverError := recover(); recoverError != nil {
			_ = transaction.Rollback()
			panic(recoverError)
		}
	}()

	if err := transactionalWork(transaction); err != nil {
		_ = transaction.Rollback()
		return err
	}

	if commitError := transaction.Commit(); commitError != nil {
		return fmt.Errorf("failed to commit transaction: %w", commitError)
	}

	return nil
}

// IsEnabled checks if a feature is enabled for a specific target
func (useCase *FeatureFlagsUseCase) IsEnabled(context context.Context, request *featureflags.IsEnabledRequest) (*featureflags.IsEnabledResponse, error) {
	if request.OrgId == 0 {
		return nil, fmt.Errorf("organization ID is required")
	}
	if request.Feature == featureflags.Feature_FEATURE_UNSPECIFIED {
		return nil, fmt.Errorf("feature is required")
	}

	enabled, reason, err := useCase.featureFlagsRepository.IsEnabled(
		context,
		nil, // No transaction needed for read operation
		request.OrgId,
		request.Feature,
		request.AssetId,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to check feature status: %w", err)
	}

	return &featureflags.IsEnabledResponse{
		Enabled:   enabled,
		Evaluated: true,
		Reason:    reason,
	}, nil
}

// SetFeatureTarget enables or disables a feature for a specific target
func (useCase *FeatureFlagsUseCase) SetFeatureTarget(context context.Context, request *featureflags.SetFeatureTargetRequest) (*featureflags.SetFeatureTargetResponse, error) {
	if request.OrgId == 0 {
		return nil, fmt.Errorf("organization ID is required")
	}
	if request.Feature == featureflags.Feature_FEATURE_UNSPECIFIED {
		return nil, fmt.Errorf("feature is required")
	}

	err := useCase.executeInTransaction(context, func(transaction *sql.Tx) error {
		return useCase.featureFlagsRepository.SetFeatureTarget(
			context,
			transaction,
			request.OrgId,
			request.Feature,
			request.AssetId,
			request.Enabled,
		)
	})
	if err != nil {
		return nil, fmt.Errorf("failed to set feature target: %w", err)
	}

	return &featureflags.SetFeatureTargetResponse{
		Success: true,
	}, nil
}

// SetMultipleFeatures enables/disables multiple features for a target in a single operation
func (useCase *FeatureFlagsUseCase) SetMultipleFeatures(context context.Context, request *featureflags.SetMultipleFeaturesRequest) (*featureflags.SetMultipleFeaturesResponse, error) {
	if request.OrgId == 0 {
		return nil, fmt.Errorf("organization ID is required")
	}

	// Validate features
	for _, feature := range request.EnableFeatures {
		if feature == featureflags.Feature_FEATURE_UNSPECIFIED {
			return nil, fmt.Errorf("invalid feature in enable list")
		}
	}
	for _, feature := range request.DisableFeatures {
		if feature == featureflags.Feature_FEATURE_UNSPECIFIED {
			return nil, fmt.Errorf("invalid feature in disable list")
		}
	}

	err := useCase.executeInTransaction(context, func(transaction *sql.Tx) error {
		return useCase.featureFlagsRepository.SetMultipleFeatures(
			context,
			transaction,
			request.OrgId,
			request.AssetId,
			request.EnableFeatures,
			request.DisableFeatures,
		)
	})
	if err != nil {
		return nil, fmt.Errorf("failed to set multiple features: %w", err)
	}

	return &featureflags.SetMultipleFeaturesResponse{
		Success: true,
	}, nil
}

// GetEnabledFeatures returns all enabled features for a target
func (useCase *FeatureFlagsUseCase) GetEnabledFeatures(context context.Context, request *featureflags.GetEnabledFeaturesRequest) (*featureflags.GetEnabledFeaturesResponse, error) {
	if request.OrgId == 0 {
		return nil, fmt.Errorf("organization ID is required")
	}

	enabledFeatures, err := useCase.featureFlagsRepository.GetEnabledFeatures(
		context,
		nil, // No transaction needed for read operation
		request.OrgId,
		request.AssetId,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get enabled features: %w", err)
	}

	return &featureflags.GetEnabledFeaturesResponse{
		EnabledFeatures: enabledFeatures,
	}, nil
}
