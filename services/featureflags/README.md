## Feature Flags Module

The **Feature Flags** module provides a centralized service for managing feature toggles across the Hero platform. It enables controlled feature rollouts at both organization-wide and asset-specific levels, allowing for gradual deployments and A/B testing scenarios.

Feature flags help to:
- **Control Feature Rollouts:** Enable or disable features without code deployments
- **Asset-Specific Targeting:** Override organization defaults for specific assets
- **Bulk Operations:** Efficiently manage multiple features at once
- **Simple Integration:** Easy-to-use API for checking feature states

---

# Data Model Reference

## Enums

### Feature

| Name                              | Value | Description                           |
|-----------------------------------|-------|---------------------------------------|
| FEATURE_UNSPECIFIED               | 0     | Default unset state.                  |
| FEATURE_EXPERIMENTAL_CAMERA       | 1     | Experimental camera features          |
| FEATURE_EXPERIMENTAL_PUSH_TO_TALK | 2     | Experimental push-to-talk feature     |
| FEATURE_EXPERIMENTAL_DEMO         | 3     | Experimental demo features            |

---

### Evaluation Logic

When checking if a feature is enabled:
1. First check for asset-specific setting (if asset_id provided)
2. Fall back to organization-wide setting
3. Default to disabled if no setting exists

---

## Overview of Endpoints

1. [IsEnabled](#1-isenabled)
2. [SetFeatureTarget](#2-setfeaturetarget)
3. [SetMultipleFeatures](#3-setmultiplefeatures)
4. [GetEnabledFeatures](#4-getenabledfeatures)

---

### 1. IsEnabled

**Method:** `IsEnabled`  
**Route:** `POST /hero.featureflags.v1.FeatureFlagsService/IsEnabled`

#### Request

**IsEnabledRequest:**

| Field    | Type    | Description                                        |
|----------|---------|----------------------------------------------------|
| org_id   | int32   | Organization ID.                                   |
| feature  | Feature | Feature to check.                                  |
| asset_id | string  | *(Optional)* Asset ID for asset-specific check.   |

**Sample Request (JSON):**
```json
{
  "org_id": 123,
  "feature": "FEATURE_EXPERIMENTAL_CAMERA",
  "asset_id": "asset_456"
}
```

#### Response

**IsEnabledResponse:**

| Field     | Type   | Description                                                     |
|-----------|--------|-----------------------------------------------------------------|
| enabled   | bool   | Whether the feature is enabled.                                |
| evaluated | bool   | Always true to indicate evaluation happened.                   |
| reason    | string | Evaluation source: "default", "org_setting", "asset_setting"   |

**Sample Response (JSON):**
```json
{
  "enabled": true,
  "evaluated": true,
  "reason": "asset_setting"
}
```

---

### 2. SetFeatureTarget

**Method:** `SetFeatureTarget`  
**Route:** `POST /hero.featureflags.v1.FeatureFlagsService/SetFeatureTarget`

#### Request

**SetFeatureTargetRequest:**

| Field    | Type    | Description                                       |
|----------|---------|---------------------------------------------------|
| org_id   | int32   | Organization ID.                                  |
| feature  | Feature | Feature to set.                                   |
| asset_id | string  | Asset ID (empty for org-wide).                   |
| enabled  | bool    | Enable or disable the feature.                   |

**Sample Request (JSON):**
```json
{
  "org_id": 123,
  "feature": "FEATURE_EXPERIMENTAL_CAMERA",
  "asset_id": "asset_456",
  "enabled": true
}
```

#### Response

**SetFeatureTargetResponse:**

| Field   | Type | Description            |
|---------|------|------------------------|
| success | bool | Operation success flag.|

**Sample Response (JSON):**
```json
{
  "success": true
}
```

---

### 3. SetMultipleFeatures

**Method:** `SetMultipleFeatures`  
**Route:** `POST /hero.featureflags.v1.FeatureFlagsService/SetMultipleFeatures`

#### Request

**SetMultipleFeaturesRequest:**

| Field            | Type              | Description                    |
|------------------|-------------------|--------------------------------|
| org_id           | int32             | Organization ID.               |
| asset_id         | string            | Asset ID (empty for org-wide). |
| enable_features  | repeated Feature  | Features to enable.            |
| disable_features | repeated Feature  | Features to disable.           |

**Sample Request (JSON):**
```json
{
  "org_id": 123,
  "asset_id": "asset_456",
  "enable_features": ["FEATURE_EXPERIMENTAL_CAMERA", "FEATURE_EXPERIMENTAL_PUSH_TO_TALK"],
  "disable_features": ["FEATURE_EXPERIMENTAL_DEMO"]
}
```

#### Response

**SetMultipleFeaturesResponse:**

| Field   | Type | Description            |
|---------|------|------------------------|
| success | bool | Operation success flag.|

**Sample Response (JSON):**
```json
{
  "success": true
}
```

---

### 4. GetEnabledFeatures

**Method:** `GetEnabledFeatures`  
**Route:** `POST /hero.featureflags.v1.FeatureFlagsService/GetEnabledFeatures`

#### Request

**GetEnabledFeaturesRequest:**

| Field    | Type   | Description                              |
|----------|--------|------------------------------------------|
| org_id   | int32  | Organization ID.                         |
| asset_id | string | *(Optional)* Asset ID for asset query.   |

**Sample Request (JSON):**
```json
{
  "org_id": 123,
  "asset_id": "asset_456"
}
```

#### Response

**GetEnabledFeaturesResponse:**

| Field            | Type             | Description               |
|------------------|------------------|---------------------------|
| enabled_features | repeated Feature | List of enabled features. |

**Sample Response (JSON):**
```json
{
  "enabled_features": [
    "FEATURE_EXPERIMENTAL_CAMERA",
    "FEATURE_EXPERIMENTAL_PUSH_TO_TALK"
  ]
}
```

---

## Usage Examples

### Checking a Feature

```go
// Check if experimental camera is enabled for a specific asset
response, err := client.IsEnabled(context, &featureflags.IsEnabledRequest{
    OrgId: 123,
    Feature: featureflags.Feature_FEATURE_EXPERIMENTAL_CAMERA,
    AssetId: "asset_456",
})

if response.Enabled {
    // Enable experimental camera features
} else {
    // Use standard camera features
}
```

### Enabling Features Organization-Wide

```go
// Enable a feature for the entire organization
response, err := client.SetFeatureTarget(context, &featureflags.SetFeatureTargetRequest{
    OrgId: 123,
    Feature: featureflags.Feature_FEATURE_EXPERIMENTAL_PUSH_TO_TALK,
    AssetId: "", // Empty string = org-wide
    Enabled: true,
})
```

### Bulk Operations

```go
// Enable multiple features for a specific asset
response, err := client.SetMultipleFeatures(context, &featureflags.SetMultipleFeaturesRequest{
    OrgId: 123,
    AssetId: "asset_456",
    EnableFeatures: []featureflags.Feature{
        featureflags.Feature_FEATURE_EXPERIMENTAL_CAMERA,
        featureflags.Feature_FEATURE_EXPERIMENTAL_PUSH_TO_TALK,
    },
    DisableFeatures: []featureflags.Feature{
        featureflags.Feature_FEATURE_EXPERIMENTAL_DEMO,
    },
})
```

---

## Environment Variables

- `DATABASE_URL` or `DB_*` - PostgreSQL connection details
- `SKIP_PERMISSIONS_CHECK` - Skip auth/permissions checks (development only)

---

## Testing

### Postman Collection

A Postman collection is available at `internal/PostmanCollection/FeatureFlags.postman_collection.json` with pre-configured requests for all APIs

### Running E2E Tests

```bash
cd services/featureflags/test
./run_tests.sh          # Run tests with cache
./run_tests.sh nocache  # Run tests without cache
```

Make sure to create a `token.txt` file with a valid authentication token before running tests.

---