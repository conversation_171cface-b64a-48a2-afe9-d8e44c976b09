package main

import (
	"common/database"
	"common/middleware"
	"database/sql"
	"log"
	"net/http"
	"os"

	featureflags "featureflags/internal"
	featureflagsRepository "featureflags/internal/data"
)

func main() {
	mux := http.NewServeMux()

	// Initialize PostgreSQL database
	databaseURL, err := database.CreateDBURL()
	if err != nil {
		log.Fatalf("Failed to get postgres db url: %v", err)
	}

	postgresDatabase, openError := sql.Open("postgres", databaseURL)
	if openError != nil {
		log.Fatalf("Failed to open postgres db: %v", openError)
	}

	// Initialize Feature Flags Repository
	featureFlagsRepo, featureFlagsDB, err := featureflagsRepository.NewFeatureFlagsRepository(postgresDatabase)
	if err != nil {
		log.Fatalf("Failed to initialize feature flags repository: %v", err)
	}

	// Register all endpoints
	featureflags.RegisterRoutes(mux, featureFlagsDB, featureFlagsRepo)

	// Create a new mux for health endpoints that bypasses auth
	healthMux := middleware.NewHealthMux(middleware.HealthMuxConfig{
		ServiceNames: []string{
			"hero.featureflags.v1.FeatureFlagsService",
		},
		HealthResponse: "FEATURE FLAGS SERVICE IS HEALTHY",
	})

	skipPermissions := os.Getenv("SKIP_PERMISSIONS_CHECK") == "true"
	server := middleware.NewServerWithHealth(
		":8080",
		mux,
		healthMux,
		!skipPermissions,
	)

	log.Println("Feature flags server listening on http://localhost:8080")
	if err := server.ListenAndServe(); err != nil {
		log.Fatalf("Failed to serve: %v", err)
	}
}
